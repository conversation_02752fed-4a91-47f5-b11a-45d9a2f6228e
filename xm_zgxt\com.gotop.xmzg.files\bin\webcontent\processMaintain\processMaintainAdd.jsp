<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%@page import="com.eos.data.datacontext.UserObject" %>
<%@include file="/coframe/dict/common.jsp"%>
<html>
<!-- 
  - Author(s): lzd
  - Date: 2018-06-04 09:36:10
  - Description:
-->
<head>
</head>
<%
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>
<body>
	<div id="fileTypeForm" style="padding-top:5px;">
		<table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
			<div class="nui-hidden"  name="inputData.orgId" value="<%=userObject.getUserOrgId()%>" ></div>
	      	<tr>
				<th class="nui-form-label"><label for="type$text">流程名称：</label></th>
				<td colspan="4">
					<input id="PROCESS_NAME" class="nui-dictcombobox nui-form-input" name="inputData.PROCESS_NAME"  emptyText="请选择"
  					valueField="dictID" textField="dictName" dictTypeId="APPLY_PROCESS" showNullItem="true" nullItemText="请选择"  required="true"/>
  				</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">起始活动节点：</label></th>
				<td colspan="4">
					<input name="inputData.START_NODENAME" id="START_NODENAME"  class="nui-hidden" style="width: 100%;" />
					
					  <input id="START_NODEID" name="inputData.START_NODEID"  class="nui-dictcombobox nui-form-input"  textField="ACTIVITYINSTNAME" valueField="ACTIVITYINSTID"   
              nullItemText="请选择" emptyText="请选择" showNullItem="true"  style="width:200px;" required="true" /> 
  				</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">终止活动节点：</label></th>
				<td colspan="4">
					<input name="inputData.END_NODENAME" id="END_NODENAME"  class="nui-hidden" style="width: 100%;" />
					
					  <input id="END_NODEID" name="inputData.END_NODEID"  class="nui-dictcombobox nui-form-input"  textField="ACTIVITYINSTNAME" valueField="ACTIVITYINSTID"   
              nullItemText="请选择" emptyText="请选择" showNullItem="true"  style="width:200px;" required="true" /> 
  				</td>
	      	</tr>
	
	      	<tr id="APPOINT_ACTIVITYINSTID" >
	      		<th class="nui-form-label"><label for="type$text">线的名称：</label></th>
	      		<td colspan="4">
	      			<input name="inputData.LINE_NAME" id="LINE_NAME"  class="nui-textbox" style="width: 100%;" required="true" />
	      		</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">排序：</label></th>
				<td colspan="4">
					<input name="inputData.SORT" id=SORT  class="nui-textbox" style="width: 100%;" />
  				</td>
	      	</tr>
		</table>
	    <div class="nui-toolbar" style="padding:0px;"   borderStyle="border:0;">
			<table width="100%">
		    	<tr>
		        	<td style="text-align:center;">
		          		<a class="nui-button" iconCls="icon-save" id="saveButtorn"  onclick="saveData">保存</a>
		          		<span style="display:inline-block;width:25px;"></span>
		          		<a class="nui-button" iconCls="icon-cancel" onclick="CloseWindow('cancel')">取消</a>
		        	</td>
		      	</tr>
		   	</table>
		</div>
	</div>
<script type="text/javascript">
    nui.parse();
    var fileTypeForm = new nui.Form("fileTypeForm");
    
        var START_NODEID = nui.get("START_NODEID");
         var END_NODEID = nui.get("END_NODEID");
         
         
         var res = nui.get("PROCESS_NAME");
		    res.on("valuechanged", function (e) {
		    var PROCESS_NAME=this.getValue();
		        $.ajax({
						url : "com.gotop.xmzg.files.processMaintain.queryNode.biz.ext",
						type : 'POST',
						data : 'PROCESS_NAME='+PROCESS_NAME,
						cache : false,
						async : false,
						dataType : 'json',
						success : function(text) {
                         var obj = nui.decode(text.resultList);
                         START_NODEID.load(obj);
                         END_NODEID.load(obj);
						}
					});
     
		      
		    });  
		    
		     START_NODEID.on("valuechanged", function (e) {
		     nui.get("START_NODENAME").setValue(e.selected.ACTIVITYINSTNAME);
		      });  
		      
		      END_NODEID.on("valuechanged", function (e) {
		     nui.get("END_NODENAME").setValue(e.selected.ACTIVITYINSTNAME);
		      });  
        
    
    //关闭添加窗口
 	function CloseWindow(action){
  		if(window.CloseOwnerWindow) 
    		return window.CloseOwnerWindow(action);
  		else
    		return window.close();
    }
    //保存数据
    function saveData(){
    	fileTypeForm.validate();            
        if (fileTypeForm.isValid() == false) return;
        var inputData = fileTypeForm.getData(true,true);
        var json = nui.encode(inputData);
        var URL="com.gotop.xmzg.files.processMaintain.addProcessMaintain.biz.ext";
        $.ajax({
        	url: URL,
            type: 'POST',
            data: json,
            cache: false,
            contentType:'text/json',
            success: function (text){
                var returnJson = nui.decode(text);
				if(returnJson.flag == "1"){
					nui.alert("保存成功", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							CloseWindow("saveSuccess");
						}
					});
				}else if(returnJson.exception == null && returnJson.flag == "exist"){
					nui.alert("档案种类已存在！");
				}else{
					nui.alert("保存失败", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							//CloseWindow("saveFailed");
						}
					});
				}
		    }
  		});   
    }
  
/* 弹出权限设置界面  */
		function onClick(e) {
			//var powerType = radio_powerType.getValue();
			var boxType = e.sender.defaultValue;
			nui.open({
	        	url: "<%=request.getContextPath() %>/files/processOrgRole/list_powertree.jsp?type="+boxType,
	        	title: "权限配置",
	            iconCls: "icon-edit", 
	            width: 350, 
	            height: 350,
	            onload: function () {
	                var iframe = this.getIFrameEl();  
	                var editTextBox = findEditTextBox( boxType);   
	                iframe.contentWindow.setTreeCheck(editTextBox.getValue());
	            },
	            ondestroy: function (action) {
					if (action == "ok") {
	                    var iframe = this.getIFrameEl();
	                    var chooseList = iframe.contentWindow.GetData();
	                    chooseList = nui.clone(chooseList);
/* 	                    if ("org" == boxType) {
	                    	chooseList = filterOrgChooseList(chooseList);  
	                    } */
	                    if (chooseList) {
	                    	putDataTextBox( boxType, chooseList, "nodeId", "nodeName");
	                    }
	                }
	            }
	        });
		}
		
		/**
		 * 往textboxlist中添加选择的数据
		 * @params powerType 权限类型
		 * @params boxType	    根据点击按钮的类型 添加到不同box里面
		 * @params list 	    获取Check选中的节点集合
		 */
		var map =["emp", "role", "org", "group"];
		function putDataTextBox( boxType, list){
			var text = "",value = "";
			var isEmp = (boxType == "emp"),isGroup = (boxType == "group");
			
			var editTextBox = findEditTextBox( boxType);
			
			var editHiddenBox = findEditHiddentBox( boxType);
			
			for (var i = 0; i < list.length; i++) {
				var node = list[i];
				if (node.nodeType == "OrgOrganization" && isEmp) continue;
				if (node.nodeType != "Group" && isGroup) continue;
				if (i == list.length -1) {
					value += node["nodeId"];
					text  += node["nodeName"];
				} else {
					value += node["nodeId"] + ",";
					text  += node["nodeName"] + ",";
				}
			}
			if (value.endsWith(",")) value = strSplitLast(value);
			if (text.endsWith(",")) text = strSplitLast(text);
			editTextBox.setValue(value);
			editTextBox.setText(text);
			
			editHiddenBox.setValue(text);
		}  
	
	
		function findEditTextBox(boxType) {
		    
	    	var boxId = "check_"+boxType+"Box";
	    	return nui.get(boxId);
	    }
	    
	    
	    function findEditHiddentBox(boxType) {
		    
	    	var boxId = "check_"+boxType+"NameBox";
	    	return nui.get(boxId);
	    }
</script>
</body>
</html>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%--  <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%>  --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2022-02-26 21:01:16
  - Description:
-->
<head>
	<title>机构业绩查询</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%--<%@include file="../init.jsp"%>--%>
</head>

<body style="width:100%;overflow:hidden;" >
 <div class="search-condition">
   <div class="list">
	  <div id="form1" class="nui-form">
	    <table class="table" style="width:100%;">
	        <tr>
		        <th class="tit">业务条线：</th> 
		        <td>
					<div id="listbox1" class="nui-combobox"  name="queryData.tip_code" 
						url = "com.gotop.xmzg.achieve.achieveOrgMng.get_all_indicators_plate.biz.ext"
					    textField="TIP_NAME" valueField="TIP_CODE"  multiSelect="false" showNullItem="true" nullItemText="--请选择业务条线--"
					    dataField="list" onValuechanged="tipcodechanged"/>
		        </td>
		        <th class="tit">指标：</th> 
		        <td>
					<div id="listbox2" class="nui-combobox" name="queryData.ti_code" 
						url = ""
					    textField="TI_NAME" valueField="TI_CODE"  multiSelect="false" showNullItem="true" nullItemText="--请选择指标--"
					    dataField="list" onValuechanged="ticodechanged"/>
		        </td>
		        <th class="tit">指标细项：</th> 
		        <td>
					<div id="listbox3" class="nui-combobox" name="queryData.tid_code" 
						url = ""
					    textField="TID_NAME" valueField="TID_CODE"  multiSelect="false" showNullItem="true" nullItemText="--请选择指标细项--"
					    dataField="list" />
		        </td>
		   </tr>
		   <tr>     
		        <th class="tit">机构号：</th> 
		        <td>
		        	<input id="tao_org" name = "queryData.tao_org"  class="nui-buttonedit" allowInput="false" onbuttonclick="OrgonButtonEdit" />
		        	<!--<a href="javascript:clearOrg();"><font style="color:red">清空</font></a>-->
		        </td>
		        <th class="tit">业绩起止日期：</th>
		        <td >
		           <input id="tao_create_time_begin" name = "queryData.tao_create_time_begin" class="nui-datepicker" style="width:110px;" allowInput="false" format="yyyyMMdd" />
		           --
		           <input id="tao_create_time_end" name = "queryData.tao_create_time_end" class="nui-datepicker"  style="width:110px;" allowInput="false" format="yyyyMMdd" />
		        </td> 
			     <td>		  
		            <a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;
		            <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
		         </td>
		          <td>		
		            <a class="nui-button"  iconCls="icon-edit" onclick="exportDetail">导出明细</a>
		         </td>
		    </tr>
	    </table>
	  </div>
    </div>
  </div>
  
	<div class="nui-toolbar" style="border-bottom:0;padding:0px;">
	  <table style="width:100%;">
	    <tr>
	       <td style="width:100%;">
	       	 <a class="nui-button" iconCls="icon-collapse" onclick="detail">详情</a>
	         <a class="nui-button" iconCls="icon-add" onclick="fp">分配</a>
	         <a class="nui-button" iconCls="icon-add" onclick="fpSh">分配（审核）</a>
	       </td>
	    </tr>
	  </table>
	</div>

  <div class="nui-fit">
	 <div id="datagrid1" dataField="list" class="nui-datagrid" style="width:100%;height:100%;" idField="TAO_ID" showColumns="true"
	 url="com.gotop.xmzg.achieve.achieveOrgMng.queryAchieveOrgList.biz.ext" totalField="page.count"  showPageInfo="true"
	  sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" onload="onGridLoad">
	    <div property="columns" >
	      <div type="checkcolumn"></div>
	      <div type="indexcolumn"></div>
	      <div field="TIP_NAME" headerAlign="center" align="center">条线</div>
	      <div field="TI_NAME" headerAlign="center" align="center">指标</div>
	      <div field="TID_NAME" headerAlign="center" align="center" width=150>指标细项</div>
	      <div field="TAO_ORG" headerAlign="center" align="center">机构号</div>  
	      <div field="TAO_ORGNAME" headerAlign="center" align="center">机构名称</div>   
	      <div field="TAO_NUM" headerAlign="center" align="center">业绩值</div>    
	      <div field="TAO_DATE" headerAlign="center" align="center">业绩时间</div>   
	      <div field="TAO_CREATE_TIME" headerAlign="center" align="center" dateFormat="yyyy-MM-dd HH:mm:ss">创建日期</div>    
	    </div>
	 </div>
  </div>
  
 <script type="text/javascript">
    nui.parse();
    var authcode = "0";
    var authmsg = "";
    var grid = nui.get("datagrid1");  
    $(function(){
    	$.ajax({
				url: "com.gotop.xmzg.achieve.achieveOrgMng.checkEmp.biz.ext",
				type: 'POST',
				data : {},
				cache: false,
				contentType:'text/json',
				success: function (res) {  
					//console.info(res);
					authcode = res.code;
					authmsg = res.msg;
					nui.get("tao_org").setValue(res.orgcode);
					nui.get("tao_org").setText(res.orgname);
					if(res.code == "0"){
						nui.alert(res.msg);
					}
				}
		});
    });
    //机构树点击事件
	function OrgonButtonEdit(e) {	
        var btnEdit = this;
        nui.open({
            url:"<%=request.getContextPath() %>/achieve/common/org_tree.jsp?type=2",
	        showMaxButton: false,
	        title: "选择树",
	        width: 350,
	        height: 350,
	        ondestroy: function (action) {                    
	            if (action == "ok") {
	                var iframe = this.getIFrameEl();
	                var data = iframe.contentWindow.GetData();
	                data = nui.clone(data);
	                if (data) {
	                    btnEdit.setValue(data.ORGCODE);
	                    btnEdit.setText(data.TEXT);
	                }
	            }
	        }
    	});            
	}
	
    function clean(){
	    var form = new nui.Form("#form1");//将普通form转为nui的form
	    form.reset();
	}
	
	function tipcodechanged(e){
		nui.get("listbox2").load("com.gotop.xmzg.achieve.achieveOrgMng.get_indicators.biz.ext?tip_code="+e.value);
		ticodechanged({value:""});
	}
	
	function ticodechanged(e){
		nui.get("listbox3").load("com.gotop.xmzg.achieve.achieveOrgMng.get_indicators_detail.biz.ext?ti_code="+e.value);
	}
	
	function clearOrg(){
		nui.get("tao_org").setValue("");
		nui.get("tao_org").setText("");
	}
	
	
	function onGridLoad(e){
		//console.info(e);
		if(e.result.code =="0"){
			nui.alert(e.result.msg);
			nui.get("datagrid1").setData({total:"0"});
		}
	}
	//查询
	function search(){
	   if(authcode == "0"){
		   nui.alert(authmsg);
		   return false;
	   }
	   var form = new nui.Form("#form1");
	   form.validate();
	   if (form.isValid() == false) return;
	   var data = form.getData(true,true);
	   grid.load(data);
	   //插入系统操作日志
		var OPE_MOD = "机构业绩查询";
		var OPE_CONTENT = "查询,输入项:"+nui.get("listbox3").getValue()+"|"+nui.get("tao_org").getText()+"|"+nui.get("tao_create_time_begin").getFormValue()+"|"+nui.get("tao_create_time_end").getFormValue();
		insert_sysope_log(OPE_MOD,OPE_CONTENT);
	}
	
	
	//分配
    function fp(){
       var rows = grid.getSelecteds();
       if(rows!=null&&rows.length==1){
       	   var row = rows[0];
       	   
       	   if("11003002" ==row.TID_CODE ||"12007001" ==row.TID_CODE ||"12006001" ==row.TID_CODE ){
       	   		nui.alert("无法分配该指标！请选择【分配（审核）】");
       	   		return false;
       	   }
       	  
	       nui.open({
	          url:"<%=request.getContextPath() %>/achieve/achieveOrgManage/achieve_emp_mng.jsp",
	          title:'分配',
	          width:700,
	          height:550,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             var data = {record:{map:row}};
                 iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                 grid.reload();
	             }
	          }
	       });
       }else{
           nui.alert("请选中一条记录！");
       }
    }
    //分配(审核)
    function fpSh(){
       var rows = grid.getSelecteds();
       if(rows!=null&&rows.length==1){
       	var row = rows[0];
      //  if("11003002" ==row.TID_CODE ||"12007001" ==row.TID_CODE ||"12006001" ==row.TID_CODE){
       	   
       	  
	       nui.open({
	          url:"<%=request.getContextPath() %>/achieve/achieveOrgManage/achieve_emp_mng_sh.jsp",
	          title:'分配（审核）',
	          width:700,
	          height:550,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             var data = {record:{map:row}};
                 iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                 grid.reload();
	             }
	          }
	       });
       	 /* }else{
       	 		nui.alert("该指标无需分配审核！请选择【分配】");
       	   		return false;
       	 }
       	    */
       }else{
           nui.alert("请选中一条记录！");
       }
    }
	
	
	function detail(){
       var rows = grid.getSelecteds();
       if(rows!=null&&rows.length==1){
       	   var row = rows[0];
	       nui.open({
	          url:"<%=request.getContextPath() %>/achieve/achieveOrgManage/achieve_emp_mng.jsp",
	          title:'详情',
	          width:700,
	          height:550,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             var data = {type:"detail",record:{map:row}};
                 iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          }
	       });
       }else{
           nui.alert("请选中一条记录！");
       }
    }
    
	 //插入系统操作日志
    function insert_sysope_log(OPE_MOD,OPE_CONTENT){
		$.ajax({
				url: "com.gotop.xmzg.performance.sysopeLog.insert_sysope_log.biz.ext",
				type: 'POST',
				data : nui.encode({"OPE_MOD":OPE_MOD,"OPE_CONTENT":OPE_CONTENT}),
				cache: false,
				contentType:'text/json',
				success: function (res) {  
				}
		});
	} 
	
	
	
	function exportDetail(){
		var data = new nui.Form("#form1").getData(true, true);
		data = nui.encode(data);
		console.log(data);
		var code = nui.get("listbox3").getValue();//指标细项
		if(code!="11003002"){
			nui.alert("只有【公司贷款利差收入】才能导出明细!");
		}else{
			var columns = nui.encode([{header:'业绩日期',field:'TZAD_DATE'}
				,{header:'机构号',field:'TZAD_ORGCODE'}
				,{header:'产品编号',field:'TZAD_PRODUCTNO'}
				,{header:'产品名称',field:'TZAD_PRODUCTNAME'}
				,{header:'业绩值',field:'TZAD_ACHIEVE'}
			]);
			
			var sheet = "公司贷款利差收入明细";
			var fileName = "公司贷款明细";
			var queryPath = "com.gotop.xmzg.achieve.report.expgsdklcsr";
			var url="<%=request.getContextPath()%>/achieve/excel/exportBigExcel.jsp";
			url += "?fileName="+fileName+"&columns="+columns
					+"&queryPath="+queryPath+"&data="+data+"&sheet="+sheet;
			console.log(url);
			window.location.replace(encodeURI(url));
		}
		
	};
    
  </script>
</body>

</html>
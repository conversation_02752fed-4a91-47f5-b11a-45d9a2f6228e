<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
<%@ page import="com.eos.data.datacontext.UserObject"%>	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): jw
  - Date: 2018-08-16 21:21:22
  - Description:
-->
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
 %>
<head>
<title>重传</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=EDGE">
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
    
</head>
<body>
	<fieldset style="border: solid 1px #aaa; padding: 3px;">
	<div style="padding: 5px;">
		<form id="filefrom" method="post">
			<input id="realFileName" name="file.realFileName" class="nui-hidden"/> 
			<input id="filePath" name="file.filePath" class="nui-hidden"/> 
			<input id="previewName" name="file.previewName" class="nui-hidden"/> 
			<input id="onlyName" name="file.onlyName" class="nui-hidden"/>
			<input id="fileId" name="file.fileId" class="nui-hidden" />
			<input id="fileName" name="file.fileName" class="nui-hidden" />
		</form>
		<form id="upload" method="post" enctype="multipart/form-data">
			<table border="0" cellpadding="1" cellspacing="2" align="center" style="width: 100%">
				<tr>
					<td style="width: 15%; text-align: right;">文件：</td>
					<td style="width: 70%"><input class="nui-htmlfile" name="Fdata" id="uploadfile" style="width: 100%;" /></td>
					<td style="width: 15%;"></td>
				</tr>
			</table>
		</form>
	</div>
	</fieldset>
	<div class="nui-toolbar" style="text-align:center;padding-top:5px;padding-bottom:5px;" borderStyle="border:0;">
	    <a class="nui-button" style="width:60px;" iconCls="icon-save" onclick="submitForm()">保存</a>
	    <span style="display:inline-block;width:25px;"></span>
	    <a class="nui-button" style="width:60px;" iconCls="icon-cancel" onclick="onCancel()">取消</a>
	</div>
	<script type="text/javascript">
	var userid="<%=userObject.getUserId()%>";
	var orgid="<%=userObject.getUserOrgId()%>";
	</script>
	
	<script src="<%= request.getContextPath() %>/js/auth.js" type="text/javascript"></script>
	<script src="<%= request.getContextPath() %>/js/jquery.form.js" type="text/javascript"></script>
	<script type="text/javascript">
    	nui.parse();
    	/* 0（预览） 1（修改） 2（删除） 3（下载）  */
		var username="<%=userObject.getUserName()%>";
		
		//路径
		var path="<%=request.getContextPath()%>";
		var userid="<%=userObject.getUserId()%>";
    	var form = new nui.Form("#filefrom");
		var fileinfo = nui.get("#uploadfile");
    	//标准方法接口定义
		function SetData(file) {
			//跨页面传递的数据对象，克隆后才可以安全使用
			file = nui.clone(file);
			form.setData(file);
        	form.setChanged(false);
		}
		//文件添加事件
		function submitForm() {
			var data=fileinfo.getValue(); 
			if(data==null||data==""||data==undefined){
				nui.alert("请选择上传文件");
			}else{
				FileUploads();
			}	
		}
    </script>
    <script src="<%= request.getContextPath() %>/js/filecs2.js" type="text/javascript"></script>
</body>
</html>
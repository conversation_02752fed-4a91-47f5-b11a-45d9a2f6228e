<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false" %>
<%@ page import="com.eos.data.datacontext.UserObject" %>	
<%@page import="com.eos.foundation.database.DatabaseExt"%>
<%@page import="java.util.HashMap"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<%--
- Author(s): Administrator
- Date: 2017-07-05 16:21:53
- Description:
    --%>
    <head>
    <%@include file="/coframe/dict/common.jsp"%>
        <title>
            	档案借阅流程审批
        </title>
        <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
        
        
        <style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style> 
    </head>
    
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
	Long workItemID = (Long)request.getAttribute("workItemID");
	//Long role = Long.parseLong(request.getParameter("role"));
 %>
 
   <body>
    
    <div align="center" >
    <h2 style="margin-top:10px;margin-bottom:0px;">档案借阅流程审批</h2>
    <fieldset style="width:800px;border:solid 1px #aaa;position:relative;margin:0px 2px 0px 2px;" >
    	 <legend>档案借阅申请表</legend>
    	 <form  id="form1" > 
    	 
    	 <input class="nui-hidden" name="workItemID"/>
		<input id="processInstID" class="nui-hidden" name="map.PROCESSINSTID"/>
		<input id="APPLY_EMPID" class="nui-hidden" name="map.APPLY_EMPID"/>
		<input class="nui-hidden" name="map.APPLY_ID" id="map.APPLY_ID"/>
    	  <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
  
     <tr>
        
        <th class="nui-form-label" style="width:12.5%"><label for="map.type$text">标题：</label></th>
        <td colspan="3"  style="width:30%">
             <input id="map.TITLE" class="nui-textbox asLabel" name="map.TITLE" readOnly="true"/>
        </td>

    
     <th class="nui-form-label" style="width:12.5%"><label for="map.type$text">联系电话：</label></th>
        <td colspan="3"  style="width:30%">
           <input id="map.PHONE_NUM" class="nui-textbox asLabel" name="map.PHONE_NUM" readOnly="true" />
      </td>
    
    
      </tr>
     
      <tr >
      
     <th class="nui-form-label" style="width:12.5%"><label for="map.device_name_id$text">申请人：</label></th>
        <td colspan="3"  style="width:30%">
          <input id="map.EMPNAME" class="nui-textbox asLabel " name="map.EMPNAME" readOnly="true" />
        </td>

    
      <th class="nui-form-label" style="width:12.5%"><label for="map.device_name_id$text">申请人所在机构：</label></th>
        <td colspan="3"  style="width:30%">
          <input id="map.ORGNAME" class="nui-textbox asLabel " name="map.ORGNAME" readOnly="true" />
        </td>

    
    
      </tr>
      
      <tr >
       
        <th class="nui-form-label"><label for="map.brandmodel_id$text">申请内容：</label></th>
        <td colspan="3"  >
           <input id="map.APPLY_REMARK" class="nui-textarea asLabel" name="map.APPLY_REMARK" readOnly="true"/>
        </td>

       
        <th class="nui-form-label"><label for="map.device_serial$text">借阅档案内容：</label></th>
        <td colspan="3"  >
          <input id="map.BORROW_APPLY_REMARK" class="nui-textarea asLabel" name="map.BORROW_APPLY_REMARK" readOnly="true"/>
        </td>

       
      </tr>
      
      
      <tr>
        <th class="nui-form-label"><label for="map.device_serial$text">借阅档案用途：</label></th>
        <td colspan="3"  >
          <input id="map.BORROW_APPLY_USE" class="nui-textarea asLabel" name="map.BORROW_APPLY_USE" readOnly="true"/>
        </td>

       <th class="nui-form-label"><label for="map.price$text">借阅方式：</label></th>
        <td colspan="3"  >
            <input id="deductscoreReason" name="map.BORROW_APPLY_WAY" class="nui-dictcheckboxgroup asLabel" dictTypeId="BORROW_APPLY_WAY"  readOnly="true"/>
        </td>

        
      </tr>
      
       <tr>
        <th class="nui-form-label"><label>附件列表：</label></th>
        <td colspan="7">
	        <div id="listbox1" class="nui-listbox" style="width:100%;height:80px;" textField="AFFILIATED_NAME" valueField="AFFILIATED_ID" 
		           dataField="resultList"  onvaluechanged="onListBoxValueChanged">
		    </div>
        </td>

     </tr>
 
    </table>
    </form>
         </fieldset>
         
          <%
          
			Object[] obj=DatabaseExt.queryByNamedSql("default", "com.gotop.xmzg.files.process.queryProcessSpDetil",workItemID);
			for(int i=0;i<obj.length;i++)
			{
			HashMap result=(HashMap) obj[i];
		 %>
         
         <fieldset style="width:800px;border:solid 1px #aaa;position:relative;margin:5px 2px 0px 2px;" >
    	 <legend>第 <%=i+1 %>次审核</legend>
    	 <div align="left" >
    	 <div id="dataform3" style="padding-top:0px;">
    	
    	 <table style="width:100%;height:95%;table-layout:fixed;" class="nui-form-table">
    	   <tr>
			        <th class="nui-form-label" style="width:15%;">审批人：</th>
			        <td style="text-align:left;width:35%;" >
			            <%=result.get("EMPNAME") %>
			        </td>

			        <th class="nui-form-label"  style="width:15%;" >审批人所在机构：</th>
			        <td style="text-align:left;width:35%;">
			            <%=result.get("ORGNAME") %>
			        </td>
			      </tr>
			      <tr>
			        <th class="nui-form-label" >审批时间：</th>
			        <td style="text-align:left;" >
			            <%=result.get("APPROVAL_TIME") %>
			        </td>
		
			        <th class="nui-form-label" >审核意见：</th>
			        <td style="text-align:left;" >
			         <%=result.get("RESULT_AUDIT") %>
                     
			        </td>
			      </tr>
			        <tr>
			        <th class="nui-form-label"  >审核说明：</th>
			        <td style="text-align:left;" colspan="3">
			            <%-- <%=result.get("APPROVAL_REMARK") %> --%>
			            <%
                     
                     if(result.get("APPROVAL_REMARK")!=null)
                     {
                     
                     result.put("AUDIT", result.get("APPROVAL_REMARK"));
                    
                     }else
                     {
                      result.put("AUDIT", "");
                     }
                     
                     %>
			          <%=result.get("AUDIT") %>
			        </td>
			      </tr>
			      
			    </table>
    	
        </div>
       </div>
      </fieldset> 
         <%
			}
		 %> 
         
         
         
         
        <fieldset style="width:800px;border:solid 1px #aaa;position:relative;margin:5px 2px 0px 2px;" >
    	 <legend>流程审批</legend>
    	 <div id="dataform2" style="padding-top:0px;">
    	 <form  id="form2" > 
   				  <input class="nui-hidden" name="map.AUDIT_ID"/>
   				  
                <table style="width:100%;height:95%;table-layout:fixed;" class="nui-form-table">
                <tr>
			        <th class="nui-form-label" style="width:20%;">上一处理人：</th>
			        <td style="text-align:left;width:80%;" >
			           <input id="process.OLD_EMPNAME" class="nui-textbox asLabel" name="process.OLD_EMPNAME"  readOnly="true" />
			           <input class="nui-hidden" name="process.OLD_EMPID" id="process.OLD_EMPID"/>
			        </td>
			     </tr>
                   <tr>
			        <th class="nui-form-label" style="width:20%;">审核意见：</th>
			        <td style="text-align:left;width:80%;" >
			           <input class="nui-combobox" name="process.APPROVAL_RESULT" id="res" data="Opinions" style="width:250px;" emptyText="请选择"  nullItemText="请选择"  required="true" />    
			        </td>
			        </tr>
			
			        <!-- <tr id="next_spempid" style="display:none">
			        <th class="nui-form-label" >下一审批人：</th>
			        <td style="text-align:left;" >
			            <input class="nui-hidden" name="process.EMPNAMES" id="process.EMPNAMES"/>
				        <input id="btnEdit1" name = "process.EMPIDS"  class="nui-textboxlist"   allowInput="false"  required="true" style="width:350px;"/>  
				         <a href="#" onclick="EmponButtonEdit()" style="color:blue;text-decoration:underline;">人员选择</a>    
				         <a href="#" onclick="cleanEmp()" style="color:blue;text-decoration:underline;">清空</a>
			        </td>
			        </tr> -->
			        <tr>
			        <th class="nui-form-label"  >审核说明：</th>
			        <td style="text-align:left;">
			           <input class="nui-textarea" id="desc" name="process.APPROVAL_REMARK" style="width:400px;"/>
			        </td>
			      </tr>
			      
			    </table>
			    </form>
            </div>
            
    	 <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
                <table width="100%">
                    <tr>
                        <td style="text-align:center;" colspan="4">
                            <a class="nui-button" iconCls="icon-ok" onclick="onOk()">
                               	 提交
                            </a>
                            <a class="nui-button" iconCls="icon-cancel" onclick="onCancel()">
                               	返回
                            </a>
                        </td>
                    </tr>
                </table>
            </div>
    	</fieldset>
    	
    	
    	
    	
    	<!--  <fieldset style="width:800px;height:350px;border:solid 1px #aaa;position:relative;margin:5px 2px 0px 2px;display:none"  id="next_spempid" >
    	 <legend>流程审批</legend>
    	 <div align="left" >
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      	<a class="nui-button" iconCls="icon-edit" onclick="borrow">借阅</a>
		<a class="nui-button" iconCls="icon-node" onclick="fileDetail">档案明细</a>  
	    <a class="nui-button" iconCls="icon-node" onclick="operateDetail">操作明细</a>   
    </div>
    档案主表信息
	  <div id="grid"  dataField="resultList" idField="dicttypeid" class="nui-datagrid" style="width:100%;height:300px;" 
	  	url="com.gotop.xmzg.files.fileList.queryFileRecordList.biz.ext" sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" >
	    	<div property="columns">
	    		<div type="checkcolumn"></div>
		        <div field="FILES_NAME" headerAlign="center">档案种类</div>
		        <div field="FILES_NAME" headerAlign="center">档案名称</div>
		        <div field="DEAL_NAME" headerAlign="center">归属机构</div>
		        <div field="EMP_NAME" headerAlign="center">客户经理</div>
		        <div field="STORAGE_ADDRESS" headerAlign="center">库存地址</div>
		        <div field="STORAGE_LOCATION" headerAlign="center">货架号/箱号</div>
		        <div field="FILES_STATUS" headerAlign="center">档案状态</div>
		    </div>
	 	</div> 
	 	</div>
    	</fieldset> -->
    	
    	

    	
       </div>  
       
    <script type="text/javascript">
        
         var Opinions = [{ id: "y", text: '通过' }, { id: "n", text: '不通过'}];
 
         var workItemID = <%=workItemID %>;
         
        <%--   var role = <%=role %>;
          alert(role);
 --%>

	
        nui.parse();
         var form = new nui.Form("#form1");
         var form2 = new nui.Form("#form2");
         
         var listbox1 = nui.get("listbox1");
         var con_id;
         
         
          
  var grid = nui.get("grid");
	grid.load(); 
    
    if(workItemID != null){
    //初始化加载数据 
	    $.ajax({
		        url:"com.gotop.xmzg.files.process.queryProcessApplyDetil.biz.ext",
		        type:'POST',
		        data:'workItemID='+workItemID,
		        cache:false,
		        async:false,
        		dataType:'json',
		        success:function(text){
		
		          var map = nui.decode(text);
		          form.setData(map);
		          nui.get("process.OLD_EMPNAME").setValue(map.map.DEAL_EMPNAME);
		          nui.get("process.OLD_EMPID").setValue(map.map.DEAL_EMPID);
		          
		            con_id=map.map.APPLY_ID;
				 listbox1.load("com.gotop.xmzg.files.process.queryAttachment.biz.ext?con_id="+con_id);
				 if(listbox1.getCount()==0){
				 	listbox1.setVisible(false);
				 }
		          
		          form.setChanged(false);
		        }
		      });
   } 
       
              //当选择不通过时，理由不能为空
      		var res = nui.get("res");
		    res.on("valuechanged", function (e) {
		    if(this.getValue()=="y")
		    {
		   nui.get("desc").setRequired(false);
		  //  nui.get("btnEdit1").setRequired(true);
		  //  document.getElementById("next_spempid").style.display = ""; 
		    
		    }else if(this.getValue()=="n")
		    {
		    nui.get("desc").setRequired(false);
		    //nui.get("btnEdit1").setRequired(false);
		    //  document.getElementById("next_spempid").style.display = "none"; 
		    }
		      
		    });  
		    
		    
		    
		     //机构树回显
     function EmponButtonEdit() {
            var btnEdit1 = nui.get("btnEdit1");
            nui.open({
                url:"<%=request.getContextPath() %>/files/process/SpEmpAndOrg_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                onload : function() {
					var iframe = this.getIFrameEl();
					// 给树设置上已选择的节点字符串     			
					iframe.contentWindow.setTreeCheck(btnEdit1.getValue());
				},
                ondestroy : function(action) {
							if (action == "ok") {
								var iframe = this.getIFrameEl();
								var list = iframe.contentWindow.GetData();
								list = nui.clone(list);
								if (list) {
									// 将树选中节点设置到box显示
									putDataTextBox(list);
								}
							}
						}
            });            
             
        }   
        
        /**
		 * 往textboxlist中添加选择的数据
		 * @params list 	 获取Check选中的节点集合
		 */
		function putDataTextBox(list){
			var text = "",value = "";
			var boxObj = nui.get("btnEdit1");
			for (var i = 0; i < list.length; i++) {
				var node = list[i];
				if (node.nodeType == "OrgOrganization") continue;
				if (i == list.length -1) {
					value += node["id"];
					text  += node["text"];
				} else {
					value += node["id"] + ",";
					text  += node["text"] + ",";
				}
			}
			if (value.endsWith(",")) value = strSplitLast(value);
			if (text.endsWith(",")) text = strSplitLast(text);
			boxObj.setValue(value);
			boxObj.setText(text);
			nui.get("process.EMPNAMES").setValue(text);
		}
					
		//判断当前字符串是否以str结束
	     if (typeof String.prototype.endsWith != 'function') {
	       String.prototype.endsWith = function (str){
	          return this.slice(-str.length) == str;
	       };
	     }
	     
	     /* 切割ID字符串最后一位 */
		function strSplitLast(obj) {
			return (obj).substring(0, obj.length - 1);
		}
    
        
    function cleanEmp(){
		 //nui.get("empids").setValue("");
		 nui.get("btnEdit1").setValue("");
		 nui.get("btnEdit1").setText("");
		 nui.get("map.EMPNAMES").setValue("");
	}
		    
    
    	 function onOk(){
                saveData();
            }
        
               
        function saveData(){
                form2.validate();
                if(form2.isValid()==false) return false;
                
                var data = form2.getData(false,true);
                var processInstID = nui.get("processInstID").getValue();
                data.process.PROCESSINSTID = processInstID;
                
                var empid = nui.get("APPLY_EMPID").getValue();
                data.process.APPLY_EMPID=empid;
                
                var appyId = nui.get("map.APPLY_ID").getValue();
                data.process.APPLY_ID=appyId;
             
                data.workItemID = workItemID;
                var json = nui.encode(data);
                var a= nui.loading("正在提交中,请稍等...","提示");
                $.ajax({
                    url:"com.gotop.xmzg.files.process.submitFilesAdminAudit.biz.ext",
                    type:'POST',
                    data:json,
                    cache:false,
                    contentType:'text/json',
                    success:function(text){
                    nui.hideMessageBox(a);//关闭加载提示（即正在提交中，请稍等）
                        var returnJson = nui.decode(text);
                        if(returnJson.exception == null){
                            alert("提交成功");
                            onCancel();
                            window.history.go(-1) ;
                        }else{
                            alert("提交失败");
                            window.history.go(-1) ;
                            }
                        }
             });
        }
        
          function onCancel(){
      //CloseWindow("cancel");
       window.history.go(-1) ;
    } 
    
    //下载附件
function onListBoxValueChanged(e) {
	            var listbox = e.sender;
	            var at_id = listbox.getValue();
	            var items=listbox.getSelecteds();
	            
	            //var json = nui.encode({AT_ID:at_id});
				nui.confirm("确定下载该附件？","系统提示",function(action){
		             if(action=="ok"){
			             var filepath=items[0].AFFILIATED_ADDRESS;
		            	 var name=items[0].AFFILIATED_NAME;
			             var url="<%=request.getContextPath() %>/files/process/AttachmentDownload.jsp?filepath="+filepath+"&filename="+name+" ";
		            	 window.location.replace(encodeURI(url)); 
		            	 
		            	 }
		             });
	           listbox.deselectAll();
	            }
	            
	      //借阅档案
    function borrow(){
    	var rows = grid.getSelecteds();
      	if(rows.length > 0){
	        nui.confirm("确定借阅选中档案？","系统提示",function(action){
	        	if(action=="ok"){
	        		var fileStatus = '2';//档案状态变更为借阅
		      		for (var i=0;i<rows.length;i++){
			        		rows[i].FILES_STATUS = fileStatus;
		        		}
		            var json = nui.encode({upDatas:rows});
		           	var a= nui.loading("正在处理中,请稍等...","提示");
		           	var URL="com.gotop.xmzg.files.fileList.uptFileRecordStatus.biz.ext";
			        	$.ajax({
			          		url:URL,
			          		type:'POST',
			          		data:json,
			          		cache: false,
			          		contentType:'text/json',
			          		success:function(text){
				          		nui.hideMessageBox(a);
				            	var returnJson = nui.decode(text);
								if(returnJson.flag == 1){
									nui.alert("借阅成功", "系统提示", function(action){
										searchData();
									});
								}else if(returnJson.flag == "noAccess"){
									nui.alert("对不起，您没有操作权限！", "系统提示", function(action){
										searchData();
									});
								}else if(returnJson.flag == 0){
									nui.alert("操作失败", "系统提示");
									grid.unmask();
								}
			          		}
			        	});
		     	}
			});
      	}else{
        	nui.alert("请至少选中一条记录！");
      	}
    }
    
    
         
     //档案明细
     function fileDetail(){
    	var rows = grid.getSelecteds();
    	var row = grid.getSelected();
    	var detail = '';
    	if(rows.length>1){
    		return nui.alert("请只选择一条记录进行明细查看！","提示");
    	}
       	if(row!=null){
       		var TABLE_NAME = getDetailTableName(row.FILES_TYPE);
       		//获取档案详情
       		var data={queryData:{INFORMATION_ID:row.INFORMATION_ID,FILES_TYPE:row.FILES_TYPE,TABLE_NAME:TABLE_NAME}};
       		var json = nui.encode(data);
		    $.ajax({
		        url:"com.gotop.xmzg.files.archiveList.getDetails.biz.ext",
		        type:'POST',
		        data:json,
		        cache:false,
		        contentType:'text/json',
		        success:function(text){
		        	obj = nui.decode(text.resultList);
		        	var detail = obj[0];
		        	row = detail;
		    	}
			});
		    nui.open({
		    	url:"<%=request.getContextPath()%>/files/archiveList/archiveListDetail.jsp",
	          	title:'档案明细',
	          	width:1150,
          		height:580,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            //方法1：后台查询一次，获取信息回显
		            /* var data = row;
		            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
		            //方法2：直接从页面获取，不用去后台获取
		            var filesType=row.FILES_TYPE;
		            var data={};
		            if(filesType == '01'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row,editData1:row}};
					}else if(filesType == '02'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row,editData2:row}};
					}else if(filesType == '03'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row,editData3:row}};
					}else if(filesType == '04'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row,editData4:row}};
					}else if(filesType == '05'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row,editData5:row}};
					}else if(filesType == '06'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row,editData6:row}};
					}else if(filesType == '07'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '08'){
						data = {pageType:"edit",record:{editData:row}};
					}else if(filesType == '09'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row,editData9:row}};
					}
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		         	 var strs= new Array();
		          	 strs=action.split(","); 
		          	 if(strs[0]=="saveSuccess"){
			         	 nui.get("files_type").setValue(strs[1]);
            	 		 searchData();
		             }
		        }
		        
		    });
       	}
    }
    
    
    //根据档案种类获取详情表名
	function getDetailTableName(filesType){
		if(filesType == '01'){
				return 'T_FILES_RETAIL_CREDIT';
			}else if(filesType == '02'){
				return 'T_FILES_RETAIL_DISBURSE';
			}else if(filesType == '03'){
				return 'T_FILES_PERSON_CREDIT';
			}else if(filesType == '04'){
				return 'T_FILES_PERSON_DISBURSE';
			}else if(filesType == '05'){
				return 'T_FILES_BILL_HONOUR';
			}else if(filesType == '06'){
				return 'T_FILES_BILL_DISCOUNT';
			}else if(filesType == '07'){
				return 'T_FILES_COOPERATE_ORG';
			}else if(filesType == '08'){
				return 'T_FILES_REFUSE_LOAN';
			}else if(filesType == '09'){
				return 'T_FILES_CREDIT';
			}
	}
	
	
//操作明细
   function operateDetail(){
       var row = grid.getSelected();
       var rows = grid.getSelecteds();
       if(rows.length>1){
    		return nui.alert("请只选择一条记录进行查看！","提示");
    	}
       if(row!=null){
       var INFORMATION_ID=row.INFORMATION_ID;
	       nui.open({
	          url:"<%=request.getContextPath() %>/files/archiveList/operationLog.jsp?infoId="+INFORMATION_ID,
	          title:'档案操作流水',
	          width:1200,
	          height:550,
	          onload:function(){
	             /* var iframe = this.getIFrameEl();
	               var data = {pageType:"edit",record:{map:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法 */
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	             }
	          }
	       });
       }else{
           nui.alert("请选中一条记录！");
       }
    }         
	            
	            
    </script>

</body>
</html>
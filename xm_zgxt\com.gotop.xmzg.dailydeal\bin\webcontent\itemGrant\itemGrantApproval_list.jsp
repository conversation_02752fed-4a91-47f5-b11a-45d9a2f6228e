<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-01-10 10:10:11
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>单册发放-审批</title>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <table class="table" style="width:100%;">       
	        <tr>
	            	
				<th class="nui-form-label" >请领日期：</th>
				<td>
					<input id="queryData.trade_date1" name = "queryData.trade_date1" class="nui-datepicker "  style="width:100px;" allowInput="false" format="yyyy-MM-dd" />~
					<input id="queryData.trade_date2" name = "queryData.trade_date2" class="nui-datepicker "  style="width:100px;"  allowInput="false" format="yyyy-MM-dd" onvalidation="comparedate"/>
				</td>
			    <th class="nui-form-label">单册代码：</th>
				<td>
					<input id="item_no" name="queryData.item_no" class="nui-textbox"  style="width:160px;" vtype="maxLength:50"/>
				</td>
				<th class="tit">单册名称：</th>
				<td> 
					<input id="item_name" name="queryData.item_name" class="nui-textbox"  style="width:160px;" vtype="maxLength:100"/>
				</td> 
				
			</tr>	
			 <tr>
	            <th class="tit">单册出处：</th>
				<td>
					<input class="nui-combobox" name = "queryData.ITEM_ADDRESS"  id="res1" data="Opinions1" style="width:200px;" 
                   emptyText="请选择"  nullItemText="请选择" showNullItem="true" />
				</td>	
				<th  class="tit">审批状态：</th>
				<td>
					<input class="nui-dictcombobox" valueField="dictID" textField="dictName" emptyText="全部"
	          	      dictTypeId="GRANT_STATUS" id="queryData.approval_status" name="queryData.approval_status" showNullItem="true" nullItemText="全部" style="width:160px;" value="7"/>
	          	      <!--<input class="nui-combobox" name="queryData.approval_status" id="res" data="Opinions" style="width:200px;"
                   emptyText="请选择"  nullItemText="请选择" showNullItem="true" value="7"/>-->
				</td>		
				<th  class="tit">审批结果：</th>
				<td>
					<input class="nui-dictcombobox" valueField="dictID" textField="dictName" emptyText="全部"
	          	      dictTypeId="APPROVAL_RESULT_TYPE" id="queryData.approval_result" name="queryData.approval_result" showNullItem="true" nullItemText="全部" style="width:160px;"/>
				</td>
				<th class="tit">
					
				</th>
				<td>
				    <a class="nui-button" iconCls="icon-search" onclick="search">查询</a>
					<a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
				</td>
			</tr>      
	    </table>
	  </div>
    </div>
  </div>
  
 <!--  <div style="margin:10px 0px 0px 0px;">--> 
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
		     <a id="update" class="nui-button" iconCls="icon-edit" onclick="applicant">审批</a>
		     <a id="update1" class="nui-button" iconCls="icon-ok" onclick="applicantSums">批量审批通过</a>
           </td>
        </tr>
      </table>
    </div>
<!--  </div> --> 
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.dailydeal.itemGrant.query_itemGrantApproval.biz.ext"
	  sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10">
	    <div property="columns" >
	      <div type="checkcolumn"></div>
	      <div field="APPLY_TIME" headerAlign="center" align="center">请领日期</div>
	      <div field="APPROVAL_STATUSNAME" headerAlign="center" align="center">审批状态</div>
	      <div field="ORGCODE" headerAlign="center" align="center">请领机构号</div>
	      <div field="APPLYORGNAME" headerAlign="center" align="center" width=130>请领机构名称</div>
	      <div field="ITEM_NO" headerAlign="center" align="center">单册代码</div>
	      <div field="NO" headerAlign="center" align="center">编号</div>
	      <div field="ITEM_NAME" headerAlign="center" align="center" width=160>单册名称</div>
	      <!-- <div field="PRE_TAX_PRICE" headerAlign="center" align="center">税前单价</div>
	      <div field="TAX_PRICE" headerAlign="center" align="center">含税单价</div> -->
	      <div field="APPLY_NUM" headerAlign="center" align="center" renderer="showColor">请领数量</div> 
	      <div field="REAL_NUM" headerAlign="center" align="center">实发数量</div>
	      <div field="ITEM_ADDRESS" headerAlign="center" align="center">单册出处</div>
	      <div field="APPLYEMPNAME" headerAlign="center" align="center">请领人</div>
		  <div field="APPLYSUBMITNAME" headerAlign="center" align="center">请领授权人</div>
		  <div field="GRANTEMPNAME" headerAlign="center" align="center">发放授权人</div>
		  <div field="GRANT_RESULTNAME" headerAlign="center" align="center">审批结果</div>
		  <div field="GRANT_APPROVAL_DATE" headerAlign="center" align="center" width=130>审批时间</div> 
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">
    nui.parse();
    var Opinions1 = [{ id: "供应商", text: '供应商' }, { id: "分行单册库", text: '分行单册库'}];
    nui.get("res1").load(Opinions1);
    
    var grid = nui.get("datagrid1");
    var form = new nui.Form("#form1");
    var data = form.getData(true,true);
    grid.load(data);
      
    function adjusttype(e){
      	    return nui.getDictText("ADJUST_TYPE", e.row.ADJUST_TYPE);
    }
    
    function search(){

       var form = new nui.Form("#form1");
            form.validate();
            if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
     
     function applicant(){  
       var row = grid.getSelected();
       if(row!=null){
       
       var data1 = {map:row};
       var json = nui.encode(data1);
           //判断审批状态(已审批)
        if(row.GRANT_RESULT!="" && row.GRANT_RESULT!=null && row.APPROVAL_STATUS!='7')
        {
	         nui.alert("已经审批的记录不能重复审批！");
	         return false;
        }
        
	       nui.open({
	          url:"<%=request.getContextPath() %>/dailydeal/itemGrant/itemGrantApproval_update.jsp",
	          title:'',
	          width:400,
	          height:200,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             
	             //方法1：后台查询一次，获取信息回显
	             /* var data = row;
	             iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
	             
	             //方法2：直接从页面获取，不用去后台获取
	               var data = {pageType:"edit",record:{map:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }else{
           nui.alert("请选中一条记录！");
       }
    }
     
     function applicantSums(){
     
	      var rows = grid.getSelecteds();
	      if(rows.length > 0){
	      for(var i=0;i<rows.length;i++)
	      {
	      		if(rows[i].APPROVAL_STATUS=='4')
	      		{
	      			nui.alert("该记录已做了审批，不能再次操作！");
	      			return false;
	      		}
	      }
    
         nui.confirm("确定审批选中记录？","系统提示",
           function(action){
             if(action=="ok"){
	            var json = nui.encode({maps:rows});
	           var a= nui.loading("正在执行中,请稍等...","提示");
		        $.ajax({
		          url:"com.gotop.xmzg.dailydeal.itemGrant.SubmitApprovalSumGrants.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	nui.hideMessageBox(a);
		            var returnJson = nui.decode(text);
					if(returnJson.exception == null && returnJson.iRtn==1){
						nui.alert("批量审批成功", "系统提示", function(action){
							grid.reload();
						});
					}else{
						nui.alert("批量审批失败", "系统提示");
						grid.unmask();
					}
					
		          }
		        });
		     }
		   });
      }else{
        nui.alert("请选中一条记录！");
      }
    }
   
    
   
   
    function approvalType(e){
      	    return nui.getDictText("APPROVAL_TYPE", e.row.APPROVAL_TYPE);
    }
    
    
    
    //时间判断开始时间不能大于结束时间
    function comparedate(e){
    //debugger;
      var startDate = nui.get("queryData.trade_date1").getFormValue();
      var endDate = nui.get("queryData.trade_date2").getFormValue();
      //if(startDate!="")
	    //startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
	  if(endDate!=""){
		//endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
        if(e.isValid){
          if(startDate>endDate){
            e.errorText="结束日期必须大于开始日期";
            e.isValid=false;
          }else
          {
          e.errorText="";
          e.isValid=true;
          }
        }
      }
    } 
    
    
    
    //重置
    function clean(){
	   //不能用form.reset(),否则会把隐藏域信息清空
	   nui.get("queryData.trade_date1").setValue("");
	   nui.get("queryData.trade_date2").setValue("");
       nui.get("item_no").setValue("");
       nui.get("item_name").setValue("");
       //nui.get("res1").setValue("");
       nui.get("queryData.approval_status").setValue("");
       nui.get("queryData.approval_result").setValue("");
       }
    
  </script>
</body>
</html>
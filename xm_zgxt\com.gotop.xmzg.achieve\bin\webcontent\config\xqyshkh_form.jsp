<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-01-05 11:41:00
  - Description:
-->
<head>
<%-- <%@include file="/coframe/tools/skins/common.jsp" %> --%>
<%@include file="/coframe/dict/common.jsp"%>
<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
	    .nui-form-label{
	    	width: 135px;
	    }
    </style>
</head>
<body>
<!-- JF_POSITION -->
<div id="form1" style="padding-top:5px;">
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
   	  <input id="TX_ID" name = "map.TX_ID" class="nui-hidden" />
   	  <input id="CUST_MGR_NAME" name = "map.CUST_MGR_NAME" class="nui-hidden" />
   	  <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>统计日期：</label></th>
        <td colspan="3" >  
        	<input id="SUMM_DATE" name = "map.SUMM_DATE" class="nui-datepicker"  style="width:100%;" allowInput="false" format="yyyyMMdd" required="true" />
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>客户号：</label></th>
        <td colspan="3" >
        	<input id="CUST_NO" name = "map.CUST_NO"  class="nui-textbox" style="width:100%;" required="true" />
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>客户名称：</label></th>
        <td colspan="3" >  
        	<input id="CUST_NAME" name = "map.CUST_NAME"  class="nui-textbox" style="width:100%;" required="true" />
        </td> 
      </tr>
       <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>客户类别：</label></th>
        <td colspan="3" >  
        	<input id="CUST_TYPE" name = "map.CUST_TYPE"  class="mini-combobox" style="width:100%;" required="true" data="Genders"/>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text">授信金额（万元）：</label></th>
        <td colspan="3" > 
            <input class="nui-spinner" id="NEW_LIMIT" name="map.NEW_LIMIT" minValue="-999999999" maxValue="999999999"  style="width:100%;" format="#,0.00"/> 
        	
        </td> 
      </tr>
      
      <!-- <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>客户经理：</label></th>
        <td colspan="3" >  
        	<input id="NEW_TAB_EMP" name = "map.CUST_MGR_NO"  class="nui-buttonedit"  allowInput="false" onbuttonclick="selectEmp" style="width:100%;" required="true" />
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>已批复额度：</label></th>
        <td colspan="3" >  
        	<input id="APPEOVED_LIMIT" name = "map.APPEOVED_LIMIT" class="nui-spinner" minValue="0" maxValue="99999999" format="#,0.00"  style="width:100%;"  required="true" />
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text">新额度期限：</label></th>
        <td colspan="3" >  
        	<input id="NEW_LIMIT_BGN_DATE" name = "map.NEW_LIMIT_BGN_DATE" class="nui-datepicker"  style="width:100%;" allowInput="false" format="yyyyMMdd"/>
        </td> 
      </tr> -->
    </table>
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save" onclick="onOk">保存</a>
	          <span style="display:inline-block;width:10px;"></span>
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  </div>

  <script type="text/javascript">
	var Genders = [{ id: 1, text: '新客户授信' }, { id: 2, text: '老客户增信'}];
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);
    var saveUrl = "com.gotop.xmzg.achieve.config.xqysxkh_add.biz.ext";
    function setData(data){  
    	  
    	  
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);
        if(infos.pageType == "edit") {
        	saveUrl = "com.gotop.xmzg.achieve.config.xqysxkh_update.biz.ext";	  
        }
        console.log(saveUrl);
        //表单数据回显
        var json = infos.record;
        form.setData(json);
    }
    
    function onOk(){
      	saveData();
    }
    
    //提交
    function saveData(){   
      form.validate();
      if(form.isValid()==false) return;
      
      var data = form.getData(true,true);
      var json = nui.encode(data);
     
      $.ajax({
        url:saveUrl,
        type:'POST',
        data:json,
        cache:false,
        contentType:'text/json',
        success:function(text){
            var returnJson = nui.decode(text);
			if(returnJson.exception == null && returnJson.iRtn == 1){
				nui.alert("操作成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveSuccess");
					}
				});
			}else if(returnJson!= null && returnJson.msg != null && returnJson.iRtn != 1){
				nui.alert(returnJson.msg, "系统提示");
			}else{
				nui.alert("操作失败", "系统提示");
			}
        }
      });
    }
    
   
    function onCancel(){
      CloseWindow("cancel");
    }
    
    function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
	
	function selectEmp(e){
        	var ele = e.sender.id;
    		var emp = nui.get(ele);
            nui.open({
                url:"<%=request.getContextPath()%>/achieve/acc/accEmptree.jsp",
                title: "选择人员",
                width: 600,
                height: 400,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = emp.getValue();
                	//frame.contentWindow.setData(data);
                	frame.contentWindow.setTreeCheck(data.value);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            emp.setValue(data.ORGID);
                            emp.setText(data.ORGNAME);
                            
                            nui.get("CUST_MGR_NAME").setValue(data.ORGNAME);
                        }
                    }

                }
            });
    	}
  </script>
</body>
</html>
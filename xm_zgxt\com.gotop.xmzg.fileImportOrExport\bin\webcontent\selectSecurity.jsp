<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): zhanghongkai
  - Date: 2019-05-09 15:48:08
  - Description:
-->
<head>
	<%@include file="/coframe/tools/skins/common.jsp" %>
	<title>选择权限</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
</head>
<body>
	<div id="form" class="nui-fit">
		<table border="0" cellpadding="1" cellspacing="2" style="width:100%;table-layout:fixed;">
		    <tr>
		        <th style="text-align:right;">机构权限：</th>
		        <td>
		            <input id="orgSecurity" class="nui-textboxlist" style="width:100%;"/>
		        </td>
		        <td>
		        	<a class="nui-button" iconCls="icon-edit" onclick="selectOrg">配置</a>
		        	<a class="nui-button" onClick="orgReset">重置</a>
		        </td>
		    </tr>
		    <tr>
		        <th style="text-align:right;">角色权限：</th>
		        <td>
		            <input id="roleSecurity" class="nui-textboxlist" style="width:100%;"/>
		        </td>
		        <td>
		        	<a class="nui-button" iconCls="icon-edit" onclick="selectRole">配置</a>
		        	<a class="nui-button" onClick="roleReset">重置</a>
		        </td>
		    </tr>
		    <tr>
		        <th style="text-align:right;">人员权限：</th>
		        <td>
		            <input id="empSecurity" class="nui-textboxlist" style="width:100%;"/>
		        </td>
		        <td>
		        	<a class="nui-button" iconCls="icon-edit" onclick="selectEmp">配置</a>
		        	<a class="nui-button" onClick="empReset">重置</a>
		        </td>
		    </tr>
		    <tr>
		        <th style="text-align:right;">群组权限：</th>
		        <td>
		            <input id="groupSecurity" class="nui-textboxlist" style="width:100%;"/>
		        </td>
		        <td>
		        	<a class="nui-button" iconCls="icon-edit" onclick="selectGroup">配置</a>
		        	<a class="nui-button" onClick="groupReset">重置</a>
		        </td>
		    </tr>
		</table>
		<div style="text-align:center">
			<a class="nui-button" iconCls="icon-ok" onClick="save">确定</a>&nbsp
			<a class="nui-button" iconCls="icon-cancel" onClick="cancel">取消</a>
		</div>
	</div>


	<script type="text/javascript">
    	nui.parse();
    	
    	var form = new nui.Form("form");
    	
    	var orgText = "";
    	var empText = "";
    	var roleText = "";
    	var groupText = "";
    	var orgValue = "";
    	var empValue = "";
    	var roleValue = "";
    	var groupValue = "";
    	
    	function orgReset(){
    		nui.get("orgSecurity").setValue();
    		nui.get("orgSecurity").setText();
    	}
    	
    	function roleReset(){
    		nui.get("roleSecurity").setValue();
    		nui.get("roleSecurity").setText();
    	}
    	
    	function empReset(){
    		nui.get("empSecurity").setValue();
    		nui.get("empSecurity").setText();
    	}
    	
    	function groupReset(){
    		nui.get("groupSecurity").setValue();
    		nui.get("groupSecurity").setText();
    	}
    	
    	function setData(data){
    		data = nui.clone(data);
    		var text = String(data.text);
    		var value = String(data.value);
    		var texts = text.split(";");
    		var temp = null;
    		for(var i=0;i<texts.length;i++){
    			temp = texts[i].split(":");
    			if(temp[0] == "机构"){
    				orgText = temp[1];
    			}else if(temp[0] == "角色"){
    				roleText = temp[1];
    			}else if(temp[0] == "人员"){
    				empText = temp[1];
    			}else if(temp[0] == "群组"){
    				groupText = temp[1];
    			}
    		}
    		var values = value.split(";");
    		for(var i=0;i<values.length;i++){
    			temp = values[i].split(":");
    			if(temp[0] == "org"){
    				orgValue = temp[1];
    			}else if(temp[0] == "role"){
    				roleValue = temp[1];
    			}else if(temp[0] == "emp"){
    				empValue = temp[1];
    			}else if(temp[0] == "group"){
    				groupValue = temp[1];
    			}
    		}
    		//先设置value再设置text！！！
    		nui.get("orgSecurity").setValue(orgValue);
    		nui.get("orgSecurity").setText(orgText);
    		nui.get("roleSecurity").setValue(roleValue);
    		nui.get("roleSecurity").setText(roleText);
    		nui.get("empSecurity").setValue(empValue);
    		nui.get("empSecurity").setText(empText);
    		nui.get("groupSecurity").setValue(groupValue);
    		nui.get("groupSecurity").setText(groupText);
    	}
    	
    	function save() {
	        closeWindow("ok");
	    }
    	
    	function getData(){
    		var orgSecurityText = nui.get("orgSecurity").getText();
    		var orgSecurityValue = nui.get("orgSecurity").getValue();
    		
    		var roleSecurityText = nui.get("roleSecurity").getText();
    		var roleSecurityValue = nui.get("roleSecurity").getValue();
    		
    		var empSecurityText = nui.get("empSecurity").getText();
    		var empSecurityValue = nui.get("empSecurity").getValue();
    		
    		var groupSecurityText = nui.get("groupSecurity").getText();
    		var groupSecurityValue = nui.get("groupSecurity").getValue();
    		
    		var text = new Array();
    		var value = new Array();
    		
    		var i = 0;
    		
    		if(orgSecurityText != ""){
    			text[i] = "机构:" + orgSecurityText;
    			value[i] = "org:" + orgSecurityValue;
    			i++;
    		}
    		
    		if(roleSecurityText != ""){
    			text[i] = "角色:" + roleSecurityText;
    			value[i] = "role:" + roleSecurityValue;
    			i++;
    		}
    		
    		if(empSecurityText != ""){
    			text[i] = "人员:" + empSecurityText;
    			value[i] = "emp:" + empSecurityValue;
    			i++;
    		}
    		
    		if(groupSecurityText != ""){
    			text[i] = "群组:" + groupSecurityText;
    			value[i] = "group:" + groupSecurityValue;
    			i++;
    		}
    		
    		text = text.join(";");
    		value = value.join(";");
    		return {text:text,value:value};
    	}
    	
    	function selectOrg(){
    		var orgSecurity = nui.get("orgSecurity");
            nui.open({
                url:"<%=request.getContextPath()%>/fileImportOrExport/fileParametersMaintain/selectOrg.jsp",
                title: "选择机构",
                width: 400,
                height: 300,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = orgSecurity.getValue();
                	frame.contentWindow.setData(data);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);    //必须
                        if (data) {
                        	var text = "";
                        	var value = "";
                        	for(var i=0;i<data.length;i++){
                        		text = text + data[i].ORGNAME + ",";
                        		value = value + data[i].ORGCODE + ",";
                        	}
                        	text = text.substring(0, text.lastIndexOf(','));
                        	value = value.substring(0, value.lastIndexOf(','));
                            orgSecurity.setValue(value);
                            orgSecurity.setText(text);
                        }
                    }

                }
            });
    	}
    	
    	function selectRole(){
    		var roleSecurity = nui.get("roleSecurity");
            nui.open({
                url:"<%=request.getContextPath()%>/fileImportOrExport/fileParametersMaintain/selectRole.jsp",
                title: "选择角色",
                width: 400,
                height: 300,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = roleSecurity.getValue();
                	frame.contentWindow.setData(data);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);    //必须
                        if (data) {
                        	var text = "";
                        	var value = "";
                        	for(var i=0;i<data.length;i++){
                        		text = text + data[i].ROLE_NAME + ",";
                        		value = value + data[i].ROLE_CODE + ",";
                        	}
                        	text = text.substring(0, text.lastIndexOf(','));
                        	value = value.substring(0, value.lastIndexOf(','));
                            roleSecurity.setValue(value);
                            roleSecurity.setText(text);
                        }
                    }

                }
            });
    	}
    	
    	function selectEmp(){
    		var empSecurity = nui.get("empSecurity");
            nui.open({
                url:"<%=request.getContextPath()%>/fileImportOrExport/fileParametersMaintain/selectEmp.jsp",
                title: "选择人员",
                width: 400,
                height: 300,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = empSecurity.getValue();
                	frame.contentWindow.setData(data);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);    //必须
                        if (data) {
                        	var text = "";
                        	var value = "";
                        	for(var i=0;i<data.length;i++){
                        		text = text + data[i].EMPNAME + ",";
                        		value = value + data[i].EMPCODE + ",";
                        	}
                        	text = text.substring(0, text.lastIndexOf(','));
                        	value = value.substring(0, value.lastIndexOf(','));
                            empSecurity.setValue(value);
                            empSecurity.setText(text);
                        }
                    }

                }
            });
    	}
    	
    	function selectGroup(e){
    		var groupSecurity = nui.get("groupSecurity");
            nui.open({
                url:"<%=request.getContextPath()%>/fileImportOrExport/fileParametersMaintain/selectGroup.jsp",
                title: "选择群组",
                width: 400,
                height: 300,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = groupSecurity.getValue();
                	frame.contentWindow.setData(data);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);    //必须
                        if (data) {
                        	var text = "";
                        	var value = "";
                        	for(var i=0;i<data.length;i++){
                        		text = text + data[i].GROUP_NAME + ",";
                        		value = value + data[i].REC_ID + ",";
                        	}
                        	text = text.substring(0, text.lastIndexOf(','));
                        	value = value.substring(0, value.lastIndexOf(','));
                            groupSecurity.setValue(value);
                            groupSecurity.setText(text);
                        }
                    }

                }
            });
    	}
    	
    	function cancel(){
    		closeWindow("cancel");
    	}
    	
    	function closeWindow(action){
    		return window.CloseOwnerWindow(action);
    	}
    </script>
</body>
</html>
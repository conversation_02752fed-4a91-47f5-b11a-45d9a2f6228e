<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-05-23 12:01:11
  - Description:单册核销明细
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>单册核销查询</title>
</head>
<body style="width:100%;overflow:hidden;">
 
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	  <div id="c_orgid" class="nui-hidden"  name="queryData.c_orgid" ></div>
	    <table class="table" style="width:100%;">
	      <tr >
	            <th class="tit" >机构名称：</th>
				<td>
					<input class="nui-buttonedit" style="width:130px" name="queryData.orgid"  id="queryData.orgid" allowInput="false"onbuttonclick="onButtonEdit"  />
				</td>
				
				<th  class="tit">是否包含下级：</th>
				<td>
					<input class="nui-dictcombobox" valueField="dictID" textField="dictName"
	          	      dictTypeId="YESORNO" id="queryData.yesorno" name="queryData.yesorno"  style="width:40px;" value="0"/>
				</td>
				
		        <th class="tit">核销日期：</th>	
				<td> 
					<input id="queryData.startDate" class="nui-datepicker" name="queryData.apply_time1"  style="width:110px;" allowInput="false" format="yyyy/MM/dd"/>
	                 ~
	                <input id="queryData.endDate" class="nui-datepicker" name="queryData.apply_time2"  style="width:110px;" allowInput="false" format="yyyy/MM/dd" onvalidation="comparedate"/>
				</td>
					
			</tr>
			<tr>	
			    <th class="tit">单册代码：</th>
				<td>
					<input id="item_no" name="queryData.item_no" class="nui-textbox" style="width:130px;" vtype="maxLength:50"/>
				</td>
			
				<th class="tit">单册名称：</th>
				<td>
					<input id="item_name" name="queryData.item_name" class="nui-textbox" style="width:130px;" vtype="maxLength:100"/>
				</td>
			    <th  class="tit">核销原因：</th>
				<td>
					<input class="nui-dictcombobox" valueField="dictID" textField="dictName" emptyText="全部"
	          	      dictTypeId="DESTROY_TYPE" id="queryData.destroy_reason" name="queryData.destroy_reason" showNullItem="true" nullItemText="全部" style="width:150px;"/>
				</td>
				
				
				
			 <th></th>	
		     <td>		  
	            <a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;
	            <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>&nbsp;
	            <input class="nui-button" text="导出报表" iconCls="icon-download" onclick="excel"/>
	         </td>
	        </tr>
	    </table>
	  </div>
    </div>
  </div>

  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.dailydeal.itemDestroy.query_itemDestroyDetail.biz.ext"
	  sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10">
	    <div property="columns" >
	      <!-- <div type="checkcolumn"></div> -->
	      <div field="DESTROY_TIME" headerAlign="center" align="center">核销日期</div>
	      <div field="ORGCODE" headerAlign="center" align="center">机构号</div>
	      <div field="ORGNAME" headerAlign="center" align="center">机构名称</div>
	      <div field="ITEM_NO" headerAlign="center" align="center">单册代码</div>
	      <div field="ITEM_NAME" headerAlign="center" align="center">单册名称</div>
	      <div field="DESTROYNUM" headerAlign="center" align="center">核销数量</div>
	      <div field="PRE_TAX_PRICE" headerAlign="center" align="center">税前单价</div>
	      <div field="TAX_PRICE" headerAlign="center" align="center">含税单价</div>
	      <div field="DESTROY_REASON_NAME" headerAlign="center" align="center" renderer="destroyType">核销原因</div>   
	      <div field="OPERATOR_TIME" headerAlign="center" align="center" width=135>操作时间</div>
	        
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">
    nui.parse();
    var grid = nui.get("datagrid1");    
    
    var  s_OrgId="";
    var  s_OrgName="";
    
     //初始化c_orgid ,若当前登录机构是归属省级的人员，查询的是全省数据；若是市级的人员查询的是全市的数据
    $.ajax({
		        url:"com.gotop.xmzg.report.reportQuery.judge_orgid.biz.ext",
		        type:'POST',
		        data:'',
		        cache:false,
		        async:false,
        		dataType:'json',
		        success:function(text){
		           //alert(text.c_orgid);
		          nui.get("c_orgid").setValue(text.c_orgid);
		          
		          nui.get("queryData.orgid").setValue(text.c_orgid);
                  nui.get("queryData.orgid").setText(text.c_orgname);
                  s_OrgId = text.c_orgid;
                  s_OrgName = text.c_orgname;
		        }
		 });
    var form = new nui.Form("#form1");
	var data = form.getData(true,true);
    
    //grid.load(data);
    
    //机构树回显
     function onButtonEdit(e) {
            var btnEdit = this;
            nui.open({
                <%--  url:"<%=request.getContextPath() %>/kpiCheckManage/ProOrgKpiManage/Prokpiorg_tree.jsp",  //初始未展开的机构树，加载快  --%>
                url:"<%=request.getContextPath() %>/report/tree/ReportOrg_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            
                        }
                    }
                }
            });            
             
        }     
    
    
    function search(){

       var form = new nui.Form("#form1");
            form.validate();
            if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
    
   function add(){
       nui.open({
          url:"<%=request.getContextPath() %>/dailydeal/itemDestroy/itemDestroy_add.jsp",
          title:'新增',
          width:400,
          height:280,
          onload:function(){
          },
          ondestroy:function(action){
             if(action=="saveSuccess"){
	            grid.reload();
	         }
          }
       });
    }
    
  /*  
    function selectionChanged(){
       var rows = grid.getSelecteds();
       if(rows.length>1){
           nui.get("update").disable();
       }else{
           nui.get("update").enable();
       }
    }*/
    
    
    
   
    function destroyType(e){
      	    return nui.getDictText("DESTROY_TYPE", e.row.DESTROY_REASON);
    }
    
    
    //重置
    function clean(){
	   //不能用form.reset(),否则会把隐藏域信息清空
	   nui.get("queryData.orgid").setValue(s_OrgId);
       nui.get("queryData.orgid").setText(s_OrgName);
       
       nui.get("queryData.startDate").setValue("");
       nui.get("queryData.endDate").setValue("");
       nui.get("item_no").setValue("");
       nui.get("item_name").setValue("");
       nui.get("queryData.destroy_reason").setValue("");
     }
     
   
       //时间判断开始时间不能大于结束时间
      function comparedate(e){
      var startDate = nui.get("queryData.startDate").getFormValue();
      var endDate = nui.get("queryData.endDate").getFormValue();
      if(startDate!="")
	    startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
	  if(endDate!=""){
		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
        if(e.isValid){
          if(startDate>endDate){
            e.errorText="结束日期必须大于或等于开始日期";
            e.isValid=false;
          }else
          {
          e.errorText="";
          e.isValid=true;
          }
        }
      }
    } 
     
   //导出Excel
function excel(){
	      
	var form=new nui.Form("form1");
	form.validate();
    if (form.isValid() == false) return;
	var data=form.getData(true, true);
	var fileName="单册核销报表";
	var queryPath="com.gotop.xmzg.report.destroyQuery.query_itemDestroy_bb";
	var columns=grid.getBottomColumns();
	columns=columns.clone();
	for(var i=0;i<columns.length;i++){
		var column=columns[i];
		if(!column.field){
			columns.removeAt(i);
		}else{
			var c={header:column.header,field:column.field };
			columns[i]=c;
		}
	}
	columns=nui.encode(columns);
	data=nui.encode(data);
     var url="<%=request.getContextPath()%>/util/exportExcel/exportExcel.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data;
	 window.location.replace(encodeURI(url));
	 
	 //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
     setMask();
} 

//设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)	
function setMask(){
	 
	 var a= nui.loading("正在操作中,请稍等...","提示"); //显示遮罩层
	 
	 var icount = setInterval(function(){  
	
		  if(document.attachEvent){   //IE浏览器
	
		 	if(document.readyState=='interactive'){

	              nui.hideMessageBox(a);  //隐藏遮罩层
	              clearTimeout(icount);
	        }
		 }else{ //谷歌浏览器
		 	if(document.readyState=='complete'){

	              nui.hideMessageBox(a);  //隐藏遮罩层
	              clearTimeout(icount);
	        }
		 } 
	 
	 }, 1); 
	 
}
     
     
    
  </script>
</body>
</html>
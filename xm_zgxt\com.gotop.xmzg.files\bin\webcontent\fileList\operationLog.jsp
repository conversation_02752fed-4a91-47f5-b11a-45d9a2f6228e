<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false" %>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<%-- <%@include file="/coframe/tools/skins/common.jsp" %> --%>
<%@include file="/common/common.jsp"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): zheng
  - Date: 2018-05-18 15:05:15
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>操作流水查询</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/common/nui/locale/zh_CN.js"></script>   
</head>
<body>
	<div align="center" style="width:100%;height:470px;">
		 <div id="datagrid1" class="nui-datagrid" style="width:100%;height:100%;" dataField="resultList" 
			url="com.gotop.xmzg.files.fileList.getOperationLog.biz.ext"
			allowResize="true" allowCellEdit="true" sizeList="[10,15,20,50,500]" pageSize="10" multiSelect="true" > 
		    <div property="columns">  
		      <div field="OPENAME" headerAlign="center" align="center" width="80px">操作人</div>	    
		      <div field="OPEORGNAME" headerAlign="center" align="center" width="120px">操作人所在机构</div>
		      <div field="OPERATION_TIME" headerAlign="center" align="center" width="150px">操作时间</div>
		      <div field="OPERATION_CONTENT" headerAlign="center" align="center" width="150px">操作内容</div>
		      <div field="FILES_TYPE" headerAlign="center" align="center" renderer="filesType" width="120px">档案种类</div>
		      <div field="BUSINESS_TYPE" headerAlign="center" align="center" width="120px">业务种类</div>
		      <div field="SUBORGNAME" headerAlign="center" align="center" width="120px">区支行</div>
		      <div field="DEALORGNAME" headerAlign="center" align="center" width="120px">经办机构</div>
		      <div field="FGEMPNAME" headerAlign="center" align="center" width="100px">分管客户经理</div>
		      <div field="CUSTOMER_NAME" headerAlign="center" align="center" width="100px">客户名称</div>
		      <div field="CUSTOMER_NO" headerAlign="center" align="center" width="120px">客户号码</div>
		      <div field="STORAGE_ADDRESS" headerAlign="center" align="center" width="120px" renderer="storAdd">存放地址</div>
		      <div field="STORAGE_LOCATION" headerAlign="center" align="center" width="120px">货架号/箱号</div>
		      <div field="BOX_NUM" headerAlign="center" align="center" width="120px">档案盒号</div>
		      <div field="CONTRACT_NUMBER" headerAlign="center" align="center" width="150px">编号</div>
		      <div field="CONTRACT_PRICE" headerAlign="center" align="center" width="100px">金额</div>
		      <div field="START_TIME" headerAlign="center" align="center" width="150px">起期</div>
		      <div field="END_TIME" headerAlign="center" align="center" width="150px">止期</div>
		      <div field="BUSINESS_LINE" headerAlign="center" align="center" width="120px">业务条线</div>
		      <div field="IMPNAME" headerAlign="center" align="center" width="100px">导入人</div>
		      <div field="IMP_TIME" headerAlign="center" align="center" width="150px">导入时间</div>          		  
		      <div field="RESERVE_REMARK2" headerAlign="center" align="center" width="200px">修改原因</div>          		  
		    </div>
		</div>
	</div>
	<script type="text/javascript">
    	nui.parse();
    	
    	var a = '<%=request.getParameter("infoId") %>';
    	
    	var grid = nui.get("datagrid1");
       var data = {queryData:{INFORMATION_ID:a}};
       debugger;
       grid.load(data);
       
       function filesType(e){
	    return nui.getDictText("FILES_TYPE", e.row.FILES_TYPE);
	}
	
	function filesType(e){
	    return nui.getDictText("FILES_TYPE", e.row.FILES_TYPE);
	}
	function storAdd(e){
	    return nui.getDictText("FILES_STORAGE_ADDRESS", e.row.STORAGE_ADDRESS);
	}
    </script>
</body>
</html>
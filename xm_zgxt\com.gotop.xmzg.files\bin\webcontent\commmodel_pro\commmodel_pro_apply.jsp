<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>

  <%@include file="/coframe/tools/skins/common.jsp" %>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<%@page import="com.eos.foundation.database.DatabaseExt"%>
<%@page import="java.util.HashMap"%>
<%--  <script type="text/javascript" src="<%=request.getContextPath() %>/files/commmodel_pro/js/jquery.min.js"></script> --%>
<script type="text/javascript" src="<%=request.getContextPath() %>/files/commmodel_pro/js/jquery.form.js"></script> 

<%--  <script type="text/javascript" src="<%=request.getContextPath() %>/files/process/js/swfupload/swfupload.js"></script>
<script type="text/javascript" src="<%=request.getContextPath() %>/files/process/js/swfupload/handlers.js"></script> --%>
<%-- <script type="text/javascript" src="<%=request.getContextPath() %>/files/commmodel_pro/js/stream-v1.js"></script>  --%>

 
<title>流程申请</title>
 <link href="<%=request.getContextPath() %>/files/commmodel_pro/css/stream-v1.css" rel="stylesheet" type="text/css">
</head>
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
	Long workItemID = (Long)request.getAttribute("workItemID");
 %>

 <style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style> 
<body>
<div align="center" >
  <div  style="padding-top:5px;">
  <fieldset style="width:1000px;border:solid 1px #aaa;position:relative;margin:0px 2px 0px 2px;" >
    	 <legend>档案借阅流程申请</legend>
  <form  id="form1"  action="com.gotop.xmzg.files.commmodel_pro_apply.flow" method="post" enctype="multipart/form-data"> 
    
                <!-- hidden域 -->
                <input class="nui-hidden" name="map/APPLY_ID" id="map.APPLY_ID"/>
			    <input class="nui-hidden" name="map/apply_empid" />
			    <input class="nui-hidden" name="map/apply_orgid" />
			    <input class="nui-hidden" name="map/processInstID" id="processinstid"/>
			    <input class="nui-hidden" name="map/workItemID" id="workItemID"/>
			    <input class="nui-hidden" name="map/APPLY_EMPID" />
			    <input id="processInstID" class="nui-hidden" name="map/PROCESSINSTID"/>
			    <input class="nui-hidden" name="map/ACTIVITYDEFID" id="map.ACTIVITYDEFID"/>
			    <input id="template_path" class="nui-hidden"/>
			    <div class="nui-hidden" name="map/arr" id="map.arr"></div>

    <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
  
     <tr>
        
        <th class="nui-form-label" style="width:12.5%"><label for="map.type$text">标题：</label></th>
        <td colspan="3"  style="width:30%">
             <input id="map.TITLE" class="nui-textbox " name="map/TITLE"  required="true" style="width:300px;"/>
        </td>

    <th class="nui-form-label" style="width:12.5%"><label for="map.type$text"></label></th>
        <td colspan="3"  style="width:30%">
      </td>
    
    
      </tr>
     
      <tr >
      
     <th class="nui-form-label" style="width:12.5%"><label for="map.device_name_id$text">申请人：</label></th>
        <td colspan="3"  style="width:30%">
          <input id="map.APPLY_EMPID" class="nui-textbox asLabel " name="map/APPLY_EMPIDNAME" readOnly="true" />
        </td>

    
      <th class="nui-form-label" style="width:12.5%"><label for="map.device_name_id$text">申请人所在机构：</label></th>
        <td colspan="3"  style="width:30%">
          <input id="map.APPLY_ORGID" class="nui-textbox asLabel " name="map/APPLY_ORGID" readOnly="true" />
        </td>

    
    
      </tr>
      
      <tr>
        
        
    
     <th class="nui-form-label" ><label for="map.type$text">联系电话：</label></th>
        <td colspan="3"  >
           <input id="map.PHONE_NUM" class="nui-textbox nui-form-input" name="map/PHONE_NUM" />
      </td>
      
      
      <th class="nui-form-label"><label for="map.brandmodel_id$text">申请时间：</label></th>
        <td colspan="3"  > 
           <input id="APPLY_TIME" class="nui-datepicker nui-form-input" name="map/APPLY_TIME"  allowInput="false" required="true" format="yyyyMMdd" />
        </td>
      
      <th class="nui-form-label" style="display:none" ><label for="map.type$text">申请流程：</label></th>
        <td colspan="3"  style="display:none" >
              <input id="map.APPLY_PROCESS" class="nui-dictcombobox nui-form-input" name="map/APPLY_PROCESS"  emptyText="请选择"
          valueField="dictID" textField="dictName" dictTypeId="APPLY_PROCESS" showNullItem="true" nullItemText="请选择" required="true" value="flow_commmodel" />
        </td>
    
    
      </tr>
      
      <tr >
       
       
        <th class="nui-form-label"><label for="map.device_serial$text">申请内容：</label></th>
        <td colspan="7"  >
          <input id="map.APPLY_REMARK" class="nui-textarea nui-form-input" name="map/APPLY_REMARK" />
        </td>

       
      </tr>
       <tr>
		      	<th class="nui-form-label"><label for="inputData.type$text">附件上传：</label></th>
		      	<td colspan="7" align="left">
					<input type="button" onclick="addFile('tabtest','uploadFile');return false;" value="新增附件" 
							style="margin-left:2px;vertical-align:middle;cursor:hand;"/>
						<font style="color: red">(说明：最多上传5个附件)</font>
						<br/>
						<table border=0 id="tabtest">
						</table>
	        	</td>
	   </tr>
      
      <tr >
	      		<th class="nui-form-label"><label for="inputData.type$text">原有附件：</label></th>
	      		<td colspan="7">
					<div id="listbox1" class="nui-listbox" style="width:100%;height:80px;" textField="AFFILIATED_NAME" valueField="AFFILIATED_ID" 
		           dataField="resultList"  onvaluechanged="onListBoxValueChanged">
		            </div>
	      		</td>
	      	</tr>
	      	
	      	
	  <tr >
      
     <th class="nui-form-label" ><label for="map.device_name_id$text">模板下载：</label></th>
       <!-- <td colspan="2"  >
         <input id="businessLine" class="nui-combobox" required="true" valueField="BUSINESS_LINE" textField="DICTNAME" style="width:100%;" dataField="resultList"
	       url="com.gotop.xmzg.files.process.getProcessDownMode.biz.ext?type=2"  name="map.BUSINESS_LINE" emptyText="请选择.."  onValueChanged="businessLineChanged"/>
						
        </td> -->

    
      <td  colspan="7" align="left" >
      <a class="nui-button" iconCls="icon-download" onclick="downloadTemplate()">模版下载</a>
      <input class="nui-combobox" required="true" valueField="ID" textField="FILE_NAME" style="width:100%;display:none"
	          			url="com.gotop.xmzg.files.commmodel_pro.getFileNames.biz.ext?business_line=10" 	value="1040"	name="map/FILE_ID" emptyText="请选择.." id="fileName" dataField="resultList" onValueChanged="fileNameChanged"/>
      </td>

    
    
      </tr>    	
      
    
      
      <tr >
      
      <th class="nui-form-label"><label for="map.device_serial$text">操作处理选择：</label></th>
        <td colspan="3"  >
            <input id="aaa" name="map/ISCONTINUE" class="nui-radiobuttonlist" textField="LINE_NAME" valueField="END_NODEID" required="true"/>
        </td>
      
        <th class="nui-form-label"><label for="map.device_serial$text">下一步处理人：</label></th>
        <td colspan="3"  >
           <input class="nui-hidden" name="map/EMPNAMES" id="EMPNAMES"/>
        <input id="btnEdit1" name = "map/EMPIDS"  class="nui-textboxlist"   allowInput="false" required="true"  style="width:300px;"/><br/>  
         <a href="#" onclick="EmponButtonEdit()" style="color:blue;text-decoration:underline;">人员选择</a>    
         <a href="#" onclick="cleanEmp()" style="color:blue;text-decoration:underline;">清空</a>
        
        </td>
        
        
        
        
      </tr>
      
      
    </table>
    </form>
    </fieldset>
    
        </fieldset>
         
          <%
          
			Object[] obj=DatabaseExt.queryByNamedSql("default", "com.gotop.xmzg.files.process.queryProcessSpDetil",workItemID);
			for(int i=0;i<obj.length;i++)
			{
			HashMap result=(HashMap) obj[i];
		 %>
         
         <fieldset style="width:800px;border:solid 1px #aaa;position:relative;margin:5px 2px 0px 2px;" >
    	 <legend><%=result.get("ACTIVITYINSTNAME") %></legend>
    	 <div align="left" >
    	 <div id="dataform3" style="padding-top:0px;">
    	
    	 <table style="width:100%;height:95%;table-layout:fixed;" class="nui-form-table">
    	   <tr>
			        <th class="nui-form-label"  >审核说明：</th>
			        <td style="text-align:left;" colspan="3">
			            <%-- <%=result.get("APPROVAL_REMARK") %> --%>
			             <%
                     
                     if(result.get("APPROVAL_REMARK")!=null)
                     {
                     
                     result.put("AUDIT", result.get("APPROVAL_REMARK"));
                    
                     }else
                     {
                      result.put("AUDIT", "");
                     }
                     
                     %>
			          <%=result.get("AUDIT") %>
			        </td>
			      </tr>
    	   <tr>
			        <th class="nui-form-label" style="width:15%;">处理人：</th>
			        <td style="text-align:left;width:35%;" >
			            <%=result.get("EMPNAME") %>
			        </td>

			        <th class="nui-form-label"  style="width:15%;" >审批人所在机构：</th>
			        <td style="text-align:left;width:35%;">
			            <%=result.get("ORGNAME") %>
			        </td>
			      </tr>
			      <tr>
			        <th class="nui-form-label" >处理时间：</th>
			        <td style="text-align:left;" >
			            <%=result.get("APPROVAL_TIME") %>
			        </td>
		
			        <th class="nui-form-label" >操作处理选择：</th>
			        <td style="text-align:left;" >
			          <%=result.get("RESULT_AUDIT") %>
			        </td>
			      </tr>
			       <tr>
			        <th class="nui-form-label"  >下一步处理人：</th>
			        <td style="text-align:left;" colspan="3">
			          <%-- <%=result.get("NEXT_DEAL_EMPNAME") %> --%>
			          <%
                     
                     if(result.get("NEXT_DEAL_EMPNAME")!=null)
                     {
                     
                     result.put("NDEAL_EMPNAME", result.get("NEXT_DEAL_EMPNAME"));
                    
                     }else
                     {
                      result.put("NDEAL_EMPNAME", "");
                     }
                     
                     %>
			          <%=result.get("NDEAL_EMPNAME") %>
			        </td>
			      </tr>
			      
			    </table>
    	
        </div>
       </div>
      </fieldset> 
         <%
			}
		 %> 
    
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-ok" onclick="onOk">提交</a>
	          <span style="display:inline-block;width:25px;"></span>
	          <a id="cancel" class="nui-button" iconCls="icon-cancel" onclick="onCancel" style="display:none">返回</a>
	           <span style="display:inline-block;width:25px;"></span> 
	          <a id="banjie" class="nui-button" iconCls="icon-cancel" onclick="banjie()" style="display:none">流程办结</a>
	        </td>
	      </tr>
	    </table>
	 </div>
	 </form>
  </div>
</div>
  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    
    var  username="<%=userObject.getUserRealName()%>";
    nui.get("map.APPLY_EMPID").setValue(username);
    
    var  userid="<%=userObject.getUserId()%>";
    
    var  userorgname="<%=userObject.getUserOrgName()%>";
    nui.get("map.APPLY_ORGID").setValue(userorgname);
    
  // nui.get("map.TITLE").setValue("信贷档案申请-"+username);
    
     var workItemID = <%=workItemID %>;
   
    
     var listbox1 = nui.get("listbox1");
     var con_id;
     
     var arr =[];
     
     
     

    if(workItemID != null){
    //初始化加载数据 
	    $.ajax({
		        url:"com.gotop.xmzg.files.process.queryProcessApplyDetil.biz.ext",
		        type:'POST',
		        data:'workItemID='+workItemID,
		        cache:false,
		        async:false,
        		dataType:'json',
		        success:function(text){
		          var map = nui.decode(text);
		          
		          //隐藏表单
		          nui.get("map.APPLY_ID").setValue(map.map.APPLY_ID);
		          nui.get("processInstID").setValue(map.map.PROCESSINSTID);
		          nui.get("map.ACTIVITYDEFID").setValue(map.map.ACTIVITYDEFID);
		          
		          
		          //表单
		          nui.get("map.TITLE").setValue(map.map.TITLE);
		          nui.get("map.PHONE_NUM").setValue(map.map.PHONE_NUM);
		          nui.get("APPLY_TIME").setValue(map.map.APPLY_TIME);
		          nui.get("map.APPLY_PROCESS").setValue(map.map.APPLY_PROCESS);
		          nui.get("map.APPLY_REMARK").setValue(map.map.APPLY_REMARK);
		          
		          //form.setData(map);
		          
		          //form.setChanged(false);
		          
		           nui.get("map.APPLY_EMPID").setValue(username);
		           nui.get("map.APPLY_ORGID").setValue(userorgname);
		           
		           
		        
		           
		           con_id=map.map.APPLY_ID;
		           
				 listbox1.load("com.gotop.xmzg.files.process.queryAttachment.biz.ext?con_id="+con_id);
				 if(listbox1.getCount()==0){
				 	listbox1.setVisible(false);
				 }
		          
		         
		           
		           
		        }
		      });
		 //将隐藏的审核信息打开    
		// document.getElementById("autho").style.display = ""; 
		 document.getElementById("banjie").style.display = "";
		 document.getElementById("cancel").style.display = "";
		// document.getElementById("cancel").style.display = "none"; 
   }else
   {
      nui.get("map.ACTIVITYDEFID").setValue("manualActivity");
      
      var d = new Date();
    var curr_date = d.getDate();
    var curr_month = d.getMonth() + 1; 
    var curr_year = d.getFullYear();
    String(curr_month).length < 2 ? (curr_month = "0" + curr_month): curr_month;
    String(curr_date).length < 2 ? (curr_date = "0" + curr_date): curr_date;
    var yyyyMMdd = curr_year + "" + curr_month +""+ curr_date;
      nui.get("APPLY_TIME").setValue(yyyyMMdd);
      
      //自动加载电话号码
       $.ajax({
		        url:"com.gotop.xmzg.files.process.getPhoneNum.biz.ext",
		        type:'POST',
		        data:'userid='+userid,
		        cache:false,
		        async:false,
        		dataType:'json',
		        success:function(text){
		          var mapList = nui.decode(text);
		           nui.get("map.PHONE_NUM").setValue(mapList.mapList.MOBILENO);	           
		           
		        }
		      });
      
   } 
     
     
     
   
    
  
     
 
    
      var ACTIVITYDEFID=nui.get("map.ACTIVITYDEFID").getValue();
       var PROCESS_NAME= nui.get("map.APPLY_PROCESS").getValue();//所属流程
    //加载流程走向
            $.ajax({
						url : "com.gotop.xmzg.files.process.queryLine.biz.ext",
						type : 'POST',
						data : 'ACTIVITYDEFID='+ACTIVITYDEFID+"&PROCESS_NAME="+PROCESS_NAME,
						cache : false,
						async : false,
						dataType : 'json',
						success : function(text) {
                         var obj = nui.decode(text.resultList);
                         nui.get("aaa").load(obj);
						}
					});
    
    
     var res = nui.get("aaa");
		    res.on("valuechanged", function (e) {
		    
		     nui.get("btnEdit1").setValue("");
		     nui.get("btnEdit1").setText("");
		     
		    }); 

    
    form.setChanged(false);
    

    
  /*   function onOk(){
      saveData();
    } */
   
    
    
    //机构树回显
     function EmponButtonEdit() {
     

          //  var ACTIVITYDEFID = nui.get("map.ACTIVITYDEFID").getValue();   //活动节点id
          
            var ACTIVITYDEFID = nui.get("aaa").getValue();   //活动节点id
            var PROCESS_NAME= nui.get("map.APPLY_PROCESS").getValue();//所属流程       
            if(PROCESS_NAME =="" ||PROCESS_NAME==null)
            {
                 alert("请先选择所属流程"); 
                 return false; 
            }
              
            var APPLY_ORGID= "<%=userObject.getUserOrgId()%>";  //申请人所在机构id
            var PROCESSINSTID=nui.get("processInstID").getValue();
     
            var btnEdit1 = nui.get("btnEdit1");
            nui.open({
                url:"<%=request.getContextPath() %>/files/process/SpEmpAndOrg_tree.jsp?ACTIVITYDEFID="+ACTIVITYDEFID+"&PROCESS_NAME="+PROCESS_NAME+"&APPLY_ORGID="+APPLY_ORGID+"&PROCESSINSTID="+PROCESSINSTID,
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                onload : function() {
					var iframe = this.getIFrameEl();
					// 给树设置上已选择的节点字符串     			
					iframe.contentWindow.setTreeCheck(btnEdit1.getValue());
				},
                ondestroy : function(action) {
							if (action == "ok") {
								var iframe = this.getIFrameEl();
								var list = iframe.contentWindow.GetData();
								list = nui.clone(list);
								if (list) {
									// 将树选中节点设置到box显示
									putDataTextBox(list);
								}
							}
						}
            });            
             
        }   
        
        /**
		 * 往textboxlist中添加选择的数据
		 * @params list 	 获取Check选中的节点集合
		 */
		function putDataTextBox(list){
			var text = "",value = "";
			var boxObj = nui.get("btnEdit1");
			for (var i = 0; i < list.length; i++) {
				var node = list[i];
				if (node.nodeType == "OrgOrganization") continue;
				if (i == list.length -1) {
					value += node["id"];
					text  += node["text"];
				} else {
					value += node["id"] + ",";
					text  += node["text"] + ",";
				}
			}
			if (value.endsWith(",")) value = strSplitLast(value);
			if (text.endsWith(",")) text = strSplitLast(text);
			boxObj.setValue(value);
			boxObj.setText(text);
			//nui.get("map.EMPNAMES").setValue(text);
		}
					
		//判断当前字符串是否以str结束
	     if (typeof String.prototype.endsWith != 'function') {
	       String.prototype.endsWith = function (str){
	          return this.slice(-str.length) == str;
	       };
	     }
	     
	     /* 切割ID字符串最后一位 */
		function strSplitLast(obj) {
			return (obj).substring(0, obj.length - 1);
		}
    
        
    function cleanEmp(){
		 //nui.get("empids").setValue("");
		 nui.get("btnEdit1").setValue("");
		 nui.get("btnEdit1").setText("");
		// nui.get("map.EMPNAMES").setValue("");
	}
        
        
       function onOk(){
       
         form.validate(); 

        if(form.isValid()==false) return false;
        
        var EMPNAMES=nui.get("btnEdit1").getText();
        nui.get("EMPNAMES").setValue(EMPNAMES);

 
        nui.get("workItemID").setValue(workItemID);
        
        nui.get("map.arr").setValue(nui.encode(arr));
     
        /* var inputArr = document.getElementById("map.arr");//取得页面标签元素数组
        for(var i=0;i<inputArr.length;i++){//循环赋值
             inputArr[i].value = arr[i];
          }  */
  

       $("#form1").submit();
        
       }
        
      /*   function saveData(){ 

			saveDataFiles();

    } */
        
        
        
        

    
    
   /*  function saveDataFiles(){ 
   
      form.validate(); 

        if(form.isValid()==false) return false;
        
        var data = form.getData(true,true);
        
       var EMPNAMES=nui.get("btnEdit1").getText();

        data.map.EMPNAMES=EMPNAMES;
 

        data.workItemID = workItemID; 
      
        data.files=files;
        data.arr=arr;
        
        var json = nui.encode(data);
         var a= nui.loading("正在提交中,请稍等...","提示");
        $.ajax({
                url:"com.gotop.xmzg.files.commmodel_pro.submitCommmodelPro.biz.ext",
                type:'POST',
                data:json,
                cache:false,
                contentType:'text/json',
                success:function(text){
                nui.hideMessageBox(a); //关闭加载提示（即正在提交中，请稍等）
                   var returnJson = nui.decode(text);
                   if(returnJson.exception == null){
    
                        alert("提交成功");
                        window.history.go(-1) ;
                        window.history.go(-1) ;
                       
                        form.reset();
                          nui.get("map.APPLY_EMPID").setValue(username);
                          nui.get("map.APPLY_ORGID").setValue(userorgname);
                          //nui.get("map.TITLE").setValue("信贷档案申请-"+username);
                          
                        window.history.go(-1) ;
                        
                     }else{
                         alert("提交失败");
                         window.history.go(-1) ;
                        }
                   }
             });



    } */
    
    function onCancel(){
      //CloseWindow("cancel");
       window.history.go(-1) ;
       
    }
    
    
    function banjie(){

       form.validate();
     //  if(form.isValid()==false) return false;
        
        var data = form.getData(false,true);

        data.workItemID = workItemID;
        var json = nui.encode(data);
        var a= nui.loading("正在提交中,请稍等...","提示");
        $.ajax({
                url:"com.gotop.xmzg.files.process.submitProcessCancelApply.biz.ext",
                type:'POST',
                data:json,
                cache:false,
                contentType:'text/json',
                success:function(text){
                nui.hideMessageBox(a); //关闭加载提示（即正在提交中，请稍等）
                   var returnJson = nui.decode(text);
                   if(returnJson.exception == null){
                        alert("流程办结成功");
                        onCancel();
                         window.history.go(-1) ;
                         
                     }else{
                         alert("流程办结失败");
                        window.history.go(-1) ;
                        }
                   }
             });
      
    }
    
    function CloseWindow(action){
 
     var flag = form.isChanged();
       if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
    
     function onListBoxValueChanged(e) {
	            var listbox = e.sender;
	            var value = listbox.getValue();
	            var items=listbox.getSelecteds();
	            var filepath=items[0].AFFILIATED_ADDRESS;
	            var jsontemp=listbox.getData();
	            nui.confirm("确定删除该附件？","系统提示",function(action){
           			// var json = nui.encode({AFFILIATED_ID:value,AFFILIATED_ADDRESS:filepath});
           			 var json = {AFFILIATED_ID:value,AFFILIATED_ADDRESS:filepath};
		             if(action=="ok"){ 
		             	arr.push(json);
		             	//arr.push("AFFILIATED_ID",AFFILIATED_ID);
		             	//arr.push("AFFILIATED_ADDRESS",AFFILIATED_ADDRESS);
		             	
				        listbox.removeItems(items);
				     } else{
					     listbox.load(jsontemp);
				     }
		            
				   });
	        }
	        
	        
	        /* function businessLineChanged(e){
	     
    		var business_line = String(e.value);
    		
    		var url = "com.gotop.xmzg.files.commmodel_pro.getFileNames.biz.ext?business_line=" + business_line;
    		nui.get("fileName").load(url);
    		
    	} */
    	
    	/* function fileNameChanged(e){
    		var fileNameId = e.value;
    		var data = e.sender.data;
    		var operation_cycle = null;
    		for(var i=0;i<data.length;i++){
    			if(data[i].ID == fileNameId){
    				nui.get("template_path").setValue(data[i].TEMPLATE_PATH);
    			
    				}
    				
    			}
    		} */
    
    	
    	function downloadTemplate(){

    	var fileNameId = nui.get("fileName").getValue();
    	var ss=nui.get("fileName");
    		var data = nui.get("fileName").data;
    		for(var i=0;i<data.length;i++){
    			if(data[i].ID == fileNameId){
    				nui.get("template_path").setValue(data[i].TEMPLATE_PATH);
    			
    				}
    				
    			}
    	
    		var file_name = nui.get("fileName").getText();
    		var file_path = nui.get("template_path").getValue();
    		file_name = file_name + file_path.substring(file_path.lastIndexOf("."));
    		window.location.href = "<%=request.getContextPath() %>/fileImportOrExport/fileExport/download.jsp?file_name=" + file_name + "&file_path=" + file_path;
    	}
    	
    	//上传附件
    	var rowId = 0;
		function addFile(tabid,varName){
		    var tab,row,td,fName,fId,tdStr;
		    var zs=$("#tabtest tbody tr").length;
		    /* tab = $id(tabid); */
		    tab = document.getElementById(tabid);
		    if (zs>=5){
		    	alert("新增附件不能超过5个");
		    	return false;
		    }
		    fName = varName;
		    fId = varName+rowId;
		    row =  tab.insertRow();
		    row.id = "fileRow"+rowId;
		    td = row.insertCell(); 
		    
		    tdStr="<input type=\"file\" name=\""+fName+"\" id=\""+fId+"\" onchange=\"CheckUpLoadFile(this,2);\" size='70' class=smallInput validateAttr=\"allowNull=false\">";
		    //tdStr="<input type=\"file\" name=\""+fName+"\" id=\""+fId+"\"  size='70' class=smallInput validateAttr=\"allowNull=false\">";
		    tdStr += "<input type=\"button\" onclick=\"delTr('fileRow"+rowId+"');\" name='button"+rowId+"' value=\"删除\" style=\"margin-left:2px;vertical-align:middle;cursor:hand;\"/>";
		    td.innerHTML = tdStr;
		    rowId = rowId+1;    
		}
			
		function delTr(id){
			$("#"+id).remove();
		}
 function CheckUpLoadFile(obj,id) { 
	DenyExt = "exe|cmd|jsp|php|asp|aspx|html|htm"; 
	var   pattern   =   /^[a-z\d\u4E00-\u9FA5]+$/i; 
	var FileExt = ""; 
	FileExt = obj.value.substr(obj.value.lastIndexOf(".") + 1).toLowerCase(); 
	var strFileName=obj.value.replace(/^.+?\\([^\\]+?)(\.[^\.\\]*?)?$/gi,"$1");  //正则表达式获取文件名，不带后缀
	if (DenyExt.indexOf(FileExt) != -1) { 
		if (!window.addEventListener) {      
			obj.outerHTML+=''; 
		}else{ 
			obj.value = ""; 
			return false; 
		} 
	} 
} 
  </script>
</body>
</html>
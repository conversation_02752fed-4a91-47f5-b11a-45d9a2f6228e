<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): zhanghongkai
  - Date: 2019-05-09 10:03:29
  - Description:
-->
<head>
	<%@include file="/coframe/tools/skins/common.jsp" %>
	<title>文件上传</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script type="text/javascript" src="<%=request.getContextPath() %>/js/jquery.form.js"></script>
</head>
<body>
	<div align="center">
		<div class="nui-toolbar" style=" width:900px;height:50px;line-height:20px" borderStyle="border:0;">
			<h1 align="center" style="margin-top:15px">文 件 上 传</h1>
		</div>
		<fieldset style="width:900px;border:solid 1px #aaa;position:relative;margin:5px 2px 0px 2px;" >
			<form id="import_form" action="com.gotop.xmzg.fileImportOrExport.fileImport.importFile.biz.ext" method="post" enctype="multipart/form-data">
	       		<input id="impType" class="nui-hidden"  name="paramMap/impType"  />  <!-- 导入文件类型，txt或excel -->
	       		<input id="impFileFlag" class="nui-hidden"  name="paramMap/impFileFlag" value="3" />  <!-- 导入文件标志，1：导入标题头， 2：导入数据 -->
	       		<input id="opWay" class="nui-hidden"  name="paramMap/opWay" value="1"  />  <!-- 操作方式（1：页面手动数据导入， 2：定时器自动导入） -->
	       		<input id="template_path" class="nui-hidden"/>
	       		<input id="menuId" class="nui-hidden" value="1" name="logMap/menuId" />
	       		<input id="menuName"  class="nui-hidden" value="空" name="logMap/menuName" />
	       		<input id="opContent"  class="nui-hidden" value="导入" name="logMap/opContent" />
				<input class="nui-hidden" id="import_type"  name="map/IMPORT_TYPE"/>
				<input class="nui-hidden" id="import_file_path"  name="map/IMPORT_FILE_PATH"/>
				<input class="nui-hidden" id="file_name"  name="map/FILE_NAME"/>
				<input class="nui-hidden" id="operation_cycle"  name="map/OPERATION_CYCLE"/>
				<input class="nui-hidden" id="file_type"  name="map/FILE_TYPE"/>
				<input class="nui-hidden" id="temp_file_path"  name="map/TEMP_FILE_PATH"/>
				<table border="0" cellpadding="1" cellspacing="2" style="width:100%;table-layout:fixed;">
					<tr>
						<th style="text-align:right;">业务条线：</th>
						<td>
							<input class="nui-dictcombobox" required="true" valueField="dictID" textField="dictName" style="width:100%;"
	          					dictTypeId="BUSINESS_LINE" name="map.BUSINESS_LINE" emptyText="请选择.." onValueChanged="businessLineChanged"/>
						</td>
						<td></td>
					</tr>
					<tr>
						<th style="text-align:right;">文件类别名称：</th>
						<td>
							<input class="nui-combobox" required="true" valueField="ID" textField="FILE_NAME" style="width:100%;"
	          					name="map/FILE_ID" emptyText="请选择.." id="fileName" dataField="resultList" onValueChanged="fileNameChanged"/>
						</td>
						<td></td>
					</tr>
					<tr>
						<th style="text-align:right;">日期：</th>
						<td>
							<input id="date" class="nui-datepicker" format="yyyy" style="width:100%;" 
								name="map/FILE_DATE" emptyText="请选择日期" allowInput="false" required="true"/>
						</td>
						<td></td>
					</tr>
					<tr id="seasonTr">
						<th style="text-align:right;"></th>
						<td>
							<input id="season" class="nui-combobox" valueField="id" textValue="text" 
								name="map/FILE_SEASON" style="width:100%;" emptyText="请选择季度" required="true"/>
						</td>
						<td></td>
					</tr>
					<tr>
						<th style="text-align:right;">文件选择：</th>
						<td>
							<input id="file" type="file" name="importFile" size="60" style="width:100%;" required="required"/>
						</td>
						<td></td>
					</tr>
					<tr>
						<th style="text-align:right;">如无法上传请下载执行注册表：</th>
						<td>
							<a style="color:blue;cursor:pointer;" onClick="downloadFile()">下载注册表</a>
						</td>
						<td></td>
					</tr>
					<tr id="tr_separator" style="display:none;">
            			<th style="text-align:right;">
                        	数据文件数据分隔符：
            			</th>
            			<td >
            	  			<input class="nui-textbox" name="paramMap/separator" style="width: 100%;" required="true"/>
            			</td>
           			</tr>	
					<tr>
						<td></td>
						<td style="text-align:center;">
							<a class="nui-button" iconCls="icon-ok" onClick="startUpload">上传</a>&nbsp
							<a id="save" class="nui-button" iconCls="icon-collapse" onclick="save">保存</a>&nbsp
							<a class="nui-button" iconCls="icon-download" onclick="downloadTemplate()">模版下载</a>&nbsp
							<a class="nui-button" iconCls="icon-reset" onclick="reset">重置</a>
						</td>
						<td></td>
					</tr>
				</table>
			</form>
			<div id="grid"></div>
		</fieldset>
	</div>


	<script type="text/javascript">
    	nui.parse();
    	
    	$("#save").hide();
    	$("#seasonTr").hide();
    	
    	var form = new nui.Form("import_form");
    	
    	function downloadTemplate(){
    		var file_name = nui.get("fileName").getText();
    		var file_path = nui.get("template_path").getValue();
    		file_name = file_name + file_path.substring(file_path.lastIndexOf("."));
    		window.location.href = "<%=request.getContextPath() %>/fileImportOrExport/fileExport/download.jsp?file_name=" + file_name + "&file_path=" + file_path;
    	}
    	
    	function downloadFile(){
    		var file_name = "IE无法上传时执行的注册表.reg";
    		var file_path = nui.getDictText("IMPORT_FILE_PATH","03");
    		window.location.href = "<%=request.getContextPath() %>/fileImportOrExport/fileExport/download.jsp?file_name=" + file_name + "&file_path=" + file_path;
    	}
    	
    	function save(){
    		var map = {};
    		map.file_id = nui.get("fileName").getValue();
    		map.data = nui.get("datagrid").getData();
    		var formdata = form.getData(true,true);
    		map.formdata = formdata;
    		var json = nui.encode({map:map});
    		var b= nui.loading("正在操作中,请稍等...","提示");
    		$.ajax({
    			url:"com.gotop.xmzg.fileImportOrExport.fileImport.saveData.biz.ext",
    			type:"post",
    			data:json,
    			contentType:"text/json",
    			success:function(text){
    				nui.hideMessageBox(b);
    				if(text.result == "ok"){
    					nui.alert("保存成功");
    				}else{
    					nui.alert("保存失败");
    				}
    			}
    		});
    	}
    	
    	function showDataGrid(data){
    		var cols = [];
    		var columns = getCols();
    		if(columns.length < 1){
    			nui.alert("该导入未配置模版！");
    			return;
    		}
    		if(data.length > 0){
    			var i = 0;
    			for(var j=0;j<columns.length;j++){
    				for(var temp in data[0]){
    					if(temp == columns[j].COLUMN_NAME){
    						cols[i] = {field:temp,align:"center",headerAlign:"center",header:columns[j].COLUMN_COMMENT};
    						i++;
    					}
    				}
    			}
    		}
    		
    		var grid = new nui.DataGrid();
    		grid.set({
    			style:"width:100%;height:400px;",
        		id:"datagrid",
        		columns: cols,
        		showPager:false
    		});
    		grid.render(document.getElementById("grid"));
    		grid.setData(data);
    		$("#save").show();
    	}
    	
    	function getCols(){
    		var file_id = nui.get("fileName").getValue();
    		var resultList = null;
    		$.ajax({
    			url:"com.gotop.xmzg.fileImportOrExport.fileImport.getCols.biz.ext",
    			type:"post",
    			data:"file_id=" + file_id,
    			async:false,
    			success:function(text){
    				resultList = text.resultList;
    			}
    		});
    		return resultList;
    	}
    	
    	function reset(){
    		form.reset();
    		var file = document.getElementById('file');
    		file.outerHTML = file.outerHTML;
    	}
    	
    	function startUpload(){
    		//$("#import_form").submit();
    		if(form.validate() != true){
    			return;
    		}
    		var fileName = $("#file").val();
    		nui.get("file_name").setValue(fileName);
    		var sufFileName = fileName.substring(fileName.lastIndexOf("."));
    		var file_type = nui.get("file_type").getValue();
    		var import_type = nui.get("import_type").getValue();
    		if(file_type == "1"){
    			if(sufFileName != ".xls"){
    				nui.alert("请选择.xls文件");
    				return;
    			}
    		}else if(file_type == "2"){
    			if(sufFileName != ".txt"){
    				nui.alert("请选择.txt文件");
    				return;
    			}
    		}else if(file_type == "3"){
    			if(import_type != "1"){
    				nui.alert("该上传需要导入数据库，不能指定文件类型为其他");
    				return;
    			}
    		}
    		var b= nui.loading("正在操作中,请稍等...","提示");
    		$("#import_form").ajaxSubmit({
    			url:"com.gotop.xmzg.fileImportOrExport.importReport.importFile",
    			type:"post",
    			dataType:"json",
    			success:function(text){
    				nui.hideMessageBox(b);
                	if(text.result.result == "ok"){
                		nui.alert("上传成功","系统提示",function(action){
                			if(action == "ok" || action == "close"){
                				$("#grid").html("");
                				showDataGrid(text.result.data);
                			}
                		});
                	}else{
                		nui.alert("上传失败");
                	}
    			}
    		});
    		//var formData = new FormData($('#import_form')[0]);
            //$.ajax({
             //   type: 'post',
             //   url: "com.gotop.xmzg.fileImportOrExport.fileImport.importFile.biz.ext",
             //   data: formData,
             //   cache: false,
             //   processData: false,
             //   contentType: false,
             //   success:function(text){
             //   	nui.hideMessageBox(b);
             //   	if(text.result.result == "ok"){
             //   		nui.alert("上传成功");
             //   		$("#grid").html("");
             //   		showDataGrid(text.result.data);
             //   	}else{
              //  		nui.alert("上传失败");
            //    	}
            //    }
           // });
    	}
    	
    	function fileNameChanged(e){
    		var fileNameId = e.value;
    		var data = e.sender.data;
    		var operation_cycle = null;
    		for(var i=0;i<data.length;i++){
    			if(data[i].ID == fileNameId){
    				operation_cycle = data[i].OPERATION_CYCLE;
    				nui.get("import_type").setValue(data[i].IMPORT_TYPE);
    				nui.get("import_file_path").setValue(data[i].IMPORT_FILE_PATH);
    				nui.get("operation_cycle").setValue(data[i].OPERATION_CYCLE);
    				nui.get("temp_file_path").setValue(data[i].TEMP_FILE_PATH);
    				nui.get("template_path").setValue(data[i].TEMPLATE_PATH);
    				var fileType = data[i].FILE_TYPE;
    				nui.get("file_type").setValue(fileType);
    				if(fileType == 1){
    					nui.get("impType").setValue("excel");
    				}else if(fileType == 2){
    					nui.get("impType").setValue("txt");
    				}else if(fileType == 3){
    					nui.get("impType").setValue("other");
    				}
    				if(fileType == 2 && data[i].IMPORT_TYPE != 1){
    					$("#tr_separator").show();
    				}else{
    					$("#tr_separator").hide();
    				}
    				if(operation_cycle == 1){
    					nui.get("date").setEnabled(true);
    					nui.get("season").setEnabled(false);
    					nui.get("date").setFormat("yyyyMMdd");
    					$("#seasonTr").hide();
    				}else if(operation_cycle == 2){
    					nui.get("date").setEnabled(true);
    					nui.get("season").setEnabled(false);
    					nui.get("date").setFormat("yyyyMM");
    					$("#seasonTr").hide();
    				}else if(operation_cycle == 3){
    					nui.get("date").setEnabled(true);
    					nui.get("season").setEnabled(true);
    					nui.get("date").setFormat("yyyy");
    					var season = [{id:1,text:"第一季度"},{id:2,text:"第二季度"},{id:3,text:"第三季度"},{id:4,text:"第四季度"}];
    					nui.get("season").setData(season);
    					$("#seasonTr").show();
    				}else if(operation_cycle == 4){
    					nui.get("date").setEnabled(true);
    					nui.get("season").setEnabled(false);
    					nui.get("date").setFormat("yyyy");
    					$("#seasonTr").hide();
    				}else if(operation_cycle == 5){
    					nui.get("date").setEnabled(false);
    					nui.get("season").setEnabled(false);
    					$("#seasonTr").hide();
    				}
    			}
    		}
    	}
    	
    	function businessLineChanged(e){
    		var business_line = String(e.value);
    		var url = "com.gotop.xmzg.fileImportOrExport.fileImport.getFileNames.biz.ext?business_line=" + business_line;
    		nui.get("fileName").load(url);
    	}
    </script>
</body>
</html>
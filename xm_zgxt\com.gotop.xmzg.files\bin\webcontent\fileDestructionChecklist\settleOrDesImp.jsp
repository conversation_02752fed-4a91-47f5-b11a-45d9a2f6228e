<%@page import="com.alibaba.fastjson.serializer.SerializerFeature"%>
<%@page import="com.alibaba.fastjson.JSON"%>
<%@page import="java.util.List"%>
<%@page import="java.util.HashMap"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): zhanghongkai
  - Date: 2019-05-09 10:03:29
  - Description:
-->
<head>
	<%@include file="/coframe/tools/skins/common.jsp" %>
	<title>文件上传</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script type="text/javascript" src="<%=request.getContextPath() %>/js/jquery.form.js"></script>
</head>
<body>
	<div align="center">
		<div class="nui-toolbar" style=" width:900px;height:50px;line-height:20px" borderStyle="border:0;">
			<h1 align="center" style="margin-top:15px">文 件 上 传</h1>
		</div>
		<fieldset style="width:900px;border:solid 1px #aaa;position:relative;margin:5px 2px 0px 2px;" >
			<form id="import_form" action="com.gotop.xmzg.files.settleOrDesImp.flow" method="post" enctype="multipart/form-data">
	       		<input id="impType" class="nui-hidden"  name="paramMap/impType"  />  <!-- 导入文件类型，txt或excel -->
	       		<input id="impFileFlag" class="nui-hidden"  name="paramMap/impFileFlag" value="3" />  <!-- 导入文件标志，1：导入标题头， 2：导入数据 -->
	       		<input id="opWay" class="nui-hidden"  name="paramMap/opWay" value="1"  />  <!-- 操作方式（1：页面手动数据导入， 2：定时器自动导入） -->
	       		<input id="template_path" class="nui-hidden"/>
	       		<input id="menuId" class="nui-hidden" value="1" name="logMap/menuId" />
	       		<input id="menuName"  class="nui-hidden" value="空" name="logMap/menuName" />
	       		<input id="opeContent"  class="nui-hidden" value="[<%=request.getRemoteAddr() %>]归档清单上传" name="logMap/opeContent" />
	       		<input class="nui-hidden" id="fileName1"  name="logMap/fileName"/>
	       		<input id="opContent"  class="nui-hidden" value="导入" name="logMap/opContent" />
				<input class="nui-hidden" id="import_type"  name="map/IMPORT_TYPE"/>
				<input class="nui-hidden" id="import_file_path"  name="map/IMPORT_FILE_PATH"/>
				<input class="nui-hidden" id="file_name"  name="map/FILE_NAME"/>
				<input class="nui-hidden" id="operation_cycle"  name="map/OPERATION_CYCLE"/>
				<input class="nui-hidden" id="file_type"  name="map/FILE_TYPE"/>
				<input class="nui-hidden" id="temp_file_path"  name="map/TEMP_FILE_PATH"/>
				<input class="nui-hidden" id="is_import_date"  name="map/IS_IMPORT_DATE"/>
				<input class="nui-hidden" id="date_column_name"  name="map/DATE_COLUMN_NAME"/>
				<input class="nui-hidden" id="is_save_no"  name="map/IS_SAVE_NO"/>
				<input class="nui-hidden" id="no_column_name"  name="map/NO_COLUMN_NAME"/>
				<input class="nui-hidden" id="BUSINESSLINE"  name="map/BUSINESS_LINE"/>
				<input class="nui-hidden" id="column_1"  name="map/COLUMN_1"/>
				<input class="nui-hidden" id="tbName"  name="map/TABLE_NAME"/>
				<!--档案种类，根据文件类别名称文本值去档案种类维护表中找档案种类对应值-->
				<input class="nui-hidden" id="fileType"  name="map/FILETYPE"/>
				<input id="filePath" class="nui-hidden">
				<input id="table_name" class="nui-hidden">
				<table border="0" cellpadding="1" cellspacing="2" style="width:100%;table-layout:fixed;">
					<tr>
						<th style="text-align:right;">业务条线：</th>
						<td>
								<input id="businessLine" class="nui-combobox" required="true" valueField="BUSINESS_LINE" textField="DICTNAME" style="width:100%;" dataField="resultList"
	          					url="com.gotop.xmzg.files.fileDestructionChecklist.getBusiessLine.biz.ext?type=1" name="map.BUSINESS_LINE" emptyText="请选择.." onValueChanged="businessLineChanged"/>
						</td>
						<td></td>
					</tr>
					<tr>
						<th style="text-align:right;">文件类别名称：</th>
						<td>
							<input class="nui-combobox" required="true" valueField="ID" textField="FILE_NAME" style="width:100%;"
	          					name="map/FILE_ID" emptyText="请选择.." id="fileName" dataField="resultList" onValueChanged="fileNameChanged"/>
						</td>
						<td></td>
					</tr>
					<tr>
						<th style="text-align:right;">文件选择：</th>
						<td>
							<input id="file" type="file" name="importFile" size="60" style="width:100%;" required="required"/>
						</td>
						<td></td>
					</tr>
					<tr>
						<td></td>
						<td style="text-align:center;">
							<a class="nui-button" iconCls="icon-ok" onClick="startUpload">上传</a>&nbsp
							<a id="save" class="nui-button" iconCls="icon-collapse" onclick="save">提交</a>&nbsp
							<a class="nui-button" iconCls="icon-download" onclick="downloadTemplate()">模版下载</a>&nbsp
							<a class="nui-button" iconCls="icon-reset" onclick="reset">重置</a>
						</td>
						<td></td>
					</tr>
				</table>
			</form>
			<div id="grid"></div>
		</fieldset>
	</div>


	<script type="text/javascript">
    	nui.parse();
    	
    	$("#save").hide();
    	
    	
    	var form = new nui.Form("import_form");
    	
    	<%
    		Object object = request.getAttribute("result");
    		HashMap resultMap = null;
    		HashMap paramMap = null;
    		String file_id = "";
    		String businessLine = "";
    		String result = "";
    		HashMap map = null;
    		String data = "";
    		String filePath = "";
    		String file_date = "";
    		String file_season = "";
    		String separator = "";
    		String message = "";
    		String operation_cycle = "";
    		String table_name = "";
    		String fileType = "";
    		if(object != null){
    			resultMap = (HashMap)object;
    			message = resultMap.get("message").toString();
    			result = resultMap.get("result").toString();
    			filePath = resultMap.get("filePath").toString();
    			map = (HashMap)request.getAttribute("map");
    			file_id = map.get("FILE_ID").toString();
    			businessLine = map.get("BUSINESS_LINE").toString();
    			table_name = map.get("TABLE_NAME").toString();
    			if(map.get("FILETYPE") != null){
    				fileType = map.get("FILETYPE").toString();
    			}
    			if(map.get("FILE_DATE") != null){
    				file_date = map.get("FILE_DATE").toString();
    			}
    			if(map.get("OPERATION_CYCLE") != null){
    				operation_cycle = map.get("OPERATION_CYCLE").toString();
    			}
    			if(map.get("FILE_SEASON") != null){
    				file_season = map.get("FILE_SEASON").toString();
    			}
    			paramMap = (HashMap)request.getAttribute("paramMap");
    			if(paramMap.get("separator") != null){
    				separator = paramMap.get("separator").toString();
    			}
    			data = JSON.toJSONString(resultMap.get("data"), SerializerFeature.DisableCircularReferenceDetect);
    		}
    	%>
    	
    	var result = "<%=result %>";
    	if(result != null && result != ""){
    		var message = "<%=message %>";
    		var data = '<%=data %>';
    		if(message != null && message != "ok"){
    			result = "fail";
    		}
    		if(result == "ok" && data != null && data != '' && data != "null"){
    			nui.alert("文件解析成功，请核对数据，数据无误后点击保存!","系统提示",function(action){
	    			if(action == "ok" || action == "close"){
	    				var file_id = "<%=file_id %>";
	    				var businessLine = "<%=businessLine %>";
	    				var filePath = "<%=filePath %>";
	    				var file_date = "<%=file_date %>";
	    				var file_season = "<%=file_season %>";
	    				var separator = "<%=separator %>";
	    				var operationCycle = "<%=operation_cycle %>";
	    				
	    				nui.get("filePath").setValue(filePath);
	    				nui.get("businessLine").setValue(businessLine);
	    				nui.get("businessLine").doValueChanged();
	    				nui.get("fileName").setValue(file_id);
	    				nui.get("fileName").doValueChanged();
	    				if(file_date.length == 4 || operationCycle == 2){
	    					file_date = file_date + "01";
	    				}
	    				if(data != null && data != '' && data != "null"){
	    					$("#grid").html("");
	    					showDataGrid(nui.decode(data));
	    				}
	    				/* $("#grid").html("");
	    				nui.get("filePath").setValue(text.result.filePath);
	    				if(text.result.data != null){
	    					showDataGrid(text.result.data);
	    				} */
	    			}
	    		});
    		}else if(result == "ok"){
    			nui.alert("上传成功!","系统提示",function(action){
	    			if(action == "ok" || action == "close"){
	    				var file_id = "<%=file_id %>";
	    				var businessLine = "<%=businessLine %>";
	    				var filePath = "<%=filePath %>";
	    				var file_date = "<%=file_date %>";
	    				var file_season = "<%=file_season %>";
	    				var separator = "<%=separator %>";
	    				var operationCycle = "<%=operation_cycle %>";
	    				nui.get("filePath").setValue(filePath);
	    				nui.get("businessLine").setValue(businessLine);
	    				nui.get("businessLine").doValueChanged();
	    				nui.get("fileName").setValue(file_id);
	    				nui.get("fileName").doValueChanged();
	    				if(file_date.length == 4 || operationCycle == 2){
	    					file_date = file_date + "01";
	    				}
	    				
	    			}
	    		});
    		}else if(result == "fail"){
    			nui.alert(message);
    		}else{
    			nui.alert("上传失败");
    		}
    	}
    	
    	function downloadTemplate(){
    		var file_name = nui.get("fileName").getText();
    		var file_path = nui.get("template_path").getValue();
    		file_name = file_name + file_path.substring(file_path.lastIndexOf("."));
    		window.location.href = "<%=request.getContextPath() %>/fileImportOrExport/fileExport/download.jsp?file_name=" + file_name + "&file_path=" + file_path;
    	}
    	
    	/*function downloadFile(){
    		var file_name = "IE无法上传时执行的注册表.reg";
    		var file_path = nui.getDictText("IMPORT_FILE_PATH","03");
    		window.location.href = "<%=request.getContextPath() %>/fileImportOrExport/fileExport/download.jsp?file_name=" + file_name + "&file_path=" + file_path;
    	}*/
    	
    	function save(){
    		nui.alert("确认数据是否无误!","系统提示",function(action){
	    			if(action == "ok"){
			    		var map = {};
			    		var filePath = nui.get("filePath").getValue();
			    		var fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
			    		var fileType = "<%=fileType %>";
			    		map.fileName = fileName;
			    		if(fileName.indexOf(".xls")!=-1 || fileName.indexOf(".xlsx")!=-1){
			    			map.isXls = true;
			    		}else map.isXls = false;
			    		map.filePath = filePath;
			    		map.table_name = nui.get("table_name").getValue();
			    		map.file_id = nui.get("fileName").getValue();
			    		map.fileType = fileType;
			    		map.data = nui.get("datagrid").getData();
			    		var formdata = form.getData(true,true);
			    		map.formdata = formdata;
			    		var json = nui.encode({map:map});
			    		var b= nui.loading("正在操作中,请稍等...","提示");
			    		$.ajax({
			    			url:"com.gotop.xmzg.files.fileDestructionChecklist.saveData.biz.ext",
			    			type:"post",
			    			data:json,
			    			contentType:"text/json",
			    			success:function(text){
			    				nui.hideMessageBox(b);
			    				if(text.result == 1){
			    					nui.alert("保存成功","",function(action){
			    						if("ok" == action || "close" == action){
			    							CloseWindow("saveSuccess");
			    						}
			    					});
			    				}else{
			    					nui.alert("保存失败");
			    				}
			    			}
			    		});
	    			}
	    		});
    	}
    	
    	function showDataGrid(data){
    		var cols = [];
    		var columns = getCols();
    		if(columns.length < 1){
    			nui.alert("该导入未配置模版！");
    			return;
    		}
    		if(data.length > 0){
    		
    			var i = 0;
    			for(var j=0;j<columns.length;j++){
    				for(var temp in data[0]){
    					if(temp == columns[j].COLUMN_NAME){
    						cols[i] = {field:temp,align:"center",headerAlign:"center",header:columns[j].COLUMN_COMMENT};
    						i++;
    					}
    				}
    			}
    		}
    		debugger;
    		
    		var grid = new nui.DataGrid();
    		grid.set({
    			style:"width:100%;height:400px;",
        		id:"datagrid",
        		columns: cols,
        		showPager:false
    		});
    		grid.render(document.getElementById("grid"));
    		grid.setData(data);
    		$("#save").show();
    	}
    	
    	function getCols(){
    		var file_id = nui.get("fileName").getValue();
    		var resultList = null;
    		$.ajax({
    			url:"com.gotop.xmzg.fileImportOrExport.fileImport.getCols.biz.ext",
    			type:"post",
    			data:"file_id=" + file_id,
    			async:false,
    			success:function(text){
    				resultList = text.resultList;
    			}
    		});
    		return resultList;
    	}
    	
    	function reset(){
    		form.reset();
    		var file = document.getElementById('file');
    		file.outerHTML = file.outerHTML;
    	}
    	
    	function startUpload(){
    		//$("#import_form").submit();
    		if(form.validate() != true){
    			return;
    		}
    		var fileName = $("#file").val();
    		var simpleFileName = fileName.substring(fileName.lastIndexOf("\\")+1);
    		nui.get("file_name").setValue(fileName);
    		nui.get("fileName1").setValue(simpleFileName);
    		var sufFileName = fileName.substring(fileName.lastIndexOf("."));
    		var file_type = nui.get("file_type").getValue();
    		var import_type = nui.get("import_type").getValue();
    		if(import_type == "1"){
    				nui.alert("请指定文件上传处理方式为导入数据库");
    				return;
    			}
    		if(file_type != "1"){
    			
				nui.alert("请指定文件类型为EXCEL！");
				return;
    		}
    		if(file_type == "1"){
    			if(sufFileName != ".xls"){
    				nui.alert("请选择.xls文件");
    				return;
    			}
    		}
    		var b= nui.loading("正在操作中,请稍等...","提示");
    		$("#import_form").submit();
    	}
    	
    	function fileNameChanged(e){
    		var fileNameId = e.value;
    		var data = e.sender.data;
    		var operation_cycle = null;
    		for(var i=0;i<data.length;i++){
    			if(data[i].ID == fileNameId){
    				operation_cycle = data[i].OPERATION_CYCLE;
    				nui.get("import_type").setValue(data[i].IMPORT_TYPE);
    				nui.get("import_file_path").setValue(data[i].IMPORT_FILE_PATH);
    				nui.get("operation_cycle").setValue(data[i].OPERATION_CYCLE);
    				nui.get("temp_file_path").setValue(data[i].TEMP_FILE_PATH);
    				nui.get("template_path").setValue(data[i].TEMPLATE_PATH);
    				nui.get("is_import_date").setValue(data[i].IS_IMPORT_DATE);
    				nui.get("date_column_name").setValue(data[i].DATE_COLUMN_NAME);
    				nui.get("is_save_no").setValue(data[i].IS_SAVE_NO);
    				nui.get("no_column_name").setValue(data[i].NO_COLUMN_NAME);
    				nui.get("table_name").setValue(data[i].TABLE_NAME);
    				nui.get("tbName").setValue(data[i].TABLE_NAME);
    				nui.get("column_1").setValue(data[i].COLUMN_1);
    				var fileType = data[i].FILE_TYPE;
    				debugger;
    				if(data[i].IMPORT_TYPE == 1){
    					if(data[i].IMPORT_FILE_PATH == null || data[i].IMPORT_FILE_PATH == ""){
    						nui.alert("文件上传路径为空，请检查！");
    						return;
    					}
    				}else if(data[i].IMPORT_TYPE == 2){
    					if(data[i].TEMP_FILE_PATH == null || data[i].IMPORT_FILE_PATH == ""){
    						nui.alert("文件导入临时路径为空，请检查！");
    						return;
    					}
    				}else if(data[i].IMPORT_TYPE == 3){
    					if(data[i].IMPORT_FILE_PATH == null || data[i].IMPORT_FILE_PATH == ""){
    						nui.alert("文件上传路径为空，请检查！");
    						return;
    					}
    				}
    				nui.get("file_type").setValue(fileType);
    				if(fileType == 1){
    					nui.get("impType").setValue("excel");
    				}else if(fileType == 2){
    					nui.get("impType").setValue("txt");
    				}else if(fileType == 3){
    					nui.get("impType").setValue("other");
    				}
    			}
    		}
    	}
    	
    	function businessLineChanged(e){
    		var business_line = String(e.value);
    		var url = "com.gotop.xmzg.fileImportOrExport.fileImport.getFileNames.biz.ext?business_line=" + business_line;
    		nui.get("fileName").load(url);
    		nui.get("BUSINESSLINE").setValue(business_line);
    	}
    	
    	
    	//获取所有档案种类
    	function getFileType(){
    		var fileType = nui.get("fileName").getText();
    		var resultList = null;
    		$.ajax({
    			url:"com.gotop.xmzg.files.archiveList.getFileType.biz.ext",
    			type:"post",
    			data:"fileType=" + fileType,
    			async:false,
    			success:function(text){
    				resultList = text.resultList;
    			}
    		});
    		return resultList;
    	}
    	
    	//关闭添加窗口
	 	function CloseWindow(action){
	  		if(window.CloseOwnerWindow) 
	    		return window.CloseOwnerWindow(action);
	  		else
	    		return window.close();
	    }
    </script>
</body>
</html>
﻿nui.ux.Portal = function () {
    this.columns = [];
    this.panels = [];
    nui.ux.Portal.superclass.constructor.call(this);
}
nui.extend(nui.ux.Portal, nui.Control, {
    columns: [],
    panels: [],

    allowDrag: true,

    width: 500,
    height: 300,
    uiCls: "nui-portal",
    _initEvents: function () {
        nui.on(this.el, "mousedown", this.__OnMouseDown, this);
    },
    destroy: function (removeEl) {

        if (this.panels) {
            var cs = this.panels.clone();
            for (var i = 0, l = cs.length; i < l; i++) {
                var p = cs[i];
                p.destroy(removeEl);
            }
            this.panels.length = 0;
            this.panels = null;
            delete this.panels;
        }
        nui.ux.Portal.superclass.destroy.call(this, removeEl);
    },
    doLayout: function () {
        if (!this.canLayout()) return;

        nui.layout(this.el.firstChild);
    },

    setColumns: function (columns) {
        //[100, "50%", 200, "100%"]
        if (!nui.isArray(columns)) columns = [];

        this.columns = columns;

        var sb = '<table class="nui-portal-table"><tr>';
        for (var i = 0, l = columns.length; i < l; i++) {
            var c = columns[i];
            if (nui.isNumber(c)) c += "px";
            sb += '<td id="' + i + '" class="nui-portal-column" style="width:' + c + '"></td>';
        }
        sb += '</tr></table>';

        this.el.innerHTML = sb;
    },
    getColumnEl: function (index) {
        return this.el.firstChild.rows[0].cells[index];
    },
    getColumnsBox: function () {
        var columns = [];
        for (var i = 0, l = this.columns.length; i < l; i++) {
            var el = this.getColumnEl(i);
            var box = nui.getBox(el);
            columns.push(box);

            box.height = 3000;
            box.bottom = box.top + box.height;
        }
        return columns;
    },

    /*
    column, id
    title, iconCls, 
    showCloseButton, showMaxButton, showMinButton
    width, height
    allowDrag
    */
    createDefaultPanel: function () {
        return {
            column: 0,
            type: "panel",
            allowDrag: true,
            showCloseButton: true,
            showCollapseButton: true,
            width: "100%",
            height: "100px"
        };
    },
    getPanelBodyEl: function (panel) {
        panel = this.getPanel(panel);
        if (!panel) return;
        return panel.getBodyEl();
    },
    getPanels: function () {
        return this.panels;
    },
    getPanel: function (id) {
        return typeof id == "string" ? nui.get(id) : id;
    },
    setPanels: function (panels) {
        for (var i = 0, l = panels.length; i < l; i++) {
            this.addPanel(panels[i]);
        }
    },
    removePanel: function (panel) {
        panel = this.getPanel(panel);
        if (!panel) return;
        this.panels.remove(panel);

        var el = panel.el;
        el.parentNode.removeChild(el);
    },
    addPanel: function (panel) {
        if (!panel) return;
        if (nui.isNumber(panel.column) == false) panel.column = 0;

        if (nui.isControl(panel) == false) {
            panel = nui.copyTo(this.createDefaultPanel(), panel);
        }

        panel = nui.getAndCreate(panel);

        panel.setWidth("100%");
        panel.addCls("nui-portal-panel");

        var column = this.getColumnEl(panel.column);
        panel.render(column);

        this.panels.push(panel);

        this.doLayout();
    },
    ///////////////////////////////////////////
    getColumnIndexByXY: function (x, y) {
        var elbox = this.getBox();
        elbox.height = 3000;
        elbox.bottom = elbox.top + elbox.height;
        var columnsBox = this.getColumnsBox();
        var index = -1;
        for (var i = 0, l = columnsBox.length; i < l; i++) {
            var box = columnsBox[i];
            if (elbox.x <= x && x <= elbox.right
                && elbox.y <= y && y <= elbox.bottom
                ) {
                if (box.x <= x && x <= box.right) {
                    return i;
                }
            }
        }
        return index;
    },
    _getPanelByY: function (y, column, noPanel) {
        for (var i = 0, l = this.panels.length; i < l; i++) {
            var panel = this.panels[i];
            if (panel.column != column || panel == noPanel) continue;
            var box = panel.getBox();
            box.height += 10;
            box.bottom += 10;

            if (box.y <= y && y <= box.bottom) {
                panel.__moveAction = "after";
                if (y < box.y + box.height / 2) panel.__moveAction = "before";
                return panel;
            }
        }
        return null;
    },
    __OnMouseDown: function (e) {

        var t = nui.findParent(e.target, 'nui-portal-panel');
        if (t) {


            var panel = nui.get(t.id);
            var sf = this;

            if (this.allowDrag && panel.allowDrag && nui.isAncestor(panel.getHeaderEl(), e.target) && !nui.findParent(e.target, "nui-tools")) {
                var box = panel.getBox();
                var drag = new nui.Drag({
                    capture: false,
                    onStart: function () {
                        nui.setOpacity(panel.el, .7);

                        panel.setWidth(box.width);
                        panel.el.style.position = "absolute";

                        jQuery(panel.el).before('<div class="nui-portal-proxy"></div>')[0];
                        sf._dragProxy = panel.el.previousSibling;
                        //nui.setHeight(sf._dragProxy, box.height);
                        sf._dragProxy.style.height = box.height + "px";

                        panel.el.style.zIndex = nui.getMaxZIndex();


                    },
                    onMove: function (drag) {
                        //document.title = "move" + new Date().getTime();
                        var x = drag.now[0] - drag.init[0], y = drag.now[1] - drag.init[1];

                        x = box.x + x;
                        y = box.y + y;

                        nui.setXY(panel.el, x, y);



                        sf._targetColumn = sf._targetPanel = null;

                        var dragBox = nui.getBox(sf._dragProxy);
                        dragBox.height += 10;
                        dragBox.bottom += 10;
                        if (dragBox.x <= x && x <= dragBox.right
                        && dragBox.y <= y && y <= dragBox.bottom
                        ) {
                            return;
                        }

                        //column
                        var column = sf.getColumnIndexByXY(x, y);
                        if (column != -1) {
                            //var y2 = y + box.height / 2;
                            var tp = sf._getPanelByY(y, column, panel);
                            //                            if (tp) document.title = tp.title + ":" + tp.__moveAction + ":" + column + ":" + new Date().getTime();
                            //                            else {
                            //                                tp = null;
                            //                            }
                            sf._targetColumn = column;
                            sf._targetPanel = tp;
                        }
                        //document.title = column;


                        if (nui.isNumber(sf._targetColumn)) {
                            if (sf._targetPanel) {
                                var el = sf._targetPanel.el;
                                if (sf._targetPanel.__moveAction == "before") {
                                    jQuery(el).before(sf._dragProxy);
                                } else {
                                    jQuery(el).after(sf._dragProxy);
                                }
                            } else {
                                var el = sf.getColumnEl(sf._targetColumn);
                                nui.append(el, sf._dragProxy);
                            }
                        }
                    },
                    onStop: function () {
                        //从_dragProxy，找column和index
                        var td = sf._dragProxy.parentNode;
                        var column = parseInt(td.id);

                        jQuery(sf._dragProxy).before(panel.el);

                        sf.panels.remove(panel);

                        var next = sf._dragProxy.nextSibling;
                        if (!next) {
                            sf.panels.push(panel);
                        } else {
                            var targetPanel = nui.get(next);

                            var index = sf.panels.indexOf(targetPanel);
                            sf.panels.insert(index, panel);
                        }

                        jQuery(sf._dragProxy).remove();
                        sf._maskProxy = null;

                        panel.el.style.position = "static";
                        panel.setWidth("100%");
                        nui.setOpacity(panel.el, 1);


                        sf._targetColumn = sf._targetPanel = null;
                    }
                });
                drag.start(e);
            }
        }
    }


});
nui.regClass(nui.ux.Portal, "portal");
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): wsd
  - Date: 2022-02-27 08:34:30
  - Description:
-->
<head>
<title>业绩账户动态比例分润</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
    <%@include file="/coframe/tools/skins/common.jsp" %>
</head>
<body>
<div id="form1" style="padding-top:5px;">
	<input id="empTree" class="nui-treeselect" style="display: none;"
	     showTreeIcon="true" dataField="resultList" textField="ORGNAME" valueField="ORGID" parentField="PARENTORGID" />
    <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
    <input id=tac_emp name = "tac_emp" class="nui-hidden"/>
    <input id=tp_auth name = "tp_auth" class="nui-hidden"/>
    <input id=tp_pro name = "tp_pro" class="nui-hidden"/>
    <input id=tab_busi_org name = "tab_busi_org" class="nui-hidden"/>
    <input id=tab_begin name = "tab_begin" class="nui-hidden"/>
    <input id=tab_end name = "tab_end" class="nui-hidden"/>
    <input id=TP_CYCLE name = "TP_CYCLE" class="nui-hidden"/>
    <input id=TP_DATE name = "TP_DATE" class="nui-hidden"/>
    <input id="tab_id" name = "tab_id" class="nui-hidden"/>
    <input id="khlx" name = "khlx" class="nui-hidden"/>
    <input id="taa_status_pro" name = "taa_status_pro" class="nui-hidden"/>
    
     <tr>
        <th class="nui-form-label"><label>产品编号：</label></th>
        <td  >  
         	<input id=tac_busi_no name = "tac_busi_no" class="nui-textbox" readonly="readonly" style="width:150px" />  
        </td>
         <th class="nui-form-label"><label>产品名称：</label></th>
        <td  >  
         	<input id="tac_busi_name" name = "tac_busi_name" class="nui-textbox asLabel" readonly="readonly" style="width:150px" />  
        </td>
      </tr>
      <tr>
        <th class="nui-form-label"><label>认领类型：</label></th>
        <td  >  
         	<input id="tac_rl_type" name = "tac_rl_type" class="nui-dictcombobox asLabel" readonly="readonly" disabled="true" style="width:150px;" valueField="dictID" textField="dictName" dictTypeId="JF_FPLX"/>  
        </td>
         <th class="nui-form-label"><label>指标或指标细项：</label></th>
        <td  >  
         	<input id="tac_zb_code" name = "tac_zb_code" class="nui-textbox asLabel" readonly="readonly" style="width:150px" />  
        </td> 
      </tr>
      <tr>
      	<th class="nui-form-label"><label>客户类型：</label></th>
        <td  >  
         	<input id="dd_cust_type" name = "dd_cust_type" class="nui-dictcombobox"  style="width:150px;" valueField="dictID" 
	        textField="dictName" dictTypeId="JF_GRCKKHLX2" required="true" onvaluechanged="typeChange"/>  
        </td> 
        <th class="nui-form-label"><label>授信额度：</label></th>
        <td  >  
         	<input id="CUST_AMT" name = "CUST_AMT" class="nui-textbox asLabel" readonly="readonly" style="width:150px" />   
        </td>
      </tr>
      <tr>
        <th class="nui-form-label"><label>归属客户经理：</label></th>
        <td  >  
         	<input id="tac_emp_name" name = "tac_emp_name" class="nui-textbox asLabel" readonly="readonly" style="width:150px" />  
        	</br><font color="red">&nbsp;</font> 
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label>可分配比例：</label></th>
        <td  >  
         	<input id="is_cross_rate" name = "is_cross_rate" class="nui-textbox asLabel" readonly="readonly" style="width:150px" />  
        </td> 
        <th class="nui-form-label"><label>未分配比例：</label></th>
        <td  >  
         	<input id="not_cross_rate" name = "not_cross_rate" class="nui-textbox asLabel" readonly="readonly" style="width:150px" /> 
        </td> 
      </tr>
    </table>
    
     <div class="nui-panel" title="当前分润信息"  style="width:100%;height:220px;" 
    	showToolbar="true" showCloseButton="false" showFooter="true"
	>
     <div class="nui-toolbar" style="border-bottom:0;padding:0px;height:14%;" id="btn">
		<a id="zb_btn1" class="nui-button" iconCls="icon-add" onclick="addAccProfits()" plain="true">增加分润</a>
		<a id="zb_btn2" class="nui-button" iconCls="icon-remove" onclick="delAccProfits()" plain="true">删除分润</a>
		<a class="nui-button" iconCls="icon-save" onclick="saveAccProfits()" plain="true">提交分润</a>
	</div>
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:85%;" idField="id"
	  url="com.gotop.xmzg.achieve.accNew.queryAccCrossListGd.biz.ext" showPager="false" allowCellEdit="true" allowCellSelect="true" multiSelect="true" 
        editNextOnEnterKey="true"  editNextRowCell="true" oncellbeginedit="OnCellBeginEdit" oncellvalidation="onCellValidation" onupdate="changeRate" oncellendedit="changeRate" >
	  
	  <div name="accProfitsDiv" class="nui-hidden" ></div>
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      <div field="tac_cr_emp" name="tac_cr_emp" vtype="required" headerAlign="center" align="center" renderer="dictEmp">分润客户经理
	       		<input property="editor" class="nui-treeselect"  
				 showTreeIcon="true" dataField="resultList" textField="ORGNAME" valueField="ORGID" parentField="PARENTORGID"  required="true"
	       		 onbeforenodeselect="beforenodeselect"  expandOnLoad="false" onclick="setTree"/>
	      </div>
	      <div field="tac_rate" vtype="int;range:1,100;" headerAlign="center" align="center">分润比例（单位:%）
	      <input property="editor" class="nui-textbox" style="width:100%;" minWidth="150" required="true" value="0"/>
	      </div>
	      <div field="tac_begin" align="center" vtype="required" headerAlign="center" renderer="dateStr">开始时间 
            <input property="editor" name="new_tac_begin"  class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd" onclick="selDate"/>
        </div>
        <div field="tac_end" align="center" vtype="required" headerAlign="center" renderer="dateStr">结束时间
            <input property="editor" class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd" />
        </div>
	    </div>
	 </div>
  </div>
    
    <div class="nui-panel" title="历史分润信息"  style="width:100%;height:200px;" 
    	showToolbar="true" showCloseButton="false" showFooter="true"
	>
	 <div id="datagrid2" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.achieve.accNew.queryAccCrossListLs.biz.ext" showPager="false" allowCellSelect="true" multiSelect="true" 
       >
	  
	  <div name="accProfitsDiv" class="nui-hidden" ></div>
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      <div field="tac_cr_empname" headerAlign="center" align="center" >分润客户经理
	       		<input property="editor" class="nui-textbox" style="width:100%;" minWidth="150"  />
	      </div>
	      <div field="tac_rate" vtype="int;range:1,100;" headerAlign="center" align="center">分润比例（单位:%）
	      <input property="editor" class="nui-textbox" style="width:100%;" minWidth="150" required="true" value="0"/>
	      </div>
	      <div field="tac_begin" align="center" vtype="required" headerAlign="center" renderer="dateStr">开始时间 
            <input property="editor" name="new_tac_begin"  class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd" onclick="selDate"/>
        </div>
        <div field="tac_end" align="center" vtype="required" headerAlign="center" renderer="dateStr">结束时间
            <input property="editor" class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd" />
        </div>
	    </div>
	 </div>
  </div>
  
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel()">关闭</a>
	        </td>
	      </tr>
	    </table>
	 </div>


	<script type="text/javascript">
    	nui.parse();
    	var form = new nui.Form("#form1");
    	
    	var grid = nui.get("datagrid1");
    	var grid2 = nui.get("datagrid2");
    	var tp_pro_sx;
		//查询
    	function search(){
       		//获取form中的表单数据
    		var formData = form.getData(true,true);
       		grid.load({"queryData":formData});
       		grid2.load({"queryData":formData});
    	}
    	
    	function CloseWindow(action){
      		if(window.CloseOwnerWindow) 
        		return window.CloseOwnerWindow(action);
      		else
        		return window.close();
    	}
    	
    	function onCancel(){
    		CloseWindow("cancel");
    	}
    	
    	//增加分润
    	function addAccProfits() {
    		var cust_type = nui.get("dd_cust_type").getValue();
			if(cust_type==null||cust_type==""){
			    nui.alert("请先选择客户类型！");
				return;
			}else if(cust_type=="10"){//自行营销客户
			var newRow = { name: "newTac" ,tac_cr_emp: nui.get("tac_emp").getValue(),tac_begin: nui.get("tab_begin").getValue(),tac_end: nui.get("tab_end").getValue()};
	            grid.addRow(newRow, 0);
	            grid.beginEditCell(newRow, "tac_cr_emp");
			}else{
				var newRow = { name: "newTac" ,tac_begin: nui.get("tab_begin").getValue(),tac_end: nui.get("tab_end").getValue()};
	            grid.addRow(newRow, 0);
	            grid.beginEditCell(newRow, "tac_cr_emp");
			}
            
        }
        
        //删除分润
        function delAccProfits() {
        	var rows = grid.getSelecteds();
        	if (rows.length > 0) {
            	grid.removeRows(rows, true);
            	changeRate();
        	}
    	}
    	
    	//提交分润结果
    	function saveAccProfits(){
    		var cust_type = nui.get("dd_cust_type").getValue();
			if(cust_type==null||cust_type==""){
			    nui.alert("请先选择客户类型！");
				return;
			}
    		var gridData = grid.getData();
			var row;
			var sum = 0;
			var empcodeArray = [];
			
			  grid.validate();
		      if (grid.isValid() == false) {   
		        var error = grid.getCellErrors()[0];
		        grid.beginEditCell(error.record, error.column);
		        return;
		      }
				      
			/* if(gridData.length <1){
				//表格没有分润数据
				nui.alert("没有分润数据，不能提交！");			
			}else{ */
				//表格已有分润数据
				//循环校验数据
				for(var i=0;i<gridData.length;i++){
					row = grid.getRow(i);
					var tac_cr_emp = row.tac_cr_emp;
					var tac_rate = row.tac_rate;
					if(tac_cr_emp == "" || tac_rate == "" || tac_cr_emp == null || tac_rate == null){
						nui.alert("存在分润客户经理或者分润比例为空的分润数据，不能提交！");
						return;
					}
					empcodeArray.push(tac_cr_emp);
					sum=parseFloat(sum)+parseFloat(tac_rate);
				}
				
				//判断是否存在客户经理重复的情况
				/* 	var nary = empcodeArray.sort();
					for(var i = 0; i < nary.length - 1; i++) {
					  if(nary[i] == nary[i + 1]) {
					   nui.alert("存在分润客户经理重复的分润数据，不能提交！");
					    return;
					  }
					}
					 */
				if(sum>tp_pro_sx){
					nui.alert("当前分润比例总和大于"+tp_pro_sx+"，不能提交！");
				}else{
					//获取grid中的所有变化的行数据
		    		var handleData = grid.getChanges(null,false);
		    		var oldCustType = nui.get("khlx").getValue();
		    		var newCustType = nui.get("dd_cust_type").getValue();
		    		if(oldCustType != newCustType){
		    			//alert("旧的客户类型"+oldCustType+";新的客户类型"+newCustType);
		    			//获取grid中的所有行数据
		    			handleData = grid.getData();
		    		}
		    		
		    		//获取form中的表单数据
		    		var formData = form.getData(true,true);
//		    		if(handleData.length ==0){
//		    			nui.alert("当前分润数据没有发生变化，不需要提交！");
//		    		}else{
		    		$.ajax({
		                url: "com.gotop.xmzg.achieve.accNew.handleAccCross_sort_accSortDt.biz.ext",
		                type: 'POST',
		                data:nui.encode({"handleData":handleData,"formData":formData}),
		                cache: false,
		                contentType:'text/json',
		                success: function (json) {
			                var returnJson = nui.decode(json);
							if(returnJson.exception == null && returnJson.iRtn == 0){
								nui.alert("提交分润成功", "系统提示", function(action){
									if(action == "ok" || action == "close"){
										//search();
										onCancel();
									}
								});
							}else{
								if(returnJson.msg == null || returnJson.msg=="")
									returnJson.msg = "提交分润失败";
								nui.alert(returnJson.msg, "系统提示", function(action){
									if(action == "ok" || action == "close"){
										
									}
								});
							}
						}
					});
//		    		}
		    		
				}
			//}
    	}
    	
    	var res = {};
    	function selDate(e){
    		nui.get(this).setMinDate(res.min);
    		nui.get(this).setMaxDate(res.max);
    	}
    	
    	var is_free = false;
    	//初始化表单数据
    	function setFormData(data) {
			var info = nui.clone(data);
			if(info.type == "detail"){
				nui.get("btn").hide();
			}else{
				nui.ajax({
		            url: "com.gotop.xmzg.achieve.accNew.handleAccCross_getDate.biz.ext",
		            type: 'POST',
		            data:nui.encode({"info":info}),
		            cache: false,
		            contentType:'text/json',
		            success: function (text) {
		            	console.log(text.res);
		            	res = text.res;
		            	nui.get("tab_begin").setValue(res.min);
		            	if(res.min == null || res.min == "")
		            		nui.get("tab_begin").setValue(res.max);
		            	console.log(info);
						nui.get("tac_busi_no").setValue(info.tac_busi_no);
						nui.get("tac_busi_name").setValue(info.tac_busi_name);
						nui.get("tac_rl_type").setValue(info.tac_rl_type);
						nui.get("tac_zb_code").setValue(info.tac_zb_code);
						nui.get("tac_emp").setValue(info.tac_emp);
						nui.get("tac_emp_name").setValue(info.tac_emp_name);
						nui.get("tp_auth").setValue(info.tp_auth);
						nui.get("tab_busi_org").setValue(info.tab_busi_org);
						nui.get("tp_pro").setValue(info.tp_pro);
						//nui.get("tab_begin").setValue(info.tab_begin);
						nui.get("tab_end").setValue(info.tab_end);
						nui.get("TP_CYCLE").setValue(info.TP_CYCLE);
						nui.get("TP_DATE").setValue(info.TP_DATE);
						var dd_cust_type = info.DD_CUST_TYPE;
						
						
						nui.get("CUST_AMT").setValue(info.CUST_AMT);//授信额度
						nui.get("tab_id").setValue(info.tab_id);
						
						nui.get("taa_status_pro").setValue(info.taa_status_pro);
						if(info.taa_status_pro != '1'){//除了已审核，其他情况，客户类型置空
							nui.get("dd_cust_type").setValue(null);
							nui.get("khlx").setValue(null);
						}else{
							nui.get("dd_cust_type").setValue(dd_cust_type);
							nui.get("khlx").setValue(dd_cust_type);
						}
						
						//alert(info.taa_status_pro);
						var type = info.tp_acrossorg;
						nui.get("empTree").load("com.gotop.xmzg.achieve.acc.getAccEmpZtree.biz.ext?type="+type+"&isrole=1&orgcode="+info.tab_busi_org);
						if(dd_cust_type==null||dd_cust_type==""){
							var formData = form.getData(true,true);
							grid2.load({"queryData":formData});
							if(info.tp_pro_sx != null && info.tp_pro_sx != ""){
								tp_pro_sx = parseInt(info.tp_pro_sx);
							}else{
							    tp_pro_sx = 100;
							}
						}else{
							typeChange();
						}
				    }
		       });
			}

		
		}
		function typeChange(){
			var cust_type = nui.get("dd_cust_type").getValue();
			if(cust_type==null||cust_type==""){
				return;
			}
			debugger;
			//对经办客户经理的分配比例按照授信额度进行限制
			if(cust_type == '11'){	
				var gridData = grid.getData();
				grid.removeRows(gridData, true);	
				var zbCode = nui.get("tac_zb_code").getValue();
				var str = nui.get("CUST_AMT").getValue();
				var sxLimit = parseInt(nui.get("CUST_AMT").getValue()); //授信额度
				if(str == ""||str == null){
					sxLimit = 0;
				}
				
		   		/* 
		   		//公司贷款利差收入 11003002,1.客户授信额度（取值一般风险额度）≤1亿元，主辅调客户经理分配比例50%；
				//2. 1亿元＜客户授信额度（取值一般风险额度）≤5亿元，主辅调客户经理分配比例40%；
				//3. 客户授信额度（取值一般风险额度）＞5亿元，主辅调客户经理分配比例30%。  
			    if(zbCode == '11003002'){
			    	if(sxLimit<=100000000){
			    		tp_pro_sx=50;
			    	}else if(sxLimit>100000000&&sxLimit<=500000000){
			    	    tp_pro_sx=40;
			    	}else if(sxLimit>500000000){
			    	    tp_pro_sx=30;
			    	}
	       	   	}
	       	   	//贸易融资表外12006001,票据承兑12007001,1.客户授信额度（取值一般风险额度）≤1亿元，主辅调客户经理分配不做限制；                                                                                             
	       	   	//2.1亿元＜客户授信额度（取值一般风险额度）≤3亿元，主辅调客户经理分配比例70%；                                                                                          
	       	   	//3.3亿元＜客户授信额度（取值一般风险额度）≤10亿元，主辅调客户经理分配比例60%；
				//4.客户授信额度（取值一般风险额度）＞10亿，主辅调客户经理分配比例50%。 
	       	   	if(zbCode == '12006001'||zbCode == '12007001'){
			    	if(sxLimit<=100000000){
			    		tp_pro_sx=100;
			    		is_free = true;
			    		//nui.get("not_cross_rate").setRequired(false);
			    	}else if(sxLimit>100000000&&sxLimit<=300000000){
			    	    tp_pro_sx=70;
			    	}else if(sxLimit>300000000&&sxLimit<=1000000000){
			    	    tp_pro_sx=60;
			    	}else if(sxLimit>1000000000){
			    	    tp_pro_sx=50;
			    	}
	       	   	} 
	       	   	*/
	       	   	
	       	   	
	       	   	//20230112更新规则：
	       	   	//公司贷款利差收入 11003002,
				//1.客户授信额度（取值一般风险额度）≤1亿元，主辅调客户经理分配不做限制；                                                                                            
	       	   	//2.1亿元＜客户授信额度（取值一般风险额度）≤3亿元，主辅调客户经理分配比例70%；                                                                                         
	       	   	//3.3亿元＜客户授信额度（取值一般风险额度）≤10亿元，主辅调客户经理分配比例60%；
				//4.客户授信额度（取值一般风险额度）＞10亿，主辅调客户经理分配比例50%。其余部分作为机构公共业绩进行分配，该部分公共业绩池仅可用于公司业务相关分配
				//5.贷款利差为负或小于13bp/年，按照13bp/年计算。
			    if(zbCode == '11003002'){
			    	if(sxLimit<=100000000){
			    		tp_pro_sx=100;
			    		is_free = true;
			    		//nui.get("not_cross_rate").setRequired(false);
			    	}else if(sxLimit>100000000&&sxLimit<=300000000){
			    	    tp_pro_sx=70;
			    	}else if(sxLimit>300000000&&sxLimit<=1000000000){
			    	    tp_pro_sx=60;
			    	}else if(sxLimit>1000000000){
			    	    tp_pro_sx=50;
			    	}
	       	   	}
	       	   	//贸易融资表外12006001,票据承兑12007001,供应链12002
	       	   	//二级福费廷（12001），贸易融资（表内）12005
	       	   	//二级福费廷放款额（存续期限≤3M）12001001，二级福费廷放款额（3M＜存续期限≤6M）12001002，二级福费廷放款额（6M＜存续期限≤1年）12001003，贸易融资（表内）12005001
	       	   	//1.客户授信额度（取值一般风险额度）≤1亿元，主辅调客户经理分配不做限制；                                                                                            
	       	   	//2.1亿元＜客户授信额度（取值一般风险额度）≤3亿元，主辅调客户经理分配比例70%；                                                                                         
	       	   	//3.3亿元＜客户授信额度（取值一般风险额度）≤10亿元，主辅调客户经理分配比例60%；
				//4.客户授信额度（取值一般风险额度）＞10亿，主辅调客户经理分配比例50%。
				//5.其余部分可分配给支撑本笔业务、营销维护本客户的相关人员。经办客户经理不参与该部分奖励的分配，原则上其他人员单人分配比例不得高于经办客户经理。
	       	   	if(zbCode == '12006001'||zbCode == '12007001'||zbCode.substring(0,5) == '12002'||zbCode.substring(0,5) == '12005'||zbCode.substring(0,5) == '12001'){
			    	if(sxLimit<=100000000){
			    		tp_pro_sx=100;
			    		is_free = true;
			    		//nui.get("not_cross_rate").setRequired(false);
			    	}else if(sxLimit>100000000&&sxLimit<=300000000){
			    	    tp_pro_sx=70;
			    	}else if(sxLimit>300000000&&sxLimit<=1000000000){
			    	    tp_pro_sx=60;
			    	}else if(sxLimit>1000000000){
			    	    tp_pro_sx=50;
			    	}
	       	   	}
	       	   	nui.get("not_cross_rate").setValue(tp_pro_sx);
		    }else{
		    	tp_pro_sx=100;
		    }
		    nui.get("is_cross_rate").setValue(tp_pro_sx);
		    search();
		    
			
		}
		/* function typeChange(){
			var cust_type = nui.get("dd_cust_type").getValue();
			if(cust_type==null||cust_type==""){
				return;
			}
			//对经办客户经理的分配比例按照授信额度进行限制
			if(cust_type == '11'){	
				var gridData = grid.getData();
				grid.removeRows(gridData, true);	
				var zbCode = nui.get("tac_zb_code").getValue();
				var sxLimit = parseInt(nui.get("CUST_AMT").getValue()); //授信额度
		   		//公司贷款利差收入 11003002,1.客户授信额度（取值一般风险额度）≤1亿元，主辅调客户经理分配比例50%；
				//2. 1亿元＜客户授信额度（取值一般风险额度）≤5亿元，主辅调客户经理分配比例40%；
				//3. 客户授信额度（取值一般风险额度）＞5亿元，主辅调客户经理分配比例30%。  
			    if(zbCode == '11003002'){
			    	if(sxLimit<=100000000){
			    		tp_pro_sx=50;
			    	}else if(100000000<sxLimit<=500000000){
			    	    tp_pro_sx=40;
			    	}else if(sxLimit>500000000){
			    	    tp_pro_sx=30;
			    	}
	       	   	}
	       	   	//贸易融资表外12006001,票据承兑12007001,1.客户授信额度（取值一般风险额度）≤1亿元，主辅调客户经理分配不做限制；                                                                                             
	       	   	//2.1亿元＜客户授信额度（取值一般风险额度）≤3亿元，主辅调客户经理分配比例70%；                                                                                          
	       	   	//3.3亿元＜客户授信额度（取值一般风险额度）≤10亿元，主辅调客户经理分配比例60%；
				//4.客户授信额度（取值一般风险额度）＞10亿，主辅调客户经理分配比例50%。 
	       	   	if(zbCode == '12006001'||zbCode == '12007001'){
			    	if(sxLimit<=100000000){
			    		tp_pro_sx=100;
			    		is_free = true;
			    		nui.get("not_cross_rate").setRequired(false);
			    	}else if(100000000<sxLimit<=300000000){
			    	    tp_pro_sx=70;
			    	}else if(300000000<sxLimit<=1000000000){
			    	    tp_pro_sx=60;
			    	}else if(sxLimit>1000000000){
			    	    tp_pro_sx=50;
			    	}
	       	   	}
	       	   	nui.get("not_cross_rate").setValue(tp_pro_sx);
		    }
		    search();
		    
			
		} */
		
		//单元格编辑前，如果是比例，则不允许修改
    	function OnCellBeginEdit(e){
    		var field = e.field;
    		var record = e.record;
    		if(record.tac_id != null && field == "tac_cr_emp"){
    		 e.cancel = true;
    		}
    		
    		
    	}
    	
    	function onCellValidation(e){
    		/* if(e.field == "tac_begin"){
    			comparedateBegin(e);
    		}
    		
    		if(e.field == "tac_end"){
    			comparedateEnd(e);
    		} */
    	
    		if (e.field == "tac_begin" || e.field == "tac_end") {
        		comparedate(e,e.row.tac_begin,e.row.tac_end);
        	}
        
    		if(e.field == "tac_rate"){
    		 	if(e.value <= 0){
	        		e.errorText="不能输入负数和0";
	            	e.isValid=false;
        		}
    		}
    	}
    	
   function comparedate(e,startDate,endDate){
    	if(startDate instanceof Date){
    		startDate = nui.formatDate ( startDate, 'yyyyMMdd' );
    	}
    	if(endDate instanceof Date){
    		endDate = nui.formatDate ( endDate, 'yyyyMMdd' );
    	}
      if(startDate!="" && startDate!=null && startDate.length > 10)
	    startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
	  if(endDate!="" && endDate!=null && endDate.length > 10)
		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
         if(e.isValid){
              if(startDate > endDate){
	            e.errorText="结束日期必须大于开始日期";
	            e.isValid=false;
             }else{
		          e.errorText="";
		          e.isValid=true;
		          if(e.row.tac_begin_old == startDate 
		          		&& e.row.tac_end_old == endDate
		          		&& e.row.tac_cr_emp_old == e.row.tac_cr_emp
		          		&& e.row.tac_rate_old == e.row.tac_rate) grid.acceptRecord(e.row);
             }
        }
    }
    
	    function comparedateBegin(e){
	    	var startDate = e.value;
	    	if(startDate instanceof Date)
	    		startDate = nui.formatDate ( startDate, 'yyyyMMdd' );
	    	
	    	if(startDate!="" && startDate!=null && startDate.length > 10)
		    	startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
		  
	    	var curr = nui.formatDate ( new Date(), 'yyyyMMdd' );
	         if(curr <= startDate){
	         	e.errorText="开始日期必须小于当前时间";
	            e.isValid=false;
	         }
	    }
	    function comparedateEnd(e){
	    	var startDate = e.value;
	    	if(startDate instanceof Date)
	    		startDate = nui.formatDate ( startDate, 'yyyyMMdd' );
	    	
	    	if(startDate!="" && startDate!=null && startDate.length > 10)
		    	startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
		  
	    	var curr = nui.formatDate ( new Date(), 'yyyyMMdd' );
	        if(curr >= startDate){
		     	e.errorText="结束日期必须大于当前时间";
		        e.isValid=false;
		     }
	    }
    
    
		function beforenodeselect(e) {

	    	//组织机构不能选 
		    if (e.node.nodeType=="OrgOrganization") {
		        e.cancel = true;
		    }
		}
	
	function dictEmp(e){
    	var empTree=nui.get("empTree").getList();
	    if(e.value != null){
	    	for(var i=0 ; i < empTree.length ; i++){
				if(empTree[i].ORGID == e.value){
					return empTree[i].ORGNAME;
				}
	    	}
	    }
    } 
    function changeRate(e){
    
    	var gridData = grid.getData();
    	var sum = 0;
    	for(var i=0;i<gridData.length;i++){
			row = grid.getRow(i);
			var tac_rate = row.tac_rate;
			if(tac_rate){
				sum=parseFloat(sum)+parseFloat(tac_rate);
			}
		}
		/* if(tp_pro_sum=="-1"){ */
			sum = tp_pro_sx - sum;
		    nui.get("not_cross_rate").setValue(sum);
		/* }else if(sum>tp_pro_sum){
			alert("分润比例总和不能大于"+);
		} */
		
    }
    
    function setTree(e){
    	 nui.get(this).setData ( nui.get("empTree").getData());
    }
    
    function dateStr(e){
    	var endDate = e.value;
    	if(endDate instanceof Date){
    		endDate = nui.formatDate ( endDate, 'yyyyMMdd' );
    	}
    	if(endDate!="" && endDate!=null && endDate.length > 10){
    		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
    	}
    	return endDate;
    }
    </script>
</body>
</html>
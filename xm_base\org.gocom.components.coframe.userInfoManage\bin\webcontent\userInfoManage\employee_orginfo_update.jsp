<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): yangzhou
  - Date: 2013-03-21 11:24:50
  - Description:
-->
<head>
<title>员工基本信息修改</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<%@include file="/coframe/tools/skins/common.jsp" %>
<%@page import="com.eos.system.utility.StringUtil"%>
<script type="text/javascript" src="<%=contextPath%>/coframe/org/js/org_common.js"></script>
<style type="text/css">
    fieldset
    {
        border:solid 1px #aaa;
    }        
    .hideFieldset
    {
        border-left:0;
        border-right:0;
        border-bottom:0;
    }
    .hideFieldset .fieldset-body
    {
        display:none;
    }
</style>
</head>
<body>
	<div id="form1" style="padding-top:5px;">
	    <input class="nui-hidden" name="map.ORGID"/>
	    <input class="nui-hidden" name="map.EMPID"/>
	    <input class="nui-hidden" id="old_orgname" name="map.OLD_ORGNAME"/>
	    <input class="nui-hidden" id="new_orgname" name="map.NEW_ORGNAME"/>
		<table style="width:100%;table-layout:fixed;" class="nui-form-table" >
			<tr>
				<th class="nui-form-label"><label for="map.type$text">员工姓名：</label></th>
		        <td colspan="3" >  
		         <input id="CONTACTMAN" name = "map.EMPNAME" class="nui-textbox" required="true" vtype="maxLength:50" allowInput="false" style="width:200px;"/>  
		          
		        </td> 
			</tr>	
			
			<tr>
				<th class="nui-form-label"><label for="map.type$text">员工号：</label></th>
		        <td colspan="3" >  
		         <input id="CONTACTMAN" name = "map.EMPCODE" class="nui-textbox" required="true" vtype="maxLength:30" allowInput="false" style="width:200px;"/>  
		          
		        </td> 
			</tr>	
			
			<tr>
		        <th class="nui-form-label"><label for="map.type$text">机构名称：</label></th>
		        <td colspan="3" >  
		         <input id="btnEdit1" name = "map.NEW_ORGID"  class="nui-buttonedit"  onbuttonclick="OrgonButtonEdit" allowInput="false" required="true"  style="width:200px;"/> 
		        </td> 
	      </tr>
		</table>
		<div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save" onclick="saveData">保存</a>
	          <span style="display:inline-block;width:25px;"></span>
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
	</div>

<script type="text/javascript">
	 nui.parse();
	 var form = new nui.Form("#form1");
     form.setChanged(false);
	 
	 //与父页面的方法2对应：页面间传输json数据
    function setFormData(data){
                   
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);

        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
        
	      //表单数据回显
             var json = infos.record;
             var form = new nui.Form("#form1");//将普通form转为nui的form
             form.setData(json);
             var btnEdit1 = nui.get("btnEdit1");
             var old_orgname = nui.get("old_orgname");
             var new_orgname = nui.get("new_orgname");
             btnEdit1.setValue(json.map.ORGID);
             btnEdit1.setText(json.map.ORGNAME);
             old_orgname.setValue(json.map.ORGNAME);//为了后台记录日志
             new_orgname.setValue(json.map.ORGNAME);
         }
    }
	 
	//机构树回显
     function OrgonButtonEdit(e) {
            var btnEdit = this;
            var new_orgname = nui.get("new_orgname");    
            nui.open({
                url:"<%=request.getContextPath() %>/userInfoManage/userInfoManage/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);  
                            new_orgname.setValue(data.TEXT);                    
                        }
                    }
                }
            });            
             
        }    
	
    function saveData(){   
      form.validate();
      if(form.isValid()==false) return;
        
      var data = form.getData(false,true);
      var json = nui.encode(data);
      $.ajax({
        url:"org.gocom.components.coframe.userInfoManage.userInfoManage.update_orgInfo.biz.ext",
        type:'POST',
        data:json,
        cache:false,
        contentType:'text/json',
        success:function(text){
            var returnJson = nui.decode(text);
			if(returnJson.exception == null && returnJson.iRtn == 1){
			
				nui.alert("修改成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
					 
						CloseWindow("saveSuccess");
					}
				});
			}else{
				nui.alert("修改失败", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveFailed");
					}
				});
			}
        }
      });
    }
    
    function onCancel(){
      CloseWindow("cancel");
    }
    
     function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
	
	
	function resetPassWord(){
		var data = form.getData(true,true);
		var json = nui.encode(data);
		if(confirm("确定重置密码吗？")){
			$.ajax({
				url:'com.gotop.userInfoManage.wj.userInfoManage.resetPassword.biz.ext',
				type:'post',
				contentType:'text/json',
				cache:'false',
				data:json,
				success:function(){
					alert("密码已重置");				
				},
				error:function(){
					alert("密码重置失败");
				}				
			});
		}
	}
	//校验日期
	function onOutdateValidation(e){
       	var o = form.getData();
       	var org = o.employee || {};
		if(org.outdate && org.indate && org.outdate<=org.indate){
			e.errorText = "离职日期必须大于入职日期";
			e.isValid = false;
		}
  
	}
	
	
    
	 	//选择离职状态
    function getstatus(){
       	var empstatus=nui.get("empstatus").getValue();
       if(empstatus=="on"){
           nui.get("userstatus").setValue("1");
       }else{
           nui.get("userstatus").setValue("9");
       }	
    }  
</script>

</body>
</html>

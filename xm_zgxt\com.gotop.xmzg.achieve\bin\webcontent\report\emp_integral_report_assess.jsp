<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): 54305
  - Date: 2022-02-26 12:27:15
  - Description:
-->
<head>
<style>
#table1 .tit{
	height: 10px;
    line-height: 10px;
    	text-align: right;
}
#table1 td{
	height: 10px;
    line-height: 10px;
}
#table1 .roleLabel{
	text-align: right;
}
#table1 .roleText{
	padding-left: 5px;
}
</style>
<%@include file="/coframe/tools/skins/common.jsp" %>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<title>行员业绩查询</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <link id="css_icon" rel="stylesheet" type="text/css" href="/default/coframe/tools/icons/icon.css"/>
	<script type="text/javascript" src="/default/common/nui/nui.js"></script>
	<script type="text/javascript" src="/default/common/nui/locale/zh_CN.js"></script>
	<link id="css_skin" rel="stylesheet" type="text/css" href="/default/coframe/tools/skins/skin1/css/style.css" />
</head>
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");

 %>
<body>
	<div class="search-condition">
		<div class="list">
			<div id="form1">
				<table class="table" style="width:100%;">							
					<tr>	
					
						<td class="tit" STYLE="width:100px;">考核方案：</td>
						<td class="">
						<input  id="queryData.TA_ID" name="queryData.TA_ID" class="nui-combobox" onvaluechanged="tAChange" textField="TEXT" valueField="ID"  dataField="list"style="width:150px;"
                  			  required="true"    />
						</td>
						<td class="tit" STYLE="width:100px;">汇总类型：</td>
						<td class="">
						<input  id="queryData.RAIS_DATA_TYPE" name="queryData.RAIS_DATA_TYPE" class="nui-combobox"   data="TYPE_DICT"  textField="text" valueField="id" dataField="list"style="width:150px;"
                  			  required="true"   />
						</td>
						
								
						<td class="tit" STYLE="width:100px;">统计日期：</td>
						<td>
							<input id="queryData.DATE_BEGIN" class="nui-datepicker" name="queryData.DATE_BEGIN"  style="width:150px;" allowInput="false" required="true"/>
						<!--	~
							<input id="queryData.DATE_END" class="nui-datepicker" name="queryData.DATE_END"  style="width:100px;" allowInput="false" required="true"/>-->
							
						</td>
						
						
						
						  <th rowspan="3"><a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp; 
				            <input class="nui-button" text="重置" iconCls="icon-reset" onclick="clean"/>
				            <br><br><a class="nui-button"  iconCls="icon-edit" onclick="exportData">导出公司存款明细</a>
				       </th>			      
					</tr>
					<tr>			
						<td class="tit" STYLE="width:100px;">机构：</td>
						<td>
							<input id="queryData.ORGCODE" name = "queryData.ORGCODE"  class="nui-buttonedit" allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;" required="true"/>
						</td>
						<td class="tit" STYLE="width:100px;">客户经理：</td>
						<td>
<!--							<input id="queryData.EMPCODE" name = "queryData.EMPCODE"  class="nui-buttonedit" allowInput="false" onbuttonclick="EmponButtonEdit" style="width:150px;" />
-->						
						<input id="queryData.TAIS_EMPNAME" name = "queryData.TAIS_EMPNAME"  class="nui-textbox"  style="width:150px;" />
</td>
						
						<td class="tit" STYLE="width:100px;">岗位名称：</td>
						<td>
						<input class="nui-combobox"  id="queryData.JF_POSITION" name="queryData.JF_POSITION"  style="width:150px;"  textField="TEXT" valueField="ID" dataField="list"
						emptyText="全部"  nullItemText="全部"   showNullItem="true"/>
						
						</td>
						
						
					</tr>
					<tr>
						<td class="tit" STYLE="width:100px;">业务条线：</td>
						<td class="">
						<input  id="queryData.TIP_ID" name="queryData.TIP_ID" class="nui-combobox"   onvaluechanged="TIPChange" textField="TEXT" valueField="ID" dataField="list" style="width:150px;"
                  			  emptyText="全部"  nullItemText="全部"   showNullItem="true"  />
						</td>
						
						<td class="tit" STYLE="width:100px;">指标：</td>
						<td class="">
						<input  id="queryData.TI_CODE" name="queryData.TI_CODE" class="nui-combobox"  onvaluechanged="TIChange" textField="TEXT" valueField="ID" dataField="list" style="width:150px;"
                  			  emptyText="全部"  nullItemText="全部"   showNullItem="true"  />
						</td>
						
						<td class="tit" STYLE="width:100px;">指标细项名称：</td>
						<td>
						<!-- 
						 <input id="queryData.TID_NAME" name = "queryData.TID_NAME"  class="nui-textbox"  style="width:150px;" />
						 -->
						 
						 <input  id="queryData.TID_CODE" name="queryData.TID_CODE" class="nui-combobox"   textField="TID_NAME" valueField="TID_CODE" dataField="list" style="width:150px;"
                  			  emptyText="全部"  nullItemText="全部"   showNullItem="true"  />
                  			  
						</td>
					</tr>
					 
					
					 
					
				</table>
			</div>
		</div>
	</div>
	<div class="nui-toolbar" style="border-bottom:0;">
			<table style="width:100%">
				<tr>
					<td>
						<a class="nui-button" iconCls="icon-add" onclick="excel">导出Excel</a>								
					</td>
				</tr>
			</table>
	</div>
    <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.achieve.report.emp_integral_report.biz.ext"   
	  sizeList=[5,10,20,50,100] multiSelect="false" pageSize="10"
	  >
	    <div property="columns" >
	            <div field="TAIS_DATE" >统计日期</div>
	            <div field="YJZH" >一级支行</div>
	            <div field="TAIS_ORGNAME" >二级支行</div>
	            <div field="TAIS_POSITION_NAME" >岗位</div>
	            <div field="TAIS_EMPNAME"  renderer="onDetail" >客户经理名称</div>
	            <div field="TAIS_EMPCODE" >客户经理编号</div>
	      	  	<div field="TA_NAME" >考核方案</div>
	            <div field="TIP_NAME" >业务条线</div>
	            <div  field="TI_NAME" >指标</div>
	            <div field="TID_NAME" >指标细项</div>
	            <div field="TAIS_UNIT" >单位</div>
	            <div field="TID_FREQUENCY_NAME" >统计周期</div>
				<div field="TAIS_ACHIEVE_PRA" >业绩</div>
				<div field="TAIS_ACHIEVE_NOT" >未算积分业绩值</div>
				<div field="TAIS_INTEGRAL" >积分</div>
				<div field="TAIS_SORT">全行排名</div>			
				<div field="TAIS_QZH_SORT">区支行排名</div>			
				<div field="TAIS_BJG_SORT">本机构排名</div>			
				<div field="TAIS_QH_SORT">业务条线排名</div>			
				<div field="TAIS_GW_SORT">岗位排名</div>			
	    </div>
	 </div>
  </div>


	
</body>
<script type="text/javascript">
	var TYPE_DICT =[{ id: "0", text: '指标细项合计' },{ id: "1", text: '指标汇总积分合计'},{ id: "2", text: '业务条线汇总积分合计'},{ id: "3", text: '人员积分汇总合计'}];
	var SORT_DICT =[{ id: "0", text: '月度' },{ id: "1", text: '季度'},{ id: "2", text: '年度'}];
	nui.parse();
	var grid = nui.get("datagrid1");
   
	
	//初始化加载
	$(function(){
		loadAccess();
		nui.get("queryData.RAIS_DATA_TYPE").setValue("0");
	});
	
	//隐藏下拉

	//加载考核方案
	function loadAccess(){
			nui.get("queryData.TA_ID").load("com.gotop.xmzg.achieve.report.loadTACombox.biz.ext");
			nui.get("queryData.TIP_ID").load("com.gotop.xmzg.achieve.report.loadTIPCombox.biz.ext");
			nui.get("queryData.JF_POSITION").load("com.gotop.xmzg.achieve.report.loadPostCombox.biz.ext");
			nui.get("queryData.TA_ID").select(0);
			tAChange();
			setOrgInfo();
			setEmpName();
	}
	
	function search(){
		
		loadGrids();
        var form = new nui.Form("#form1");
        form.validate();
        if (form.isValid() == false) return;
        var data = form.getData(true,true);   
        
        grid.load(data);
    }
    
    function TIPChange(){
    	var TIP_CODE = nui.get("queryData.TIP_ID").getValue();
    	nui.get("queryData.TI_CODE").load("com.gotop.xmzg.achieve.report.loadTICombox.biz.ext?TIP_CODE="+TIP_CODE);
    
    }
    
    function TIChange(){
    	var ti_code = nui.get("queryData.TI_CODE").getValue();
    	nui.get("queryData.TID_CODE").load("com.gotop.xmzg.achieve.achieveOrgMng.get_indicators_detail.biz.ext?ti_code="+ti_code);
    
    }
    	
    	
    	
    //机构树点击事件
	function OrgonButtonEdit(e) {	
        var btnEdit = this;
        nui.open({
            url:"<%=request.getContextPath() %>/achieve/report/org_tree.jsp",
        showMaxButton: false,
        title: "选择树",
        width: 350,
        height: 350,
        ondestroy: function (action) {                    
            if (action == "ok") {
                var iframe = this.getIFrameEl();
                var data = iframe.contentWindow.GetData();
                data = nui.clone(data);
                if (data) {
                    btnEdit.setValue(data.CODE);
                    btnEdit.setText(data.TEXT);
                    var form = new nui.Form("#form1");
       				form.validate();
                    
                }
            }
        }
    	});            
	}
		
	function EmponButtonEdit(e) {
	var ele = e.sender.id;
    		var emp = nui.get(ele);
            nui.open({
                url:"<%=request.getContextPath() %>/achieve/report/emp_tree.jsp?type=emp",
                title: "选择人员",
                width: 600,
                height: 400,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = emp.getValue();
                	//frame.contentWindow.setData(data);
                	frame.contentWindow.setTreeCheck(data.value);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);
                        debugger;    //必须
                        if (data) {
                            emp.setValue(data.id);
                            emp.setText(data.text);
                            //将值人员id转换成人员code
                            var data={empId:data.id};
					        var json = nui.encode(data);
					        console.log(data);
					        console.log(json);
					        var URL="com.gotop.xmzg.files.fileLedger.getEmpcode.biz.ext";
					        $.ajax({
					        	url: URL,
					            type: 'POST',
					            data: json,
					            async:false,
					            cache: false,
					            contentType:'text/json',
					            success: function (text){
					                var returnJson = nui.decode(text);
					                var result = returnJson.resultList;
					                emp.setValue(result[0].EMPCODE);
					                var form = new nui.Form("#form1");
       								form.validate();
							    }
					  		});
                        }
                    }

                }
            });             
	}
	
	//导出Excel
	function excel(){
		var form=new nui.Form("form1");
		form.validate();
	    if (form.isValid() == false) return;
		var data=form.getData(true, true);
		var fileName="人员业绩报表";
		var queryPath="com.gotop.xmzg.achieve.report.query_emp_integral_report";
		var columns=grid.getBottomColumns();
		columns=columns.clone();
		for(var i=0;i<columns.length;i++){
			var column=columns[i];
			if(!column.field){
				columns.removeAt(i);
			}else{
				var c={header:column.header,field:column.field };
				columns[i]=c;
			}
		}
		var sheet = nui.get("queryData.RAIS_DATA_TYPE").getText();
		
		columns=nui.encode(columns);
		data=nui.encode(data);
	    var url="<%=request.getContextPath()%>/achieve/excel/exportBigExcel.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data+"&sheet="+sheet;
		window.location.replace(encodeURI(url));
		 
		 //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
	     setMask();
	} 
	
	function setMask(){
 		var a= nui.loading("正在操作中,请稍等...","提示"); //显示遮罩层
 		var icount = setInterval(function(){  
	  	if(document.attachEvent){   //IE浏览器
	 		if(document.readyState=='interactive'){
              	nui.hideMessageBox(a);  //隐藏遮罩层
              	clearTimeout(icount);
        	}
	 	}else{ //谷歌浏览器
	 		if(document.readyState=='complete'){
              	nui.hideMessageBox(a);  //隐藏遮罩层
              	clearTimeout(icount);
        	}
	 	} 
 		}, 1); 
	}
	
	
	function loadGrids(){
	
		var RAIS_DATA_TYPE = nui.get("queryData.RAIS_DATA_TYPE").getValue();
		var columns = [];
		if(RAIS_DATA_TYPE == 0){
		columns= [
		 { field: "TAIS_DATE", headerAlign: "center",align: "center", header: "统计日期" },
		 { field: "YJZH", headerAlign: "center",align: "center", header: "一级支行" },
		 { field: "TAIS_ORGNAME", headerAlign: "center",align: "center", header: "二级支行" },
		 { field: "TAIS_POSITION_NAME", headerAlign: "center",align: "center", header: "岗位" },
		 { field: "TAIS_EMPNAME", headerAlign: "center",align: "center", header: "客户经理名称" ,renderer:"onDetail"},
		 { field: "TAIS_EMPCODE", headerAlign: "center",align: "center", header: "客户经理编号" },
		 { field: "TA_NAME", headerAlign: "center",align: "center", header: "考核方案" },
		 { field: "TIP_NAME", headerAlign: "center",align: "center", header: "业务条线" },
		 { field: "TI_NAME", headerAlign: "center",align: "center", header: "指标" },
		 { field: "TID_NAME", headerAlign: "center",align: "center", header: "指标细项" },
		 { field: "TAIS_UNIT", headerAlign: "center",align: "center", header: "单位" },
		 { field: "TID_FREQUENCY_NAME", headerAlign: "center",align: "center", header: "业绩统计周期" },
		 { field: "TAIS_ACHIEVE_PRA", headerAlign: "center",align: "center", header: "当前业绩" },
		 { field: "TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "当前积分" },
		 { field: "TAIS_ACHIEVE_NOT", headerAlign: "center",align: "center", header: "当前未算积分业绩值" },
		 
		 { field: "Y_TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "月度积分" },
		 { field: "Y_TAIS_QH_SORT", headerAlign: "center",align: "center", header: "月度全行排名" },
		 { field: "Y_TAIS_QZH_SORT", headerAlign: "center",align: "center", header: "月度区支行排名" },
		 { field: "Y_TAIS_BJG_SORT", headerAlign: "center",align: "center", header: "月度本机构排名" },
		 { field: "Y_TAIS_YWTX_SORT", headerAlign: "center",align: "center", header: "月度业务条线排名" },
		 { field: "Y_TAIS_GW_SORT", headerAlign: "center",align: "center", header: "月度岗位排名" },
		 { field: "J_TAIS_ACHIEVE_PRA", headerAlign: "center",align: "center", header: "季度业绩" },
		 { field: "J_TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "季度积分" },
		 { field: "J_TAIS_QH_SORT", headerAlign: "center",align: "center", header: "季度全行排名" },
		 { field: "J_TAIS_QZH_SORT", headerAlign: "center",align: "center", header: "季度区支行排名" },
		 { field: "J_TAIS_BJG_SORT", headerAlign: "center",align: "center", header: "季度本机构排名" },
		 { field: "J_TAIS_YWTX_SORT", headerAlign: "center",align: "center", header: "季度业务条线排名" },
		 { field: "J_TAIS_GW_SORT", headerAlign: "center",align: "center", header: "季度岗位排名" },
		 
		 { field: "N_TAIS_ACHIEVE_PRA", headerAlign: "center",align: "center", header: "年度业绩" },
		 
		 { field: "N_TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "年度积分" },
		 { field: "N_TAIS_QH_SORT", headerAlign: "center",align: "center", header: "年度全行排名" },
		 { field: "N_TAIS_QZH_SORT", headerAlign: "center",align: "center", header: "年度区支行排名" },
		 { field: "N_TAIS_BJG_SORT", headerAlign: "center",align: "center", header: "年度本机构排名" },
		 { field: "N_TAIS_YWTX_SORT", headerAlign: "center",align: "center", header: "年度业务条线排名" },
		 { field: "N_TAIS_GW_SORT", headerAlign: "center",align: "center", header: "年度岗位排名" },
		]
		}else if (RAIS_DATA_TYPE == 1){
		columns= [
		 { field: "TAIS_DATE", headerAlign: "center",align: "center", header: "统计日期" },
		 { field: "YJZH", headerAlign: "center",align: "center", header: "一级支行" },
		 { field: "TAIS_ORGNAME", headerAlign: "center",align: "center", header: "二级支行" },
		 { field: "TAIS_POSITION_NAME", headerAlign: "center",align: "center", header: "岗位" },
		 { field: "TAIS_EMPNAME", headerAlign: "center",align: "center", header: "客户经理名称" ,renderer:"onDetail"},
		 { field: "TAIS_EMPCODE", headerAlign: "center",align: "center", header: "客户经理编号" },
		 { field: "TA_NAME", headerAlign: "center",align: "center", header: "考核方案" },
		 { field: "TIP_NAME", headerAlign: "center",align: "center", header: "业务条线" },
		 { field: "TI_NAME", headerAlign: "center",align: "center", header: "指标" },
		 { field: "TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "当前积分" },
		 { field: "TAIS_ACHIEVE_NOT", headerAlign: "center",align: "center", header: "当前未算积分业绩值" },
		 { field: "Y_TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "月度积分" },
		 { field: "Y_TAIS_QH_SORT", headerAlign: "center",align: "center", header: "月度全行排名" },
		 { field: "Y_TAIS_QZH_SORT", headerAlign: "center",align: "center", header: "月度区支行排名" },
		 { field: "Y_TAIS_BJG_SORT", headerAlign: "center",align: "center", header: "月度本机构排名" },
		 { field: "Y_TAIS_YWTX_SORT", headerAlign: "center",align: "center", header: "月度业务条线排名" },
		 { field: "Y_TAIS_GW_SORT", headerAlign: "center",align: "center", header: "月度岗位排名" },
		 { field: "J_TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "季度积分" },
		 { field: "J_TAIS_QH_SORT", headerAlign: "center",align: "center", header: "季度全行排名" },
		 { field: "J_TAIS_QZH_SORT", headerAlign: "center",align: "center", header: "季度区支行排名" },
		 { field: "J_TAIS_BJG_SORT", headerAlign: "center",align: "center", header: "季度本机构排名" },
		 { field: "J_TAIS_YWTX_SORT", headerAlign: "center",align: "center", header: "季度业务条线排名" },
		 { field: "J_TAIS_GW_SORT", headerAlign: "center",align: "center", header: "季度岗位排名" },
		 { field: "N_TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "年度积分" },
		 { field: "N_TAIS_QH_SORT", headerAlign: "center",align: "center", header: "年度全行排名" },
		 { field: "N_TAIS_QZH_SORT", headerAlign: "center",align: "center", header: "年度区支行排名" },
		 { field: "N_TAIS_BJG_SORT", headerAlign: "center",align: "center", header: "年度本机构排名" },
		 { field: "N_TAIS_YWTX_SORT", headerAlign: "center",align: "center", header: "年度业务条线排名" },
		 { field: "N_TAIS_GW_SORT", headerAlign: "center",align: "center", header: "年度岗位排名" },
		]
		}else if (RAIS_DATA_TYPE == 2){
		columns= [
		 { field: "TAIS_DATE", headerAlign: "center",align: "center", header: "统计日期" },
		 { field: "YJZH", headerAlign: "center",align: "center", header: "一级支行" },
		 { field: "TAIS_ORGNAME", headerAlign: "center",align: "center", header: "二级支行" },
		 { field: "TAIS_POSITION_NAME", headerAlign: "center",align: "center", header: "岗位" },
		 { field: "TAIS_EMPNAME", headerAlign: "center",align: "center", header: "客户经理名称" ,renderer:"onDetail"},
		 { field: "TAIS_EMPCODE", headerAlign: "center",align: "center", header: "客户经理编号" },
		 { field: "TA_NAME", headerAlign: "center",align: "center", header: "考核方案" },
		 { field: "TIP_NAME", headerAlign: "center",align: "center", header: "业务条线" },
		 { field: "TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "当前积分" },
		 { field: "TAIS_ACHIEVE_NOT", headerAlign: "center",align: "center", header: "当前未算积分业绩值" },
		 { field: "Y_TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "月度积分" },
		 { field: "Y_TAIS_QH_SORT", headerAlign: "center",align: "center", header: "月度全行排名" },
		 { field: "Y_TAIS_QZH_SORT", headerAlign: "center",align: "center", header: "月度区支行排名" },
		 { field: "Y_TAIS_BJG_SORT", headerAlign: "center",align: "center", header: "月度本机构排名" },
		 { field: "Y_TAIS_YWTX_SORT", headerAlign: "center",align: "center", header: "月度业务条线排名" },
		 { field: "Y_TAIS_GW_SORT", headerAlign: "center",align: "center", header: "月度岗位排名" },
		 { field: "J_TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "季度积分" },
		 { field: "J_TAIS_QH_SORT", headerAlign: "center",align: "center", header: "季度全行排名" },
		 { field: "J_TAIS_QZH_SORT", headerAlign: "center",align: "center", header: "季度区支行排名" },
		 { field: "J_TAIS_BJG_SORT", headerAlign: "center",align: "center", header: "季度本机构排名" },
		 { field: "J_TAIS_YWTX_SORT", headerAlign: "center",align: "center", header: "季度业务条线排名" },
		 { field: "J_TAIS_GW_SORT", headerAlign: "center",align: "center", header: "季度岗位排名" },
		 { field: "N_TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "年度积分" },
		 { field: "N_TAIS_QH_SORT", headerAlign: "center",align: "center", header: "年度全行排名" },
		 { field: "N_TAIS_QZH_SORT", headerAlign: "center",align: "center", header: "年度区支行排名" },
		 { field: "N_TAIS_BJG_SORT", headerAlign: "center",align: "center", header: "年度本机构排名" },
		 { field: "N_TAIS_YWTX_SORT", headerAlign: "center",align: "center", header: "年度业务条线排名" },
		 { field: "N_TAIS_GW_SORT", headerAlign: "center",align: "center", header: "年度岗位排名" },
		]
		}else if (RAIS_DATA_TYPE == 3){
		columns= [
		 { field: "TAIS_DATE", headerAlign: "center",align: "center", header: "统计日期" },
		 { field: "YJZH", headerAlign: "center",align: "center", header: "一级支行" },
		 { field: "TAIS_ORGNAME", headerAlign: "center",align: "center", header: "二级支行" },
		 { field: "TAIS_POSITION_NAME", headerAlign: "center",align: "center", header: "岗位" },
		 { field: "TAIS_EMPNAME", headerAlign: "center",align: "center", header: "客户经理名称" ,renderer:"onDetail"},
		 { field: "TAIS_EMPCODE", headerAlign: "center",align: "center", header: "客户经理编号" },
		 { field: "TA_NAME", headerAlign: "center",align: "center", header: "考核方案" },
		  { field: "TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "当前积分" },
		 { field: "TAIS_ACHIEVE_NOT", headerAlign: "center",align: "center", header: "当前未算积分业绩值" },
		 { field: "Y_TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "月度积分" },
		 { field: "Y_TAIS_QH_SORT", headerAlign: "center",align: "center", header: "月度全行排名" },
		 { field: "Y_TAIS_QZH_SORT", headerAlign: "center",align: "center", header: "月度区支行排名" },
		 { field: "Y_TAIS_BJG_SORT", headerAlign: "center",align: "center", header: "月度本机构排名" },
		 { field: "Y_TAIS_YWTX_SORT", headerAlign: "center",align: "center", header: "月度业务条线排名" },
		 { field: "Y_TAIS_GW_SORT", headerAlign: "center",align: "center", header: "月度岗位排名" },
		 { field: "J_TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "季度积分" },
		 { field: "J_TAIS_QH_SORT", headerAlign: "center",align: "center", header: "季度全行排名" },
		 { field: "J_TAIS_QZH_SORT", headerAlign: "center",align: "center", header: "季度区支行排名" },
		 { field: "J_TAIS_BJG_SORT", headerAlign: "center",align: "center", header: "季度本机构排名" },
		 { field: "J_TAIS_YWTX_SORT", headerAlign: "center",align: "center", header: "季度业务条线排名" },
		 { field: "J_TAIS_GW_SORT", headerAlign: "center",align: "center", header: "季度岗位排名" },
		 { field: "N_TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "年度积分" },
		 { field: "N_TAIS_QH_SORT", headerAlign: "center",align: "center", header: "年度全行排名" },
		 { field: "N_TAIS_QZH_SORT", headerAlign: "center",align: "center", header: "年度区支行排名" },
		 { field: "N_TAIS_BJG_SORT", headerAlign: "center",align: "center", header: "年度本机构排名" },
		 { field: "N_TAIS_YWTX_SORT", headerAlign: "center",align: "center", header: "年度业务条线排名" },
		 { field: "N_TAIS_GW_SORT", headerAlign: "center",align: "center", header: "年度岗位排名" },
		]
		}
		
		
		grid.set({
		    		 "columns":columns
		    	});
	}
	
	
	function tAChange(){
		var TA_ID = nui.get("queryData.TA_ID").getValue();
		var json= {"TA_ID":TA_ID};
		$.ajax({
			url: "com.gotop.xmzg.achieve.report.getTaMaxDate.biz.ext",
            type: 'POST',
            data: nui.encode(json),
            async:false,
            cache: false,
            contentType:'text/json',
            success: function (text){
           	 	var returnJson = nui.decode(text);
           	 	console.log(returnJson.result.length);
           	 
           	 	if(returnJson.result.length>0){
           	 		var BDATE = returnJson.result[0]["BDATE"];
           	 		var EDATE = returnJson.result[0]["EDATE"];
           	 		console.log(BDATE);
           	 		console.log(EDATE);
           	 		nui.get("queryData.DATE_BEGIN").setValue(BDATE);
           	 		nui.get("queryData.DATE_END").setValue(EDATE);
           	 	}
           	 	
           
			}
		});
		
	}
	
	
	function setOrgInfo(){
		$.ajax({
				url: "com.gotop.xmzg.achieve.report.getUserInfo.biz.ext",
	            type: 'POST',
	            
	            async:false,
	            cache: false,
	            contentType:'text/json',
	            success: function (text){
	           	 	var returnJson = nui.decode(text);
	           	 	console.log(returnJson.userInfo);
	           	 	
	       	 		var ORGCODE = returnJson.userInfo.ORGCODE;
	       	 		var ORGNAME = returnJson.userInfo.ORGNAME;
	       	 		console.log(ORGCODE);
	       	 		console.log(ORGNAME);
	       	 		nui.get("queryData.ORGCODE").setValue(ORGCODE);
	       	 		nui.get("queryData.ORGCODE").setText(ORGNAME);
				}
			});
	}
	
	
	
	function setEmpName(){
		$.ajax({
				url: "com.gotop.xmzg.achieve.report.getReportRole.biz.ext",
	            type: 'POST',
	            async:false,
	            cache: false,
	            contentType:'text/json',
	            success: function (text){
	           	 	var returnJson = nui.decode(text);
	           	 	console.log(returnJson.userInfo);
	       	 		if(returnJson.userInfo.empname){
	       	 			nui.get("queryData.TAIS_EMPNAME").setValue(returnJson.userInfo.empname);
	       	 			nui.get("queryData.TAIS_EMPNAME").setReadOnly(true);
	       	 			
	       	 		}
				}
			});
	}
	
	function clean(){
	   var form = new nui.Form("#form1");
	   form.clear();
	}
	
	function onDetail(e){
	var record = e.record;
		var TAIS_EMPNAME = record.TAIS_EMPNAME;
	  	s = "<a class=\"dgBtn1\" style=\"color:blue\" href=\"javascript:void(0);\" onclick=\"infodetail("+e.rowIndex+")\">"+TAIS_EMPNAME+"</a>";
	  	return s;
	}
	
	
	function infodetail(value){
    	
   		var row= grid.data[value];
    	nui.open({
				url: "<%=request.getContextPath() %>/achieve/report/emp_yj_detail.jsp",
	          	title:'详情',
	          	width:1250,
          		height:600,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            var data = {record:{map:row}};
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){		        	
		              <!-- grid.load("org.gocom.components.coframe.auth.notice.select_infoApplyList.biz.ext");	 -->            
		        }
		});
    };
    
    
    function exportData(){
		var data = new nui.Form("#form1").getData(true, true);
		data = nui.encode(data);
		console.log(data);
		var columns = nui.encode([{header:'日期',field:'SUMM_DATE'}
			,{header:'账户归属一级支行',field:'UPPER_NAME'}
			,{header:'公司活期存款-积分系数',field:'RATE1'}
			,{header:'公司价值存款-积分系数',field:'RATE2'}
			,{header:'客户经理',field:'EMPNAME'}
			,{header:'客户经理工号',field:'TAB_EMP'}
			,{header:'公司活期存款-月日均',field:'F'}
			,{header:'公司活期存款-2022年日均',field:'G'}
			,{header:'公司活期存款-2021年日均',field:'H'}
			,{header:'公司活期存款-本季日均',field:'I'}
			,{header:'公司价值存款（活期存款外）-月日均',field:'J'}
			,{header:'公司价值存款（活期存款外）-2022年日均',field:'K'}
			,{header:'公司价值存款（活期存款外）-2021年日均',field:'L'}
			,{header:'公司价值存款（活期存款外）-本季日均',field:'M'}
			,{header:'合计价值存款-月日均',field:'N'}
			,{header:'合计价值存款-2022年日均',field:'O'}
			,{header:'合计价值存款-2021年日均',field:'P'}
			,{header:'合计价值存款-本季日均',field:'Q'}
		]);
		
		var sheet = "公司存款明细";
		var fileName = "公司存款明细信息";
		var queryPath = "com.gotop.xmzg.achieve.report.expgsck";
		var url="<%=request.getContextPath()%>/achieve/excel/exportBigExcel.jsp";
		url += "?fileName="+fileName+"&columns="+columns
				+"&queryPath="+queryPath+"&data="+data+"&sheet="+sheet;
		console.log(url);
		window.location.replace(encodeURI(url));
	};
    </script>
</html>
<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-01-05 11:41:00
  - Description:
-->
<head>
<%-- <%@include file="/coframe/tools/skins/common.jsp" %> --%>
<%@include file="/coframe/dict/common.jsp"%>
<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
</head>
<body>
<!-- JF_POSITION -->
<div id="form1" style="padding-top:5px;">
	<input id="TJID_ID" name = "map.TJID_ID" class="nui-hidden" />
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>考核方案：</label></th>
        <td colspan="3" >  
        	<input  id="map.TA_ID" name="map.TA_ID" class="nui-combobox" 
        	url="com.gotop.xmzg.achieve.report.loadTACombox.biz.ext"
        	textField="TEXT" valueField="ID"  dataField="list" style="width:100%;"required="true"    />
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>业务条线：</label></th>
        <td colspan="3" >  
        	<div id="TIP_CODE" name="map.TIP_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TIP_NAME" valueField="TIP_CODE" 
		    	url="com.gotop.xmzg.achieve.configClaPro.tip_get.biz.ext" multiSelect="false" allowInput="false" showNullItem="false" emptyText="请选择..."
		    	onvaluechanged="onTipChanged" required="true">     
			    <div property="columns">
			        <div header="业务条线代码" field="TIP_CODE" width="60"></div>
			        <div header="业务条线名称" field="TIP_NAME" width="120"></div>
			    </div>
		    </div>
		</div>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>指标：</label></th>
        <td colspan="3" >  
        	<div id="TI_CODE" name="map.TI_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TI_NAME" valueField="TI_CODE" 
		    	multiSelect="false" allowInput="false" showNullItem="false" emptyText="请先选择业务条线..." required="true"
		    	onvaluechanged="onTiChanged">     
			    <div property="columns">
			        <div header="指标代码" field="TI_CODE" width="60"></div>
			        <div header="指标名称" field="TI_NAME" width="120"></div>
			    </div>
		    </div>
		</div>
        </td> 
      </tr>
      <tr id="tidTr">
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>指标细项：</label></th>
        <td colspan="3" >  
        	<div id="TID_CODE" name="map.TID_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TID_NAME" valueField="TID_CODE" 
		    	multiSelect="false" allowInput="false" showNullItem="false" emptyText="请先选择指标..." required="true">     
			    <div property="columns">
			        <div header="指标细项代码" field="TID_CODE" width="60"></div>
			        <div header="指标细项名称" field="TID_NAME" width="120"></div>
			    </div>
		    </div>
		</div>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>积分日期：</label></th>
        <td colspan="3" >  
        	<input id="TAIS_DATE" name = "map.TAIS_DATE" class="nui-datepicker"  style="width:100%;" allowInput="false" format="yyyyMMdd" required="true" />
        </td> 
      </tr>
	  <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>客户经理：</label></th>
        <td colspan="3" >  
        	<input id="TAIS_EMPCODE" name = "map.TAIS_EMPCODE"  class="nui-buttonedit" allowInput="false" onbuttonclick="selectEmp" style="width:100%;" required="true"/>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>调整积分：</label></th>
        <td colspan="3" >  
        	<input class="nui-spinner" id="TAIS_INTEGRAL" name="map.TAIS_INTEGRAL" minValue="-999999999" maxValue="999999999"  style="width:100%;" format="#,0.00" required="true"/>
        </td> 
      </tr>
      
    </table>
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save" onclick="onOk">保存</a>
	          <span style="display:inline-block;width:10px;"></span>
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  </div>

  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);
	
    var saveUrl = "com.gotop.xmzg.achieve.config.adjust_add.biz.ext";
    function setData(data){
               	    
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);
        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
        	console.log(infos);
          saveUrl = "com.gotop.xmzg.achieve.config.adjust_update.biz.ext";
	      //表单数据回显
          var json = infos.record;
          onTipChanged(null,json.map.TIP_CODE);
		  onTiChanged(null,json.map.TI_CODE);
		  
          form.setData(json);
          var emp = nui.get("TAIS_EMPCODE");
          emp.setValue(json.map.TAIS_EMPCODE);
          emp.setText(json.map.TAIS_EMPNAME);
        }
         
    }
    
    function onOk(){
      saveData();
    }
    
    //提交
    function saveData(){   
      form.validate();
      if(form.isValid()==false) return;
      
      var data = form.getData(true,true);
      var json = nui.encode(data);
     
      $.ajax({
        url:saveUrl,
        type:'POST',
        data:json,
        cache:false,
        contentType:'text/json',
        success:function(text){
            var returnJson = nui.decode(text);
			if(returnJson.exception == null && returnJson.iRtn == 1){
				nui.alert("操作成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveSuccess");
					}
				});
			}else{
				nui.alert("操作失败", "系统提示");
			}
        }
      });
    }
    
    
    
    function onCancel(){
      CloseWindow("cancel");
    }
    
    function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }

    function isSelTid(e){
    	var datas = nui.get("TID_CODE").getData();
    	var value = nui.get("TID_CODE").getValue();
    	for(var i=0;i< datas.length;i++){
    		if(datas[i].TID_CODE == value){
    			e.errorText="";
          		e.isValid=true;
          		return;
    		}
    	}
    	 e.errorText="请选择指标细项";
         e.isValid=false;
    } 
    
    var tip = nui.get("TIP_CODE");
    var ti = nui.get("TI_CODE");
    var tid = nui.get("TID_CODE");
    function onTipChanged(e,val){
        ti.setValue("");
        tid.setValue("");
        if(!val) val = tip.getValue();
        var url = "com.gotop.xmzg.achieve.configClaPro.ti_get.biz.ext?code=" + val;
        ti.setUrl(url);
    }
    
    function onTiChanged(e,val){
        tid.setValue("");
        if(!val) val = ti.getValue();
        var url = "com.gotop.xmzg.achieve.configClaPro.tid_get.biz.ext?code=" + val;
        tid.setUrl(url);
    }
    
    
    function selectEmp(e){
        	var ele = e.sender.id;
    		var emp = nui.get(ele);
            nui.open({
                url:"<%=request.getContextPath()%>/achieve/acc/accEmptree.jsp",
                title: "选择人员",
                width: 600,
                height: 400,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = emp.getValue();
                	//frame.contentWindow.setData(data);
                	frame.contentWindow.setTreeCheck(data.value);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            emp.setValue(data.ORGID);
                            emp.setText(data.ORGNAME);
                        }
                    }

                }
            });
    	}
    	
  </script>
</body>
</html>
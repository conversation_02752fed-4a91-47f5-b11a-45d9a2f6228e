<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): liuzd
  - Date: 2018-05-18
  - Description:
-->
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>待归档清单</title>
</head>
<body style="margin:0px;width:100%;overflow:hidden;" >
	<div class="search-condition">
		<div class="list">
			<div id="filesForm">
				<div class="nui-hidden"  name="condition.userOrgId" id="userOrgId"></div>
				<div class="nui-hidden"  name="condition.userId"  id="userId" ></div>
		   		<table class="table" style="width:100%;">
		      		<tr>
						<td align="right">档案种类：</td>
						<td>
							<input id="files_type" class="nui-dictcombobox" name="queryData.FILES_TYPE"  emptyText="请选择"
          					valueField="dictID" textField="dictName" dictTypeId="FILES_TYPE" showNullItem="true" nullItemText="请选择" style="width:180px;"/>
						</td>
						<td align="right">档案名称：</td>
						<td>
							<input name="queryData.FILES_NAME" class="nui-textbox" style="width:180px;"/>
						</td>
						<th align="right">经办机构：</th>
						<td>
							<input id="btnEdit1" name = "queryData.DEAL_ORG"  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:180px;"/>
						</td>
						<!-- <td align="right">录入日期：</td>
						<td>
							<input name="condition.SUMM_DATE1" id="summDate1" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:90px;"/>
							-
							<input name="condition.SUMM_DATE2" id="summDate2" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:90px;"/>
						</td> -->
						<td rowspan="12" class="btn-wrap">
							<a class="nui-button" iconCls="icon-search" onclick="searchData();">查询</a>
							<a class="nui-button" iconCls="icon-reset"  onclick="clean()"/>重置</a>
						</td>
					</tr>
					<tr>
			        	<td align="right">分管客户经理名称：</td>
						<td>
							<input name="queryData.EMPNAME" class="nui-textbox" style="width:180px;"/>
						</td>
						
						<td align="right">起期区间：</td>
						<td>
							<input name="queryData.startTime" id="startTime" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:90px;"/>
							-
							<input name="queryData.startTime1" id="startTime1" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:90px;"/>
						</td>
			        </tr>
		    	</table>
		  	</div>
		</div>
	</div>
	<div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      	<table style="width:100%;">
        	<tr>
           		<td style="width:100%;">
			     	<a id="add" class="nui-button" iconCls="icon-add" onclick="dataInput()" >添加</a>
					<a id="eidt" class="nui-button" iconCls="icon-edit" onclick="dataEdit();">修改</a> 
					<a id="del" class="nui-button" iconCls="icon-remove" onclick="dataDelete">删除</a>
					<a class="nui-button" iconCls="icon-goto" onclick="apply()" >归档申请</a>
					<a id="fileDetail" class="nui-button" iconCls="icon-node" onclick="fileDetail();">档案详情</a>  
	             	<a id="operationDetail" class="nui-button" iconCls="icon-node" onclick="operateDetail()">操作记录</a>
	             	<a id="imp" class="nui-button" iconCls="icon-upload" onclick="importExcel" >导入</a>
           		</td>
        	</tr>
      	</table>
    </div>
    <!-- 档案主表信息 -->
  	<div class="nui-fit" id="fieldset1">
	 	<div id="grid"  dataField="resultList" idField="dicttypeid" class="nui-datagrid" style="width:100%;height:100%;" 
	  	url="com.gotop.xmzg.files.archiveList.getFilesInfo.biz.ext" sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" >
	    	<div property="columns">
	    		<div type="checkcolumn"></div>
	    		<div field="INFORMATION_ID" class="nui-hidden" visible="false">ID</div>
		        <div field="FILES_NAME" headerAlign="center">档案名称</div>
		        <div field="FILES_TYPE" headerAlign="center" renderer="filesType">档案种类</div>
		        <div field="DEAL_NAME" headerAlign="center">经办机构</div>
		        <div field="EMP_NAME" headerAlign="center">分管客户经理</div>
		        <div field="START_TIME" headerAlign="center">起期</div>
		        <div field="END_TIME" headerAlign="center">止期</div>
		        <div field="CONTRACT_PRICE" headerAlign="center">金额</div>
		        <div field="IMP_TIME" headerAlign="center">导入时间</div>
		    </div>
	 	</div>
  	</div>
</body>
</html>
<script type="text/javascript">
	nui.parse();
	var form = new nui.Form("filesForm");
	var grid = nui.get("grid");
	//按钮权限
	isFhOrbm();
	var data = form.getData(true,true);
	//grid.load(data);
	
	function searchData(){	
			//form.validate();            
        	//if (form.isValid() == false) return;
        	//var temp = nui.get("files_type").getValue();
        	var data = form.getData(true,true);
        	/* if(temp == '01'){
				data.queryData.TABLE_NAME = 'T_FILES_RETAIL_CREDIT';
			}else if(temp == '02'){
				data.queryData.TABLE_NAME = 'T_FILES_RETAIL_DISBURSE';
			}else if(temp == '03'){
				data.queryData.TABLE_NAME = 'T_FILES_PERSON_CREDIT';
			}else if(temp == '04'){
				data.queryData.TABLE_NAME = 'T_FILES_PERSON_DISBURSE';
			}else if(temp == '05'){
				data.queryData.TABLE_NAME = 'T_FILES_BILL_HONOUR';
			}else if(temp == '06'){
				data.queryData.TABLE_NAME = 'T_FILES_BILL_DISCOUNT';
			}else if(temp == '07'){
				data.queryData.TABLE_NAME = 'T_FILES_COOPERATE_ORG';
			}else if(temp == '08'){
				data.queryData.TABLE_NAME = 'T_FILES_REFUSE_LOAN';
			}else if(temp == '09'){
				data.queryData.TABLE_NAME = 'T_FILES_CREDIT';
			} */
    		grid.load(data);
    	}
    	
    	
	function dataInput(){
       nui.open({
       	  targetWindow: window,
          url:"<%=request.getContextPath()%>/files/archiveList/archiveListAdd.jsp",
          title:'待归档清单录入',
          width:1150,
          height:350,
          onload:function(){
          	 var iframe = this.getIFrameEl();  
          	 var data = { action: "new" };
             //iframe.contentWindow.SetData(data);
          },
          ondestroy:function(action){
          	 var strs= new Array();
          	 strs=action.split(","); 
	         if(strs[0]=="saveSuccess"){
	         	 nui.get("files_type").setValue(strs[1]);
            	 searchData();
             }
          }
       });
    }
    
    function dataEdit(){
    	var rows = grid.getSelecteds();
    	var row = grid.getSelected();
    	var detail = '';
    	if(rows.length>1){
    		return nui.alert("请只选择一条记录进行编辑！","提示");
    	}
       	if(row!=null){
       		var filesType = row.FILES_TYPE;
       		//综合类档案
       		if(noun(filesType)){
       			other(filesType,row,'edit');
       		}else {     //基础类档案
       			oneToNine(row,'edit');
       		}
       	}else{
        	nui.alert("请选中一条记录进行编辑！","提示");
    	}
    }
    
    function oneToNine(row,mark){
    		var aa = "<%=request.getContextPath()%>/files/archiveList/archiveListUpdate.jsp";
    		if(mark == 'detail'){
    			aa = "<%=request.getContextPath()%>/files/archiveList/archiveListDetail.jsp";
    		}
       		
		    nui.open({
		    	url:aa,
	          	title:'编辑',
	          	width:1150,
          		height:340,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            //方法1：后台查询一次，获取信息回显
		            /* var data = row;
		            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
		            //方法2：直接从页面获取，不用去后台获取
		            var filesType=row.FILES_TYPE;
		            var data={};
		            if(filesType == '01'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '02'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '03'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '04'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '05'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '06'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '07'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '08'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '09'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		        	debugger;
		         	 var strs= new Array();
		          	 strs=action.split(","); 
		          	 if(strs[0]=="saveSuccess"){
			         	 nui.get("files_type").setValue(strs[1]);
            	 		 searchData();
		             }
		        }
		    });
    }
    
    
    function other(filesType,row,mark){
    		var path ='';
    		if(mark == 'edit'){
    			if(filesType == '15'){
    				//信审档案修改界面
    				path = "<%=request.getContextPath()%>/files/archiveList/xsUpdate.jsp";
    			}else {
    				//综合类档案的其它档案修改界面
    				var path = "<%=request.getContextPath()%>/files/archiveList/otherUpdate.jsp";
    			}
    		}else{
    			if(filesType == '15'){
    				//信审档案详情界面
    				path = "<%=request.getContextPath()%>/files/archiveList/xsDetail.jsp";
    			}else {
    				//综合类档案的其它档案详情界面
    				var path = "<%=request.getContextPath()%>/files/archiveList/otherDetail.jsp";
    			}
    		}
    		
		    nui.open({
		    	url:path,
	          	title:'编辑',
	          	width:1150,
          		height:360,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            //方法1：后台查询一次，获取信息回显
		            /* var data = row;
		            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
		            //方法2：直接从页面获取，不用去后台获取
		            var filesType=row.FILES_TYPE;
		            var data = {pageType:'edit',filesType:filesType,record:{editData:row}};
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		        	debugger;
		         	 var strs= new Array();
		          	 strs=action.split(","); 
		          	 if(strs[0]=="saveSuccess"){
			         	 nui.get("files_type").setValue(strs[1]);
            	 		 searchData();
		             }
		        }
		    });
    }
    
    function fileDetail(){
    	var rows = grid.getSelecteds();
    	var row = grid.getSelected();
    	if(rows.length>1){
    		return nui.alert("请只选择一条记录进行查看！","提示");
    	}
       	if(row!=null){
       		var filesType = row.FILES_TYPE;
       		//综合类档案
       		if(noun(filesType)){
       			other(filesType,row,'detail');
       		}else {     //基础类档案
       			oneToNine(row,'detail');
       		}
       	}else{
        	nui.alert("请选中一条记录进行查看！","提示");
    	}
    }
    
    
    function dataDelete(){
    	var rows = grid.getSelecteds();
      	if(rows.length > 0){
      		for(var i=0;i<rows.length;i++){
      			if(rows[i].SP_STATUS != '0'){
      				nui.alert('请选择审批状态为待归档的档案！');
      				return;
      			}
      		}
	        nui.confirm("确定删除选中记录？","系统提示",function(action){
	        	if(action=="ok"){
	            	var json = nui.encode({deleteDatas:rows});
	           		var a= nui.loading("正在删除中,请稍等...","提示");
	           		var URL="com.gotop.xmzg.files.archiveList.deleteData.biz.ext";
		        	$.ajax({
		          		url:URL,
		          		type:'POST',
		          		data:json,
		          		cache: false,
		          		contentType:'text/json',
		          		success:function(text){
			          		nui.hideMessageBox(a);
			            	var returnJson = nui.decode(text);
							if(returnJson.flag == 1){
								nui.alert("删除成功", "系统提示", function(action){
									searchData();
								});
							}else{
								nui.alert("删除失败", "系统提示");
								getGridId().unmask();
							}
		          		}
		        	});
		     	}
			});
      	}else{
        	nui.alert("请选中一条记录！");
      	}
    }
    
    
    
    function apply(){
    	var rows = grid.getSelecteds();
    	var row = grid.getSelected();
    	var detail = '';
    	if(rows.length>1){
    		return nui.alert("请只选择一条记录进行申请！","提示");
    	}
       	if(row!=null){
		    nui.open({
		    	url:"<%=request.getContextPath()%>/files/archiveList/archiveApply.jsp",
	          	title:'归档申请',
	          	width:800,
          		height:450,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            //方法1：后台查询一次，获取信息回显
		            /* var data = row;
		            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
		            //方法2：直接从页面获取，不用去后台获取
		            var filesType=row.FILES_TYPE;
		            var data={};
		            data = {pageType:"apply",record:{inputData:row}};
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		          	 if(action=="saveSuccess"){
            	 		 searchData();
		             }
		        }
		    });
       	}else{
        	nui.alert("请选中一条记录进行编辑！","提示");
    	}
    }
   
    
    //重置查询信息
	function clean(){
	   	//不能用form.reset(),否则会把隐藏域信息清空
	   	form.reset();  
    }
    
    //机构树回显
     function OrgonButtonEdit(e){
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/files/archiveList/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            
                        }
                    }
                }
            });            
             
        }   
        
     //归档清单导入
    function importExcel(){
    	nui.open({
       	  targetWindow: window,
          url:"<%=request.getContextPath()%>/files/archiveList/archiveListImp.jsp",
          title:'待归档清单导入',
          width:800,
          height:400,
          onload:function(){
          },
          ondestroy:function(action){
          	 var strs= new Array();
          	 strs=action.split(","); 
	         if(strs[0]=="saveSuccess"){
	         	 nui.get("files_type").setValue(strs[1]);
            	 searchData();
             }
          }
       });
    } 
    
    function filesType(e){
	    return nui.getDictText("FILES_TYPE", e.row.FILES_TYPE);
	}
	
	
	//详情
       function operateDetail(){
       var row = grid.getSelected();
       var rows = grid.getSelecteds();
       if(rows.length>1){
    		return nui.alert("请只选择一条记录进行查看！","提示");
    	}
       if(row!=null){
       var INFORMATION_ID=row.INFORMATION_ID;
       debugger;
	       nui.open({
	          url:"<%=request.getContextPath() %>/files/archiveList/operationLog.jsp?infoId="+INFORMATION_ID,
	          title:'档案操作流水',
	          width:1200,
	          height:550,
	          onload:function(){
	             /* var iframe = this.getIFrameEl();
	               var data = {pageType:"edit",record:{map:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法 */
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	             }
	          }
	       });
       }else{
           nui.alert("请选中一条记录！");
       }
    }
    
    function isFhOrbm(){
    	$.ajax({
      		url:"com.gotop.xmzg.files.archiveList.isFhOrBm.biz.ext",
      		type:'GET',
      		cache: false,
      		contentType:'text/json',
      		success:function(text){
            	var returnJson = nui.decode(text);
				if(returnJson.flag == 0){
					nui.get("add").disable();
					nui.get("del").disable();
					nui.get("eidt").disable();
					nui.get("imp").disable();
				}else{
					
				}
      		}
    	});
    }
    
    function noun(fy){
		if(fy == '10'){
			return true;
		}else if(fy == '11'){
			return true;
		}else if(fy == '12'){
			return true;
		}else if(fy == '13'){
			return true;
		}else if(fy == '14'){
			return true;
		}else if(fy == '15'){
			return true;
		}else {
			return false;
		}
	}
</script>
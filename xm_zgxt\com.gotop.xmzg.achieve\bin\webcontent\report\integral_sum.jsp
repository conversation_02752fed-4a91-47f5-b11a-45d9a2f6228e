<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): xwl
  - Date: 2023-05-04 10:17:26
  - Description:
-->
<head>
<style>
#table1 .tit{
	height: 10px;
    line-height: 10px;
    	text-align: right;
}
#table1 td{
	height: 10px;
    line-height: 10px;
}
#table1 .roleLabel{
	text-align: right;
}
#table1 .roleText{
	padding-left: 5px;
}
</style>
<%@include file="/coframe/tools/skins/common.jsp" %>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<title>综合营销率统计表</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
  <script type="text/javascript" src="/default/common/nui/nui.js"></script>
	<script type="text/javascript" src="/default/common/nui/locale/zh_CN.js"></script>
	<link id="css_skin" rel="stylesheet" type="text/css" href="/default/coframe/tools/skins/skin1/css/style.css" />
    
</head>
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");

 %>
<body>
<div class="search-condition">
		<div class="list">
			<div id="form1">
			<table class="table" style="width:100%;">							
				<tr>	
					<td class="tit" STYLE="width:100px;">统计日期:</td>
					<td>
						<input id="queryData.TAIS_DATE" format="yyyyMMdd" class="nui-datepicker" name="queryData.TAIS_DATE"  style="width:150px;" allowInput="false" required="true" />	
					</td>
					
					<td class="tit" STYLE="width:100px;">岗位：</td>
					<td>
					<input class="nui-combobox"  id="queryData.JF_POSITION" name="queryData.JF_POSITION"  style="width:150px;"  textField="TEXT" valueField="ID" dataField="list"
					emptyText="全部"  nullItemText="全部"   showNullItem="true"/>
					</td> 
                    <th rowspan="3"><a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp; 
			        <input class="nui-button" text="重置" iconCls="icon-reset" onclick="clean"/>  
				</tr>
				<tr>	
                    <td class="tit" STYLE="width:100px;">员工号:</td>
					<td>
						<input id="queryData.TID_CODE" name = "queryData.TID_CODE"  class="nui-textbox"  style="width:150px;" />
                    </td>
                        
                    <td class="tit" STYLE="width:100px;">姓名:</td>
					<td>
						<input id="queryData.TID_NAME" name = "queryData.TID_NAME"  class="nui-textbox"  style="width:150px;" />
					</td>    			      
				</tr>			
			</table>
		</div>
	</div>
</div>
<div class="nui-toolbar" style="border-bottom:0;">
		<table style="width:100%">
			<tr>
				<td>
					<a class="nui-button" iconCls="icon-add" onclick="excel">导出Excel</a>								
				</td>
			</tr>
		</table>
</div>	
	
<div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.achieve.report.integral_sum.biz.ext" 
	  sizeList=[5,10,20,50,100] multiSelect="false" pageSize="10">	  
	    <div property="columns" >           
	            <div field="TAIS_EMPNAME" >姓名</div>
			    <div field="TAIS_EMPCODE">工号</div>
			    <div field="TEXT">岗位</div>	
			    <div field="GRJR">个人金融</div>
			    <div field="GRJR2">个人金融（网络金融部）</div>
			    <div field="GRJR3">个人金融（信用卡部）</div>
			    <div field="SN">三农金融</div>
			    <div field="XQY">小企业金融</div>
			    <div field="GS">公司金融</div>   
			    <div field="JYYH">交易银行</div>
			    <div field="JRTY">金融同业</div>
			    <div field="HJ">营销积分合计</div>
			    <div field="ZHXY">综合营销积分合计</div>
				<div field="ZHYXL">综合营销率</div>
				<div field="ZHYXXS">综合营销系数</div>	
				<div field="YXJFJL">营销积分奖励</div>		
	    </div>
	 </div>
  </div>
</body>
<script type="text/javascript">

    nui.parse();
	var grid = nui.get("datagrid1");
	nui.get("queryData.JF_POSITION").load("com.gotop.xmzg.achieve.report.loadPostCombox.biz.ext");
    //grid.load();
	function search(){
     	var form = new nui.Form("#form1");
    	form.validate();
        if (form.isValid() == false) return; 
        var data = form.getData(true,true);     
        grid.load(data);
    }
	
	function clean(){
	   var form = new nui.Form("#form1");
	   form.clear();
	}
	
	//导出Excel
	function excel(){		      
		var form=new nui.Form("form1");
		form.validate();
	    if (form.isValid() == false) return;
		var data=form.getData(true, true);
		var fileName="综合营销率统计表";
		var queryPath="com.gotop.xmzg.achieve.report.query_integral_sum";
		var columns=grid.getBottomColumns();
		columns=columns.clone();
		for(var i=0;i<columns.length;i++){
			var column=columns[i];
			if(!column.field){
				columns.removeAt(i);
			}else{
				var c={header:column.header,field:column.field };
				columns[i]=c;
			}
		}
		columns=nui.encode(columns);
		data=nui.encode(data);
	    var url="<%=request.getContextPath()%>/util/exportExcel/exportExcel.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data;
		window.location.replace(encodeURI(url));
		 
		//设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
	    setMask();
	} 
	
    </script>
</html>
<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>

  <%@include file="/coframe/tools/skins/common.jsp" %>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<%@page import="com.eos.foundation.database.DatabaseExt"%>
<%@page import="java.util.HashMap"%>
 <script type="text/javascript" src="<%=request.getContextPath() %>/files/process/js/swfupload/swfupload.js"></script>
<script type="text/javascript" src="<%=request.getContextPath() %>/files/process/js/swfupload/handlers.js"></script>
<script type="text/javascript" src="<%=request.getContextPath() %>/files/commmodel_pro/js/stream-v1.js"></script> 
<title>流程申请</title>
 <link href="<%=request.getContextPath() %>/files/commmodel_pro/css/stream-v1.css" rel="stylesheet" type="text/css">
</head>
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
	Long workItemID = (Long)request.getAttribute("workItemID");
 %>

 <style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style> 
<body>
<div align="center" >
  <div  style="padding-top:5px;">
  <fieldset style="width:1000px;border:solid 1px #aaa;position:relative;margin:0px 2px 0px 2px;" >
    	 <legend>档案借阅流程申请</legend>
  <form  id="form1" > 
    
                <!-- hidden域 -->
                <input class="nui-hidden" name="map.APPLY_ID" id="map.APPLY_ID"/>
			    <input class="nui-hidden" name="map.apply_empid" />
			    <input class="nui-hidden" name="map.apply_orgid" />
			    <input class="nui-hidden" name="map.processInstID" id="processinstid"/>
			    <input class="nui-hidden" name="workItemID"/>
			    <input class="nui-hidden" name="map.APPLY_EMPID" />
			    <input id="processInstID" class="nui-hidden" name="map.PROCESSINSTID"/>
			    <input class="nui-hidden" name="map.ACTIVITYDEFID" id="map.ACTIVITYDEFID"/>
			   
<input id="template_path" class="nui-hidden"/>

    <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
  
     <tr>
        
        <th class="nui-form-label" style="width:12.5%"><label for="map.type$text">标题：</label></th>
        <td colspan="3"  style="width:30%">
             <input id="map.TITLE" class="nui-textbox " name="map.TITLE"  required="true" style="width:300px;"/>
        </td>

    <th class="nui-form-label" style="width:12.5%"><label for="map.type$text"></label></th>
        <td colspan="3"  style="width:30%">
      </td>
    
    
      </tr>
     
      <tr >
      
     <th class="nui-form-label" style="width:12.5%"><label for="map.device_name_id$text">申请人：</label></th>
        <td colspan="3"  style="width:30%">
          <input id="map.APPLY_EMPID" class="nui-textbox asLabel " name="map.APPLY_EMPID" readOnly="true" />
        </td>

    
      <th class="nui-form-label" style="width:12.5%"><label for="map.device_name_id$text">申请人所在机构：</label></th>
        <td colspan="3"  style="width:30%">
          <input id="map.APPLY_ORGID" class="nui-textbox asLabel " name="map.APPLY_ORGID" readOnly="true" />
        </td>

    
    
      </tr>
      
      <tr>
        
        

    
     <th class="nui-form-label" ><label for="map.type$text">联系电话：</label></th>
        <td colspan="3"  >
           <input id="map.PHONE_NUM" class="nui-textbox nui-form-input" name="map.PHONE_NUM" />
      </td>
      
      
      <th class="nui-form-label"><label for="map.brandmodel_id$text">申请时间：</label></th>
        <td colspan="3"  > 
           <input id="APPLY_TIME" class="nui-datepicker nui-form-input" name="map.APPLY_TIME"  allowInput="false" required="true" format="yyyyMMdd" />
        </td>
      
      <th class="nui-form-label" style="display:none" ><label for="map.type$text">申请流程：</label></th>
        <td colspan="3"  style="display:none" >
              <input id="map.APPLY_PROCESS" class="nui-dictcombobox nui-form-input" name="map.APPLY_PROCESS"  emptyText="请选择"
          valueField="dictID" textField="dictName" dictTypeId="APPLY_PROCESS" showNullItem="true" nullItemText="请选择" required="true" value="flow_commmodel" />
        </td>
    
    
      </tr>
      
      <tr >
       
         
        <!--  <th class="nui-form-label"><label for="map.brandmodel_id$text">申请时间：</label></th>
        <td colspan="3"  > 
           <input id="APPLY_TIME" class="nui-datepicker nui-form-input" name="map.APPLY_TIME"  allowInput="false" required="true" format="yyyyMMdd" />
        </td> -->
       
        <th class="nui-form-label"><label for="map.device_serial$text">申请内容：</label></th>
        <td colspan="7"  >
          <input id="map.APPLY_REMARK" class="nui-textarea nui-form-input" name="map.APPLY_REMARK" />
        </td>

       
      </tr>
      
      	<tr>
		      	<th class="nui-form-label"><label for="inputData.type$text">附件上传：</label></th>
		      	<td colspan="7">
					<input type="button" class="btn btn-default" id="i_select_files"  value="添加文件"/>
	        	</td>
	      	</tr>
	      	<tr align="center">
	      		<th class="nui-form-label"><label for="inputData.type$text">上传进度：</label></th>
	      		<td colspan="7">
					<!-- 回显进度 -->
					<div id="i_stream_files_queue" ></div>
	      		</td>
	      	</tr>
      
      <tr align="center">
	      		<th class="nui-form-label"><label for="inputData.type$text">原有附件：</label></th>
	      		<td colspan="7">
					<div id="listbox1" class="nui-listbox" style="width:100%;height:80px;" textField="AFFILIATED_NAME" valueField="AFFILIATED_ID" 
		           dataField="resultList"  onvaluechanged="onListBoxValueChanged">
		            </div>
	      		</td>
	      	</tr>
	      	
	      	
	      	
	  <tr >
      
     <th class="nui-form-label" ><label for="map.device_name_id$text">模板下载：</label></th>
        <td colspan="2"  >
        <input id="businessLine" class="nui-combobox" required="true" valueField="BUSINESS_LINE" textField="DICTNAME" style="width:100%;" dataField="resultList"
	       url="com.gotop.xmzg.files.process.getProcessDownMode.biz.ext?type=2"  name="map.BUSINESS_LINE" emptyText="请选择.."  onValueChanged="businessLineChanged"/>
						
        </td>

    
      <td  colspan="2">
      <input class="nui-combobox" required="true" valueField="ID" textField="FILE_NAME" style="width:100%;"
	          					name="map/FILE_ID" emptyText="请选择.." id="fileName" dataField="resultList" onValueChanged="fileNameChanged"/>
      </td>
        <td colspan="2"  >
          <a class="nui-button" iconCls="icon-download" onclick="downloadTemplate()">模版下载</a>
        </td>

    
    
      </tr>    	
      
    
      
      <tr >
      
      <th class="nui-form-label"><label for="map.device_serial$text">操作处理选择：</label></th>
        <td colspan="3"  >
            <input id="aaa" name="map.ISCONTINUE" class="nui-radiobuttonlist" textField="LINE_NAME" valueField="END_NODEID" required="true"/>
        </td>
      
        <th class="nui-form-label"><label for="map.device_serial$text">下一步处理人：</label></th>
        <td colspan="3"  >

        <input id="btnEdit1" name = "map.EMPIDS"  class="nui-textboxlist"   allowInput="false" required="true"  style="width:300px;"/><br/>  
         <a href="#" onclick="EmponButtonEdit()" style="color:blue;text-decoration:underline;">人员选择</a>    
         <a href="#" onclick="cleanEmp()" style="color:blue;text-decoration:underline;">清空</a>
        
        </td>
        
        
        
        
      </tr>
      
    

      
      
    </table>
    </fieldset>
    
        </fieldset>
         
          <%
          
			Object[] obj=DatabaseExt.queryByNamedSql("default", "com.gotop.xmzg.files.process.queryProcessSpDetil",workItemID);
			for(int i=0;i<obj.length;i++)
			{
			HashMap result=(HashMap) obj[i];
		 %>
         
         <fieldset style="width:800px;border:solid 1px #aaa;position:relative;margin:5px 2px 0px 2px;" >
    	 <legend><%=result.get("ACTIVITYINSTNAME") %></legend>
    	 <div align="left" >
    	 <div id="dataform3" style="padding-top:0px;">
    	
    	 <table style="width:100%;height:95%;table-layout:fixed;" class="nui-form-table">
    	   <tr>
			        <th class="nui-form-label"  >审核说明：</th>
			        <td style="text-align:left;" colspan="3">
			            <%-- <%=result.get("APPROVAL_REMARK") %> --%>
			             <%
                     
                     if(result.get("APPROVAL_REMARK")!=null)
                     {
                     
                     result.put("AUDIT", result.get("APPROVAL_REMARK"));
                    
                     }else
                     {
                      result.put("AUDIT", "");
                     }
                     
                     %>
			          <%=result.get("AUDIT") %>
			        </td>
			      </tr>
    	   <tr>
			        <th class="nui-form-label" style="width:15%;">处理人：</th>
			        <td style="text-align:left;width:35%;" >
			            <%=result.get("EMPNAME") %>
			        </td>

			        <th class="nui-form-label"  style="width:15%;" >审批人所在机构：</th>
			        <td style="text-align:left;width:35%;">
			            <%=result.get("ORGNAME") %>
			        </td>
			      </tr>
			      <tr>
			        <th class="nui-form-label" >处理时间：</th>
			        <td style="text-align:left;" >
			            <%=result.get("APPROVAL_TIME") %>
			        </td>
		
			        <th class="nui-form-label" >操作处理选择：</th>
			        <td style="text-align:left;" >
			          <%=result.get("RESULT_AUDIT") %>
			        </td>
			      </tr>
			       <tr>
			        <th class="nui-form-label"  >下一步处理人：</th>
			        <td style="text-align:left;" colspan="3">
			          <%-- <%=result.get("NEXT_DEAL_EMPNAME") %> --%>
			          <%
                     
                     if(result.get("NEXT_DEAL_EMPNAME")!=null)
                     {
                     
                     result.put("NDEAL_EMPNAME", result.get("NEXT_DEAL_EMPNAME"));
                    
                     }else
                     {
                      result.put("NDEAL_EMPNAME", "");
                     }
                     
                     %>
			          <%=result.get("NDEAL_EMPNAME") %>
			        </td>
			      </tr>
			      
			    </table>
    	
        </div>
       </div>
      </fieldset> 
         <%
			}
		 %> 
    
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-ok" onclick="onOk">提交</a>
	          <span style="display:inline-block;width:25px;"></span>
	          <a id="cancel" class="nui-button" iconCls="icon-cancel" onclick="onCancel" style="display:none">返回</a>
	           <span style="display:inline-block;width:25px;"></span> 
	          <a id="banjie" class="nui-button" iconCls="icon-cancel" onclick="banjie()" style="display:none">流程办结</a>
	        </td>
	      </tr>
	    </table>
	 </div>
	 </form>
  </div>
</div>
  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    
    var  username="<%=userObject.getUserRealName()%>";
    nui.get("map.APPLY_EMPID").setValue(username);
    
    var  userid="<%=userObject.getUserId()%>";
    
    var  userorgname="<%=userObject.getUserOrgName()%>";
    nui.get("map.APPLY_ORGID").setValue(userorgname);
    
  // nui.get("map.TITLE").setValue("信贷档案申请-"+username);
    
     var workItemID = <%=workItemID %>;
   
    
     var listbox1 = nui.get("listbox1");
     var con_id;
     
     var arr =[];
     
     
     

    if(workItemID != null){
    //初始化加载数据 
	    $.ajax({
		        url:"com.gotop.xmzg.files.process.queryProcessApplyDetil.biz.ext",
		        type:'POST',
		        data:'workItemID='+workItemID,
		        cache:false,
		        async:false,
        		dataType:'json',
		        success:function(text){
		        debugger;
		          var map = nui.decode(text);
		          
		          form.setData(map);
		          
		          form.setChanged(false);
		          
		           nui.get("map.APPLY_EMPID").setValue(username);
		           nui.get("map.APPLY_ORGID").setValue(userorgname);
		           
		           
		         //  nui.get("map.EMPNAMES").setValue(map.map.DEAL_EMPNAME);
		           
		           con_id=map.map.APPLY_ID;
		           
				 listbox1.load("com.gotop.xmzg.files.process.queryAttachment.biz.ext?con_id="+con_id);
				 if(listbox1.getCount()==0){
				 	listbox1.setVisible(false);
				 }
		          
		          /*  nui.get("btnEdit1").setValue(map.map.DEAL_EMPID);
		           nui.get("btnEdit1").setText(map.map.DEAL_EMPNAME); */
		           
		           
		        }
		      });
		 //将隐藏的审核信息打开    
		// document.getElementById("autho").style.display = ""; 
		 document.getElementById("banjie").style.display = "";
		 document.getElementById("cancel").style.display = "";
		// document.getElementById("cancel").style.display = "none"; 
   }else
   {
      nui.get("map.ACTIVITYDEFID").setValue("manualActivity");
      
      //自动加载电话号码
       $.ajax({
		        url:"com.gotop.xmzg.files.process.getPhoneNum.biz.ext",
		        type:'POST',
		        data:'userid='+userid,
		        cache:false,
		        async:false,
        		dataType:'json',
		        success:function(text){
		          var mapList = nui.decode(text);
		           nui.get("map.PHONE_NUM").setValue(mapList.mapList.MOBILENO);	           
		           
		        }
		      });
      
   } 
     
     
     
    var files=new Array();
	//stream 插件配置
	var config = {
		browseFileId : "i_select_files", /** 选择文件的ID, 默认: i_select_files */
		browseFileBtn : "<div>请选择文件</div>", /** 显示选择文件的样式, 默认: `<div>请选择文件</div>` */
		dragAndDropArea: "i_select_files", /** 拖拽上传区域，Id（字符类型"i_select_files"）或者DOM对象, 默认: `i_select_files` */
		dragAndDropTips: "<span>把文件(文件夹)拖拽到这里</span>", /** 拖拽提示, 默认: `<span>把文件(文件夹)拖拽到这里</span>` */
		filesQueueId : "i_stream_files_queue", /** 文件上传容器的ID, 默认: i_stream_files_queue */
		filesQueueHeight : 200, /** 文件上传容器的高度（px）, 默认: 450 */
		messagerId : "i_stream_message_container", /** 消息显示容器的ID, 默认: i_stream_message_container */
		multipleFiles: true, /** 多个文件一起上传, 默认: false */
		onRepeatedFile: function(f) {
			alert("文件："+f.name +" 大小："+f.size + " 已存在于上传队列中。");
			return false;	
		},
//		autoUploading: false, /** 选择文件后是否自动上传, 默认: true */
//		autoRemoveCompleted : true, /** 是否自动删除容器中已上传完毕的文件, 默认: false */
//		maxSize: 104857600//, /** 单个文件的最大大小，默认:2G */
//		retryCount : 5, /** HTML5上传失败的重试次数 */
//		postVarsPerFile : { /** 上传文件时传入的参数，默认: {} */
//			param1: "val1",
//			param2: "val2"
//		},
		swfURL : "/swf/FlashUploader.swf" ,/** SWF文件的位置 */
		tokenURL : "<%=request.getContextPath()%>/tk", /** 根据文件名、大小等信息获取Token的URI（用于生成断点续传、跨域的令牌） */
		frmUploadURL : "<%=request.getContextPath()%>/fd", /** Flash上传的URI */
		uploadURL : "<%=request.getContextPath()%>/upload" ,/** HTML5上传的URI */
		filesQueueHeight :100,
//		simLimit: 200, /** 单次最大上传文件个数, */
//		extFilters: [".txt", ".rpm", ".rmvb", ".gz", ".rar", ".zip", ".avi", ".mkv", ".mp3"], /** 允许的文件扩展名, 默认: [] */
//		onSelect: function(list) {alert('onSelect')}, /** 选择文件后的响应事件 */
//		onMaxSizeExceed: function(size, limited, name) {alert('onMaxSizeExceed')}, /** 文件大小超出的响应事件 */
//		onFileCountExceed: function(selected, limit) {alert('onFileCountExceed')}, /** 文件数量超出的响应事件 */
//		onExtNameMismatch: function(name, filters) {alert('onExtNameMismatch')}, /** 文件的扩展名不匹配的响应事件 */
//		onCancel : function(file) {alert('Canceled:  ' + file.name)}, /** 取消上传文件的响应事件 */
		onComplete: function(file) {
			//alert(file.name);
			files.push(file);
			console.log(files);
		
		} /** 单个文件上传完毕的响应事件 */
//		onQueueComplete: function() {alert('onQueueComplete')} /** 所以文件上传完毕的响应事件 */
//		onUploadError: function(status, msg) {alert('onUploadError')} /** 文件上传出错的响应事件 */
//		onDestroy: function() {alert('onDestroy')} /** 文件上传出错的响应事件 */
	};
	//启动stream
	var _t = new Stream(config);
    
    
    //关闭添加窗口
 	function CloseWindow(action){
 		if((action == 'cancel'||action == 'close') && files.length != 0){
 			var data = {};
 			data.files = files;
 			var json = nui.encode(data);
        $.ajax({
                url:"com.gotop.xmzg.files.fileList.delTempFiles.biz.ext",
                type:'POST',
                data:json,
                cache:false,
                async:false,
                contentType:'text/json',
                success:function(text){
                   var returnJson = nui.decode(text);
                   if(returnJson.exception == null && returnJson.flag == 1){
                        files.splice(0, files.length);//文件上传成功清空文件对象数组避免重复提交
                     }else{
                         alert("临时文件删除失败！");
                        }
                   }
             });
 		}
  		if(window.CloseOwnerWindow) 
    		return window.CloseOwnerWindow(action);
  		else
    		return window.close();
    }
    
     
 
    
      var ACTIVITYDEFID=nui.get("map.ACTIVITYDEFID").getValue();
       var PROCESS_NAME= nui.get("map.APPLY_PROCESS").getValue();//所属流程
    //加载流程走向
            $.ajax({
						url : "com.gotop.xmzg.files.process.queryLine.biz.ext",
						type : 'POST',
						data : 'ACTIVITYDEFID='+ACTIVITYDEFID+"&PROCESS_NAME="+PROCESS_NAME,
						cache : false,
						async : false,
						dataType : 'json',
						success : function(text) {
                         var obj = nui.decode(text.resultList);
                         nui.get("aaa").load(obj);
						}
					});
    
    
     var res = nui.get("aaa");
		    res.on("valuechanged", function (e) {
		    
		     nui.get("btnEdit1").setValue("");
		     nui.get("btnEdit1").setText("");
		     
		    }); 

    
    form.setChanged(false);
    
    function onOk(){
      saveData();
    }
   
    
    
    //机构树回显
     function EmponButtonEdit() {
     

          //  var ACTIVITYDEFID = nui.get("map.ACTIVITYDEFID").getValue();   //活动节点id
          
            var ACTIVITYDEFID = nui.get("aaa").getValue();   //活动节点id
            var PROCESS_NAME= nui.get("map.APPLY_PROCESS").getValue();//所属流程       
            if(PROCESS_NAME =="" ||PROCESS_NAME==null)
            {
                 alert("请先选择所属流程"); 
                 return false; 
            }
              
            var APPLY_ORGID= "<%=userObject.getUserOrgId()%>";  //申请人所在机构id
            var PROCESSINSTID=nui.get("processInstID").getValue();
     
            var btnEdit1 = nui.get("btnEdit1");
            nui.open({
                url:"<%=request.getContextPath() %>/files/process/SpEmpAndOrg_tree.jsp?ACTIVITYDEFID="+ACTIVITYDEFID+"&PROCESS_NAME="+PROCESS_NAME+"&APPLY_ORGID="+APPLY_ORGID+"&PROCESSINSTID="+PROCESSINSTID,
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                onload : function() {
					var iframe = this.getIFrameEl();
					// 给树设置上已选择的节点字符串     			
					iframe.contentWindow.setTreeCheck(btnEdit1.getValue());
				},
                ondestroy : function(action) {
							if (action == "ok") {
								var iframe = this.getIFrameEl();
								var list = iframe.contentWindow.GetData();
								list = nui.clone(list);
								if (list) {
									// 将树选中节点设置到box显示
									putDataTextBox(list);
								}
							}
						}
            });            
             
        }   
        
        /**
		 * 往textboxlist中添加选择的数据
		 * @params list 	 获取Check选中的节点集合
		 */
		function putDataTextBox(list){
			var text = "",value = "";
			var boxObj = nui.get("btnEdit1");
			for (var i = 0; i < list.length; i++) {
				var node = list[i];
				if (node.nodeType == "OrgOrganization") continue;
				if (i == list.length -1) {
					value += node["id"];
					text  += node["text"];
				} else {
					value += node["id"] + ",";
					text  += node["text"] + ",";
				}
			}
			if (value.endsWith(",")) value = strSplitLast(value);
			if (text.endsWith(",")) text = strSplitLast(text);
			boxObj.setValue(value);
			boxObj.setText(text);
			//nui.get("map.EMPNAMES").setValue(text);
		}
					
		//判断当前字符串是否以str结束
	     if (typeof String.prototype.endsWith != 'function') {
	       String.prototype.endsWith = function (str){
	          return this.slice(-str.length) == str;
	       };
	     }
	     
	     /* 切割ID字符串最后一位 */
		function strSplitLast(obj) {
			return (obj).substring(0, obj.length - 1);
		}
    
        
    function cleanEmp(){
		 //nui.get("empids").setValue("");
		 nui.get("btnEdit1").setValue("");
		 nui.get("btnEdit1").setText("");
		// nui.get("map.EMPNAMES").setValue("");
	}
        
        
   
        
        function saveData(){ 

			saveDataFiles();

    }
        
        
        
        

    
    
    function saveDataFiles(){ 
   
      form.validate(); 

        if(form.isValid()==false) return false;
        
        var data = form.getData(true,true);
        
       var EMPNAMES=nui.get("btnEdit1").getText();

        data.map.EMPNAMES=EMPNAMES;
 

        data.workItemID = workItemID; 
        debugger;
        data.files=files;
        data.arr=arr;
        
        var json = nui.encode(data);
         var a= nui.loading("正在提交中,请稍等...","提示");
        $.ajax({
                url:"com.gotop.xmzg.files.commmodel_pro.submitCommmodelPro.biz.ext",
                type:'POST',
                data:json,
                cache:false,
                contentType:'text/json',
                success:function(text){
                nui.hideMessageBox(a); //关闭加载提示（即正在提交中，请稍等）
                   var returnJson = nui.decode(text);
                   if(returnJson.exception == null){
    
                        alert("提交成功");
                        window.history.go(-1) ;
                        window.history.go(-1) ;
                       
                        form.reset();
                          nui.get("map.APPLY_EMPID").setValue(username);
                          nui.get("map.APPLY_ORGID").setValue(userorgname);
                          //nui.get("map.TITLE").setValue("信贷档案申请-"+username);
                          
                        window.history.go(-1) ;
                        
                     }else{
                         alert("提交失败");
                         window.history.go(-1) ;
                        }
                   }
             });



    }
    
    function onCancel(){
      //CloseWindow("cancel");
       window.history.go(-1) ;
       
    }
    
    
    function banjie(){

       form.validate();
     //  if(form.isValid()==false) return false;
        
        var data = form.getData(false,true);

        data.workItemID = workItemID;
        var json = nui.encode(data);
        var a= nui.loading("正在提交中,请稍等...","提示");
        $.ajax({
                url:"com.gotop.xmzg.files.process.submitProcessCancelApply.biz.ext",
                type:'POST',
                data:json,
                cache:false,
                contentType:'text/json',
                success:function(text){
                nui.hideMessageBox(a); //关闭加载提示（即正在提交中，请稍等）
                   var returnJson = nui.decode(text);
                   if(returnJson.exception == null){
                        alert("流程办结成功");
                        onCancel();
                         window.history.go(-1) ;
                         
                     }else{
                         alert("流程办结失败");
                        window.history.go(-1) ;
                        }
                   }
             });
      
    }
    
    function CloseWindow(action){
 
     var flag = form.isChanged();
       if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
    
     function onListBoxValueChanged(e) {
	            var listbox = e.sender;
	            var value = listbox.getValue();
	            var items=listbox.getSelecteds();
	            var filepath=items[0].AFFILIATED_ADDRESS;
	            var jsontemp=listbox.getData();
	            nui.confirm("确定删除该附件？","系统提示",function(action){
           			// var json = nui.encode({AFFILIATED_ID:value,AFFILIATED_ADDRESS:filepath});
           			 var json = {AFFILIATED_ID:value,AFFILIATED_ADDRESS:filepath};
		             if(action=="ok"){ 
		             	arr.push(json);
		             	//arr.push("AFFILIATED_ID",AFFILIATED_ID);
		             	//arr.push("AFFILIATED_ADDRESS",AFFILIATED_ADDRESS);
		             	
				        listbox.removeItems(items);
				     } else{
					     listbox.load(jsontemp);
				     }
		            
				   });
	        }
	        
	        
	        function businessLineChanged(e){
	        debugger;
    		//var business_line = String(e.value);
    		var business_line = "10";
    		var url = "com.gotop.xmzg.files.commmodel_pro.getFileNames.biz.ext?business_line=" + business_line;
    		nui.get("fileName").load(url);
    		//nui.get("BUSINESSLINE").setValue(business_line);
    	}
    	
    	function fileNameChanged(e){
    		//var filesType = getFileType(e.text);
    		//nui.get("fileType").setValue(filesType[0].DICTID);
    		var fileNameId = e.value;
    		var data = e.sender.data;
    		var operation_cycle = null;
    		for(var i=0;i<data.length;i++){
    			if(data[i].ID == fileNameId){
    				nui.get("template_path").setValue(data[i].TEMPLATE_PATH);
    			
    				}
    				
    			}
    		}
    
    	
    	function downloadTemplate(){
    	debugger;
    		var file_name = nui.get("fileName").getText();
    		var file_path = nui.get("template_path").getValue();
    		file_name = file_name + file_path.substring(file_path.lastIndexOf("."));
    		window.location.href = "<%=request.getContextPath() %>/fileImportOrExport/fileExport/download.jsp?file_name=" + file_name + "&file_path=" + file_path;
    	}
 
  </script>
</body>
</html>
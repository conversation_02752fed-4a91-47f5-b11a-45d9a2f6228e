<%@page import="org.apache.commons.lang.StringUtils"%>
<%@page import="com.eos.access.http.security.config.HttpSecurityConfig"%>
<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page import="com.primeton.cap.AppUserManager"%>
<html>
<!-- 
  - Author(s): wj
  - Date: 2018-08-22 16:22:15
  - Description:密码必须是英文和数字组合
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp"%>
<title>修改密码</title>
<%
   String url = null;
   HttpSecurityConfig securityConfig = new HttpSecurityConfig();
   boolean isOpenSecurity = securityConfig.isOpenSecurity();
   if(isOpenSecurity){
   		boolean isAllInHttps = securityConfig.isAllInHttps();
   		if(!isAllInHttps){
   			String ip = securityConfig.getHost();
   			String https_port = securityConfig.getHttps_port();
   			url = "https://" + ip + ":" + https_port + request.getContextPath() + "/coframe/rights/user/org.gocom.components.coframe.rights.user.cipher.update_password.flow";
   		}else{
   			url = "org.gocom.components.coframe.rights.user.cipher.update_password.flow"; 
   		}
   }else{
   		url = "org.gocom.components.coframe.rights.user.cipher.update_password.flow";
   }
 %>
</head>
<body>
<div style="padding-top:5px;">
	<form method="post" id = "form1"	name="UpdateForm" action="<%=url%>"  onsubmit="return checkFormSelf(this);">
		<input id="operatorId" class="nui-hidden" name="user/operatorId" />
		<input class="nui-hidden" id="createtime" name="user/createtime" />
		<input id="userId" class="nui-hidden" name="user/userId" value="<%=AppUserManager.getCurrentUserId() %>"/>
		<table class="nui-form-table" style="width:100%;">
	      <tr class="nui-hidden">
	        <th class="nui-form-label" style="width:110px;"><label for="user.userId$text">当前用户：</label></th>
	        <td>
	          <input   id="currentUser" class="nui-textbox nui-form-input" value="<%=AppUserManager.getCurrentUserId() %>" enabled="false" />
	        </td>
	      </tr>
	      <tr class="odd">
	        <th class="nui-form-label"><label for="password$text">输入旧密码：</label></th>
	        <td>
	          <input id="password" class="nui-password nui-form-input" name="password" required="true" onvalidation="onCheckRight"/>
	        </td>
	      </tr>
	      <tr>
	        <th class="nui-form-label"><label for="pwd1$text">输入新密码：</label></th>
	        <td>
	          <input class="nui-password nui-form-input" id="pwd1" required="true" vtype="english;minLength:8;maxLength:30" onvalidation="onCheckNewAndOld"/>
	        </td>
	      </tr>
	      <tr class="odd">
	        <th class="nui-form-label"><label for="pwd2$text">确定新密码：</label></th>
	        <td>
	          <input class="nui-password nui-form-input" id="pwd2" name="user/password" required="true" onvalidation="onCheckEqual"/>
	        </td>
	      </tr>
	      <tr class="odd">
				<td colspan="2" align="left">
				   <font style="margin-left:20px;color:red;">
				        *密码必须包含大写字母、小写字母、数字和特殊字符,至少8位
				   </font>	
				 <br>  
					<font style="margin-left:20px;color:red;">
	                        *新密码与旧密码不能一样 
	                </font>	
				</td>	
			</tr>
	    </table>
	    <div class="nui-toolbar" style="border:0;padding:0px;">
		   <table width="100%">
		      <tr>
		        <td style="text-align:center;">
		          <a class="nui-button" iconCls="icon-save" onclick="save">保存</a>
              <span style="display:inline-block;width:25px;"></span>
              <a class="nui-button" iconCls="icon-cancel" onclick="cancel">取消</a>
		        </td>
		      </tr>
		   </table>
		</div>
	</form>
</div>

<script type="text/javascript">    
    nui.parse();
    var form = new nui.Form("#form1");
    
    //自定义vtype
     //nui.VTypes["englishErrorText"] = "密码必须由大写和小写以及数字组成";
     nui.VTypes["englishErrorText"] = "密码必须包含大写字母、小写字母、数字和特殊字符,至少8位";
        nui.VTypes["english"] = function (v) {
           // var re = new RegExp("^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{8,30}$");
            var re = new RegExp("^(?![A-Za-z0-9]+$)(?![a-z0-9\\W]+$)(?![A-Za-z\\W]+$)(?![A-Z0-9\\W]+$)[a-zA-Z0-9\\W]{8,20}$");
            if (re.test(v)) return true;
            return false; 
        }

    function save () {
      //var form = document.getElementById("form1");
      //form.submit();
      nui.get("createtime").setValue(new Date()); 
      form.validate();
      
      var pwd1 = nui.get("pwd1").value;
      var pwd2 = nui.get("pwd2").value;
     
      if(pwd1 != pwd2){
      	alert("确认密码与新密码不一致");
      	return false;
      }
      
      
      if(form.isValid()==false) return;
      
      var data = form.getData(false,true);
      var json = nui.encode(data);
      
      $.ajax({
        url:"org.gocom.components.coframe.rights.UserManager.saveUptPwd.biz.ext",
        type:'POST',
        data:json,
        cache:false,
        contentType:'text/json',
        success:function(text){
            var returnJson = nui.decode(text);
			if(returnJson.exception == null){
				if(returnJson.retValue == "false"){
					nui.alert("旧密码填写错误！");
				}else{
					
				var OPE_MOD = "修改密码";  
				var OPE_CONTENT = "修改密码";
		        var json = nui.encode({OPE_MOD:OPE_MOD,OPE_CONTENT:OPE_CONTENT});
			    $.ajax({
			        url:"org.gocom.components.coframe.userInfoManage.userInfoManage.addSysLog.biz.ext",
			        type:'POST',
			        data:json,
			        cache:false,
			        contentType:'text/json'
			      });
					
					nui.alert("密码修改成功！", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						window.CloseOwnerWindow();
					}
				});
					
					//form.clear();
				}
				
			}else{
				nui.alert("密码修改失败!");
			}
        }
      });
      
    }

     

    <%-- <%
    	Object result = request.getAttribute("retValue");
    	String retValue = "";
     	if(result != null){
     		retValue = (String)result;
     		if(retValue.equals("true")){
     			out.println("setSuccess()");
     		}else if(retValue.equals("false")){
				out.println("setError()");  
     		}
     	}
     %> --%>
	function setError(){
    	window.alert("原密码填写错误！");
    }    
    function setSuccess(){
    	window.alert("密码修改成功！");
    	//CloseWindow("SaveSuccess");
    }
    function cancel(){
      CloseWindow("cancel");
    }
    
    function onCheckRight(e){
      if(e.isValid){
       // if(retValue=="false"){
        //   e.errorText = "密码不正确";
        //   e.isValid = false;
       // }
      }
    }
    
    function onCheckNewAndOld(e){
      if(e.isValid){
        var pwd = nui.get("password").value;
        if(e.value==pwd){
          e.errorText = "新密码与旧密码不能一样";
          e.isValid = false;
        }
      }
    }
  
    function onCheckEqual(e){
      if(e.isValid){
        var pwd = nui.get("pwd1").value;
        if(e.value!=pwd){
          e.errorText = "确认密码与新密码不一致";
          e.isValid = false;
        }
      }
    }
    
    function checkFormSelf(form1){
 		form.validate();
      	if (form.isValid() == true) {
      		return true;
      	}
    	return false;
    }
    
    function CloseWindow(action){
      if(action=="close" && form.isChanged()){
        if(confirm("数据已改变,是否先保存?")){
          return false;
        }
      }else if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
  </script>
</body>
</html>

<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): xwl
  - Date: 2023-05-05 11:26:28
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>汽车分起商户引荐人信息维护</title>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <table class="table" style="width:100%;">
			<tr>	
				<th  class="tit">推广机构：</th>
				<td >
				    <input id="queryData.TJQ_ORG_CODE" name = "queryData.TJQ_ORG_CODE"  class="nui-buttonedit" allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;" />
				</td>
				<th  class="tit">推荐人姓名：</th>
				<td >
					<input id="TJQ_YJ_EMP_NAME" name = "queryData.TJQ_YJ_EMP_NAME" class="nui-textbox" style="width:150px;" />  
				</td>
				
				<th>
				  <a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;
				  <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
				</th>
			</tr>	
			<tr>	
				<th  class="tit">商户号：</th>
				<td >
					<input id="TJQ_MERCH_CODE" name = "queryData.TJQ_MERCH_CODE" class="nui-textbox" style="width:150px;" />  
				</td>
				<th  class="tit">商户名称：</th>
				<td >
					<input id="TJQ_MERCH_NAME" name = "queryData.TJQ_MERCH_NAME" class="nui-textbox" style="width:150px;" />  
				</td>
			</tr>
	    </table>
	  </div>
    </div>
  </div>
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
             <a class="nui-button" iconCls="icon-add" onclick="add">增加</a>
		     <a class="nui-button" iconCls="icon-edit" onclick="update">修改</a>
		     <a class="nui-button" iconCls="icon-remove" onclick="remove">删除</a>
           </td>
        </tr>
      </table>
    </div>
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;"
	  url="com.gotop.xmzg.achieve.config.qcfqyjr_list.biz.ext" multiSelect="true"
	  sizeList=[5,10,20,50,100]  pageSize="10">
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      <div field="TJQ_ORG_CODE" headerAlign="center" align="center">推广机构</div>
	      <div field="TJQ_ORG_NAME" headerAlign="center" align="center">机构名称</div>
	      <div field="TJQ_MERCH_CODE" headerAlign="center" align="center">商户号</div>
	      <div field="TJQ_MERCH_NAME" headerAlign="center" align="center">商户名称</div>
	      <div field="TJQ_YJ_ORG_NAME" headerAlign="center" align="center">推荐人机构名称</div>
	      <div field="TJQ_YJ_EMP_NAME" headerAlign="center" align="center">推荐人姓名</div>
	      <div field="TJQ_YJ_EMP_CODE" headerAlign="center" align="center" >推荐人工号</div>
	      <!-- <div field="TJQ_UPDATE_TIME" headerAlign="center" align="center" >修改时间</div> -->
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">  
    nui.parse();
 	
    var grid = nui.get("datagrid1");
    var form = new nui.Form("#form1");
    search();
    
    function search(){
       form.validate();
       if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
    
    //机构树点击事件
	function OrgonButtonEdit(e) {	
        var btnEdit = this;
        nui.open({
            url:"<%=request.getContextPath() %>/achieve/report/org_tree.jsp",
        showMaxButton: false,
        title: "选择树",
        width: 350,
        height: 350,
        ondestroy: function (action) {                    
            if (action == "ok") {
                var iframe = this.getIFrameEl();
                var data = iframe.contentWindow.GetData();
                data = nui.clone(data);
                if (data) {
                    btnEdit.setValue(data.CODE);
                    btnEdit.setText(data.TEXT);
                    var form = new nui.Form("#form1");
       				form.validate();
                    
                }
            }
        }
    	});            
	}
    
    function add(){
      nui.open({
	          url:"<%=request.getContextPath() %>/achieve/config/qcfqyjr_add.jsp",
	          title:'新增',
	          width:500,
	          height:350,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             var data = {pageType:"add"};
                  iframe.contentWindow.setData(data);
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	   });

    }
    
    function update(){
      var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
	       nui.open({
	          url:"<%=request.getContextPath() %>/achieve/config/qcfqyjr_form.jsp",
	          title:'编辑',
	          width:500,
	          height:350,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             var data = {pageType:"edit",record:{map:rows[0]}};
                  iframe.contentWindow.setData(data);
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }
    }
    
    function remove(){
      var rows = grid.getSelecteds();
     if(rows.length > 0){
         nui.confirm("确定删除选中记录？","系统提示",
           function(action){
             if(action=="ok"){
	           var json = nui.encode({maps:rows});
	           var a= nui.loading("正在执行中,请稍等...","提示");
		        $.ajax({
		          url:"com.gotop.xmzg.achieve.config.qcfqyjr_del.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	nui.hideMessageBox(a);
		            var result = text.result;
					if(result !=null && result.code==1){
						nui.alert(result.msg, "系统提示", function(action){
							grid.reload();
						});
					}else{
						nui.alert(result.msg, "系统提示");
						grid.unmask();
					}
		          }
		        });
		     }
		   });
      }else{
        nui.alert("请选中一条记录！");
      }
    }
    
  
 
  	function CloseWindow(action){
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
  
   	//重置
    function clean(){
	   form.reset();
    }
 
  </script>
</body>
</html>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2019-06-04 14:51:16
  - Description:
-->
<head>
	<title>人员岗位查询</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%@include file="../init.jsp"%>
</head>

<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="send_bw">
	    <table class="table" style="width:100%;">
	        <tr>
		        <th class="tit">人员姓名：</th> 
		        <td><input class="nui-textbox" id=empname name="obj.empname"/></td>
		        <th class="tit">岗位名称：</th> 
		        <td><input class="nui-dictcombobox" valueField="dictID" textField="dictName" id="JF_POSITION" name="obj.JF_POSITION" dictTypeId="JF_POSITION"/></td>
				<th><a class="nui-button"  iconCls="icon-search" onclick="search">查询</a></th>
				<th><a class="nui-button" iconCls="icon-reset"  onclick="clean">重置</a></th>
		    </tr>
	    </table>
	  </div>
    </div>
  </div>
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
             <a class="nui-button" iconCls="icon-add" onclick="add">新增</a>
             <a class="nui-button" iconCls="icon-edit" onclick="update">修改</a>
             <a class="nui-button" iconCls="icon-remove" onclick="del">删除</a>
             <a class="nui-button" iconCls="icon-upload"  onclick="importData">导入</a>
             <a class="nui-button" iconCls="icon-download"  onclick="exportData">导出 </a>
             <a class="nui-button" iconCls="icon-download"  onclick="downloadTemplate">下载导入模板 </a>
           </td>
        </tr>
      </table>
    </div>

  <div class="nui-fit">
	 	 <div id="bw_grid" class="nui-datagrid"
			 style="height: 100%;"
			 idField="id" 
			 multiSelect="true"  
			 pageSize="20"
			 sizeList=[5,10,20,50,100]
	         dataField="list"
			 url="com.gotop.xmzg.achieve.personnel.personnel_list.biz.ext"
			 >
		
		 <div field="TE_POSITION_OLD" headerAlign="center" align="center" allowSort="true">原岗位编号</div>
	      <div field="TE_ORGCODE_OLD" headerAlign="center" align="center" allowSort="true">原机构号</div> 
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      <div field="TE_EMPCODE" headerAlign="center" align="center" allowSort="true" >员工CODE</div>
		  <div field="TE_POSITION" headerAlign="center" align="center" allowSort="true" >岗位编号</div>
		  <div field="DICTNAME" headerAlign="center" align="center" allowSort="true" >岗位名称</div>
		  <div field="EMPNAME" headerAlign="center" align="center" allowSort="true" >员工姓名</div>
		  <div field="TE_ORGCODE" headerAlign="center" align="center" allowSort="true" >机构号</div>
		  <div field="ORGNAME" headerAlign="center" align="center" allowSort="true" >机构名称</div>
		  <div field="TE_UPDATE_ORGCODE" headerAlign="center" align="center" allowSort="true">创建机构</div>
		  <div field="TE_UPDATE_EMPCODE" headerAlign="center" align="center" allowSort="true">创建人</div>
		  <div field="TE_UPDATE_TIME" headerAlign="center" align="center" allowSort="true">创建时间</div>
	    </div>
	 </div>
  </div>
  
 <script type="text/javascript">
 
    nui.parse();
    var grid = nui.get("bw_grid");
	var form = new nui.Form("send_bw");
	
	//初始化加载
	$(function(){
		search();
	});
	
	//查询
    function search() {
 		form.validate();
      	if (form.isValid() == false) return;
      	grid.load(form.getData());
	}
	
	//转换时间
    function setdate(e){
 		var date = e.record.TI_CREATETIME;
 		if(!isNullOrEmpty(date) && date.length == 14){
 			return changeDate(date);
 		}else{
 			return "";
 		}
 	}
	
    //打开添加页面
	function add(){
		   //权限验证
	          nui.ajax({
		       url: "com.gotop.xmzg.achieve.personnel.personnel_qry_auth.biz.ext",
                        type: "post",
                        success: function (text) {
                     	   var code = text.code;
                     	   var msg = text.msg;
                     	   var obj = text.obj;
                     	   console.log(code);
                     	   console.log(msg);
                     	   console.log(obj);
                     	  if(code == '2'){
						  nui.alert(msg, "系统提示", function(action){});
						  return;
						}else{
	
			nui.open({
			url:bactpath+"/achieve/personnel/personnel_add.jsp",
			title:"人员岗位新增",
			width:380,
			height:230,
			onload:function(){},
			ondestroy:function(action){
				if(action=="saveSuccess"){
	                grid.reload();
	       	     }
				}
			});
			
						}
		      		}
		    }); 
	}
	
	//打开修改页面
	function update(){
		var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
		
		//权限验证
	          nui.ajax({
		       url: "com.gotop.xmzg.achieve.personnel.personnel_qry_auth.biz.ext",
                        type: "post",
                        success: function (text) {
                     	   var code = text.code;
                     	   var msg = text.msg;
                     	   var obj = text.obj;
                     	   console.log(code);
                     	   console.log(msg);
                     	   console.log(obj);
                     	  if(code == '2'){
						  nui.alert(msg, "系统提示", function(action){});
						  return;
						}else{
						
			nui.open({
			url:bactpath+"/achieve/personnel/personnel_update.jsp",
				title:"人员岗位修改",
				width:380,
				height:230,
				onload:function(){
					var iframe = this.getIFrameEl();
 	      	    	iframe.contentWindow.setData(rows[0]);
				},
				ondestroy:function(action){
					if(action=="saveSuccess"){
		                grid.reload();
		            }
				}
			});
			}
		      		}
		    }); 
		
		}
	}
	
	//重置
	  function clean(){
	   form.clear();
       }
	
	//删除
	function del(){
		var rows = grid.getSelecteds();
		if(rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
		
		//权限验证
	          nui.ajax({
		       url: "com.gotop.xmzg.achieve.personnel.personnel_qry_auth.biz.ext",
                        type: "post",
                        success: function (text) {
                     	   var code = text.code;
                     	   var msg = text.msg;
                     	   var obj = text.obj;
                     	   console.log(code);
                     	   console.log(msg);
                     	   console.log(obj);
                     	  if(code == '2'){
						  nui.alert(msg, "系统提示", function(action){});
						  return;
						}else{
		
			nui.confirm("确定删除以下选择的数据？", "确定？",
	           function (action) {
	             if (action == "ok") {
	            	 var arr = [];
	            	 for(var i = 0;i<rows.length;i++){
	            	  var json = {
      					  "TE_EMPCODE" : rows[i].TE_EMPCODE,
        				  "TE_POSITION" : rows[i].TE_POSITION,
        				  "TE_ORGCODE" : rows[i].TE_ORGCODE
   						 };
	            		 arr.push(json);
	            	 }
	            	 var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
	            	 //提交数据
                     nui.ajax({
                        url: "com.gotop.xmzg.achieve.personnel.personnel_del.biz.ext",
                        type: "post",
                        data: nui.encode({"arr": arr}),
                        contentType:'text/json',
                        success: function (text) {
                           nui.hideMessageBox(load);  //隐藏遮罩层
                     	   var code = text.code;
                     	   var msg = text.msg;
                     	   nui.alert(msg, "系统提示", function(action){});
                     	   grid.reload();
                        }
                    });
	             }
			});
						}
		      		}
		    }); 
		
		
		}
	}
	
	//导出报表
	function exportData(){
	
			//权限验证
	          nui.ajax({
		       url: "com.gotop.xmzg.achieve.personnel.personnel_qry_auth.biz.ext",
                        type: "post",
                        success: function (text) {
                     	   var code = text.code;
                     	   var msg = text.msg;
                     	   var obj = text.obj;
                     	   console.log(code);
                     	   console.log(msg);
                     	   console.log(obj);
                     	  if(code == '2'){
						  nui.alert(msg, "系统提示", function(action){});
						  return;
						}else{
	
						form.validate();
					    if (form.isValid() == false) return;
						var data=form.getData(true, true);
						var fileName="人员岗位报表";
						var queryPath="com.gotop.xmzg.achieve.personnel.personnel_list";
						var columns=grid.getBottomColumns();
						columns=columns.clone();
						for(var i=0;i<columns.length;i++){
							var column=columns[i];
							if(!column.field){
								columns.removeAt(i);
							}else{
								var c={header:column.header,field:column.field };
								columns[i]=c;
							}
						}
						var sheet = "人员岗位报表";
						columns=nui.encode(columns);
						//获取empcode
						data.obj.empcode = obj.emp_code;
						data=nui.encode(data);
					    var url="<%=request.getContextPath()%>/achieve/personnel/exportPersonnel.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data+"&sheet="+sheet;
						window.location.replace(encodeURI(url));
						
						 //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
					     //setMask();
	       }
	      }
	     });
	     
	     }
	     
	     	function downloadTemplate(){
    		var file_name = "人员岗位报表导入模板.xls";
    		 var url="<%=request.getContextPath() %>/achieve/personnel/templateDownload.jsp?filename="+file_name+"&filename2="+file_name;
	      window.location.replace(encodeURI(url));
    	}
	     
	     //导入数据
	 function importData(){
			          nui.ajax({
				       url: "com.gotop.xmzg.achieve.personnel.personnel_qry_auth.biz.ext",
		                        type: "post",
		                        success: function (text) {
		                     	   var code = text.code;
		                     	   var msg = text.msg;
		                     	   var obj = text.obj;
		                     	   console.log(code);
		                     	   console.log(msg);
		                     	   console.log(obj);
		                     	  if(code == '2'){
								  nui.alert(msg, "系统提示", function(action){});
								  return;
								}else{
								     	nui.open({
									            url:"<%=request.getContextPath()%>/achieve/personnel/personPosition_imp.jsp",
									            title: "导入人员岗位数据",
									            width: 550,
									            height: 230,
									            ondestroy: function (action) {
							                		grid.reload();
									            }
									        });
		
									 }
				      			}
				  		 	 }); 
							}
  </script>
</body>

</html>
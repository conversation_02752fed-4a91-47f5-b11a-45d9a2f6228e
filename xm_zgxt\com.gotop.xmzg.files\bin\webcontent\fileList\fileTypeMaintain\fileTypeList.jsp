<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): lyl
  - Date: 2019-11-29
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>档案种类维护</title>
</head>
<body style="margin:0px;width:100%;overflow:hidden;" >
	<div class="search-condition">
		<div class="list">
			<div id="filesForm">
				<div class="nui-hidden"  name="condition.userOrgId" id="userOrgId"></div>
				<div class="nui-hidden"  name="condition.userId"  id="userId" ></div>
		   		<table class="table" style="width:100%;">
		      		<tr>
						<td align="right">档案种类：</td>
						<td>
							<input class="nui-dictcombobox " name="queryData.FILES_TYPE"   dictTypeId="FILES_TYPE" style="width:160px;" emptyText="全部" allowInput="true"/>
						</td>
						<td align="right">档案类别：</td>
						<td>
						 	<input class="nui-dictcombobox" name="queryData.FILES_CATEGORY" dictTypeId="FILE_CATEGORY" style="width:160px;" emptyText="全部" allowInput="true"/>
						</td>
						<td align="right">分区类别：</td>
						<td>
						 	<input class="nui-dictcombobox" name="queryData.PARTITION_CATEGORY" dictTypeId="PARTITION_CATEGORY" style="width:160px;" emptyText="全部" allowInput="true"/>
						</td>
						<td rowspan="12" class="btn-wrap">
							<a class="nui-button" iconCls="icon-search" onclick="searchData();">查询</a>
							<a class="nui-button" iconCls="icon-reset"  onclick="clean()"/>重置</a>
						</td>
					</tr>
		    	</table>
		  	</div>
		</div>
	</div>
	<div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      	<table style="width:100%;">
        	<tr>
           		<td style="width:100%;">
			     	<a id="add" class="nui-button" iconCls="icon-add" onclick="dataInput()" >新增</a>
					<a id="eidt" class="nui-button" iconCls="icon-edit" onclick="dataEdit();">修改</a> 
	             	<a id="del" class="nui-button" iconCls="icon-remove" onclick="dataDelete">删除</a>
           		</td>
        	</tr>
      	</table>
    </div>
    <!-- 档案种类表信息 -->
  	<div class="nui-fit" id="fieldset1">
	 	<div id="grid"  dataField="resultList" idField="dicttypeid" class="nui-datagrid" style="width:100%;height:100%;" 
	  	url="com.gotop.xmzg.files.fileList.queryFileTypeList.biz.ext" sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" >
	    	<div property="columns">
	    		<div type="checkcolumn"></div>
	    		<div field="TYPE_ID" class="nui-hidden" visible="false">ID</div>
		        <div field="FILES_TYPE_NAME" 		headerAlign="center" >档案种类</div>
		        <div field="FILE_CATEGORY_NAME" 	headerAlign="center" >档案类别</div>
		        <div field="PARTITION_CATEGORY_NAME" headerAlign="center">分区类别</div>
		        <div field="MANAGER_ORG_NAME" 		headerAlign="center" >管理部门</div>
		        <div field="REMARK" 			headerAlign="center">说明</div>
		    </div>
	 	</div>
  	</div>
</body>
</html>
<script type="text/javascript">
	nui.parse();
	var form = new nui.Form("filesForm");
	var grid = nui.get("grid");
	//按钮权限
	//isFhOrbm();
	var data = form.getData(true,true);
	grid.load(data);
	
	function searchData(){	
        	var data = form.getData(true,true);
    		grid.load(data);
    	}
    	
    //新增档案种类	
	function dataInput(){
       nui.open({
       	  targetWindow: window,
          url:"<%=request.getContextPath()%>/files/fileList/fileTypeMaintain/fileTypeAdd.jsp",
          title:'新增',
          width:400,
          height:280,
          ondestroy:function(action){
            	 searchData();
          }
       });
    }
    
    function dataEdit(){
    	var row = grid.getSelected();
    /* 	if(rows.length>1){
    		return nui.alert("请只选择一条记录进行编辑！","提示");
    	} */
       	if(row!=null){
		    nui.open({
		    	url:"<%=request.getContextPath()%>/files/fileList/fileTypeMaintain/fileTypeEdit.jsp",
	          	title:'编辑',
	          	width:400,
          		height:280,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            //方法1：后台查询一次，获取信息回显
		            /* var data = row;
		            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
		            //方法2：直接从页面获取，不用去后台获取
		            var data = {pageType:"edit",record:{inputData:row}};
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		        	if(action=="saveSuccess"){
		                //重定向
		                window.location.href="<%=request.getContextPath() %>/files/fileList/fileTypeMaintain/fileTypeList.jsp";
		             }
		        }
		    });
       	}else{
        	nui.alert("请选中一条记录进行编辑！","提示");
    	}
    }
    
    function dataDelete(){
    	var rows = grid.getSelecteds();
      	if(rows.length > 0){
	        nui.confirm("确定删除选中记录？","系统提示",function(action){
	        	if(action=="ok"){
	            	var json = nui.encode({deleteDatas:rows});
	           		var a= nui.loading("正在删除中,请稍等...","提示");
	           		var URL="com.gotop.xmzg.files.fileList.deleteFileType.biz.ext";
		        	$.ajax({
		          		url:URL,
		          		type:'POST',
		          		data:json,
		          		cache: false,
		          		contentType:'text/json',
		          		success:function(text){
			          		nui.hideMessageBox(a);
			            	var returnJson = nui.decode(text);
							if(returnJson.flag == 1){
								nui.alert("删除成功", "系统提示", function(action){
									searchData();
								});
							}else{
								nui.alert("删除失败", "系统提示");
								grid.unmask();
							}
		          		}
		        	});
		     	}
			});
      	}else{
        	nui.alert("请选中一条记录！");
      	}
    }
    
    
    
    
    //重置查询信息
	function clean(){
	   	//不能用form.reset(),否则会把隐藏域信息清空
	   	form.reset();  
    }
    
    //机构树回显
     function OrgonButtonEdit(e){
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/files/archiveList/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            
                        }
                    }
                }
            });            
             
        }   
        
      /*    //显示机构名称
    function showOrgName(e){
    	debugger;
    	var orgId = e.value;
    	var ORGNAME = "";
    	$.ajax({
				url:"com.gotop.xmzg.files.fileList.getOrgInfo.biz.ext",
				type:'post',
				data:nui.encode({orgIds:orgId}),
				cache:false,
				async:false,
				contentType:'text/json',
				success: function (text) {
					var returnJson = nui.decode(text);
					ORGNAME = returnJson.result.ORGNAME;
				}
				
			});
		return ORGNAME;
    } */
        
    
</script>
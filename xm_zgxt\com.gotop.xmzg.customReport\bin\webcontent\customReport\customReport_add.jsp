<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-06-05 18:03:00
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>

</head>
<body>
  <div id="form1" style="padding-top:5px;">
    <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">     
       <tr>
        <th class="nui-form-label"><label for="map.type$text">报表名称：</label></th>
        <td colspan="3" >  
         <input id="report_name" name = "map.REPORT_NAME" class="nui-textbox" vtype="maxLength:100" required="true"  style="width:400px" onvalidation="onReportNameChanged"/>  
          
        </td> 
      </tr>
      
      <tr>
        <th class="nui-form-label"><label for="map.type$text">字段名称：</label></th>
        <td colspan="3" >  
         <input id="field_name" name = "map.FIELD_NAME" class="nui-textarea" vtype="maxLength:1000" required="true"  style="width:400px;height:100px"/>
         <font color="red">(请以":"间隔)</font>        
        </td> 
      </tr>
      
      <tr>
        <th class="nui-form-label"><label for="map.type$text">字段代码：</label></th>
        <td colspan="3" >  
         <input id="field_code" name = "map.FIELD_CODE" class="nui-textarea" vtype="maxLength:1000"  required="true" style="width:400px;height:100px"/>        
         <font color="red">(请以":"间隔)</font> 
        </td> 
      </tr>
      
       <tr>
        <th class="nui-form-label"><label for="map.type$text">自定义SQL：</label></th>
        <td colspan="3" >  
         <input id="custom_sql" name = "map.CUSTOM_SQL" class="nui-textarea" required="true" style="width:400px;height:200px"/>        
        </td> 
      </tr>
      
      
      
     
      
    </table>
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save" onclick="onOk">保存</a>
	          <span style="display:inline-block;width:25px;"></span>
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  </div>

  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);
     
    function onOk(){
      saveData();
    }
    
    function onReportNameChanged(e){
	    var booll=false;
	    var REPORT_NAME = nui.get("report_name").getValue();
        $.ajax({
        url:"com.gotop.xmzg.customReport.customReport.queryReportNameByName.biz.ext",
        type:'POST',
        data:'REPORT_NAME='+REPORT_NAME,
        cache:false,
        async:false,
        dataType:'json',
        success:function(text){
        //debugger;
        obj = nui.decode(text.resultList[0]);
        booll=text.bool;
        
        if(booll == true)
        {	
            e.errorText="该报表名称已存在";
            e.isValid=false;
        }else
        {
          e.errorText="";
          e.isValid=true;
         }
        
       }
      });       
     } 
    
    function saveData() {        
            form.validate();
            if (form.isValid() == false) return;
            
            var field_name = nui.get("field_name");
		    var field_code = nui.get("field_code");
		    
		    //去除所有空格,以及将：替换成:
		    var xxName = field_name.getValue().replace(/\s+/g,"").replace(new RegExp(/(：)/g),":");
		    var xxCode = field_code.getValue().replace(/\s+/g,"").replace(new RegExp(/(：)/g),":").toUpperCase();
		    
		    if(xxName.split(":").length != xxCode.split(":").length){
		      nui.alert("字段名称与字段代码没有一一对应！");
		      return false;
		    }
		    
		    
		    field_name.setValue(xxName);
            field_code.setValue(xxCode);
            
            var data = form.getData(true,true);
            
            var json = nui.encode(data);
            
            //判断sql是否能查询出数据
            if(ValueisExist(json)== true)
            {
             nui.alert("无效SQL语句！");
             return false;
            }

            $.ajax({
                url: "com.gotop.xmzg.customReport.customReport.add_customReport.biz.ext",
                type: 'POST',
                data:json,
                cache: false,
                contentType:'text/json',
                success: function (json) {
                
                var returnJson = nui.decode(json);
			if(returnJson.exception == null && returnJson.iRtn == 1){
			
				nui.alert("新增成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveSuccess");
					}
				});
			}else{
				nui.alert("新增失败", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveFailed");
					}
				});
			}
			}
       }); 
       
	}
    
    //判断sql是否能查询出数据
    function ValueisExist(map){
      var bool;
      $.ajax({
        url:"com.gotop.xmzg.customReport.customReport.checkIsExit.biz.ext",
        type: 'POST',
        data:map,
        cache: false,
        async:false,
        contentType:'text/json',
        success:function(text){
          bool = text.bool;
        }
      });
      return bool;
    }
    
    function onCancel(){
      CloseWindow("cancel");
    }
    
     function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
    
  </script>
</body>
</html>
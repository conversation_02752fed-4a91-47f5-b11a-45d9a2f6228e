<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-05-29 16:10:11
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>积分计算</title>
<style>
.tit{
	text-align: right;
}

</style>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <table class="table" style="width:100%;">       
	        <tr>
	    
				<th class="tit" >考核方案：</th>
				<td >
					<input id="param.TA_ID" name="param.TA_ID" class="nui-combobox"  textField="TEXT" valueField="ID"  dataField="list" style="width:40%;" 
                  		required="true" emptyText="请选择"/>
				</td>
				<td colspan="2" ></td>
			</tr>
			<tr>	
				<th  class="tit">业务条线：</th>
				<td >
				    <div id="TIP_CODE" name="param.TIP_CODE" class="nui-combobox" style="width:40%;" dataField="datas" textField="TIP_NAME" valueField="TIP_CODE" 
				    	url="com.gotop.xmzg.achieve.configClaPro.tip_get.biz.ext" multiSelect="false" allowInput="false" showNullItem="false" nullItemText="请选择" emptyText="请选择"
				    	onvaluechanged="onTipChanged" required="true">     
					    <div property="columns">
					        <div header="业务条线代码" field="TIP_CODE" width="60"></div>
					        <div header="业务条线名称" field="TIP_NAME" width="120"></div>
					    </div>
				    </div>
				</td>
				<td colspan="2" ></td>
			</tr>
			<tr>	
				<th  class="tit">指标：</th>
				<td >
					<div id="TI_CODE" name="param.TI_CODE" class="nui-combobox" style="width:40%;" dataField="datas" textField="TI_NAME" valueField="TI_CODE" 
				    	multiSelect="false" allowInput="false" showNullItem="false" nullItemText="请选择" emptyText="请选择"
				    	required="true">
				    	<!-- onvaluechanged="onTiChanged"  -->
					    <div property="columns">
					        <div header="指标代码" field="TI_CODE" width="60"></div>
					        <div header="指标名称" field="TI_NAME" width="120"></div>
					    </div>
				    </div>
				</td>
				<td colspan="2" ></td>
			</tr>
			<!-- <tr>	
				<th class="tit">指标细项：</th>
				<td >
					<div id="TID_CODE" name="param.TID_CODE" class="nui-combobox" style="width:40%;" dataField="datas" textField="TID_NAME" valueField="TID_CODE" 
				    	multiSelect="false" allowInput="true" showNullItem="false" nullItemText="请选择" emptyText="请选择"
				    	required="true">     
					    <div property="columns" >
					        <div header="指标细项代码" field="TID_CODE" width="60"></div>
					        <div header="指标细项名称" field="TID_NAME" width="120"></div>
					    </div>
				    </div>
				</td>
				<td colspan="2" ></td>
			</tr> -->
			<tr>	
				<th  class="tit">时间：</th>
				<td >
					<input id="TIME" name = "param.TIME" class="nui-datepicker"  style="width:40%;" allowInput="false" format="yyyyMMdd" 
					required="true"/>
				</td>
				<td colspan="2" ></td>
			</tr>
			<tr>	
				<th class="tit">&nbsp;&nbsp;</th>
				<th style="text-align: left;">
				  <a class="nui-button"  iconCls="icon-save" onclick="jfData">计算积分</a>&nbsp;&nbsp;
				  <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
				</th>
				<td colspan="2" ></td>
			</tr>
	    </table>
	  </div>
    </div>
  </div>
  
  <script type="text/javascript">  
    nui.parse();
    loadAccess();
    
  	var form = new nui.Form("#form1");

    //提交
    function jfData(){   
      form.validate();
      if(form.isValid()==false) return;
      
      var data = form.getData(true,true);
      var json = nui.encode(data);
     
      $.ajax({
        url:"com.gotop.xmzg.achieve.config.startJf.biz.ext",
        type:'POST',
        data:json,
        cache:false,
        contentType:'text/json',
        success:function(text){
            var returnJson = nui.decode(text);
			nui.alert(returnJson.res.msg, "系统提示");
        }
      });
    }
    
 	function CloseWindow(action){
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
  
   	//重置
    function clean(){
	   form.reset();
    }
    
    var ti = nui.get("TI_CODE");
    //var tid = nui.get("TID_CODE");
    function onTipChanged(e){
        ti.setValue("");
        //tid.setValue("");
        var url = "com.gotop.xmzg.achieve.configClaPro.ti_get.biz.ext?code=" + e.value;
        ti.setUrl(url);
    }
    
    function onTiChanged(e){
        tid.setValue("");
        var url = "com.gotop.xmzg.achieve.configClaPro.tid_get.biz.ext?code=" + e.value;
        tid.setUrl(url);
    }
    
    function loadAccess(){
		nui.get("param.TA_ID").load("com.gotop.xmzg.achieve.report.loadTACombox.biz.ext");
		nui.get("param.TA_ID").select(0);
	}
  </script>
</body>
</html>
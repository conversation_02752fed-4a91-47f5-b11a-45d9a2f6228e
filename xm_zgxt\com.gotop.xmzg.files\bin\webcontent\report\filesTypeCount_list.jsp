<%@page pageEncoding="UTF-8"%>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): zyp
  - Date: 2017-07-24 16:20:11
  - Description:
-->
<%
UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>


<style>
#table1 .tit{
	height: 10px;
    line-height: 10px;
}
#table1 td{
	height: 10px;
    line-height: 10px;
}
</style>
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>借阅清单查询</title>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	     <table class="table" id="table1" >
	    <tr>
	     <td class="tit" >年份：</td>	     
	        <td >
	           <input id="in_year" name="queryData.in_year" class="nui-textbox "  style="width:200px;"/>
	        </td>
	        
	        <td class="tit" >档案种类：</td>
	        <td >
	        
	        <input id="queryData.files_type" class="nui-dictcombobox " name="queryData.files_type"  emptyText="全部"
          valueField="dictID" textField="dictName" dictTypeId="FILES_TYPE" style="width:200px;" showNullItem="true" nullItemText="全部"  />
	          		
	        </td>
	  
	        
	        <td rowspan="4" class="btn-wrap">
					<a class="nui-button" iconCls="icon-search" onclick="search">查询</a>
					<a class="nui-button" iconCls="icon-reset"  onclick="reset()"/>重置</a>
					<input class="nui-button" text="导出Excel" iconCls="icon-download" onclick="excel"/>
				</td>
	        
	      </tr>
	        

	       
	    </table>
	  </div>
    </div>
  </div>
  

  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.files.report.queryFilesTypeCountList.biz.ext"
	  sizeList=[5,10,20,50,100] multiSelect="true" pageSize="20" onselectionchanged="selectionChanged" >
	    <div property="columns" >
	      <!-- <div type="checkcolumn"></div> -->
	      <div field="IN_YEAR" headerAlign="center" align="center">年份</div>
	      <div field="FILES_TYPE_NAME" headerAlign="center" align="center" >档案种类</div>      	      
	      <div field="OLD_COUNT_SUM" headerAlign="center" align="center">去年存量（箱）</div>
	      <div field="NEW_COUNT_SUM" headerAlign="center" align="center">本年新增（箱）</div>
	      <div field="COUNT_SUM" headerAlign="center" align="center">结存量（箱）去年存量+本年新增</div>
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">
    nui.parse();
    var grid = nui.get("datagrid1");
    //grid.load();
    
function onAddressRenderer(e){
    return nui.getDictText("FILES_STORAGE_ADDRESS", e.row.STORAGE_ADDRESS);
    }
    
  function onActionRenderer(e){
    return nui.getDictText("FILES_TYPE", e.row.FILES_TYPE);
    }
    
    
    function search(){
       var form = new nui.Form("#form1");
       var data = form.getData(true,true);
       grid.load(data);
    }
    
    function reset(){
    var form = new nui.Form("#form1");//将普通form转为nui的form
    form.reset();
}

 	//导出Excel
	function excel(){
		var form=new nui.Form("form1");
	    form.validate();
	    if (form.isValid() == false) return;
		var data=form.getData(true, true);
		data.queryData.userOrgId = "<%=userObject.getUserOrgId()%>";
		data.queryData.userId = "<%=userObject.getUserId()%>";

		var fileName="档案结存量统计";
		var queryPath="com.gotop.xmzg.files.report.select_filesTypeCountList";
		  var columns=grid.getBottomColumns();
		columns=columns.clone();
		for(var i=0;i<columns.length;i++){
			var column=columns[i];
			if(!column.field){
				columns.removeAt(i);
			}else{
				var c={header:column.header,field:column.field };
				columns[i]=c;
			}
		}
		columns=nui.encode(columns); 
		data=nui.encode(data);
		var url="<%=request.getContextPath()%>/util/exportExcel/exportExcel.jsp?fileName="+fileName+"&queryPath="+queryPath+"&data="+data+"&columns="+columns;
		 window.location.replace(encodeURI(url));
		 
		 //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
	     setMask();
	} 
	
	//设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)	
	function setMask(){
 		var a= nui.loading("正在操作中,请稍等...","提示"); //显示遮罩层
 		var icount = setInterval(function(){  
	  	if(document.attachEvent){   //IE浏览器
	 		if(document.readyState=='interactive'){
              	nui.hideMessageBox(a);  //隐藏遮罩层
              	clearTimeout(icount);
        	}
	 	}else{ //谷歌浏览器
	 		if(document.readyState=='complete'){
              	nui.hideMessageBox(a);  //隐藏遮罩层
              	clearTimeout(icount);
        	}
	 	} 
 		}, 1); 
	}
  </script>
</body>
</html>
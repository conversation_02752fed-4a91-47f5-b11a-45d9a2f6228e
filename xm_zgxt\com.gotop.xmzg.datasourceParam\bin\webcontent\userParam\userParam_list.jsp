<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): lyl
  - Date: 2019-05-10 17:15:05
  - Description:
-->
<head>
<title>生产系统用户参数维护</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <table class="table" style="width:100%;">
	        <tr>
                <th style="width:12%;">系统名称：&nbsp;&nbsp;</th> 
		        <td style="width:28%;"><input name="queryData.sysName" id="queryData.sysName" class="nui-textbox " style="width:170px;"   /></td>
                <th style="width:12%;">组名称：&nbsp;&nbsp;</th> 
		        <td style="width:28%;"><input name="queryData.groupName" id="queryData.groupName" class="nui-textbox " style="width:170px;"   /></td>
				<th style="width:60%;">
					<input class="nui-button" text="查询" iconCls="icon-search" onclick="search"/>&nbsp;&nbsp;&nbsp;
					<a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
				</th>
			</tr>
	    </table>
	  </div>
    </div>
  </div>
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
		     <a id="update" class="nui-button" iconCls="icon-edit" onclick="update">修改</a>
           </td>
        </tr>
      </table>
    </div>
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.datasourceParam.userParam.queryUserParamList.biz.ext"
	  sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" onselectionchanged="selectionChanged">
	    <div property="columns" >
	       <div type="checkcolumn"></div> 
	      <div field="systemName" headerAlign="center"  align="center"  >系统名称</div>
	      <div field="systemUrl" headerAlign="center"  align="center"  >系统网址</div>
	      <div field="managerOrgName" headerAlign="center"  align="center"  >维护部门</div>
	      <div field="userName" headerAlign="center"  align="center" >用户名</div>
	    </div>
	 </div>
  </div>
  
 <script type="text/javascript">
    nui.parse();
    var grid = nui.get("datagrid1");
    grid.load();
    
    function search(){
       var form = new nui.Form("#form1");
       var data = form.getData(true,true);
       grid.load(data);
    }
    
    function update(){
       var row = grid.getSelected();
       if(row!=null){
	       nui.open({
	          url:"<%=request.getContextPath() %>/datasourceParam/userParam/userParam_upt.jsp",
	          title:'编辑',
	          width:"65%",
          	  height:"36%",
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             
	             //方法2：直接从页面获取，不用去后台获取
	               var data = {pageType:"edit",record:{map:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }else{
           nui.alert("请选中一条记录！");
       }
    }
    function selectionChanged(){
       var rows = grid.getSelecteds();
       if(rows.length>1){
           nui.get("update").disable();
       }else{
           nui.get("update").enable();
       }
    }
    
    //重置
    function clean(){
     	var form = new nui.Form("#form1");
		form.reset();
    }
  </script>
</body>
</html>
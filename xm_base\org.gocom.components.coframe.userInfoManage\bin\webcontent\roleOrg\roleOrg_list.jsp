<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): wj
  - Date: 2018-08-16 12:01:11
  - Description: 角色机构管理
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>角色机构管理</title>
</head>
<body style="width:100%;overflow:hidden;">
 
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	  <div id="c_orgid" class="nui-hidden"  name="queryData.c_orgid" ></div>
	  <div  class="nui-hidden"  id="c_role" ></div>
	    <table class="table" style="width:100%;">
	      <tr >
		     
			<th class="tit">角色代码：</th>
			<td>
				<input id="roleCode" name="queryData.roleCode" class="nui-textbox" style="width:200px;"/>
			</td>
			
			<th class="tit">角色名称：</th>
			<td>
				<input id="roleName" name="queryData.roleName" class="nui-textbox" style="width:200px;"/>
			</td>	

	         <th ></th>
		     <td>		  
	            <a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;
	            <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
	         </td>
	        </tr>
	    </table>
	  </div>
    </div>
  </div>
  
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;"> 
		     <a id="update" class="nui-button" iconCls="icon-edit" onclick="update">角色所属机构维护</a>
           </td>
        </tr>
      </table>
    </div>
 
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="org.gocom.components.coframe.userInfoManage.roleOrg.query_roleOrg.biz.ext"
	  sizeList=[5,10,20,50,100]  pageSize="10" onselectionchanged="selectionChanged">
	    <div property="columns" >
	      <div type="checkcolumn"></div>
	      <div field="ROLE_CODE" headerAlign="center" align="center">角色代码</div>
	      <div field="ROLE_NAME" headerAlign="center" align="center">角色名称</div>
	      <div field="ORGNAMES" headerAlign="center" align="center">分管机构</div>
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">
    nui.parse();
    var grid = nui.get("datagrid1");   
    
    //判断当前登录人是否是系统管理员
    $.ajax({
		        url:"com.gotop.userInfoManage.wj.groupManage.isSysadmin.biz.ext",
		        type:'POST',
		        data:'',
		        cache:false,
		        async:false,
        		dataType:'json',
		        success:function(text){
		          //如果是系统管理员
		          if(text.flag == "1"){
		           nui.get("c_role").setValue("sysadmin");
		          }
		        }
		 });
     
    //grid.load();
    var a;
    
    
    function search(){

       var form = new nui.Form("#form1");
            form.validate();
            if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
    
    //角色所属机构维护
    function update(){
       var row = grid.getSelected();
       if(row!=null){
	      nui.open({
	          url:"<%=request.getContextPath() %>/userInfoManage/roleOrg/roleOrg_update.jsp",
	          title:'编辑',
	          width:600,
	          height:300,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             
	             //方法1：后台查询一次，获取信息回显
	             /* var data = row;
	             iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
	             
	             //方法2：直接从页面获取，不用去后台获取
	               var data = {pageType:"edit",record:{map:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }else{
           nui.alert("请选中一条记录！");
       }
    }
    
 
    
    function selectionChanged(){
       var rows = grid.getSelecteds();
       if(rows.length>1){
           nui.get("update").disable();
       }else{
           nui.get("update").enable();
       }
    }
    
    
    //重置
    function clean(){ 
       nui.get("roleCode").setValue("");
       nui.get("roleName").setValue("");
     }

  </script>
</body>
</html>
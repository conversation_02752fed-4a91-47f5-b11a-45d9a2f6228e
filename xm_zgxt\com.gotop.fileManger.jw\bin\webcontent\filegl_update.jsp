<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
<%@ page import="com.eos.data.datacontext.UserObject"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): jw
  - Date: 2018-08-02 09:53:00
  - Description:
-->
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
 %>
<!-- $Header: svn://**************:7029/svn/xm_project/xm_zgxt/com.gotop.fileManger.jw/src/webcontent/filegl_update.jsp 1463 2018-08-14 01:33:12Z jw $-->
<title>文件修改</title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<%@include file="/coframe/tools/skins/common.jsp"%>
<script src="<%=request.getContextPath()%>/common/nui/nui.js" type="text/javascript"></script>
<style type="text/css">
html,body {
	padding: 0;
	margin: 0;
	border: 0;
	height: 100%;
	overflow-y:auto;
}

.nui-textarea {
	width: 100%;
}

.nui-popup .nui-shadow {
	height: 150px;
}

.nui-textboxlist {
	width: 100%;
	height: 40px;
}
.errorText{
	color:red;
	font-size: 10px;
}
</style>
</head>
<body>
	<fieldset style="border: solid 1px #aaa; padding: 3px;margin-top: 2%">
		<legend>文件基本信息</legend>
		<div style="padding: 5px;">
			<form id="filefrom" method="post">
				<table border="0" cellpadding="1" cellspacing="2" align="center" style="width: 100%">
					<tr>
						<td style="width: 15%; text-align: right;">文号：</td>
						<td style="width: 70%">
							<input id="fileNo" name="file.fileNo" class="nui-textbox" required="true" style="width: 100%;" onblur="checkFileNo" />
							<input id="fileNoOid" class="nui-hidden"/>
						</td>
						<td style="width: 15%;" id="fileno_error" class="errorText"><input id="fileno_cheack" class="nui-hidden" /></td>
					</tr>
					<tr>
						<td style="width: 15%; text-align: right;">关键字：</td>
						<td style="width: 70%"><input id="fileKey" name="file.fileKey" class="nui-textbox" required="true" style="width: 100%;" /></td>
						<td style="width: 15%;"></td>
					</tr>
					<tr>
						<td style="width: 15%; text-align: right;">所属目录：</td>
						<td style="width: 70%"><input id="vest_meun" class="nui-treeselect" multiSelect="false" valueFromSelect="false" textField="LISTNAME"
							name="file.listId" valueField="LISTID" parentField="LISTPID" allowInput="true" showRadioButton="true" showFolderCheckBox="true" style="width: 100%;" 
							onvaluechanged="valuechanged"/></td>
						<td style="width: 15%;"><input class="nui-hidden" id="dirname" name="dirname"></td>
					</tr>
					<tr>
						<td style="width: 15%; text-align: right;">文件名：</td>
						<td style="width: 70%"><input id="fileName" name="file.fileName" class="nui-textbox" required="true" style="width: 100%;" allowInput="false" /></td>
						<td style="width: 15%;" id="filename_error" class="errorText"><input id="filename_cheack" class="nui-hidden" /></td>
					</tr>
				</table>
				<input id="fileid" name="file.fileId" class="nui-hidden" /> 
				<input id="uploadname" name="file.uploadName" class="nui-hidden" /> 
				<input id="fileStatus" name="file.fileStatus" class="nui-hidden" />
				<input id="path" class="nui-hidden" />
				<input id="previewname" class="nui-hidden" />
				<input id="fileids" class="nui-hidden" />
			</form> 
			<form id="upload" method="post" enctype="multipart/form-data">
				<table border="0" cellpadding="1" cellspacing="2" align="center" style="width: 100%">
					<tr>
						<td style="width: 15%; text-align: right;">文件：</td>
						<td style="width: 70%"><input class="nui-htmlfile" name="Fdata" id="uploadfile" style="width: 100%;" onfileselect="onFileSelect" /></td>
						<td style="width: 15%;"></td>
					</tr>
				</table>
			</form>
		</div>
	</fieldset>
	
	<div style="text-align: center; padding: 10px;">
		<a class="nui-button" onclick="submitForm()" style="width: 60px; margin-right: 20px;">确定</a> <a class="nui-button" onclick="" style="width: 60px;">取消</a>
	</div>
	<script src="<%= request.getContextPath() %>/js/jquery.form.js" type="text/javascript"></script>
	<script type="text/javascript">
			nui.parse();
			
			/* 0（预览） 1（修改） 2（删除） 3（下载）  */
			var username="<%=userObject.getUserName()%>";
			//console.log(username);
			//路径
			var path="<%= request.getContextPath() %>";
			
			//所属目录id
			var vestmeun=nui.get("vest_meun");
			
			var form = new nui.Form("#filefrom");
			var fileinfo=nui.get("#uploadfile");
			//标准方法接口定义
	        function SetData(data,datalist,username,file){
	        	//跨页面传递的数据对象，克隆后才可以安全使用
	        	data = nui.clone(data);	        	
	        	vestmeun.loadList(datalist, "LISTID", "LISTPID");
	        	vestmeun.setValue(data.id);
	        	nui.get("uploadname").setValue(username);
	        	nui.get("fileNoOid").setValue(file.file.fileNo);
	        	//console.log(file);
	        	form.setData(file);
	        	form.setChanged(false);
	        	filenames();
	        	nui.get("fileno_cheack").setValue(true);
	        	nui.get("filename_cheack").setValue(true);
			}
			//获取文件名
			function filenames(){
				nui.get("dirname").setValue(vestmeun.getSelectedNode().LISTNAME);
			}
			//下拉框值改变事件
			function valuechanged(e){
				filenames();
				checkFileName(nui.get("fileName").getValue());
			}
	      	//文件浏览按钮触发事件
			function onFileSelect(e){				
				var path=fileinfo.value;								
				filename=path.substring(path.lastIndexOf("\\")+1,path.length);
				nui.get("fileName").setValue(filename);
				checkFileName(filename);
			}
	      	
			//动态查询文件文号是否重复
	        function checkFileNo(e){
	        	var filenoerror =document.getElementById("fileno_error");
	        	var fileNoOid =nui.get("fileNoOid").getValue();
				filenoerror.innerHTML ="";
				nui.get("fileno_cheack").setValue(true);
	        	var meunid=vestmeun.getSelectedNode().LISTID;//获取id
	        	if(fileNoOid!=e.sender.value){
	        		var json = nui.encode({"fileno":e.sender.value,"meunid":meunid}); //序列化成JSON
		        	$.ajax({
						url : "com.gotop.fileManger.jw.action.filegl.checkFileNo.biz.ext",
						type : "post",
						data : json,
						cache : false,
						contentType : 'text/json',
						success : function(data) {
							//console.log(data);
							if(data.msg.resCode==1){			          				           
					            filenoerror.innerHTML = data.msg.resDes;
					            nui.get("fileno_cheack").setValue(false);
							}
						}			
					}); 
	        	}
	        	
	        }
	      	//动态查询文件名是否重复
	        function checkFileName(filename){
	        	nui.get("fileids").setValue("");
	        	nui.get("path").setValue("");
	        	nui.get("previewname").setValue("");
	        	var filenameerror =document.getElementById("filename_error");
	        	filenameerror.innerHTML ="";
	        	nui.get("filename_cheack").setValue(true);
	        	var meunid=vestmeun.getSelectedNode().LISTID;//获取id
	        	var filename=nui.get("fileName").getValue();//文件名
	        	var json = nui.encode({"filename":filename,"meunid":meunid}); //序列化成JSON
	        	$.ajax({
					url : "com.gotop.fileManger.jw.action.filegl.checkFileName.biz.ext",
					type : "post",
					data : json,
					cache : false,
					contentType : 'text/json',
					success : function(data) {
						//console.log(data);
						if(data.msg.resCode==1){			          				           
							filenameerror.innerHTML = "已存在文件名";
							nui.get("filename_cheack").setValue(false);
							var file=data.msg.file[0];
							nui.get("path").setValue(file.FILEPATH);
							nui.get("previewname").setValue(file.PREVIEWNAME);
							nui.get("fileids").setValue(file.FILEID);						
						}
						
					}			
				}); 
	        }
	      	
	      	//文件添加事件
			function submitForm() {
				var fliees=nui.get("uploadfile").getValue();
				var listjs=vestmeun.getSelectedNode().LISTPID;//获取pid
				var meunid=vestmeun.getSelectedNode().LISTID;//获取id
				var meunName=vestmeun.getSelectedNode().LISTNAME;//获取目录名称
				var fileno=nui.get("fileNo").getValue();//文号
				var filename=nui.get("fileName").getValue();//文件名
				var fileid=nui.get("fileid").getValue();//文件id(页面传过来的)
				var fileids=nui.get("fileids").getValue();//文件id(数据库传来的)
				if(listjs===0){
					nui.alert("请选择二级目录添加文件", "系统提示", function(action){		                        
		              });
				}else{
					//判断文号是否重复
					if(fliees!==null && fliees!=="" && fliees!==undefined){
						if(fileids!==null && fileids!=="" && fileids!==undefined){
							checkfilenoornames(fileids,fileno,filename,meunid,meunName);
						}else{
							checkfilenoornames(fileid,fileno,filename,meunid,meunName);
						}					
					}else{
						nui.alert("请上传文件", "系统提示", function(action){});
					}
				}	  
			}
			//文件重复查询（文号和文件名）
			function checkfilenoornames(fileid,fileno,filename,meunid,meunName){
				var nock=nui.get("fileno_cheack").getValue();
				var nameck=nui.get("filename_cheack").getValue();
				if(!nock){
					nui.alert("请查看填写项", "系统提示", function(action){});
					return false;
				}else{
					if(!nameck){
						nui.confirm("该文件已存在是否要覆盖", "确定？",
				            function (action) {
				                if (action == "ok") {				           
				                	FileUpload(fileid,fileno,meunName,nui.get("path").getValue(),nui.get("previewname").getValue(),2,1);				                 
				                }
				            }
				        );
					}else{
						FileUpload(fileid,fileno,meunName,"","",2,1);
					}
				}	
				
			}
	      	
		
			
			//关闭窗口
	        function CloseWindow(action) {
	            if (action == "close" && form.isChanged()) {
	                if (confirm("数据被修改了，是否先保存？")) {
	                    saveData();
	                }
	            }
	            if (window.CloseOwnerWindow)
	            return window.CloseOwnerWindow(action);
	            else window.close();
	        }
	</script>
	<script src="<%= request.getContextPath() %>/js/filecs.js" type="text/javascript"></script>
</body>
</html>
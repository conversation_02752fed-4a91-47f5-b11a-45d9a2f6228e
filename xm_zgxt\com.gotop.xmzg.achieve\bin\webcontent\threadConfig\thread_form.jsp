<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-01-05 11:41:00
  - Description:
-->
<head>
<%-- <%@include file="/coframe/tools/skins/common.jsp" %> --%>
<%@include file="/coframe/dict/common.jsp"%>
<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
	    .nui-form-label{
	    	width: 135px;
	    }
    </style>
</head>
<body>
<!-- JF_POSITION -->
<div id="form1" style="padding-top:5px;">
   <input class="nui-hidden" id="TAI_ID" name="map.TAI_ID" /> 
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
   	  <input id="PROC_ID_UP" name = "map.PROC_ID_UP" class="nui-hidden" />
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>存储过程名称：</label></th>
        <td colspan="3" >
        	<input id="PROC_ID" name = "map.PROC_ID" vtype="maxLength:200" class="nui-textbox" required="true" style="width:100%;" />
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text">存储过程中文名称：</label></th>
        <td colspan="3" >  
        	<input id="PROC_NAME" name = "map.PROC_NAME" vtype="maxLength:200" class="nui-textbox"  style="width:100%;" />
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>开始时间值：</label></th>
        <td colspan="3" >  
        	<input id="PROC_DAYS_START" name = "map.PROC_DAYS_START" vtype="int" class="nui-textbox" required="true"  style="width:100%;" onvalidation="comparedate"/>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>结束时间值：</label></th>
        <td colspan="3" >  
        	<input id="PROC_DAYS_END" name = "map.PROC_DAYS_END" vtype="int" class="nui-textbox" required="true"  style="width:100%;" onvalidation="comparedate"/>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text">前置存储过程名称：</label></th>
        <td colspan="3" >  
        	<input id="PROC_PRE_ID" name = "map.PROC_PRE_ID" vtype="maxLength:200" class="nui-textbox" style="width:100%;" />
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text">前置存储过程中文名称：</label></th>
        <td colspan="3" >  
        	<input id="PROC_PRE_NAME" name = "map.PROC_PRE_NAME" vtype="maxLength:200" class="nui-textbox"  style="width:100%;" />
        </td> 
      </tr>
    </table>
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save" onclick="onOk">保存</a>
	          <span style="display:inline-block;width:10px;"></span>
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  </div>

  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);
	
    var saveUrl = "com.gotop.xmzg.achieve.thread.thread_add.biz.ext";
    function setData(data){     	    
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);
        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
          nui.get("PROC_ID").setEnabled(false);
          saveUrl = "com.gotop.xmzg.achieve.thread.thread_update.biz.ext";
	      //表单数据回显
          var json = infos.record;
          form.setData(json);
          nui.get("PROC_ID_UP").setValue(json.map.PROC_ID);
          
        }
    }
    
    function onOk(){
      saveData();
    }
    
    //提交
    function saveData(){   
      form.validate();
      if(form.isValid()==false) return;
      
      var data = form.getData(true,true);
      var json = nui.encode(data);
     
      $.ajax({
        url:saveUrl,
        type:'POST',
        data:json,
        cache:false,
        contentType:'text/json',
        success:function(text){
            var returnJson = nui.decode(text);
			if(returnJson.exception == null && returnJson.iRtn == 1){
				nui.alert("操作成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveSuccess");
					}
				});
			}else if(returnJson!= null && returnJson.msg != null && returnJson.iRtn != 1){
				nui.alert(returnJson.msg, "系统提示");
			}else{
				nui.alert("操作失败", "系统提示");
			}
        }
      });
    }
    
   
    function onCancel(){
      CloseWindow("cancel");
    }
    
    function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
	
    function comparedate(e){
      var startDate = nui.get("PROC_DAYS_START").getFormValue();
      var endDate = nui.get("PROC_DAYS_END").getFormValue();
      
      if(startDate == null || endDate== null || startDate == "" || endDate== ""){
      		e.errorText="结束时间值或开始时间值不能为空";
          	e.isValid=false;
          	return;
      }
      if(parseInt(startDate) > parseInt(endDate)){
          e.errorText="结束时间值必须大于等于开始时间值";
          e.isValid=false;
      }else{
          e.errorText="";
          e.isValid=true;
      }
    } 
 	
  </script>
</body>
</html>
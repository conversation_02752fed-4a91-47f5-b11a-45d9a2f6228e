
<!-- $Header: svn://**************:7029/svn/xm_project/xm_zgxt/com.gotop.fileManger.jw/src/webcontent/upload1.jsp 1986 2018-09-20 06:32:37Z jw $-->
<%@page import="net.sf.json.*"%>
<%@page import="java.net.*"%>
<%@page import="java.io.File"%>
<%@page import="java.util.Enumeration"%>
<%@page import="com.oreilly.servlet.multipart.DefaultFileRenamePolicy"%>
<%@page import="com.oreilly.servlet.MultipartRequest"%>
<%@page import="com.gotop.xm.util.OfficeToHtml"%>
<%@page import="com.gotop.xm.util.FileUtil"%>
<%@page import="com.gotop.xm.util.MethodUilt"%>
<%@page import="com.gotop.xm.util.Config"%>
<%@page import="com.gotop.xm.util.Office2PdfUtil"%>
<%@page import="java.nio.charset.Charset"%>
<%@page import="com.eos.server.dict.DictManager"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" import="java.util.*"%>
<%
	JSONObject json  = new JSONObject();
	//判断当前环境
	//Properties prop = System.getProperties();
	//String os = prop.getProperty("os.name");
	//boolean isWin=(os.startsWith("win") || os.startsWith("Win"));
	boolean isWin=MethodUilt.Systems();
	String dir= URLDecoder.decode(request.getParameter("dir"),"UTF-8");
	System.out.println("传过来的目录地址---->"+dir);
	//dir=dir.replace(",", "/");
	//System.out.println("---->"+dir);
	//获取数据字典中设定的存放文件夹   比如: /app/err_excel/
    String dictVir = "";
    if(isWin==true){
    	dictVir=DictManager.getDictName("MLLJ","1");
    }else{
    	dictVir=DictManager.getDictName("MLLJ","0");
    }
	//创建目录文件夹
	String uploadPath = "文件系统/"+dir;
	//String uploadPath = dictVir+"文件系统/"+dir;
	//String saveDirectory = session.getServletContext().getRealPath("//" + uploadPath);
	String saveDirectory=dictVir+uploadPath;
	System.out.println("完整目录地址---->"+saveDirectory);
	if(isWin==true){	
		saveDirectory = saveDirectory.replaceAll("/", "\\\\");
		System.out.println("*******判断系统转换斜杆："+saveDirectory+"*****");
	}
	
	System.out.println(saveDirectory);
	File file = new File(saveDirectory);
	FileUtil.judeDirExistss(file);
	//创建目录文件夹
	//每个文件最大100m,最多10个文件,所以...
	int maxPostSize = 10 * 100 * 1024 * 1024;
	//MultipartRequest multi = new MultipartRequest(request,saveDirectory, maxPostSize, "UTF-8");
	//获取系统默认编码
	String ends=System.getProperty("file.encoding");
	System.out.println("系统编码============= " +  ends);
	MultipartRequest multi = new MultipartRequest(request,saveDirectory, maxPostSize,"UTF-8");

	//如果有上传文件, 则保存到数据内
	Enumeration files = multi.getFileNames();
	boolean boo=false;//判断转换是否成功(转成html)
	String dxs="0";//转成doc或者xls文件名
	while (files.hasMoreElements()) {
		String name = (String) files.nextElement();
		File f = multi.getFile(name);
		if (f != null) {
			try {
				String filePath = f.getAbsolutePath();
		        String fname = f.getName();//附件原名
		        System.out.println("上传文件原名============= " +  fname);
		        System.out.println("文件上传地址 =================== " +  filePath);
		        //获取系统当前时间
		        java.text.SimpleDateFormat date = new java.text.SimpleDateFormat("yyyyMMddHHmmss");
		        String currentTimeMillis = date.format(new Date(System.currentTimeMillis()))+FileUtil.getUuid();
		        System.out.println("系统当前时间 =================== " +  currentTimeMillis);
		        String suffix = fname.substring(fname.lastIndexOf("."));
				//重新命名文件
            	fname = currentTimeMillis + suffix;
				System.out.println("重新命名文件 =================== " +  fname);
				//完整路径
				String newpath=saveDirectory+"/"+fname;
				if(isWin==true){	
					newpath = newpath.replaceAll("/", "\\\\");
					System.out.println("*******判断系统转换斜杆2："+newpath+"*****");
				}
				File newfile = new File(newpath);
            	f.renameTo(newfile);
            	System.out.println("**************"+suffix);
	            if(".txt".equals(suffix)||".TXT".equals(suffix)){
	        		FileUtil.transition(newpath);
	        	}
	            if(".wps".equals(suffix)||".WPS".equals(suffix)){
	            	dxs=FileUtil.Tran(newpath,currentTimeMillis,"doc");
	        	}
	            if(".et".equals(suffix)||".ET".equals(suffix)){
	            	dxs=FileUtil.Tran(newpath,currentTimeMillis,"xls");
	        	}
            	System.out.println("上传成功！ ");
				// 获取需要转换的文件名,将路径名中的'\'替换为'/'
				String converfilename =null;
				if("0".equals(dxs)){
					converfilename=saveDirectory+"/"+fname;
					System.out.println("===========没有包含wps或者et文档===========");
				}else{
					if("1".equals(dxs)){
						System.out.println("==========wps或者et文档转换失败============");
						System.out.println("=========="+dxs);
						return;
					}else{
						converfilename=saveDirectory+"/"+dxs;
						System.out.println("==========wps或者et文档转换成功============");
					}
				}
				 
				System.out.println("获取需要转换的文件名 =================== " +  converfilename);
				boolean b=FileUtil.Judge(suffix);//判断后缀是否在转换里面
				if(b){
					// 调用转换类DocConverter,并将需要转换的文件传递给该类的构造方法
					OfficeToHtml d = new OfficeToHtml(converfilename,uploadPath);
					// 调用conver方法开始转换，先执行doc2pdf()将office文件转换为pdf;再执行pdf2swf()将pdf转换为swf;			
					boo=d.conver();
					if(boo){
						json.put("onlyName", fname);			
						json.put("previewName", currentTimeMillis+".html");			
						json.put("realFileName", multi.getFilesystemName(name));
						json.put("msg", "ok");
						json.put("filePath",uploadPath);
					}else{						
						json.put("onlyName", fname);			
						json.put("previewName", "");			
						json.put("realFileName", multi.getFilesystemName(name));
						json.put("msg", "error2");
						json.put("filePath", uploadPath);
					}
				}else{
					boolean c=FileUtil.Judge1(suffix);//判断后缀是否pdf
					if(c){
						System.out.println("文件是pdf需要复制");
						FileUtil.pdfCut(converfilename, uploadPath);
						json.put("onlyName", fname);			
						json.put("previewName", currentTimeMillis+".pdf");			
						json.put("realFileName", multi.getFilesystemName(name));
						json.put("msg", "ok");
						json.put("filePath", uploadPath);
					}else{
						json.put("onlyName", fname);			
						json.put("previewName", "");			
						json.put("realFileName", multi.getFilesystemName(name));
						json.put("msg", "ok");
						json.put("filePath", uploadPath);
					}
				}
				
			} catch (Exception e) {
				e.printStackTrace();
				json.put("msg", "error");
			}
			response.getWriter().write(json.toString());
			response.getWriter().close();
		} 
	}
	
%>

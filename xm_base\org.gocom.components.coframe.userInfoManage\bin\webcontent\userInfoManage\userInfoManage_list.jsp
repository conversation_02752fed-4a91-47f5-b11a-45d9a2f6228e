<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): wj
  - Date: 2018-07-24 12:01:11
  - Description: 人员信息管理
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>人员信息管理</title>
</head>
<body style="width:100%;overflow:hidden;">
 
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	  <div id="p_orgid" class="nui-hidden"  name="queryData.p_orgid" ></div>
	    <table class="table" style="width:100%;">
	      <tr >
		        <th class="tit">机构名称：</th>
				<td>
					<input id="btnEdit1" name = "queryData.orgid"  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:200px;"/>

				</td>
				<th class="tit">人员姓名：</th>
				<td>
					<input id="empName" name="queryData.empName" class="nui-textbox" style="width:200px;" vtype="maxLength:250"/>
				</td>
				
				
				
		   </tr>
		   <tr>
		        <th class="tit">人员工号：</th>
				<td>
					<input id="empCode" name="queryData.empCode" class="nui-textbox" style="width:200px;" vtype="maxLength:250"/>
				</td>	
		        <th class="tit">业务角色：</th>
				<td>
					<input id="btnEdit2" name = "queryData.roleid"  class="nui-buttonedit"  allowInput="false" onbuttonclick="RoleButtonEdit" style="width:200px;"/>
				</td>
	         
	         <th ></th>
		     <td>		  
	            <a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;&nbsp;
	            <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
	         </td>
	        </tr>
	    </table>
	  </div>
    </div>
  </div>
  
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
             
		     <a id="update1" class="nui-button" iconCls="icon-edit" onclick="update(1)">人员维护</a>
		     <a id="update2" class="nui-button" iconCls="icon-edit" onclick="update(2)">所属机构的修改维护</a>
		     <a id="update3" class="nui-button" iconCls="icon-edit" onclick="update(3)" style="display:none">权限设置</a>
		     <a id="unlock" class="nui-button" iconCls="icon-edit" onclick="unlock()">密码解冻</a>
		     
           </td>
        </tr>
      </table>
    </div>
 
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="org.gocom.components.coframe.userInfoManage.userInfoManage.query_userInfoManage.biz.ext"
	  sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" onselectionchanged="selectionChanged">
	    <div property="columns" >
	      <div type="checkcolumn"></div>
	      <div field="EMPNAME" headerAlign="center" align="center">人员姓名</div>
	      <div field="GENDERN" headerAlign="center" align="center">性别</div>
	      <div field="EMPCODE" headerAlign="center" align="center">人员工号</div>
	      <div field="ORGNAME" headerAlign="center" align="center">所属单位</div>
	      <div field="EMPSTATUSN" headerAlign="center" align="center">人员状态</div>
	      <div field="STATUS" headerAlign="center" align="center">密码状态</div>
	      <div field="MOBILENO" headerAlign="center" align="center">手机号</div>   
	      <div field="ERRCOUNT" headerAlign="center" align="center">当日密码错误次数</div>   
	      <div field="ERRNUM" headerAlign="center" align="center">累计密码错误次数</div>   
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">
    nui.parse();
    
    //查找上级机构  如果当前机构是厦门分行，则上级机构为厦门分行(因为厦门分行的上级机构是null，会导致查不出数据)  
    $.ajax({
		        url:"org.gocom.components.coframe.userInfoManage.userInfoManage.query_orgidAndPorgid.biz.ext",
		        type:'POST',
		        data:'',
		        cache:false,
		        async:false,
        		dataType:'json',
		        success:function(text){
		        
		          nui.get("p_orgid").setValue(text.p_orgid);
		          
		          /*用于刚点进菜单时回显机构名称 */
		          //nui.get("queryData.orgid").setValue(text.c_orgid);
                  //nui.get("queryData.orgid").setText(text.c_orgname);
                  
                  /*用于记录，便于重置时恢复刚点进菜单时机构输入框显示的数据  */
                  //s_OrgId = text.c_orgid;
                  //s_OrgName = text.c_orgname;
		          
		        }
		 });
		 
	  //判断当前登录人是否是系统管理员
	    		$.ajax({
			        url:"org.gocom.components.coframe.userInfoManage.userInfoManage.isSysadmin.biz.ext",
			        type:'POST',
			        data:'',
			        cache:false,
			        async:false,
	        		dataType:'json',
			        success:function(text){
			          //如果是系统管理员/超级管理员
			          if(text.flag == "1"){
		               //可见
			           document.getElementById("update3").style.display = "";
			          }
			        }
			 });
		 
    var grid = nui.get("datagrid1");    
    //grid.load();
    var a;
    
    //业务角色回显
     function RoleButtonEdit(e) {   
         var btnEdit2 = this;        
            nui.open({
                url:"<%=request.getContextPath() %>/userInfoManage/userInfoManage/role_list.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 450,
                height: 450,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit2.setValue(data.id);
                            btnEdit2.setText(data.text);
                            
                        }
                    }
                }
            });            
        }    
        
     
    //机构树回显
     function OrgonButtonEdit(e) {
            var btnEdit = this;
            
            nui.open({
                url:"<%=request.getContextPath() %>/userInfoManage/userInfoManage/org_multSelectTree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.id);
                            btnEdit.setText(data.text);
                            
                        }
                    }
                }
            });
             
        }    
        
    
    function search(){
       var form = new nui.Form("#form1");
            form.validate();
            if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
    
    
    function unlock(){
		var rows = grid.getSelecteds();
		if(rows.length > 0){
			nui.confirm("确定解冻密码吗？","系统提示",function(action){
				if(action == "ok"){
					var empcodes = "";
				debugger;
					for(var i =0;i<rows.length;i++){
						empcodes += rows[i].EMPCODE + ",";
					}
					var json = nui.encode({"empcodes":empcodes.substring(0, empcodes.length-1)});
					var a= nui.loading("正在解冻密码,请稍等...","提示");
					$.ajax({
						url:"org.gocom.components.coframe.userInfoManage.userInfoManage.unlockUser.biz.ext",
						type:"post",
						data:json,
						contentType:"text/json",
						async:false,
						success:function(text){
						nui.hideMessageBox(a); //关闭加载提示（即正在提交中，请稍等）
							if(text.code == "1"){
								nui.alert("解冻成功","系统提示",function(action){
									if(action == "ok" || action == "close"){
									 	search();
									}
								});
							}else if(text.code == "-1"){
								nui.alert("密码解冻失败，系统异常");
							}else{
								nui.alert("密码解冻成功，记录操作日志失败");
							}
						}
					});
				}
			});
		}else{
			nui.alert("请至少选择一条数据");
		}
	}
    
    function CloseWindow(action) {
        if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
        else window.close();            
    }
    
    function update(flag){
       
       var row = grid.getSelected();
       if(row!=null){
       
       if(flag == '1'){
         nui.open({
	          url:"<%=request.getContextPath() %>/userInfoManage/userInfoManage/employee_basicinfo_update.jsp",
	          title:'编辑',
	          width:900,
	          height:400,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             
	             //方法1：后台查询一次，获取信息回显
	             /* var data = row;
	             iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
	             
	             //方法2：直接从页面获取，不用去后台获取
	               var data = {pageType:"edit",record:{employee:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }else if(flag == '2'){
         nui.open({
	          url:"<%=request.getContextPath() %>/userInfoManage/userInfoManage/employee_orginfo_update.jsp",
	          title:'编辑',
	          width:400,
	          height:200,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             
	             //方法1：后台查询一次，获取信息回显
	             /* var data = row;
	             iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
	             
	             //方法2：直接从页面获取，不用去后台获取
	               var data = {pageType:"edit",record:{map:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }else if(flag == '3'){
         nui.open({
	          url:"<%=request.getContextPath() %>/userInfoManage/userInfoManage/auth.jsp",
	          title:'编辑',
	          width:700,
	          height:500,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             
	             //方法1：后台查询一次，获取信息回显
	             /* var data = row;
	             iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
	             
	             //方法2：直接从页面获取，不用去后台获取
	               var data = {pageType:"edit",record:{map:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }
       
	       
       }else{
           nui.alert("请选中一条记录！");
       }
    }
    
 
    
    function selectionChanged(){
       var rows = grid.getSelecteds();
       if(rows.length>1){
           nui.get("update1").disable();
           nui.get("update2").disable();
           nui.get("update3").disable();
       }else{
           nui.get("update1").enable();
           nui.get("update2").enable();
           nui.get("update3").enable();
       }
    }
    
    
    function remove(){
      var rows = grid.getSelecteds();
      if(rows.length > 0){
        var json = nui.encode({maps:rows});
        /*var a= nui.loading("正在检测删除的数据是否有【在用】状态的，请稍等...","提示");
          if(checkisExist(json)!= 0)
	        {
	         nui.hideMessageBox(a);
	         nui.alert("删除失败，只能删除【停用】状态的数据！");
	         return false;
	        }
	     nui.hideMessageBox(a);   */
	     
         nui.confirm("确定注销选中记录？","系统提示",
           function(action){
             if(action=="ok"){
	            var json = nui.encode({maps:rows});
	           var a= nui.loading("正在注销中,请稍等...","提示");
		        $.ajax({
		          url:"com.gotop.xmzg.storeManage.sroreManage.delete_storeManage.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	nui.hideMessageBox(a);
		            var returnJson = nui.decode(text);
					if(returnJson.exception == null){
						nui.alert("注销成功", "系统提示", function(action){
							grid.reload();
						});
					}else{
						nui.alert("注销失败", "系统提示");
						grid.unmask();
					}
					
		          }
		        });
		     }
		   });
      }else{
        nui.alert("请选中一条记录！");
      }
    }
   
   
    function statustype(e){
      	    return nui.getDictText("STORE_STATUS", e.row.STATUS);
    }
    
    //判断要删除的数据是否有【在用】状态的
    function checkisExist(map){
      var vala;
      $.ajax({
        url:"com.gotop.xmzg.storeManage.sroreManage.checkIsExit.biz.ext",
        type: 'POST',
        data:map,
        cache: false,
        async:false,
        contentType:'text/json',
        success:function(text){
          vala = text.a;
          str=text.str;
        }
      });
      return vala;
    }
    
    //重置
    function clean(){
	   //不能用form.reset(),否则会把隐藏域信息清空
	   nui.get("btnEdit1").setValue("");
       nui.get("btnEdit1").setText(""); //nui-buttonedit插件需要setText()和setValue()同时清空
       nui.get("btnEdit2").setValue("");
       nui.get("btnEdit2").setText(""); //nui-buttonedit插件需要setText()和setValue()同时清空
       nui.get("empName").setValue("");
       nui.get("empCode").setValue("");
     }
     
   
       //时间判断开始时间不能大于结束时间
      function comparedate(e){
      var startDate = nui.get("queryData.startDate").getFormValue();
      var endDate = nui.get("queryData.endDate").getFormValue();
      if(startDate!="")
	    startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
	  if(endDate!=""){
		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
        if(e.isValid){
          if(startDate>endDate){
            e.errorText="结束日期必须大于或等于开始日期";
            e.isValid=false;
          }else
          {
          e.errorText="";
          e.isValid=true;
          }
        }
      }
    } 
     
     
     
    
  </script>
</body>
</html>
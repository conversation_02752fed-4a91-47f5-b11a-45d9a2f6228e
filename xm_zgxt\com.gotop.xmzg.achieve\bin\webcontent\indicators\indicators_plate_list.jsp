<%@page pageEncoding="UTF-8"%>	
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2019-06-04 14:51:16
  - Description:
-->
<head>
	<title>指标业务条线查询</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%@include file="/achieve/init.jsp"%>
</head>

<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="send_bw" class="nui-form">
	    <table class="table" style="width:100%;">
	        <tr>
	        	<th class="tit">指标业务条线代码：</th> 
		        <td><input class="nui-textbox" id="tip_code" name="obj/tip_code" style="width:170px;" /></td>
				<th class="tit">指标业务条线名称：</th> 
		        <td><input class="nui-textbox" id="tip_name" name="obj/tip_name" style="width:170px;" /></td>
				<th><a class="nui-button"  iconCls="icon-search" onclick="search">查询</a></th>
				<th><a class="nui-button" iconCls="icon-reset"  onclick="clean">重置</a></th>
		    </tr>
	    </table>
	  </div>
    </div>
  </div>
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
             <a class="nui-button" iconCls="icon-add" onclick="add">新增</a>
             <a class="nui-button" iconCls="icon-edit" onclick="update">修改</a>
             <a class="nui-button" iconCls="icon-remove" onclick="del">删除</a>
           </td>
        </tr>
      </table>
    </div>

  <div class="nui-fit">
	 <div id="bw_grid" class="nui-datagrid"
			 style="height: 100%;"
			 idField="id" 
			 totalField="page.count"  
			 showPageInfo="true"
			 allowResize="false"  
			 pageSize="20"  
			 allowUnselect="true"
			 multiSelect="true"
			 sizeList=[5,10,20,50,100] 
	         dataField="list"
			 url="com.gotop.xmzg.achieve.indicators.indicators_plate_list.biz.ext">
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      <div field="TIP_ORGCODES" visible="false"></div>
	      <div field="TIP_CODE" headerAlign="center" align="center" allowSort="true" >业务条线代码</div>
		  <div field="TIP_NAME" headerAlign="center" align="center" allowSort="true" >业务条线名称</div>
		  <div field="TIP_SORTING" headerAlign="center" align="center" allowSort="true" >排序</div>
		  <div field="ORGCODES" headerAlign="center" align="center" allowSort="true">管理机构</div>
		  <div field="TIP_CREATETIME" headerAlign="center" align="center" allowSort="true" renderer="setdate">创建时间</div>
		  <div field="CREATEORGNAME" headerAlign="center" align="center" allowSort="true">创建机构</div>
		  <div field="CREATORNAME" headerAlign="center" align="center" allowSort="true">创建人</div>
	    </div>
	 </div>
  </div>
  
 <script type="text/javascript">
    nui.parse();
    var grid = nui.get("bw_grid");
	search();
	//查询
    function search() {
    	var form = new nui.Form("#send_bw");
 		form.validate();
      	if (form.isValid() == false) return;
      	grid.load(form.getData());
      	//插入系统操作日志
        var OPE_MOD = "指标业务条线管理维护";
    	var OPE_CONTENT = "查询,输入项:"+nui.get("tip_code").getValue()+"|"+nui.get("tip_name").getValue();
      	insert_sysope_log(OPE_MOD,OPE_CONTENT);
	}
	//转换时间
    function setdate(e){
 		var date = e.record.TIP_CREATETIME;
 		if(!isNullOrEmpty(date) && date.length == 14){
 			return changeDate(date);
 		}else{
 			return "";
 		}
 	}
    //打开添加页面
	function add(){
		nui.open({
			url:bactpath+"/achieve/indicators/indicators_plate_add.jsp",
			title:"新增指标业务条线",
			width:500,
			height:250,
			onload:function(){},
			ondestroy:function(action){
				if(action=="saveSuccess"){
	                grid.reload();
	            }
			}
		});
	}
	
	//打开修改页面
	function update(){
		var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
			nui.open({
				url:bactpath+"/achieve/indicators/indicators_plate_update.jsp",
				title:"修改指标业务条线",
				width:500,
				height:250,
				onload:function(){
					var iframe = this.getIFrameEl();
 	      	    	iframe.contentWindow.setData(rows[0]);
				},
				ondestroy:function(action){
					if(action=="saveSuccess"){
		                grid.reload();
		            }
				}
			});
		}
	}
	//删除
	function del(){
		var rows = grid.getSelecteds();
		if(rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
			nui.confirm("确定删除以下选择的数据？", "确定？",
	           function (action) {
	             if (action == "ok") {
	            	 var newMaps = [];
	            	 var arr = [];
	            	 var str = "";
	            	 for(var i = 0;i<rows.length;i++){
	            		 var ids = {tip_code:rows[i].TIP_CODE,tip_name:rows[i].TIP_NAME};
	            		 newMaps.push(ids);
	            		 arr.push(rows[i].TIP_CODE);
	            		 if(i==rows.length-1){
	            			 str+=rows[i].TIP_CODE;
	            		 }else{
	            			 str+=rows[i].TIP_CODE+",";
	            		 }
	            	 }
	            	 str = "删除以下【"+str+"】指标条线";
	            	 var json = nui.encode({maps:newMaps,str:str,arr:arr});
	            	 var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
	            	 //提交数据
                     nui.ajax({
                        url: "com.gotop.xmzg.achieve.indicators.indicators_plate_del.biz.ext",
                        type: "post",
                        data: json,
                        contentType:'text/json',
                        success: function (text) {
                           nui.hideMessageBox(load);  //隐藏遮罩层
                     	   var code = text.code;
                     	   var msg = text.msg;
                     	   nui.alert(msg, "系统提示", function(action){});
                     	   grid.reload();
                        }
                    });
	             }
			});
		}
	}
	//插入系统操作日志
    function insert_sysope_log(OPE_MOD,OPE_CONTENT){
		$.ajax({
				url: "com.gotop.xmzg.achieve.indicators.insert_sysope_log.biz.ext",
				type: 'POST',
				data : nui.encode({"OPE_MOD":OPE_MOD,"OPE_CONTENT":OPE_CONTENT}),
				cache: false,
				contentType:'text/json',
				success: function (res) {  
				}
		});
	}
	//重置
	function clean(){
		nui.get("tip_name").setValue("");
		nui.get("tip_code").setValue("");
 	}
  </script>
</body>

</html>
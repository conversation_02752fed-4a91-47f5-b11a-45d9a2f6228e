<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-01-05 11:41:00
  - Description:
-->
<head>
<%-- <%@include file="/coframe/tools/skins/common.jsp" %> --%>
<%@include file="/coframe/dict/common.jsp"%>
<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
    <%
		java.text.SimpleDateFormat formatter = new java.text.SimpleDateFormat("yyyyMMdd"); 
        java.util.Date currentTime = new java.util.Date();//得到当前系统时间 
        String str_date = formatter.format(currentTime); //将日期时间格式化 
        java.util.Calendar cal = java.util.Calendar.getInstance();
        int year = cal.get(java.util.Calendar.YEAR);
        String str_date_end = year + "1231";
    %>
</head>
<body>
<!-- JF_POSITION -->
<input class="nui-dictcombobox" valueField="dictID" textField="dictName" id="JF_POSITION" dictTypeId="JF_POSITION" style="display:none;"/>
<input id="orgTree" class="nui-treeselect"  multiSelect="true" url="com.gotop.xmzg.achieve.common.get_OrgTree.biz.ext"
    dataField="treeNodes" textField="TEXT" valueField="ORGCODE" parentField="PCODE" checkRecursive="true" 
    showFolderCheckBox="true"  expandOnLoad="true" showClose="true" oncloseclick="onCloseClick"
    popupWidth="230" style="display:none;"/>  
  <div id="form1" style="padding-top:5px;">
   <input class="nui-hidden" id="TA_ID" name="map.TA_ID" /> 
   <input class="nui-hidden" id="TA_START" name="map.TA_START" value="1"/> 
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>考核方案名称：</label></th>
        <td colspan="3" >  
        	<input  id="item_no" name = "map.TA_NAME"  class="nui-textbox" required="true" style="width:100%;" vtype="maxLength:50"/>
        </td> 
      </tr>
      
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>考核时间：</label></th>
        <td  colspan="3">  
        	<input id="TA_TIME_START" name = "map.TA_TIME_START" class="nui-datepicker"  style="width:45%;" required="true" allowInput="false" format="yyyyMMdd"  />
        <span style="width:8.5%; text-align:center;display: inline-block;">至</span>
        <input id="TA_TIME_END" name = "map.TA_TIME_END" class="nui-datepicker"  style="width:45%;" required="true" allowInput="false" format="yyyyMMdd" />
        </td>

      </tr>
      
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>考核对象：</label></th>
        <td colspan="3" >  
        	<input id="TA_OBJECT"  class="nui-dictcombobox" name="map.TA_OBJECT"  style="width:100%;" allowInput="false" 
                valueField="dictID" textField="dictName" dictTypeId="JF_KHDX"   nullItemText="请选择" emptyText="请选择" showNullItem="true"  required="true"                
             	value = "1" onvaluechanged="onObjectChanged" />    
        </td> 
      </tr>
      
      <tr id="orgTr" >
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>机构范围：</label></th>
        <td colspan="3" >  
        	<!-- <input id="btnEdit" name = "map.TA_ORGTYPE"  class="nui-buttonedit"  allowInput="false" onclick="Orgon" onbuttonclick="OrgonButtonEdit" style="width:100%;" required="true"/> --> 
        	<input id="btnEdit" name = "map.TA_ORGTYPE"  class="nui-textboxlist belongDownInput" style="width: 100%;" allowInput="false" required="true"/>     
        </td> 
      </tr>
      <tr id="empTr" >
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>人员类别：</label></th>
        <td colspan="3" >  
        	<div id="EMPTYPE" name = "map.TA_EMPTYPE" class="mini-combobox" style="width:100%;"  popupWidth="600" textField="dictName" valueField="dictID"  required="true" 
		     multiSelect="true"  showClose="true" oncloseclick="onCloseClick" onclick="setDataEmp">     
		    <div property="columns">
		        <!-- <div header="code" field="dictID" width=60></div> -->
		        <div header="名称" field="dictName" ></div>
		    </div> 
		    
		</div>  
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>管理机构：</label></th>
        <td colspan="3" >  
        	<input id="btnEdit1" name = "map.TA_ORGCODES"  class="nui-textboxlist belongDownInput"  allowInput="false" style="width:100%;" required="true"/>    
        </td> 
      </tr>
       <tr id="khzbTr">
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>考核目标：</label></th>
        <td colspan="3" >  
        	
        </td> 
      </tr>
    </table>
	</div>
    <div id="khzb">
    <center>
    <div style="width:95%; " >
		        <div class="mini-toolbar" style="border-bottom:0;padding:0px;">
		            <table style="width:100%;">
		                <tr>
		                    <td style="width:100%;">
		                    	<div id="formQuery" style="float:right">
					   				<input id="q_TA_ID" name="queryData.TA_ID" class="nui-hidden"/> 
					   				<input id="q_TID_NAME" name="queryData.TID_NAME"  class="nui-textbox" emptyText="请输入指标细项名称"/>
					   				<a id="q_btn" class="nui-button"  iconCls="icon-search" onclick="search">查询</a>
					   			</div>
					   			
		                    </td>
		                    
		                </tr>
		            </table>           
		            
		        </div>
		    </div>
		    
		    <div id="datagrid1" class="nui-datagrid" style="width:95%;height:245px;" 
		        url = "com.gotop.xmzg.achieve.assess.assessTarget_list.biz.ext" dataField="resultList"
		        allowResize="true" pageSize="20" 
		        allowCellEdit="true" allowCellSelect="true" multiSelect="true"
		        allowCellValid="true" oncellvalidation="onCellValidation" >
		        <div property="columns">
		            <div type="indexcolumn"></div>
            		<div type="checkcolumn"></div>
            		<div field="TIP_NAME" align="center"  headerAlign="center">业务条线名称</div>  
		            <div field="TI_NAME" align="center"  headerAlign="center">指标名称</div>                
		            <div field="TID_NAME"  align="center" headerAlign="center">指标细项名称</div>
		            <div field="TAT_TIME_START" align="center" headerAlign="center" renderer="dateStr">开始时间 
		                <input property="editor"  class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd"/>
		            </div>
		            <div field="TAT_TIME_END" align="center" headerAlign="center" renderer="dateStr">结束时间
		                <input property="editor" class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd" />
		            </div>                               
		            <div field="TAT_TYPE" align="center" headerAlign="center" renderer="dictMblx">目标类型
		               <input property="editor" class="nui-dictcombobox" dictTypeId="JF_MBLX"  valueField="dictID" textField="dictName"  style="width:100%;"/>
		            </div>  
		            <div field="TAT_SPEC" align="center" headerAlign="center" renderer="dictMbzl">目标种类
		               <input property="editor" class="nui-dictcombobox"  dictTypeId="JF_MBZL"  valueField="dictID" textField="dictName"  style="width:100%;"/>
		            </div>  
		            <div field="TAT_ORGCODE"  align="center" headerAlign="center" renderer="dictOrg">机构
		                <input property="editor" class="nui-treeselect"  url="com.gotop.xmzg.achieve.common.get_OrgTree.biz.ext"
					        dataField="treeNodes" textField="TEXT" valueField="ORGCODE" parentField="PCODE" checkRecursive="false" multiSelect="true" 
					        showFolderCheckBox="true"  expandOnLoad="0" showClose="true" oncloseclick="onCloseClick" popupWidth="230"
					        />
		               <!-- <input property="editor" class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:100%;"/>  -->
		            </div>
		            <div field="TAT_POSITION"  align="center" headerAlign="center" renderer="dictPos">岗位
		               <div property="editor" class="mini-combobox" style="width:100%;"  popupWidth="200" textField="dictName" valueField="dictID"
					     multiSelect="true"  showClose="true" oncloseclick="onCloseClick" onclick="setDataEmp">     
					    <div property="columns">
					        <!-- <div header="code" field="dictID" width=60></div> -->
					        <div header="名称" field="dictName" ></div>
					    </div>
					    </div>
		            </div>
		            <div field="TAT_TARGET_YEAR" vtype="float" align="center" headerAlign="center">年度目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_YEAR1" vtype="float" align="center" headerAlign="center">上半年目标
		                <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_YEAR2" vtype="float" align="center" headerAlign="center">下半年目标
		                <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_QUAR1" vtype="float" align="center" headerAlign="center">一季度目标
		                <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_QUAR2" vtype="float" align="center" headerAlign="center">二季度目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_QUAR3" vtype="float" align="center" headerAlign="center">三季度目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_QUAR4" vtype="float" align="center" headerAlign="center">四季度目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_MONTH1"  vtype="float" align="center" headerAlign="center">1月目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_MONTH2" vtype="float" align="center" headerAlign="center">2月目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_MONTH3" vtype="float" align="center" headerAlign="center">3月目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_MONTH4" vtype="float" align="center" headerAlign="center">4月目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_MONTH5" vtype="float" align="center" headerAlign="center">5月目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_MONTH6" vtype="float" align="center" headerAlign="center">6月目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_MONTH7" vtype="float" align="center" headerAlign="center">7月目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_MONTH8"  vtype="float" align="center" headerAlign="center">8月目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            	
		            <div field="TAT_TARGET_MONTH9" vtype="float" align="center" headerAlign="center">9月目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_MONTH10" vtype="float" align="center" headerAlign="center">10月目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_MONTH11" vtype="float" align="center" headerAlign="center">11月目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_MONTH12" vtype="float" align="center" headerAlign="center">12月目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAT_TARGET_PHASE" vtype="float" align="center" headerAlign="center">阶段目标
		               <input property="editor" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		        </div>
		    </div>
    </center>
    </div>
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a id="save" class="nui-button" iconCls="icon-save" onclick="onOk">保存</a>
	          <span style="display:inline-block;width:10px;"></span>
	          <a  class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>


  <script type="text/javascript">
  	var open_type = "add";
  	
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);
    
    var saveUrl = "com.gotop.xmzg.achieve.assess.assessTarget_add.biz.ext";
    function setData(data){
                   
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);
		open_type = infos.pageType;
          
	      //表单数据回显
          var json = infos.record;
          objectChanged(json.map.TA_OBJECT);
          form.setData(json);
          nui.get("q_TA_ID").setValue(json.map.TA_ID);
          nui.get("btnEdit1").setText(json.map.ORGCODES);
          
          if(json.map.TA_EMPTYPE){
          	nui.get("EMPTYPE").setText(json.map.EMPTYPE);
          }
          if(json.map.TA_ORGTYPE){
          	nui.get("btnEdit").setText(json.map.ORGTYPE);
          }

			var controls=form.getFields();
			for(var i = 0 ; i < controls.length ; i++){
				controls[i].setEnabled(false);
			}
         search();  
    }
    
    function search(){
       var formQuery = new nui.Form("#formQuery");
       formQuery.validate();
       if (formQuery.isValid() == false) return;
       var data = formQuery.getData(true,true);
       grid.load(data);
    }
    
    function onOk(){
      saveData();
    }
    
    //提交
    function saveData(){   
      form.validate();
      if(form.isValid()==false) return;
      
      var data = form.getData(true,true);
      
      grid.validate();
      if (grid.isValid() == false) {   
        var error = grid.getCellErrors()[0];
        grid.beginEditCell(error.record, error.column);
        return;
      }
      var tats = grid.getChanges();
      data.tats = tats;
      console.log(data);
      var json = nui.encode(data);
     
      $.ajax({
        url:saveUrl,
        type:'POST',
        data:json,
        cache:false,
        contentType:'text/json',
        success:function(text){
            var returnJson = nui.decode(text);
			if(returnJson.exception == null && returnJson.iRtn == 1){
				nui.alert("操作成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveSuccess");
					}
				});
			}else if(returnJson != null && returnJson.iRtn == -1){
				nui.alert("您没有操作管理权限！", "系统提示");
			}else{
				nui.alert("操作失败", "系统提示");
			}
        }
      });
    }
    
    function onObjectChanged(e){
    	objectChanged(e.value);
    }
    
    function objectChanged(val){
    	if(val == 0){
    		//$("#orgTr").show();
    		$("#empTr").hide();
    	}else{
    		$("#empTr").show();
    		//$("#orgTr").hide();
    	}
    }
    
    function setDataEmp(){
    	this.setData(nui.get("JF_POSITION").data);
    }
    
    function onCloseClick(e){
    	var obj = e.sender;
    	obj.setText("");
        obj.setValue("");
    }
    var orgs = [];
    function OrgonButtonEdit(e) {
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/achieve/common/org_multSelectTree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                onload : function() {
					var iframe = this.getIFrameEl();
					iframe.contentWindow.setTreeCheck(btnEdit.getValue());
				},
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ORGCODE);
                            btnEdit.setText(data.TEXT);
                            orgs.push({ids:data.ORGCODE,names:data.TEXT});
                        }
                    }
                }
            });
        }
    
    
    function dictOrg(e){
    	var orgTree=nui.get("orgTree").getList();
	    if(e.value != null){
	    	var str = "";
	    	var datas = e.value.split(",");
	    	for(var i=0 ; i < orgTree.length ; i++){
    			for(var x=0 ; x < datas.length ; x++){
    				if(orgTree[i].ORGCODE == datas[x]){
    					str += orgTree[i].TEXT +",";
    				}
				}
	    	}
	    	if(str != "") str = str.slice(0,str.length-1);
	    	return str;
	    }
    } 
    
    function dictPos(e){
    	if(e.value != null){
    		var str = "";
    		var datas = e.value.split(",");
    		for(var i=0 ; i < datas.length ; i++){
    			str += nui.getDictText("JF_POSITION", datas[i])+",";
			}
			if(str != "") str = str.slice(0,str.length-1);
	    	return str;
		} 
    }  
    function onCancel(){
      CloseWindow("cancel");
    }
    
    function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
  
  	//时间判断开始时间不能大于结束时间
    function comparedate(e,startDate,endDate){
    	if(startDate instanceof Date){
    		startDate = nui.formatDate ( startDate, 'yyyyMMdd' );
    	}
    	if(endDate instanceof Date){
    		endDate = nui.formatDate ( endDate, 'yyyyMMdd' );
    	}
      if(startDate!="" && endDate!=null && endDate.length > 10)
	    startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
	  if(endDate!="" && endDate!=null && endDate.length > 10)
		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
         if(e.isValid){
          if(startDate > endDate){
            e.errorText="结束日期必须大于开始日期";
            e.isValid=false;
          }else{
          e.errorText="";
          e.isValid=true;
        }
        }
    }
    
     
    var grid = nui.get("datagrid1");

    function onCellValidation(e) {
        if (e.field == "TAT_TIME_START" || e.field == "TAT_TIME_END") {
        	comparedate(e,e.row.TAT_TIME_START,e.row.TAT_TIME_END);
        }
        if (e.field == "TAT_TARGET_YEAR" 
			|| e.field == "TAT_TARGET_QUAR1" || e.field == "TAT_TARGET_QUAR2" || e.field == "TAT_TARGET_QUAR3" || e.field == "TAT_TARGET_QUAR4" 
			|| e.field == "TAT_TARGET_MONTH1" || e.field == "TAT_TARGET_MONTH2" || e.field == "TAT_TARGET_MONTH3" || e.field == "TAT_TARGET_MONTH4" 
			|| e.field == "TAT_TARGET_MONTH5" || e.field == "TAT_TARGET_MONTH6" || e.field == "TAT_TARGET_MONTH7" || e.field == "TAT_TARGET_MONTH8" 
			|| e.field == "TAT_TARGET_MONTH9" || e.field == "TAT_TARGET_MONTH10" || e.field == "TAT_TARGET_MONTH11" || e.field == "TAT_TARGET_MONTH12" 
			|| e.field == "TAT_TARGET_YEAR1" || e.field == "TAT_TARGET_YEAR2" || e.field == "TAT_TARGET_PHASE"
		) {
        	if(e.value < 0){
        		e.errorText="不能输入负数";
            	e.isValid=false;
        	}
        }
    }
    function dateStr(e){
    	var endDate = e.value;
    	if(endDate instanceof Date){
    		return endDate = nui.formatDate ( endDate, 'yyyyMMdd' );
    	}
    	if(endDate!="" && endDate!=null && endDate.length > 10){
    		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
    	}
    	return endDate;
    }
    function dictMblx(e){
    	return nui.getDictText("JF_MBLX", e.value);
    }
    
    function dictMbzl(e){
    	return nui.getDictText("JF_MBZL", e.value);
    }
  </script>
</body>
</html>
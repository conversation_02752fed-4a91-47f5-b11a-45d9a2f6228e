<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-08-23 00:22:11
  - Description:群组查询，查看公共群组和个人群组
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>群组查询</title>
</head>
<body style="width:100%;overflow:hidden;">
 
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	  <div id="c_orgid" class="nui-hidden"  name="queryData.c_orgid" ></div>
	  <div  class="nui-hidden"  id="c_role" ></div>
	    <table class="table" style="width:100%;">
	      <tr >
		     
			<th class="tit">群组名称：</th>
			<td>
				<input id="groupName" name="queryData.groupName" class="nui-textbox" style="width:200px;" vtype="maxLength:250"/>
			</td>	

	         <th ></th>
		     <td>		  
	            <a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;
	            <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
	         </td>
	        </tr>
	    </table>
	  </div>
    </div>
  </div>
  
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
		     <a id="update" class="nui-button" iconCls="icon-edit" onclick="update">详情</a>
           </td>
        </tr>
      </table>
    </div>
 
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="org.gocom.components.coframe.userInfoManage.groupManage.query_groupManage.biz.ext"
	  sizeList=[5,10,20,50,100]  pageSize="10" onselectionchanged="selectionChanged">
	    <div property="columns" >
	      <div type="checkcolumn"></div>
	      <div field="GROUP_NAME" headerAlign="center" align="center">群组名称</div>
	      <div field="GROUP_DETAIL" headerAlign="center" align="center" width=150>群组描述</div>
	      <div field="GROUP_TYPE" headerAlign="center" align="center">类型</div>    
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">
    nui.parse();
    var grid = nui.get("datagrid1");   
     
    //grid.load();
    var a;
    
    function search(){

       var form = new nui.Form("#form1");
            form.validate();
            if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
    
    
    function update(){
       var row = grid.getSelected();
       if(row!=null){
          var json = nui.encode({map:row});
          
	       nui.open({
	          url:"<%=request.getContextPath() %>/userInfoManage/groupManage/groupDetail/groupDetail_update.jsp",
	          title:'详情',
	          width:600,
	          height:400,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             
	             //方法1：后台查询一次，获取信息回显
	             /* var data = row;
	             iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
	             
	             //方法2：直接从页面获取，不用去后台获取
	               var data = {pageType:"edit",record:{map:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }else{
           nui.alert("请选中一条记录！");
       }
    }
    
 
    
    function selectionChanged(){
       var rows = grid.getSelecteds();
       if(rows.length>1){
           nui.get("update").disable();
       }else{
           nui.get("update").enable();
       }
    }
    
    
    //重置
    function clean(){
       nui.get("groupName").setValue("");
     }

  </script>
</body>
</html>
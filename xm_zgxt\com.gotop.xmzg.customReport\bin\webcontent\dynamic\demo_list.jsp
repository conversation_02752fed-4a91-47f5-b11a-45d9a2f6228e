<%@page pageEncoding="UTF-8"%>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-07-04 14:44:11
  - Description:
-->
<%
UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>

<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>demo报表</title>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <div class="nui-hidden"  name="queryData.userOrgId" id="userOrgId"></div>
		<div class="nui-hidden"  name="queryData.userId"  id="userId" ></div>
	    <table class="table" style="width:100%;">
	        <tr>
	            <th class="tit" >机构号：</th>
				<td>
					<input id="queryData.orgid" name="queryData.orgid" class="nui-textbox" style="width:130px;" vtype="maxLength:30"/>
				</td>
				
				<th class="tit" >单册代码：</th>
				<td>
					<input id="queryData.item_no" name="queryData.item_no" class="nui-textbox" style="width:130px;" vtype="maxLength:50"/>
				</td>
										
				<th  class="btn-wrap">
					<input class="nui-button" text="查询" iconCls="icon-search" onclick="search"/>&nbsp;&nbsp;
					<input class="nui-button" text="重置" iconCls="icon-reset" onclick="clean"/>&nbsp;&nbsp;
					<input class="nui-button" text="导出报表" iconCls="icon-download" onclick="excel"/>
				</th>
			</tr>
	    </table>
	  </div>
    </div>
  </div>
   <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.customReport.dynamic.select_demo.biz.ext"
	  sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10">
	    <div property="columns" >
	      <div field="ORGNAME" headerAlign="center" align="center" width=200>机构名称</div>
	      <div field="ORGCODE" headerAlign="center" align="center">机构号</div>
	      <div field="ITEM_NO" headerAlign="center" align="center">单册代码</div>
	      <div field="ITEM_NAME" headerAlign="center" align="center">单册名称</div>
	      <div field="PRE_TAX_PRICE" headerAlign="center" align="center">税前单价</div>
	      <div field="TAX_PRICE" headerAlign="center" align="center">含税单价</div>
	      <div field="NUM" headerAlign="center" align="center">库存</div>
	      <div field="PRE_TAX_AMT" headerAlign="center" align="center">税前金额</div>
	      <div field="TAX_AMT" headerAlign="center" align="center">含税金额</div>
	           
	    </div>
	 </div>
  </div>
  <script type="text/javascript">
     nui.parse();
    var grid = nui.get("datagrid1");
    var  userOrgId="<%=userObject.getUserOrgId()%>";
	var  userId="<%=userObject.getUserId()%>";
    
	nui.get("userOrgId").setValue(userOrgId);
    nui.get("userId").setValue(userId);
    
    var form = new nui.Form("#form1");
	var data = form.getData(true,true);
    //grid.load(data);
    
    function search(){
       form = new nui.Form("#form1");
       form.validate();
       if (form.isValid() == false) return;
       data = form.getData(true,true);
       grid.load(data);
    }
    
//导出Excel
function excel(){     
	var form=new nui.Form("form1");
	form.validate();
    if (form.isValid() == false) return;
	var data=form.getData(true, true);
	var fileName="库存查询报表";
	var queryPath="com.gotop.xmzg.report.libraryQuery.query_library";
	var columns=grid.getBottomColumns();
	columns=columns.clone();
	for(var i=0;i<columns.length;i++){
		var column=columns[i];
		if(!column.field){
			columns.removeAt(i);
		}else{
			var c={header:column.header,field:column.field };
			columns[i]=c;
		}
	}
	columns=nui.encode(columns);
	data=nui.encode(data);
     var url="<%=request.getContextPath()%>/util/exportExcel/exportExcel.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data;
	 window.location.replace(encodeURI(url));
	 
	 //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
     setMask();
} 

    //机构树回显
     function onButtonEdit(e) {
            var btnEdit = this;
            nui.open({
                <%--  url:"<%=request.getContextPath() %>/kpiCheckManage/ProOrgKpiManage/Prokpiorg_tree.jsp",  //初始未展开的机构树，加载快  --%>
                url:"<%=request.getContextPath() %>/report/tree/ReportOrg_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            
                        }
                    }
                }
            });            
             
        }  


 //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)	
function setMask(){
	 
	 var a= nui.loading("正在操作中,请稍等...","提示"); //显示遮罩层
	 
	 var icount = setInterval(function(){  
	
		  if(document.attachEvent){   //IE浏览器
	
		 	if(document.readyState=='interactive'){

	              nui.hideMessageBox(a);  //隐藏遮罩层
	              clearTimeout(icount);
	        }
		 }else{ //谷歌浏览器
		 	if(document.readyState=='complete'){

	              nui.hideMessageBox(a);  //隐藏遮罩层
	              clearTimeout(icount);
	        }
		 } 
	 
	 }, 1); 
	 
}

  function adjusttype(e){
      	    return nui.getDictText("PER_ADJUST_TYPE", e.row.ADJUST_TYPE);
  }
   
   function clean(){  
       nui.get("queryData.orgid").setValue(""); 
       nui.get("queryData.item_no").setValue("");
   }

  </script>
</body>
</html>
<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): liuyl
  - Date: 2019-05-10 17:41:00
  - Description:
-->
<head>
<%@include file="/coframe/dict/common.jsp"%>
<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
</style>
</head>
<body>
  <div id="form1" style="padding-top:5px;">
    <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
     <tr>
      	<th align="center" colspan="8" style="font-size:18px;">数据源系统参数维护</th>
      </tr>
      <tr>
      	<th class="nui-form-label" style="width:150px;"><label for="map.type$text">系统名称：</label></th>
        <td colspan="3" >    
          <input id="map.systemName" name="map.systemName"   style="width:150px;" class="nui-textbox asLabel" readOnly="true"/>
        </td> 
      	<th class="nui-form-label" style="width:150px;"><label for="map.type$text">登录网址：</label></th>
        <td colspan="3" >    
          <input id="map.systemUrl" name="map.systemUrl"   style="width:200px;" class="nui-textbox asLabel" readOnly="true"/>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label" style="width:150px;"><label for="map.type$text">维护部门：</label></th>
        <td colspan="7" >  
            <input id="map.managerOrgName" name = "map.managerOrgName"  class="nui-textbox asLabel"  readOnly="true"  style="width:150px;"/>      
            <input id="map.managerOrg" name = "map.managerOrg"  class="nui-hidden"  readOnly="true"  style="width:150px;"/>      
        </td> 
       </tr>
      <!--  <tr>
        <th class="nui-form-label" style="width:150px;"><label for="map.status$text">是否输入验证码：</label></th>
		<td colspan="7">
            <input class="nui-radiobuttonlist"  id="map.isEncry"  name="map.isEncry" data="[{id:'1',text:'是'},{id:'2',text:'否'}]"  textField="text" valueField="id" value="1" />
		           					
		</td>
      </tr> -->
      </div>
    </table>
  </div>

  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);
    loadData();
    //页面打开时调用方法
	function loadData() {
	        	var id='<%=request.getParameter("id")%>';
				nui.ajax({
					url : "com.gotop.xmzg.datasourceParam.dataSourceParam.getDataSourceParamDetail.biz.ext",
					data : nui.encode({"id":id}),
					type : "POST",
					cache : false,
					contentType : "text/json",
					success : function(text) {
					    form.setData(text,false);
					},
					error : function(jqXHR, textStatus, errorThrown) {
						alert(jqXHR.responseText);
					}
				});
				
								
			}

  </script>
</body>
</html>
<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<%@ page import="com.eos.data.datacontext.UserObject" %>	
<!-- 
  - Author(s): lyl
  - Date: 2019-12-03
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>档案台账清单</title>
</head>
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
 %>
<body style="margin:0px;width:100%;overflow:hidden;" >
	<div class="search-condition">
		<div class="list">
			<div id="filesForm" class="nui-form">
				<div class="nui-hidden"  name="queryData.userOrgId" id="userOrgId"></div>
				<div class="nui-hidden"  name="queryData.userId"  id="userId" ></div>
		   		<table class="table" style="width:100%;">
		      		<tr>
						<td align="right">档案种类：</td>
						<td>
							<input  class="nui-dictcombobox" id="filesType" name="queryData.filesType"  emptyText="请选择"  dictTypeId="FILES_TYPE" style="width:160px;"/>
						</td>
						<td align="right">档案名称：</td>
						<td>
							<input name="queryData.filesName" id="filesName" class="nui-textbox" style="width:160px;"/>
						</td>
						<td align="right">档案状态：</td>
						<td>
							<input  class="nui-dictcombobox" name="queryData.fileStatus"  emptyText="请选择" dictTypeId="FILES_STATUS" style="width:160px;"/>
						</td>
						<td >
							<a class="nui-button" iconCls="icon-search" onclick="searchData();">查询</a>
							<a class="nui-button" iconCls="icon-reset"  onclick="clean()"/>重置</a>
						</td>
						</tr>
						<tr>
						<td align="right">库存地址：</td>
						<td>
							<input  class="nui-dictcombobox" name="queryData.storageAddr"  emptyText="请选择" dictTypeId="FILES_STORAGE_ADDRESS" style="width:160px;"/>
						</td>
			        	<td align="right">货架号/箱号：</td>
						<td>
							<input name="queryData.storageLocation"  class="nui-textbox" style="width:160px;"/>
						</td>
						<td align="right">档案盒号：</td>
						<td>
							<input name="queryData.boxNum"  class="nui-textbox" style="width:160px;"/>
						</td>
						<td >
							<a id="imp" class="nui-button" iconCls="icon-upload" onclick="importExcel" >数据初始化/批量入库</a>
							<a class="nui-button" iconCls="icon-reset"  onclick="exportExcel()"/>导出</a>
						</td>
				
					</tr>
					<tr>
						<td align="right">起期区间：</td>
						<td>
							<input name="queryData.startTime" id="startTime" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:90px;"/>
							-
							<input name="queryData.startTime1" id="startTime1" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:90px;"/>
						</td>
						<td align="right">经办机构：</td>
						<td>
							<input id="btnEdit1" name = "queryData.DEAL_ORG"  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:180px;"/>
						</td>
						<td align="right">分管客户经理名称：</td>
						<td>
							<input name="queryData.EMPNAME" class="nui-textbox" style="width:180px;"/>
						</td>
					</tr>
		    	</table>
		  	</div>
		</div>
	</div>
	<div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      	<table style="width:100%;">
        	<tr>
           		<td style="width:100%;">
			     	<a class="nui-button" iconCls="icon-add" onclick="upAddress" >入库/移库</a>
					<a class="nui-button" iconCls="icon-edit" onclick="borrow">借阅</a> 
					<a class="nui-button" iconCls="icon-edit" onclick="giveBack">归还</a> 
					<!-- <a class="nui-button" iconCls="icon-edit" onclick="apply">修改</a>  -->
 					<a class="nui-button" iconCls="icon-edit" onclick="update">修改</a>
					<a class="nui-button" iconCls="icon-node" onclick="fileDetail">档案明细</a>  
	             	<a class="nui-button" iconCls="icon-node" onclick="operateDetail">操作明细</a>   
           		</td>
        	</tr>
      	</table>
    </div>
    <!-- 档案主表信息 -->
  	<div class="nui-fit" id="fieldset1">
	 	<div id="grid"  dataField="resultList" idField="dicttypeid" class="nui-datagrid" style="width:100%;height:100%;"
	 	allowCellWrap = "true" 
	  	url="com.gotop.xmzg.files.fileList.queryFileRecordList.biz.ext" sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" >
	    	<div property="columns">
	    		<div type="checkcolumn"></div>
	    		<div field="ROWN" width="30px">序号</div>
		        <div field="FILES_TYPE" headerAlign="center" align="center" width="120px" renderer="showFilesType">档案种类</div>
	    		<div field="BUSINESS_TYPE" headerAlign="center">业务种类</div>
	    		<div field="DEAL_NAME" headerAlign="center">经办机构</div>
	    		<div field="DEAL_ORG" headerAlign="center">经办机构号</div>
	    		<div field="EMP_NAME" headerAlign="center">分管客户经理</div>
	    		<div field="EMPNAME" headerAlign="center">分管客户经理号</div>
	    		<div field="CUSTOMER_NAME" headerAlign="center">客户名称</div>
	    		<div field="CUSTOMER_NO" headerAlign="center">客户号码</div>
	    		<div field="CONTRACT_NUMBER" headerAlign="center">编号</div>
		        <div field="CONTRACT_PRICE" headerAlign="center" align="center" width="80px">金额</div>
		        <div field="START_TIME" headerAlign="center" align="center" width="80px">起期</div>
		        <div field="END_TIME" headerAlign="center" align="center" width="80px">止期</div>
		        <div field="BUSINESS_LINE" headerAlign="center">业务条线</div>
		        <div field="CHECK_TIME" headerAlign="center">归档申请日期</div>
		        <div field="SP_STATUS" headerAlign="center" renderer="getTextSPStatus">档案审批状态</div>
		        <div field="FILES_STATUS" headerAlign="center" align="center" width="100px" renderer="showStatusName">档案状态</div>
		        <div field="STORAGE_ADDRESS" headerAlign="center" align="center" width="80px" renderer="showAddress">存放地址</div>
		        <div field="STORAGE_LOCATION" headerAlign="center" align="center" width="100px">货架号/箱号</div>
		        <div field="BOX_NUM" headerAlign="center" align="center" width="100px">档案盒号</div>
		        <div field="AFFILIATED_NAMES" headerAlign="center" renderer="onActionRender" align="center" width="150px">附件</div>
		    </div>
	 	</div>
  	</div>
</body>
</html>
<script type="text/javascript">
	nui.parse();
	var grid = nui.get("grid");
	var form = new nui.Form("filesForm");
//	var formData = new nui.Form("filesForm").getData(true,true);
//	grid.load();
	
	function searchData(){	
		var formData = new nui.Form("filesForm").getData(true,true);
    	grid.load(formData);
    }
    	
    //数据字典项回显
    //档案状态
    function showStatusName(e){
    	return nui.getDictText("FILES_STATUS", e.row.FILES_STATUS);
    }
    //档案种类
    function showFilesType(e){
    	return nui.getDictText("FILES_TYPE", e.row.FILES_TYPE);
    }
    //库存地址
    function showAddress(e){
    	return nui.getDictText("FILES_STORAGE_ADDRESS", e.row.STORAGE_ADDRESS);
    }
    //档案审批状态
    function getTextSPStatus(e){
	    return nui.getDictText("FILES_TO_ARCHIVE_STATUS", e.row.SP_STATUS);
	}	
	
    //借阅档案
    function borrow(){
    	var rows = grid.getSelecteds();
      	if(rows.length > 0){
	        nui.confirm("确定借阅选中档案？","系统提示",function(action){
	        	if(action=="ok"){
	        		var fileStatus = '2';//档案状态变更为借阅
		      		for (var i=0;i<rows.length;i++){
			        		rows[i].FILES_STATUS = fileStatus;
		        		}
		            var json = nui.encode({upDatas:rows});
		           	var a= nui.loading("正在处理中,请稍等...","提示");
		           	var URL="com.gotop.xmzg.files.fileList.uptFileRecordStatus.biz.ext";
			        	$.ajax({
			          		url:URL,
			          		type:'POST',
			          		data:json,
			          		cache: false,
			          		contentType:'text/json',
			          		success:function(text){
				          		nui.hideMessageBox(a);
				            	var returnJson = nui.decode(text);
								if(returnJson.flag == 1){
									nui.alert("借阅成功", "系统提示", function(action){
										searchData();
									});
								}else if(returnJson.flag == "noAccess"){
									nui.alert("对不起，您没有操作权限！", "系统提示", function(action){
										searchData();
									});
								}else if(returnJson.flag == 0){
									nui.alert("操作失败", "系统提示");
									grid.unmask();
								}
			          		}
			        	});
		     	}
			});
      	}else if(rows.length > 1){
      		nui.alert("请选择一条记录！");
      	}else{
        	nui.alert("请至少选中一条记录！");
      	}
    }
    //归还档案
    function giveBack(){
    	var rows = grid.getSelecteds();
      	if(rows.length > 0){
	        nui.confirm("确定归还选中档案？","系统提示",function(action){
	        	if(action=="ok"){
		        	var fileStatus = '1';//归还档案，档案状态变更为已归档
		      		for (var i=0;i<rows.length;i++){
			        		rows[i].FILES_STATUS = fileStatus;
		        		}
		            var json = nui.encode({upDatas:rows});
		           	var a= nui.loading("正在处理中,请稍等...","提示");
		           	var URL="com.gotop.xmzg.files.fileList.uptFileRecordStatus.biz.ext";
			        	$.ajax({
			          		url:URL,
			          		type:'POST',
			          		data:json,
			          		cache: false,
			          		contentType:'text/json',
			          		success:function(text){
				          		nui.hideMessageBox(a);
				            	var returnJson = nui.decode(text);
								if(returnJson.flag == 1){
									nui.alert("归还成功", "系统提示", function(action){
										searchData();
									});
								}else if(returnJson.flag == "noAccess"){
									nui.alert("对不起，您没有操作权限！", "系统提示", function(action){
										searchData();
									});
								}else if(returnJson.flag == 0){
									nui.alert("操作失败", "系统提示");
									grid.unmask();
								}
			          		}
			        	});
		     	}
			});
      	}else if(rows.length > 1){
      		nui.alert("请选择一条记录！");
      	}else{
        	nui.alert("请至少选中一条记录！");
      	}
    }
   
    
    //重置查询信息
	function clean(){
	   	//不能用form.reset(),否则会把隐藏域信息清空
	   	form.reset();  
    }
    
    //机构树回显
     function OrgonButtonEdit(e){
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/files/archiveList/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            
                        }
                    }
                }
            });            
             
        }   
        
     //档案详情
     function fileDetail(){
    	var rows = grid.getSelecteds();
    	var row = grid.getSelected();
    	var detail = '';
    	if(rows.length>1){
    		return nui.alert("请只选择一条记录进行明细查看！","提示");
    	}
       	if(row!=null){
       		var fy = row.FILES_TYPE;
       		//综合类档案
       		if(noun(fy)){
       			other(fy,row);
       		}else {     //基础类档案
       			oneToNine(row,fy);
       		}
       	}else{
        	nui.alert("请选中一条记录进行查看！","提示");
    	}
    }
    
    function oneToNine(row,fy){
    	//var TABLE_NAME = getDetailTableName(fy);
	    /*    		//获取档案详情
	       		var data={queryData:{INFORMATION_ID:row.INFORMATION_ID,FILES_TYPE:row.FILES_TYPE,TABLE_NAME:TABLE_NAME}};
	       		var json = nui.encode(data);
	       		console.log("获取档案详情："+nui.encode(json));
			    $.ajax({
			        url:"com.gotop.xmzg.files.fileList.getFileDetail.biz.ext",
			        type:'POST',
			        data:json,
			        cache:false,
			        async:false,
			        contentType:'text/json',
			        success:function(text){
			        	obj = nui.decode(text.resultList);
			        	var detail = obj[0];
			        	row = detail;
			    	}
				}); */
		    nui.open({
		    	url:"<%=request.getContextPath()%>/files/fileList/fileDetail.jsp",
	          	title:'档案详情',
	          	width:1150,
          		height:300,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            //方法1：后台查询一次，获取信息回显
		            /* var data = row;
		            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
		            //方法2：直接从页面获取，不用去后台获取
		            var filesType=row.FILES_TYPE;
		            var data={};
		            if(filesType == '01'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '02'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '03'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '04'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '05'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '06'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '09'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else{
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		        	
		        }
		    });
    }
    
     function other(fy,row){
		if(fy == '15'){
			//信审档案详情界面
			var path = "<%=request.getContextPath()%>/files/fileList/xsDetail.jsp";
		}else {
			//综合类档案的其它档案详情界面
			var path = "<%=request.getContextPath()%>/files/fileList/otherDetail.jsp";
		}
		
	    nui.open({
	    	url:path,
          	title:'编辑',
          	width:1150,
      		height:300,
          	onload:function(){
	        	var iframe = this.getIFrameEl();
	            //方法1：后台查询一次，获取信息回显
	            /* var data = row;
	            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
	            //方法2：直接从页面获取，不用去后台获取
	            var filesType=row.FILES_TYPE;
	            var data = {pageType:'edit',filesType:filesType,record:{editData:row}};
            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	        },
	        ondestroy:function(action){
	        	
	        }
	    });
    }
    
    
    //根据档案种类获取详情表名
	/* function getDetailTableName(filesType){
		if(filesType == '01'){
				return 'T_FILES_RETAIL_CREDIT';
			}else if(filesType == '02'){
				return 'T_FILES_RETAIL_DISBURSE';
			}else if(filesType == '03'){
				return 'T_FILES_PERSON_CREDIT';
			}else if(filesType == '04'){
				return 'T_FILES_PERSON_DISBURSE';
			}else if(filesType == '05'){
				return 'T_FILES_BILL_HONOUR';
			}else if(filesType == '06'){
				return 'T_FILES_BILL_DISCOUNT';
			}else if(filesType == '07'){
				return 'T_FILES_COOPERATE_ORG';
			}else if(filesType == '08'){
				return 'T_FILES_REFUSE_LOAN';
			}else if(filesType == '09'){
				return 'T_FILES_CREDIT';
			}
	}
	 */
	
//操作明细
   function operateDetail(){
       var row = grid.getSelected();
       var rows = grid.getSelecteds();
       if(rows.length>1){
    		return nui.alert("请只选择一条记录进行查看！","提示");
    	}
       if(row!=null){
       var INFORMATION_ID=row.INFORMATION_ID;
	       nui.open({
	          url:"<%=request.getContextPath() %>/files/fileList/operationLog.jsp?infoId="+INFORMATION_ID,
	          title:'档案操作流水',
	          width:1200,
	          height:500,
	          onload:function(){
	             /* var iframe = this.getIFrameEl();
	               var data = {pageType:"edit",record:{map:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法 */
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	             }
	          }
	       });
       }else{
           nui.alert("请选中一条记录！");
       }
    }
    
    //入库/移库
    function upAddress(){
		var rows = grid.getSelecteds();
		var row = grid.getSelected();
     	if(rows.length>1){
    		return nui.alert("请只选择一条记录进行编辑！","提示");
    	} 
    	var STORAGE_ADDRESS = '';         
        var STORAGE_LOCATION =  '';    
       	if(row != null){
		    nui.open({
		    	url:"<%=request.getContextPath()%>/files/fileList/fileStorageEdit.jsp",
	          	title:'入库/移库',
	          	width:900,
          		height:400,
		       	onload:function(){
		        	var iframe = this.getIFrameEl();
		            //方法1：后台查询一次，获取信息回显
		            /* var data = row;
		            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
		            //方法2：直接从页面获取，不用去后台获取
		            var data = {pageType:"edit",record:{inputData:row}};
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		        	if(action=="saveSuccess"){
		        		nui.alert("保存成功", "系统提示", function(action){
						if(action == "ok" || action == "close"){
						 	//重定向（保留查询条件）
		                	//window.location.href="<%=request.getContextPath() %>/files/fileList/fileRecordList.jsp";
							searchData();
						}
						});
		             }
		        }
		    });
    	}else{
    		nui.alert("请选中一条记录进行入库/移库！","提示");
    	}
    }
     //新增附件	
    function addAccessory(value){
   		var row = grid.getRow(value);
   		nui.open({
		    	url:"<%=request.getContextPath()%>/files/fileList/addAccessory.jsp",
	          	title:'新增附件',
	          	width:800,
          		height:400,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            //方法1：后台查询一次，获取信息回显
		            /* var data = row;
		            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
		            //方法2：直接从页面获取，不用去后台获取
		            var data={};
		            data = {pageType:"add",record:{map:row}};
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		          	 if(action=="saveSuccess"){
            	 		 searchData();
		             }
		        }
		    });
    }
    //修改
    function update(){
    	var row = grid.getSelected();
    	var rows = grid.getSelecteds();
     	if(rows.length>1){
    		return nui.alert("请只选择一条记录进行编辑！","提示");
    	}
       	if(row!=null){
       		var filesType = row.FILES_TYPE;
       		var path="";
       		if(noun(filesType)){//综合类档案
       			if(filesType == '15'){
    				//信审档案修改界面
    				path = "<%=request.getContextPath()%>/files/fileList/xsUpdate.jsp";
    			}else {
    				//综合类档案的其它档案修改界面
    				path = "<%=request.getContextPath()%>/files/fileList/otherUpdate.jsp";
    			}
       		}else{//基础档案
       			path = "<%=request.getContextPath()%>/files/fileList/fileEdit.jsp";
       		}
       		
       		nui.open({
			    	url:path,
		          	title:'修改',
		          	width:1150,
	          		height:560,
		          	onload:function(){
			        	var iframe = this.getIFrameEl();
			            //方法2：直接从页面获取，不用去后台获取
			            var data = {pageType:"edit",record:{editData:row}};
		            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
			        },
			        ondestroy:function(action){
				         var strs= new Array();
			          	 strs=action.split(","); 
			          	 if(strs[0]=="saveSuccess"){
			          	 	//设置档案名称
				         	 nui.get("filesName").setValue(strs[1]);
	            	 		 searchData();
			             }
			        }
		    	});
       	}else{
        	nui.alert("请选中一条记录进行编辑！","提示");
    	}
    }
    
    
    <%-- function apply(){
    	var rows = grid.getSelecteds();
    	var row = grid.getSelected();
    	var detail = '';
    	if(rows.length>1){
    		return nui.alert("请只选择一条记录进行申请！","提示");
    	}
       	if(row!=null){
       		if(row.FILES_STATUS != 3){
       			return nui.alert("请选择档案状态为入库的档案！","提示");
       		}
		    nui.open({
		    	url:"<%=request.getContextPath()%>/files/fileLedger/archiveApply.jsp",
	          	title:'归档申请',
	          	width:1150,
          		height:340,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            //方法1：后台查询一次，获取信息回显
		            /* var data = row;
		            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
		            //方法2：直接从页面获取，不用去后台获取
		            var filesType=row.FILES_TYPE;
		            var data={};
		            data = {pageType:"apply",record:row};
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		          	 if(action=="saveSuccess"){
            	 		 searchData();
		             }
		        }
		    });
       	}
    } --%>
    
    
    //批量入库
    function importExcel(){
    	nui.open({
       	  targetWindow: window,
          url:"<%=request.getContextPath()%>/files/fileList/bulkStorage.jsp",
          title:'数据初始化/批量入库',
          width:800,
          height:400,
          onload:function(){
          },
          ondestroy:function(action){
          	 var strs= new Array();
          	 strs=action.split(","); 
	         if(strs[0]=="saveSuccess"){
            	 searchData();
             }
          }
       });
    }
    
     //导出Excel
	function exportExcel(){
			if(grid.totalCount==0){
				alert("当前无数据无法导出报表！");
				return false;
			}
			//当前操作人
			var userId = '<%=userObject.getUserId()%>';
			//当前操作机构
			var userOrgId = '<%=userObject.getUserOrgId()%>';
			
			
			var form=new nui.Form("filesForm");
			var data=form.getData(true, true);
			data.queryData.opTlrNo = userId;
			data.queryData.orgId = userOrgId;
			var fileName="档案台账清单";
			var queryPath="com.gotop.xmzg.files.fileList.query_filelist_record_export";//命名sql
			var columns=grid.getBottomColumns();
			columns=columns.clone();
			for(var i=0;i<columns.length;i++){
				var column=columns[i];
				if(!column.field){
					columns.removeAt(i);
				}else{
					var c={header:column.header,field:column.field };
					columns[i]=c;
				}
			}
			columns=nui.encode(columns);
			data=nui.encode(data);
		    var url="<%=request.getContextPath()%>/util/exportExcel/exportExcel.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data;
			window.location.replace(url); 
		} 
		
	function noun(fy){
		if(fy == '10'){
			return true;
		}else if(fy == '11'){
			return true;
		}else if(fy == '12'){
			return true;
		}else if(fy == '13'){
			return true;
		}else if(fy == '14'){
			return true;
		}else if(fy == '15'){
			return true;
		}else {
			return false;
		}
	}
	
	
	 //操作列：查看/下载附件
      function onActionRender(e){
      		var row  = grid.getRow(e.rowIndex);
      		var s =e.value;
      		console.log(e.value);
      		var d = row.AFFILIATED_IDS;
      		 if(s==null){
	           	s="无附件";
      		}else{
      			var names = s.split(",");
      			var ids = d.split(",");
      			var a = [];
      			for(var i=0;i<names.length;i++){
		           var s1=	"<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\"  onclick=\"downAccessory(\'"+ids[i]+"\',\'"+names[i]+"\')\">"+names[i]+"</a>";
		           a.push(s1);
	            }
	            s = a; 
      		} 
           	return s;
      }
	
	
	//下载附件
	function downAccessory(id,name){
       	var fileId = id;
       	var fileName = name;
       	var url="<%=request.getContextPath()%>/files/fileList/downloadFile.jsp?fileId="+fileId+"&fileName="+fileName;
		window.location.replace(url);
	  }
</script>
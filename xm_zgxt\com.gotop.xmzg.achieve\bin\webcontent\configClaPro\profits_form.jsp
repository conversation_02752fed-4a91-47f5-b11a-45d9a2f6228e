<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-01-05 11:41:00
  - Description:
-->
<head>
<%-- <%@include file="/coframe/tools/skins/common.jsp" %> --%>
<%@include file="/coframe/dict/common.jsp"%>
<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
</head>
<body>
<!-- JF_POSITION -->
<div id="form1" style="padding-top:5px;">
   <input class="nui-hidden" id="TAI_ID" name="map.TAI_ID" /> 
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>认领类型：</label></th>
        <td colspan="3" >  
        	<input id="TP_INDIC_TYPE" value="2" class="nui-dictcombobox" name="map.TP_INDIC_TYPE"  id="JF_FPLX" dictTypeId="JF_FPLX"  onvaluechanged="onTypeChanged"  valueField="dictID" textField="dictName"  required="true" style="width:100%;"/>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>业务条线：</label></th>
        <td colspan="3" >  
        	<div id="TIP_CODE" name="map.TIP_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TIP_NAME" valueField="TIP_CODE" 
		    	url="com.gotop.xmzg.achieve.configClaPro.tip_get.biz.ext" multiSelect="false" allowInput="false" showNullItem="false" emptyText="请选择..."
		    	onvaluechanged="onTipChanged" required="true">     
			    <div property="columns">
			        <div header="业务条线代码" field="TIP_CODE" width="60"></div>
			        <div header="业务条线名称" field="TIP_NAME" width="120"></div>
			    </div>
		    </div>
		</div>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>指标：</label></th>
        <td colspan="3" >  
        	<div id="TI_CODE" name="map.TI_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TI_NAME" valueField="TI_CODE" 
		    	multiSelect="false" allowInput="false" showNullItem="false" emptyText="请先选择业务条线..." required="true"
		    	onvaluechanged="onTiChanged">     
			    <div property="columns">
			        <div header="指标代码" field="TI_CODE" width="60"></div>
			        <div header="指标名称" field="TI_NAME" width="120"></div>
			    </div>
		    </div>
		</div>
        </td> 
      </tr>
      <tr id="tidTr">
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>指标细项：</label></th>
        <td colspan="3" >  
        	<div id="TID_CODE" name="map.TID_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TID_NAME" valueField="TID_CODE" 
		    	multiSelect="false" allowInput="false" showNullItem="false" emptyText="请先选择指标..." required="true">     
			    <div property="columns">
			        <div header="指标细项代码" field="TID_CODE" width="60"></div>
			        <div header="指标细项名称" field="TID_NAME" width="120"></div>
			    </div>
		    </div>
		</div>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>分润方式：</label></th>
        <td colspan="3" >  
        	<!--  -->
        	<input onvaluechanged="onProChanged" class="nui-dictcombobox" name="map.TP_PROFITS"  id="JF_FRLX" dictTypeId="JF_FRLX"  valueField="dictID" textField="dictName"  required="true" style="width:100%;"/>
        </td> 
      </tr>
      <tr id="proTr">
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>分润比率(%)：</label></th>
        <td colspan="3" >  
        	<input id="TP_PRO" name = "map.TP_PRO" onvalidation="comparePro" vtype="maxLength:100" class="nui-textbox"  style="width:100%;" required="true"/>
        	<span style="color:red" id="ProText">可维护多个，使用符号","隔开</span>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>是否认领：</label></th>
        <td colspan="3" >  
        	<input class="nui-dictcombobox" name="map.TP_CLAIM"  id="JF_SFRL" dictTypeId="JF_SFRL"  valueField="dictID" textField="dictName"  required="true" style="width:100%;"/>
        </td>
      </tr>
      
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>是否跨机构：</label></th>
        <td colspan="3" >  
        	<input class="nui-dictcombobox" name="map.TP_ACROSSORG"  id="JF_KJG" dictTypeId="JF_KJG"  valueField="dictID" textField="dictName"  required="true" style="width:100%;"/>
        </td> 
      </tr>
      
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>是否审核：</label></th>
        <td colspan="3" >  
        	<input class="nui-dictcombobox" name="map.TP_AUTH"  id="JF_SFSP" dictTypeId="JF_SFSP"  valueField="dictID" textField="dictName"  required="true" style="width:100%;"/>
        </td>
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>分润截至日：</label></th>
        <td >  
        	<input class="nui-textbox" name="map.TP_DATE" id="TP_DATE"  vtype="int;range:-1,31;" required="true" style="width:100%;">
        </td>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>分润周期：</label></th>
        <td >  
        	<input class="nui-dictcombobox" name="map.TP_CYCLE"  id="TP_CYCLE" dictTypeId="JF_RL_CYCLE"  valueField="dictID" textField="dictName"  required="true" style="width:100%;"/>
        </td>
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>是否二次分润：</label></th>
        <td colspan="3" >  
        	<!--  -->
        	<input onvaluechanged="onProChanged" class="nui-dictcombobox" name="map.TP_TWO_PROFITS"  id="TP_TWO_PROFITS" dictTypeId="JF_YES_NO"  valueField="dictID" textField="dictName"  required="true" style="width:100%;"/>
        </td> 
      </tr>
      <tr id="proTr">
        <th class="nui-form-label"><label for="map.type$text">二次分润比率(%)：</label></th>
        <td colspan="3" >  
        	<input  name = "map.TP_TWO_PRO"  id="TP_TWO_PRO" vtype="maxLength:100" class="nui-textbox"  style="width:100%;" />
        	<span style="color:red" id="ProText">可维护多个，使用符号","隔开</span>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text">二次分润是否审核：</label></th>
        <td colspan="3" >  
        	<input class="nui-dictcombobox" name="map.TP_TWO_AUTH"  dictTypeId="JF_YES_NO"  valueField="dictID" textField="dictName"  style="width:100%;"/>
        </td>
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>判断产品是否存在：</label></th>
        <td colspan="3" >  
        	<input onvaluechanged="onProductChanged" class="nui-dictcombobox" name="map.TP_PRODUCT_EXIST"  id="JF_YES_NO" dictTypeId="JF_YES_NO"  valueField="dictID" textField="dictName"  required="true" style="width:100%;"/>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text">产品名称：</label></th>
        <td colspan="3" >  
        	<input id="TP_PRODUCTNAME" name = "map.TP_PRODUCTNAME" vtype="maxLength:50" class="nui-textbox"  style="width:100%;" />
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text">产品对应库表名：</label></th>
        <td colspan="3" >  
        	<input id="TP_PRODUCT_TABLE" name = "map.TP_PRODUCT_TABLE" vtype="maxLength:100" class="nui-textbox"  style="width:100%;" />
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text">产品字段名：</label></th>
        <td colspan="3" >  
        	<input id="TP_PRODUCT_FIELD" name = "map.TP_PRODUCT_FIELD" vtype="maxLength:100" class="nui-textbox"  style="width:100%;" />
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text">机构字段名：</label></th>
        <td colspan="3" >  
        	<input id="TP_ORGFIELD" name = "map.TP_ORGFIELD" vtype="maxLength:100" class="nui-textbox"  style="width:100%;" />
        </td> 
      </tr>
    </table>
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save" onclick="onOk">保存</a>
	          <span style="display:inline-block;width:10px;"></span>
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  </div>

  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);
	
    var saveUrl = "com.gotop.xmzg.achieve.configClaPro.profits_add.biz.ext";
    function setData(data){
               	    
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);
		if(infos.pageType == "add"){
         	//新增初始化
         	var json = {};
         	var map = {};
         	map.TP_PROFITS = 1;
         	map.TP_CLAIM = 0;
         	map.TP_INDIC_TYPE = 2;
         	map.TP_ACROSSORG = 0;
         	map.TP_AUTH = 0;
         	map.TP_PRODUCT_EXIST = 0;
         	json.map = map;
         	form.setData(json);
         }
         
        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
          nui.get("TP_INDIC_TYPE").setEnabled(false);
          nui.get("TIP_CODE").setEnabled(false);
          nui.get("TI_CODE").setEnabled(false);
          nui.get("TID_CODE").setEnabled(false);
          saveUrl = "com.gotop.xmzg.achieve.configClaPro.profits_update.biz.ext";
	      //表单数据回显
          var json = infos.record;
          typeChanged(json.map.TP_INDIC_TYPE);
          proChanged(json.map.TP_PROFITS);
          productChanged(json.map.TP_PRODUCT_EXIST);
          onTipChanged(null,json.map.TIP_CODE);
		  onTiChanged(null,json.map.TI_CODE);
		  
          form.setData(json);
          
        }
         
    }
    
    function onOk(){
      saveData();
    }
    
    //提交
    function saveData(){   
      form.validate();
      if(form.isValid()==false) return;
      
      var data = form.getData(true,true);
      data.map.TP_NAME = nui.get("TI_CODE").getText();
      if(data.map.TP_INDIC_TYPE == 2){
      	data.map.TP_NAME = nui.get("TID_CODE").getText();
      }
      
      if(data.map.TP_TWO_PROFITS == '1'){
      	if(data.map.TP_TWO_PRO==""||data.map.TP_TWO_PRO==null){
      		nui.alert("二次分润比例不能为空", "系统提示");
      		
      	}else{
      		if(compareTwoPro(data.map.TP_TWO_PRO)){
      		}else{
      		return;
      		}
      	}
      	if(data.map.TP_TWO_AUTH==""||data.map.TP_TWO_AUTH==null){
      		nui.alert("请选择二次分润是否审核", "系统提示");
      		return;
      	}
      }
	 
      var json = nui.encode(data);
     
      $.ajax({
        url:saveUrl,
        type:'POST',
        data:json,
        cache:false,
        contentType:'text/json',
        success:function(text){
            var returnJson = nui.decode(text);
			if(returnJson.exception == null && returnJson.iRtn == 1){
				nui.alert("操作成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveSuccess");
					}
				});
			}else{
				if(returnJson.msg != null && returnJson.msg != ""){
					nui.alert(returnJson.msg, "系统提示");
				}else{
					nui.alert("操作失败", "系统提示");
				}
				
			}
        }
      });
    }
    
    function onProductChanged(e){
    	productChanged(e.value);
    }
    function productChanged(val) {
		 if(val == 1){
		 	nui.get("TP_PRODUCTNAME").setRequired(true);
			nui.get("TP_PRODUCT_TABLE").setRequired(true);
			nui.get("TP_PRODUCT_FIELD").setRequired(true);
			nui.get("TP_ORGFIELD").setRequired(true);
		 }else{
		 	nui.get("TP_PRODUCTNAME").setRequired(false);
			nui.get("TP_PRODUCT_TABLE").setRequired(false);
			nui.get("TP_PRODUCT_FIELD").setRequired(false);
			nui.get("TP_ORGFIELD").setRequired(false);
		 }
    }
    
    
    function onProChanged(e) {
		 proChanged(e.value);
    }
    function proChanged(val) {
		 if(val == 1){
		 	//$("#proTr").show();
		 	//$("#ProText").text('可维护多个，使用符号","隔开');
		 	nui.get("TP_PRO").setRequired(true);
		 }else{
		 	//$("#proTr").hide();
		 	//$("#ProText").text('可动态分润比率上限');
		 	nui.get("TP_PRO").setRequired(false);
		 }
    }
    
    function onTypeChanged(e) {
		 typeChanged(e.value);
    }
    function typeChanged(val) {
		 if(val == 2){
		 	$("#tidTr").show();
		 }else{
		 	$("#tidTr").hide();
		 }
    }
    
    var tip = nui.get("TIP_CODE");
    var ti = nui.get("TI_CODE");
    var tid = nui.get("TID_CODE");
    function onTipChanged(e,val){
        ti.setValue("");
        tid.setValue("");
        if(!val) val = tip.getValue();
        var url = "com.gotop.xmzg.achieve.configClaPro.ti_get.biz.ext?code=" + val;
        ti.setUrl(url);
    }
    
    function onTiChanged(e,val){
        tid.setValue("");
        if(!val) val = ti.getValue();
        var url = "com.gotop.xmzg.achieve.configClaPro.tid_get.biz.ext?code=" + val;
        tid.setUrl(url);
    }
    
    
    function onCancel(){
      CloseWindow("cancel");
    }
    
    function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
    
    function comparePro(e){
		var num = 0 ;
		var datas = e.value.split(",");
    	for(var x=0 ; x < datas.length ; x++){
    		var index = Number(datas[x]);
    		if(index == 0){
	            e.errorText= "分润比率不能为0";
	            e.isValid=false;
	            return;
      		}
			num += index;
		}
		
/* 		if(nui.get("JF_FRLX").getValue() != 1){
			if(datas.length > 1){
				e.errorText= "分润比率格式有误！只能配置一个整数";
            	e.isValid=false;
            	return;
			}
		}
		 */
		if(num < 0){
            e.errorText= "分润比率不能为0";
            e.isValid=false;
      	}else if(num > 100){
          	e.errorText= "分润比率不能大于100";
          	e.isValid=false;
      	}else if(isNaN(num) || !(Math.floor(num) == num)){
			e.errorText= "分润比率格式有误！只能配置整数";
            e.isValid=false;
		}else{
          	e.errorText="";
          	e.isValid=true;
      	}
    	
    }
    function compareTwoPro(e){
		var num = 0 ;
		var datas = e.split(",");
    	for(var x=0 ; x < datas.length ; x++){
    		var index = Number(datas[x]);
    		if(index == 0){
	           nui.alert("二次分润比率不能为0", "系统提示");
	           return;
      		}
			num += index;
		}
		
		if(num < 0){
            nui.alert("二次分润比率不能为0", "系统提示");
            return;
      	}else if(num > 100){
      	    nui.alert("二次分润比率不能大于100", "系统提示");
      	    return;
          	
      	}else if(isNaN(num) || !(Math.floor(num) == num)){
      		nui.alert("二次分润比率格式有误！只能配置整数");
      	    return;
		}else{
			
      	    return true;
      	}
    	
    }
  </script>
</body>
</html>
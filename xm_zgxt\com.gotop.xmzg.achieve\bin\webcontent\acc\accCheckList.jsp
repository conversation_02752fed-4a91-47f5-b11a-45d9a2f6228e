<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): wsd
  - Date: 2022-02-26 20:18:20
  - Description:
-->
<head>
<title>业绩账户分配/机构业绩分配/分润审核查询</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
</head>

<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <table class="table" style="width:100%;">
	        <tr>
				<th class="tit">产品编号：&nbsp;&nbsp;</th> 
		        <td><input name="queryData.taa_busi_no" id="taa_busi_no" class="nui-textbox " style="width:150px;"/></td>
		    	<th class="tit">指标类型：&nbsp;&nbsp;</th> 
		        <td><input name="queryData.taa_rl_type" id="taa_rl_type" class="nui-dictcombobox " style="width:150px;" 
		        valueField="dictID" textField="dictName" dictTypeId="JF_FPLX" emptyText="全部" showNullItem="true" nullItemText="全部"/></td>
		    	<th class="tit">指标或者细项编号：&nbsp;&nbsp;</th> 
		        <td><input name="queryData.taa_zb_code" id="taa_zb_code" class="nui-textbox " style="width:150px;"/></td>
		    	
		    </tr>
		   
		    <tr>
		    	<th class="tit">归属客户经理名称：&nbsp;&nbsp;</th> 
		        <td>
		        <input name="queryData.taa_emp_name" id="taa_emp_name" class="nui-textbox " style="width:150px;"/>
		        </td>
		        <th class="tit">审核状态：&nbsp;&nbsp;</th> 
		        <td><input name="queryData.taa_status" id="taa_status" class="nui-dictcombobox " style="width:150px;" valueField="dictID" 
		        textField="dictName" dictTypeId="JF_SHZG" emptyText="全部" showNullItem="true" nullItemText="全部" value="0"/></td>
		       
		        <th class="tit">变更类型：&nbsp;&nbsp;</th> 
		        <td><input name="queryData.taa_type" id="taa_type" class="nui-dictcombobox " style="width:150px;" valueField="dictID" 
		        textField="dictName" dictTypeId="JF_BGLX" emptyText="全部" showNullItem="true" nullItemText="全部" /></td>
		       
		       
		        <th class="tit"><a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>
		        <a class="nui-button" iconCls="icon-reset"  onclick="clean">重置</a>
		    </tr>
	    </table>
	  </div>
    </div>
  </div>
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
             <a class="nui-button" iconCls="icon-collapse" onclick="accCheck">审核</a>
             <a class="nui-button" iconCls="icon-ok" onclick="gotoChecks(1)">批量审核通过</a>
             <a class="nui-button" iconCls="icon-no" onclick="gotoChecks(2)">批量审核不通过</a>
           </td>
        </tr>
      </table>
    </div>
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.achieve.acc.queryAccCheckList.biz.ext"
	  sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10">
	  <div name="accCheckDiv" class="nui-hidden" ></div>
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      <div field="TAB_APP_ID" class="nui-hidden" visible="false">APPID</div>
	      <div field="TAA_STATUS" headerAlign="center" align="center" renderer="onStatus">审核状态</div>
	      <div field="TAA_BUSI_NO" headerAlign="center" align="center">产品编号</div>
	      <div field="TAA_BUSI_NAME" headerAlign="center" align="center">产品名称</div>
	      <div field="TAA_RL_TYPE" headerAlign="center" align="center" renderer="onRlType">认领类型</div>
	      <div field="TAA_ZB_CODE" headerAlign="center" align="center">指标或细项编号</div>
	      <div field="TAA_ZB_NAME" headerAlign="center" align="center">指标或细项名称</div>
	      <div field="TAA_TYPE" headerAlign="center" align="center" renderer="onType">变更类型</div>
	      <div field="TAA_EMP_NAME" headerAlign="center" align="center">归属客户经理</div>
	      <div field="TAA_BEGIN" headerAlign="center" align="center">开始时间</div>
	      <div field="TAA_END" headerAlign="center" align="center">结束时间</div>
	      <div field="TAA_APP_TIME" headerAlign="center" align="center">审核时间</div>
	    </div>
	 </div>
  </div>
	<script type="text/javascript">
    	nui.parse();
    	var path = '<%=request.getContextPath() %>';
    	var grid = nui.get("datagrid1");
    	
    	//查询
    	function search(){
       		var form = new nui.Form("#form1");
       		var data = form.getData(true,true);
       		grid.load(data);
    	}
    	
    	//重置
		function clean(){
     		var form = new nui.Form("#form1");
			form.reset();
    	}
    	
    	function onType(e){
    		if(e.row.TAA_IS_DEL == 1){
    			return nui.getDictText("JF_BGLX",e.value) + "-删除";
    		}
    		return nui.getDictText("JF_BGLX",e.value);
    	}
    	
    	function onRlType(e){
    		return nui.getDictText("JF_FPLX",e.value);
    	}
    	
    	function onStatus(e){
    		return nui.getDictText("JF_SHZG",e.value);
    	}
    	//批量审核
    	function gotoChecks(flag){
       		var rows = grid.getSelecteds();
			if(rows.length > 0){
				var newRows = [];
				var isPost = "";
				var count11or00 = 0;
				var count15 = 0;
				var isZbCode1003 = "0";
	            for(var i = 0 ; i < rows.length ; i++){
	           		if(rows[i].TAA_STATUS != 0){
	           			nui.alert("选择记录有已审核记录！");
						return ;
	           		}
	           		var t1003=false;
					var zbCode = rows[i].TAB_ZB_CODE;
				   if(zbCode == '10003' || zbCode == '10003001'|| zbCode == '10003002'||zbCode == '10003003'){
				   		isZbCode1003 = "1";
				   		t1003 = true;
				   }
				   
				   if(isZbCode1003 == "1" && !t1003){
				   		nui.alert("储蓄存款需单独审核,批量审核指标需全为储蓄存款！", "系统提示");
		       	   		return ;
				   }
	      	   	    var data = {
			                    taa_busi_no : rows[i].TAA_BUSI_NO,
			                    taa_busi_name : rows[i].TAA_BUSI_NAME,
			                    taa_busi_org : rows[i].TAA_BUSI_ORG,
			                    taa_rl_type : rows[i].TAA_RL_TYPE,
			                    taa_zb_code : rows[i].TAA_ZB_CODE,
			                    taa_zb_name : rows[i].TAA_ZB_NAME,
			                    taa_type : rows[i].TAA_TYPE,
			                    taa_emp : rows[i].TAA_EMP,
			                    taa_emp_name_gs : rows[i].TAA_EMP_NAME,
			                    taa_emp_name_fr : rows[i].TAA_EMP_NAME,
			                    taa_begin_gs : rows[i].TAA_BEGIN,
			                    taa_begin_fr : rows[i].TAA_BEGIN,
			                    taa_rate : rows[i].TAA_RATE,
			                    taa_pre_id : rows[i].TAA_PRE_ID,
			                    taa_id : rows[i].TAA_ID,
			                    taa_change_type : rows[i].TAA_CHANGE_TYPE,
			                    flag : flag,
			                    num :rows.length,
			                    tgnum :rows.length,
			                    taa_app_id : rows[i].TAA_APP_ID,
			                    dd_cust_type : rows[i].DD_CUST_TYPE,
			                    taa_end_gs : rows[i].TAA_END,
			                    taa_end_fr :  rows[i].TAA_END
			                    
			         };
			         newRows.push(data);
			         
			         if(rows[i].TAA_ZB_CODE!="12007001"&&rows[i].TAA_ZB_CODE!="12006001"&&rows[i].TAA_ZB_CODE!="11003002"){
					     count11or00++;
					 }else{
					 	 count15++;
					 }
					 
	      	   }
	      	   if(count11or00>0&&count15==0){//11和00岗位
				   isPost="11";
			   }else if(count11or00==0&&count15>0){//15岗位
			   	   isPost="15";
			   }else if(count11or00>0&&count15>0){
			   	   nui.alert("公司贷款\贸易融资\票据承兑不能与其他贷种混合进行批量审批", "系统提示");
		       	   return ;
			   }
					 
       	   var load= nui.loading("正在审核请稍侯...","温馨提示 =^_^=");
	       nui.ajax({
				url: "com.gotop.xmzg.achieve.acc.checkAccBatch.biz.ext",
				type: "post",
				data: nui.encode({objs:newRows,isPost:isPost}),
				contentType:'text/json',
				success: function (text) {
					nui.hideMessageBox(load);  //隐藏遮罩层
		        	   var iRth = text.iRth;
		        	   var msg = text.msg;
		        	   if(iRth == "0"){
		        		  	nui.alert(msg, "系统提示", function(action){
								grid.reload();
							});
			           }else{
		          			nui.alert(msg, "系统提示", function(action){
		          				if(action == "ok" || action == "close"){
									
								}
							});
			          }
				}
			});
       }else{
           nui.alert("请至少选择一条记录进行审核！");
       }  
    }
    
    	//选择一条记录进行审核
    	function accCheck(e){
        	var rows = grid.getSelecteds();
        	if(rows.length>1 || rows.length==0){
    			nui.alert("请选择一条待审核的记录进行审核！");
    		}else{
				var row = grid.getSelected();
				if("0" == row.TAA_STATUS){
					var data = {
	                    taa_app_id : row.TAA_APP_ID,
	                    taa_busi_no : row.TAA_BUSI_NO,
	                    taa_busi_name : row.TAA_BUSI_NAME,
	                    taa_busi_org : row.TAA_BUSI_ORG,
	                    taa_rl_type : row.TAA_RL_TYPE,
	                    taa_zb_code : row.TAA_ZB_CODE,
	                    taa_zb_name : row.TAA_ZB_NAME,
	                    taa_type : row.TAA_TYPE,
	                    taa_emp : row.TAA_EMP,
	                    taa_emp_name : row.TAA_EMP_NAME,
	                    taa_begin : row.TAA_BEGIN,
	                    taa_rate : row.TAA_RATE,
	                    taa_pre_id : row.TAA_PRE_ID,
	                    taa_id : row.TAA_ID,
	                    taa_change_type : row.TAA_CHANGE_TYPE,
	                    dd_cust_type : row.DD_CUST_TYPE,
	                    taa_end : row.TAA_END
	                 };
	                 if(row.TAA_ZB_CODE=="12007001"||
	                    row.TAA_ZB_CODE=="12006001"||
	                    row.TAA_ZB_CODE=="11003002"){//指标是公司贷款 贸易融资 和票据承兑的时候 审批的才是一支行长
	                 	
	                 	nui.ajax({
						url: "com.gotop.xmzg.achieve.acc.CheckRoleIsBy15.biz.ext",
						type: "post",
						data: nui.encode({formData:data}),
						contentType:'text/json',
						success: function (text) {
							if(text.code==1){         
								if(row.TAA_TYPE == '1'){
									nui.open({
				                		url: path+"/achieve/acc/accCrossCheckForm.jsp",
						                title: "业绩账户分润审核",
						                iconCls: "icon-edit", 
						                width: 850, 
						                height: 710,
						                onload: function () {
						                    var iframe = this.getIFrameEl();
						                  	iframe.contentWindow.setFormData(data);
						                },
						                ondestroy: function (action) {
						                	if (action == "ok") {
						                		grid.reload();
						                	}else if (action == "cancel") {
						                		grid.reload();
						                	}
						                }
				           			});
								}else if(row.TAA_TYPE == '2'){
									nui.open({
				                		url: path+"/achieve/acc/accCrossCheckFormTwo.jsp",
						                title: "业绩账户二次分润审核",
						                iconCls: "icon-edit", 
						                width: 850, 
						                height: 710,
						                onload: function () {
						                    var iframe = this.getIFrameEl();
						                  	iframe.contentWindow.setFormData(data);
						                },
						                ondestroy: function (action) {
						                	if (action == "ok") {
						                		grid.reload();
						                	}else if (action == "cancel") {
						                		grid.reload();
						                	}
						                }
				           			});
								}else if(row.TAA_TYPE == '3'){
									nui.open({
				                		url: path+"/achieve/acc/orgCheckForm.jsp",
						                title: "机构业绩分配审核",
						                iconCls: "icon-edit", 
						                width: 500, 
						                height: 500,
						                onload: function () {
						                    var iframe = this.getIFrameEl();
						                  	iframe.contentWindow.setFormData(data);
						                },
						                ondestroy: function (action) {
						                	if (action == "ok") {
						                		grid.reload();
						                	}else if (action == "cancel") {
						                		grid.reload();
						                	}
						                }
				           			});
								}else{
									nui.open({
				                		url: path+"/achieve/acc/accCheckForm.jsp",
						                title: "业绩账户认领/转移审核",
						                iconCls: "icon-edit", 
						                width: 500, 
						                height: 500,
						                onload: function () {
						                    var iframe = this.getIFrameEl();
						                  	iframe.contentWindow.setFormData(data);
						                },
						                ondestroy: function (action) {
						                	if (action == "ok") {
						                		grid.reload();
						                	}else if (action == "cancel") {
						                		grid.reload();
						                	}
						                }
				           			});
				           		}
					          }else{
									nui.alert("您没有权限进行审核！");
							  }
							}	
						});
	                 }else{
					    nui.ajax({
							url: "com.gotop.xmzg.achieve.acc.checkRoleIs.biz.ext",
							type: "post",
							data: nui.encode({formData:data}),
							contentType:'text/json',
							success: function (text) {
								if(text.code==1){            
									if(row.TAA_TYPE == '1'){
										nui.open({
					                		url: path+"/achieve/acc/accCrossCheckForm.jsp",
							                title: "业绩账户分润审核",
							                iconCls: "icon-edit", 
							                width: 850, 
							                height: 710,
							                onload: function () {
							                    var iframe = this.getIFrameEl();
							                  	iframe.contentWindow.setFormData(data);
							                },
							                ondestroy: function (action) {
							                	if (action == "ok") {
							                		grid.reload();
							                	}else if (action == "cancel") {
							                		grid.reload();
							                	}
							                }
					           			});
									}else if(row.TAA_TYPE == '2'){
										nui.open({
					                		url: path+"/achieve/acc/accCrossCheckFormTwo.jsp",
							                title: "业绩账户二次分润审核",
							                iconCls: "icon-edit", 
							                width: 850, 
							                height: 710,
							                onload: function () {
							                    var iframe = this.getIFrameEl();
							                  	iframe.contentWindow.setFormData(data);
							                },
							                ondestroy: function (action) {
							                	if (action == "ok") {
							                		grid.reload();
							                	}else if (action == "cancel") {
							                		grid.reload();
							                	}
							                }
					           			});
									}else if(row.TAA_TYPE == '3'){
									nui.open({
				                		url: path+"/achieve/acc/orgCheckForm.jsp",
						                title: "机构业绩分配审核",
						                iconCls: "icon-edit", 
						                width: 500, 
						                height: 500,
						                onload: function () {
						                    var iframe = this.getIFrameEl();
						                  	iframe.contentWindow.setFormData(data);
						                },
						                ondestroy: function (action) {
						                	if (action == "ok") {
						                		grid.reload();
						                	}else if (action == "cancel") {
						                		grid.reload();
						                	}
						                }
				           			});
								}else if(row.TAA_TYPE == '6'){//个金认领审核
									nui.open({
				                	   url: path+"/achieve/accNew/accGjCheckForm.jsp",
						                title: "个金业绩账户认领审核",
						                iconCls: "icon-edit", 
						                width: 850, 
						                height: 710,
						                onload: function () {
						                    var iframe = this.getIFrameEl();
						                  	iframe.contentWindow.setFormData(data);
						                },
						                ondestroy: function (action) {
						                	if (action == "ok") {
						                		grid.reload();
						                	}else if (action == "cancel") {
						                		grid.reload();
						                	}
						                }
				           			});
								}else if(row.TAA_TYPE == '7'){//个金分润审核
									nui.open({
				                		url: path+"/achieve/accNew/accGjCrossCheckForm.jsp",
						                title: "个金业绩账户分润审核",
						                iconCls: "icon-edit", 
						                width: 850, 
						                height: 710,
						                onload: function () {
						                    var iframe = this.getIFrameEl();
						                  	iframe.contentWindow.setFormData(data);
						                },
						                ondestroy: function (action) {
						                	if (action == "ok") {
						                		grid.reload();
						                	}else if (action == "cancel") {
						                		grid.reload();
						                	}
						                }
				           			});
								}else{
										nui.open({
					                		url: path+"/achieve/acc/accCheckForm.jsp",
							                title: "业绩账户认领/转移审核",
							                iconCls: "icon-edit", 
							                width: 500, 
							                height: 500,
							                onload: function () {
							                    var iframe = this.getIFrameEl();
							                  	iframe.contentWindow.setFormData(data);
							                },
							                ondestroy: function (action) {
							                	if (action == "ok") {
							                		grid.reload();
							                	}else if (action == "cancel") {
							                		grid.reload();
							                	}
							                }
					           			});
					           		}
					          }else{
									nui.alert("您没有权限进行审核！");
							  }
						}	
					});
				}
			}else{
				nui.alert("请选择一条待审核的记录进行审核！");
			}
    		}
    	}
    	
    	$(function(){
    		//初始化默认查询待审核的审核数据
    		nui.get("taa_status").setValue("0");
    		search();
		});

    </script>
</body>
</html>
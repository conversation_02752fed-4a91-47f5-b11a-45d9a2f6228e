<?xml version="1.0" encoding="UTF-8"?>
<contribution xmlns="http://www.primeton.com/xmlns/eos/1.0">
	<!-- MBean config -->
	<module name="Mbean">
		<!-- DataSourceMBean config -->
		<group name="DatasourceMBean">
			<configValue key="Type">config</configValue>
			<configValue key="Class">com.eos.system.management.config.mbean.Config</configValue>
			<configValue key="Handler">com.eos.common.connection.mbean.ContributionDataSourceConfigHandler</configValue>
			<configValue key="ConfigFileType">config</configValue>
		</group>
		<group name="ContributionLoggerMBean">
			<configValue key="Type">config</configValue>
			<configValue key="Class">com.eos.system.management.config.mbean.Config</configValue>
			<configValue key="Handler">com.eos.common.logging.mbean.LogConfigHandler</configValue>
			<configValue key="ConfigFileType">log</configValue>
		</group>
	</module>

	<!-- datasource config -->	
	<module name="DataSource">
		<group name="Reference">
			<!--
				the configuration below describes 
				the corresponding relationship between contribution datasource and application datasource, 
				multiple datasources can be defined. 
				the value 'default' of attibute 'key' denotes a contribution datasource 
				and the field value 'default' of 'configValue' node stands for an application datasource. 
			-->
			<configValue key="default">default</configValue>
		</group>
	</module>
	
	
	
	<module name="Right">
		<group name="Cipher">
			<configValue key="Class">org.gocom.components.coframe.rights.user.cipher.DESedeUserPasswordCipher</configValue>
			<!-- <configValue key="Class">org.gocom.components.coframe.rights.user.cipher.MD5UserPasswordCipher</configValue> -->
			<!-- <configValue key="Class">org.gocom.components.coframe.rights.user.cipher.SHAUserPasswordCipher</configValue> -->
		</group>
	</module>	
	
</contribution>

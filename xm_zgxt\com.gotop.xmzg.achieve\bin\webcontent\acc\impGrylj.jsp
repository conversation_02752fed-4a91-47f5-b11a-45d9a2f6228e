<%@page import="com.alibaba.fastjson.JSONObject"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): 86188
  - Date: 2022-12-29 15:55:26
  - Description:
-->
<head>
<title>批量认定导入</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
    
</head>
<body>
	<div align="center" id="form">
		<fieldset style="width:860px;border:solid 1px #aaa;position:relative;margin:5px 2px 0px 2px;" >
			<form id="import_form" action="com.gotop.xmzg.achieve.impGrylj.flow" method="post" enctype="multipart/form-data">
				<input class="nui-hidden" id="aa"  name="_eosFlowAction"  />
				<input class="nui-hidden" id="bb"  name="excelFile"  />
				<table style="width:100%;height:95%;table-layout:fixed;" class="nui-form-table">					
					<tr>
						<th class="nui-form-label" style="text-align:right;">导入文件：</th>
						<td style="text-align:left;">
							<input id="file" type="file" name="importFile" size="60" style="width:90%;"/>
						</td>
						<td></td>
					</tr>	
					<tr>
						<td colspan="3" style="text-align:center;">
							<a class="nui-button" iconCls="icon-ok" onClick="startUpload">导入</a>&nbsp
							<a class="nui-button" iconCls="icon-download" onclick="templateDown">下载模版</a>
						</td>
					</tr>
				</table>
			</form>
		</fieldset>
	</div>

	<script type="text/javascript">
		nui.parse();	
		var form = new nui.Form("#form");     
    	<%
    		JSONObject result = (JSONObject)request.getAttribute("result");
	 	%>
		var result = <%=result %>;
		if(result != null && result != ""){
			console.log(result);
			nui.alert(result.msg);
			
		}
    	
    	
		//导入
        function startUpload() {
	    	var form = $("#import_form");
	        var excelFile = $("#file").val();
	        if (excelFile == "") {
				nui.alert("请选择文件！");
				return;
			}
			var reg = /.xls$/;
			if (!reg.test(excelFile)){
				nui.alert('请选择Excel格式(*.xls)文件！');
				return;
			}
			excelFile = excelFile.substr(excelFile.lastIndexOf("\\") + 1);
	        nui.alert("是否确定导入?", "系统提示", function(action){
				if(action == "close"){
					return false;
				}else{
					nui.get("aa").setValue("importFile");
					nui.get("bb").setValue(excelFile);
					form.submit();
					a= nui.loading("正在操作中,请稍等...","提示");
				}
			});
		}
	
		
		function templateDown(){
			var file_name = "grylj.xls";
			var file_name1 = "个人养老金批量认定导入模板.xls";
    		 var url="<%=request.getContextPath() %>/achieve/personnel/templateDownload.jsp?filename="+file_name+"&filename2="+file_name1;
	      window.location.replace(encodeURI(url));
			
		}
    </script>
</body>
</html>
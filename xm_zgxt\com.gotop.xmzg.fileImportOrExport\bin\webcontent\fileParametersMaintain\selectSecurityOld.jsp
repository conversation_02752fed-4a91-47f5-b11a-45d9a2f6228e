<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): zhanghongkai
  - Date: 2019-05-09 15:48:08
  - Description:
-->
<head>
	<%@include file="/coframe/tools/skins/common.jsp" %>
	<title>选择权限</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
</head>
<body>
	<div id="form" class="nui-fit">
		<table border="0" cellpadding="1" cellspacing="2" style="width:100%;table-layout:fixed;">
		    <tr>
		        <th style="text-align:right;">机构权限：</th>
		        <td>
		            <input id="orgSecurity" class="nui-buttonedit" style="width:100%;" onbuttonclick="selectOrg"/>
		        </td>
		        <td>
		        	<a class="nui-button" onClick="orgReset">重置</a>
		        </td>
		    </tr>
		    <tr>
		        <th style="text-align:right;">角色权限：</th>
		        <td>
		            <input id="roleSecurity" class="nui-buttonedit" style="width:100%;" onbuttonclick="selectRole"/>
		        </td>
		        <td>
		        	<a class="nui-button" onClick="roleReset">重置</a>
		        </td>
		    </tr>
		    <tr>
		        <th style="text-align:right;">人员权限：</th>
		        <td>
		            <input id="empSecurity" class="nui-buttonedit" style="width:100%;" onbuttonclick="selectEmp"/>
		        </td>
		        <td>
		        	<a class="nui-button" onClick="empReset">重置</a>
		        </td>
		    </tr>
		    <tr>
		        <th style="text-align:right;">群组权限：</th>
		        <td>
		            <input id="groupSecurity" class="nui-buttonedit" style="width:100%;" onbuttonclick="selectGroup"/>
		        </td>
		        <td>
		        	<a class="nui-button" onClick="groupReset">重置</a>
		        </td>
		    </tr>
		</table>
		<div style="text-align:center">
			<a class="nui-button" iconCls="icon-ok" onClick="save">确定</a>&nbsp
			<a class="nui-button" iconCls="icon-cancel" onClick="cancel">取消</a>
		</div>
	</div>


	<script type="text/javascript">
    	nui.parse();
    	
    	var form = new nui.Form("form");
    	
    	var orgText = "";
    	var empText = "";
    	var roleText = "";
    	var groupText = "";
    	var orgValue = "";
    	var empValue = "";
    	var roleValue = "";
    	var groupValue = ""; 
    	
    	function orgReset(){
    		nui.get("orgSecurity").setValue();
    		nui.get("orgSecurity").setText();
    	}
    	
    	function roleReset(){
    		nui.get("roleSecurity").setValue();
    		nui.get("roleSecurity").setText();
    	}
    	
    	function empReset(){
    		nui.get("empSecurity").setValue();
    		nui.get("empSecurity").setText();
    	}
    	
    	function groupReset(){
    		nui.get("groupSecurity").setValue();
    		nui.get("groupSecurity").setText();
    	}
    	
    	function setData(data){
    		data = nui.clone(data);
    		var text = String(data.text);
    		var value = String(data.value);
    		var texts = text.split(";");
    		var temp = null;
    		for(var i=0;i<texts.length;i++){
    			temp = texts[i].split(":");
    			if(temp[0] == "机构"){
    				orgText = texts[i];
    			}else if(temp[0] == "角色"){
    				roleText = texts[i];
    			}else if(temp[0] == "人员"){
    				empText = texts[i];
    			}else if(temp[0] == "群组"){
    				groupText = texts[i];
    			}
    		}
    		var values = value.split(";");
    		for(var i=0;i<values.length;i++){
    			temp = values[i].split(":");
    			if(temp[0] == "org"){
    				orgValue = values[i];
    			}else if(temp[0] == "role"){
    				roleValue = values[i];
    			}else if(temp[0] == "emp"){
    				empValue = values[i];
    			}else if(temp[0] == "group"){
    				groupValue = values[i];
    			}
    		}
    		nui.get("orgSecurity").setText(orgText);
    		nui.get("orgSecurity").setValue(orgValue);
    		nui.get("roleSecurity").setText(roleText);
    		nui.get("roleSecurity").setValue(roleValue);
    		nui.get("empSecurity").setText(empText);
    		nui.get("empSecurity").setValue(empValue);
    		nui.get("groupSecurity").setText(groupText);
    		nui.get("groupSecurity").setValue(groupValue);
    	}
    	
    	function save() {
	        closeWindow("ok");
	    }
    	
    	function getData(){
    		var orgSecurityText = nui.get("orgSecurity").getText();
    		var orgSecurityValue = nui.get("orgSecurity").getValue();
    		
    		var roleSecurityText = nui.get("roleSecurity").getText();
    		var roleSecurityValue = nui.get("roleSecurity").getValue();
    		
    		var empSecurityText = nui.get("empSecurity").getText();
    		var empSecurityValue = nui.get("empSecurity").getValue();
    		
    		var groupSecurityText = nui.get("groupSecurity").getText();
    		var groupSecurityValue = nui.get("groupSecurity").getValue();
    		
    		var text = new Array();
    		var value = new Array();
    		
    		var i = 0;
    		
    		if(orgSecurityText != ""){
    			text[i] = orgSecurityText;
    			value[i] = orgSecurityValue;
    			i++;
    		}
    		
    		if(roleSecurityText != ""){
    			text[i] = roleSecurityText;
    			value[i] = roleSecurityValue;
    			i++;
    		}
    		
    		if(empSecurityText != ""){
    			text[i] = empSecurityText;
    			value[i] = empSecurityValue;
    			i++;
    		}
    		
    		if(groupSecurityText != ""){
    			text[i] = groupSecurityText;
    			value[i] = groupSecurityValue;
    			i++;
    		}
    		
    		text = text.join(";");
    		value = value.join(";");
    		return {text:text,value:value};
    	}
    	
    	function selectOrg(e){
    		var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath()%>/fileImportOrExport/fileParametersMaintain/selectOrg.jsp",
                title: "选择机构",
                width: 400,
                height: 300,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = nui.get("orgSecurity").getValue();
                	frame.contentWindow.setData(data);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);    //必须
                        if (data) {
                        	var text = "机构:";
                        	var value = "org:";
                        	for(var i=0;i<data.length;i++){
                        		text = text + data[i].ORGNAME + ",";
                        		value = value + data[i].ORGCODE + ",";
                        	}
                        	text = text.substring(0, text.lastIndexOf(','));
                        	value = value.substring(0, value.lastIndexOf(','));
                            btnEdit.setValue(value);
                            btnEdit.setText(text);
                        }
                    }

                }
            });
    	}
    	
    	function selectRole(e){
    		var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath()%>/fileImportOrExport/fileParametersMaintain/selectRole.jsp",
                title: "选择角色",
                width: 400,
                height: 300,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = nui.get("roleSecurity").getValue();
                	frame.contentWindow.setData(data);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);    //必须
                        if (data) {
                        	var text = "角色:";
                        	var value = "role:";
                        	for(var i=0;i<data.length;i++){
                        		text = text + data[i].ROLE_NAME + ",";
                        		value = value + data[i].ROLE_CODE + ",";
                        	}
                        	text = text.substring(0, text.lastIndexOf(','));
                        	value = value.substring(0, value.lastIndexOf(','));
                            btnEdit.setValue(value);
                            btnEdit.setText(text);
                        }
                    }

                }
            });
    	}
    	
    	function selectEmp(e){
    		var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath()%>/fileImportOrExport/fileParametersMaintain/selectEmp.jsp",
                title: "选择人员",
                width: 400,
                height: 300,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = nui.get("empSecurity").getValue();
                	frame.contentWindow.setData(data);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);    //必须
                        if (data) {
                        	var text = "人员:";
                        	var value = "emp:";
                        	for(var i=0;i<data.length;i++){
                        		text = text + data[i].EMPNAME + ",";
                        		value = value + data[i].EMPCODE + ",";
                        	}
                        	text = text.substring(0, text.lastIndexOf(','));
                        	value = value.substring(0, value.lastIndexOf(','));
                            btnEdit.setValue(value);
                            btnEdit.setText(text);
                        }
                    }

                }
            });
    	}
    	
    	function selectGroup(e){
    		var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath()%>/fileImportOrExport/fileParametersMaintain/selectGroup.jsp",
                title: "选择群组",
                width: 400,
                height: 300,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = nui.get("groupSecurity").getValue();
                	frame.contentWindow.setData(data);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);    //必须
                        if (data) {
                        	var text = "群组:";
                        	var value = "group:";
                        	for(var i=0;i<data.length;i++){
                        		text = text + data[i].GROUP_NAME + ",";
                        		value = value + data[i].REC_ID + ",";
                        	}
                        	text = text.substring(0, text.lastIndexOf(','));
                        	value = value.substring(0, value.lastIndexOf(','));
                            btnEdit.setValue(value);
                            btnEdit.setText(text);
                        }
                    }

                }
            });
    	}
    	
    	function cancel(){
    		closeWindow("cancel");
    	}
    	
    	function closeWindow(action){
    		return window.CloseOwnerWindow(action);
    	}
    </script>
</body>
</html>
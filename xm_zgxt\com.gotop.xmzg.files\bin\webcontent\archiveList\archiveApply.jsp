<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%@page import="com.eos.data.datacontext.UserObject" %>
<%@include file="/coframe/dict/common.jsp"%>
<%@page import="com.eos.foundation.database.DatabaseExt"%>
<%@page import="java.util.HashMap"%>
 <script type="text/javascript" src="<%=request.getContextPath() %>/files/process/js/swfupload/swfupload.js"></script>
<script type="text/javascript" src="<%=request.getContextPath() %>/files/process/js/swfupload/handlers.js"></script>
<html>
<!-- 
  - Author(s): lzd
  - Date: 2018-06-04 09:36:10
  - Description:
-->
<head>
<!-- stream插件 -->
<link href="../../css/stream-v1.css" rel="stylesheet" type="text/css">
	<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
</head>
<%
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>
<body>
	<div id="applyForm" style="padding-top:5px;">
		<input name="inputData.INFORMATION_ID" class="nui-hidden"/>
		<input name="inputData.STORAGE_ADDRESS" class="nui-hidden"/>
		<input name="inputData.STORAGE_LOCATION" class="nui-hidden"/>
		<input name="inputData.BOX_NUM" class="nui-hidden"/>
		<table style="width:100%;height:100%;table-layout:fixed;" align="center" class="nui-form-table" >
	    	<tr>
	        	<th class="nui-form-label"><label for="inputData.type$text">档案名称：</label></th>
	        	<td>
	         		<input name="inputData.FILES_NAME" vtype="maxLength:25" readOnly="true" class="nui-textbox" style="width:500px;"/>
	        	</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="inputData.type$text">档案种类：</label></th>
	        	<td colspan="3">
	        		<input id="files_type" class="nui-dictcombobox" readOnly="true" name="inputData.FILES_TYPE"  emptyText="请选择"
  					valueField="dictID" textField="dictName" dictTypeId="FILES_TYPE" showNullItem="true" nullItemText="请选择" style="width:180px;"/>
	        	</td>  
	        	<th class="nui-form-label"><label for="inputData.type$text">归属机构：</label></th>
	        	<td colspan="3">
	        		<input id="DEAL_ORG" name = "inputData.DEAL_NAME" readOnly="true"  vtype="maxLength:25" class="nui-textbox" style="width:180px;"/>
	        	</td> 
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="inputData.type$text">客户经理：</label></th>
	        	<td colspan="3">
	        		<input id="EMPNAME" name = "inputData.EMP_NAME" readOnly="true"  vtype="maxLength:25" class="nui-textbox" style="width:180px;"/>
	        	</td> 
		      	<th class="nui-form-label"><label for="inputData.type$text">附件上传：</label></th>
		      	<td colspan="7">
		      		<!-- <div id="i_select_files" >
						</div> -->
					<input type="button" class="btn btn-default" id="i_select_files"  value="添加文件"/>
	        	</td>
	      	</tr>
	      	<tr >
				<th class="nui-form-label"><label for="inputData.type$text">附件：</label></th>
				<td colspan="10">
					<input class="nui-textarea" name="inputData.AFFILIATED_NAME" id="AFFILIATED_NAME" style="width:550px;"/>
  				</td>
	      	</tr>
	      	<tr align="center">
	      		<th class="nui-form-label"><label for="inputData.type$text">上传进度：</label></th>
	      		<td colspan="7">
					<!-- 回显进度 -->
					<div id="i_stream_files_queue" ></div>
	      		</td>
	      	</tr>
	      	
		</table>
	    <div class="nui-toolbar" style="padding:0px;"   borderStyle="border:0;">
			<table width="100%">
		    	<tr>
		        	<td style="text-align:center;">
		          		<a class="nui-button" iconCls="icon-save" onclick="saveData">提交</a>
		          		<span style="display:inline-block;width:25px;"></span>
		          		<a class="nui-button" iconCls="icon-cancel" onclick="CloseWindow('cancel')">取消</a>
		        	</td>
		      	</tr>
		   	</table>
		</div>
	</div>
<script type="text/javascript" src="../../js/stream-v1.js"></script> 
<script type="text/javascript">
    nui.parse();
    var form = new nui.Form("applyForm");
    var cloneData = '';
    var ids = []; //档案附件id   1.jpg,2.txt...
    var names = []; //档案附件名称   xx,xx,xx
    var delFileId = []; //记录要删除得附件id
    
    
    var files=new Array();
	//stream 插件配置
	var config = {
		browseFileId : "i_select_files", /** 选择文件的ID, 默认: i_select_files */
		browseFileBtn : "<div>请选择文件</div>", /** 显示选择文件的样式, 默认: `<div>请选择文件</div>` */
		dragAndDropArea: "i_select_files", /** 拖拽上传区域，Id（字符类型"i_select_files"）或者DOM对象, 默认: `i_select_files` */
		dragAndDropTips: "<span>把文件(文件夹)拖拽到这里</span>", /** 拖拽提示, 默认: `<span>把文件(文件夹)拖拽到这里</span>` */
		filesQueueId : "i_stream_files_queue", /** 文件上传容器的ID, 默认: i_stream_files_queue */
		filesQueueHeight : 200, /** 文件上传容器的高度（px）, 默认: 450 */
		messagerId : "i_stream_message_container", /** 消息显示容器的ID, 默认: i_stream_message_container */
		multipleFiles: true, /** 多个文件一起上传, 默认: false */
		onRepeatedFile: function(f) {
			alert("文件："+f.name +" 大小："+f.size + " 已存在于上传队列中。");
			return false;	
		},
//		autoUploading: false, /** 选择文件后是否自动上传, 默认: true */
//		autoRemoveCompleted : true, /** 是否自动删除容器中已上传完毕的文件, 默认: false */
//		maxSize: 104857600//, /** 单个文件的最大大小，默认:2G */
//		retryCount : 5, /** HTML5上传失败的重试次数 */
//		postVarsPerFile : { /** 上传文件时传入的参数，默认: {} */
//			param1: "val1",
//			param2: "val2"
//		},
		swfURL : "/swf/FlashUploader.swf" ,/** SWF文件的位置 */
		tokenURL : "<%=request.getContextPath()%>/tk", /** 根据文件名、大小等信息获取Token的URI（用于生成断点续传、跨域的令牌） */
		frmUploadURL : "<%=request.getContextPath()%>/fd", /** Flash上传的URI */
		uploadURL : "<%=request.getContextPath()%>/upload" ,/** HTML5上传的URI */
		filesQueueHeight :100,
//		simLimit: 200, /** 单次最大上传文件个数, */
//		extFilters: [".txt", ".rpm", ".rmvb", ".gz", ".rar", ".zip", ".avi", ".mkv", ".mp3"], /** 允许的文件扩展名, 默认: [] */
//		onSelect: function(list) {alert('onSelect')}, /** 选择文件后的响应事件 */
//		onMaxSizeExceed: function(size, limited, name) {alert('onMaxSizeExceed')}, /** 文件大小超出的响应事件 */
//		onFileCountExceed: function(selected, limit) {alert('onFileCountExceed')}, /** 文件数量超出的响应事件 */
//		onExtNameMismatch: function(name, filters) {alert('onExtNameMismatch')}, /** 文件的扩展名不匹配的响应事件 */
//		onCancel : function(file) {alert('Canceled:  ' + file.name)}, /** 取消上传文件的响应事件 */
		onComplete: function(file) {
			//alert(file.name);
			files.push(file);
			console.log(files);
		
		} /** 单个文件上传完毕的响应事件 */
//		onQueueComplete: function() {alert('onQueueComplete')} /** 所以文件上传完毕的响应事件 */
//		onUploadError: function(status, msg) {alert('onUploadError')} /** 文件上传出错的响应事件 */
//		onDestroy: function() {alert('onDestroy')} /** 文件上传出错的响应事件 */
	};
	//启动stream
	var _t = new Stream(config);
    
    
    //关闭添加窗口
 	function CloseWindow(action){
 		if((action == 'cancel'||action == 'close') && files.length != 0){
 			var data = {};
 			data.files = files;
 			var json = nui.encode(data);
        $.ajax({
                url:"com.gotop.xmzg.files.fileList.delTempFiles.biz.ext",
                type:'POST',
                data:json,
                cache:false,
                async:false,
                contentType:'text/json',
                success:function(text){
                   var returnJson = nui.decode(text);
                   if(returnJson.exception == null && returnJson.flag == 1){
                        files.splice(0, files.length);//文件上传成功清空文件对象数组避免重复提交
                     }else{
                         alert("临时文件删除失败！");
                        }
                   }
             });
 		}
  		if(window.CloseOwnerWindow) 
    		return window.CloseOwnerWindow(action);
  		else
    		return window.close();
    }
    
	
	//与父页面的方法2对应：页面间传输json数据
    function setFormData(data){
        //跨页面传递的数据对象，克隆后才可以安全使用
    	var infos = nui.clone(data);
    	cloneData = infos;
        //如果是点击编辑类型页面
        if (infos.pageType == "apply") {
	      	//表单数据回显
        	var json = infos.record;
         	var form = new nui.Form("#applyForm");//将普通form转为nui的form
         	form.setData(json);
         	
         	//附件相关
	        var id = json.inputData.AFFILIATED_IDS;
	        var name = json.inputData.AFFILIATED_NAMES;
		    fj(id,name);
        }
    }
    
    //操作列：查看/下载附件
      function fj(id,name){
      var s = "";
      if(name!=null){
      		names = name.split(",");
      		ids = id.split(",");
      		var a = [];
      		for(var i=0;i<names.length;i++){
		           var s1=	"<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\"  onclick=\"downAccessory(\'"+ids[i]+"\',\'"+names[i]+"\')\">"+names[i]+"</a>";
		           a.push(s1);
	        }
	        s = a; 
      	}else{
      		s="无附件";
      	}
      	$("#AFFILIATED_NAME").html(""+s+"");
      }
     
     //下载附件或记录要删除附件（视觉删除）
	 function downAccessory(id,name){
	 	nui.confirm("是否删除文件？","系统提示",function(action){
	 		if(action == "ok"){
	 			var s1 = "";
	 			delFileId.push(id);  //记录删除附件id
	 			var index = ids.indexOf(id);
	 			//将要删除得附件id从初始数据中移除，并更新附件展示
	 			if(index > -1){
	 				ids.splice(index,1);
	 				names.splice(index,1);
	 				if(names.length != 0){
	 					var a1 = [];
		 				for(var i=0;i<names.length;i++){
				           var s2=	"<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\"  onclick=\"downAccessory(\'"+ids[i]+"\',\'"+names[i]+"\')\">"+names[i]+"</a>";
				           a1.push(s2);
		        		}
		        		s1 = a1;
	 				}else{
	 					s1="无附件";
	 				}
	 				$("#AFFILIATED_NAME").html(""+s1+"");
	 			}
	 		}else{  //点击文件，不删除，即默认下载操作
	 			var fileId = id;
		       	var fileName = name;
		       	var url="<%=request.getContextPath()%>/files/fileList/downloadFile.jsp?fileId="+fileId+"&fileName="+fileName;
				window.location.replace(url);
	 		}
	 	});
	  }
      
        
        
        
        
        
        function saveData(){ 

			saveDataFiles();

    }
        
        
        
        

    
    
    function saveDataFiles(){ 
        
        var data = cloneData.record;
        //var n = {files:files};
        //上生产时打开底下if
        /* if(files.length == 0 && data.inputData.FILES_TYPE != '01' && data.inputData.FILES_TYPE != '06'){
        	nui.alert("请上传扫描件！");
        	return;
        } */
        data.files=files;
        data.delFileIds = delFileId;
		debugger;
        var json = nui.encode(data);
         var a= nui.loading("正在提交中,请稍等...","提示");
        $.ajax({
                url:"com.gotop.xmzg.files.archiveList.submitApply.biz.ext",
                type:'POST',
                data:json,
                cache:false,
                contentType:'text/json',
                success:function(text){
                nui.hideMessageBox(a); //关闭加载提示（即正在提交中，请稍等）
                   var returnJson = nui.decode(text);
                   if(returnJson.exception == null && returnJson.flag == 1){
                        files.splice(0, files.length);//文件上传成功清空文件对象数组避免重复提交
                        nui.alert("提交成功", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							CloseWindow("saveSuccess");
							}
						});
                     }else if(returnJson.exception == null && returnJson.flag == "2"){
							nui.alert("无权操作！");
						}else{
                         alert("提交失败");
                         window.history.go(-1) ;
                        }
                   }
             });



    }
	
</script>
</body>
</html>
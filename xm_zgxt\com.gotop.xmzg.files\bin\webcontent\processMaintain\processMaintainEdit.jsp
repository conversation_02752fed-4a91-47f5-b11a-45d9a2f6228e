<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%@page import="com.eos.data.datacontext.UserObject" %>
<%@include file="/coframe/dict/common.jsp"%>
<html>
<!-- 
  - Author(s): lzd
  - Date: 2018-06-04 09:36:10
  - Description:
-->
<head>
       <style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style> 
</head>
<%
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>
<body>
	<div id="fileTypeForm" style="padding-top:5px;">
		<table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
			<div class="nui-hidden"  name="inputData.MAINTAIN_ID" ></div>
	      	<tr>
				<th class="nui-form-label"><label for="type$text">流程名称：</label></th>
				<td colspan="4"> 
					<input id="PROCESS_NAME" class="nui-dictcombobox asLabel" name="inputData.PROCESS_NAME"  emptyText="请选择"
  					valueField="dictID" textField="dictName" dictTypeId="APPLY_PROCESS" showNullItem="true" nullItemText="请选择"  readOnly="true"/>
  				</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">起始活动节点：</label></th>
				<td colspan="4">
					<input name="inputData.START_NODENAME" id="START_NODENAME"  class="nui-hidden" style="width: 100%;" />
					
					  <input id="START_NODEID" name="inputData.START_NODEID"  class="nui-dictcombobox nui-form-input"  textField="ACTIVITYINSTNAME" valueField="ACTIVITYINSTID"   
              nullItemText="请选择" emptyText="请选择" showNullItem="true"  style="width:200px;" required="true" /> 
  				</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">终止活动节点：</label></th>
				<td colspan="4">
					<input name="inputData.END_NODENAME" id="END_NODENAME"  class="nui-hidden" style="width: 100%;" />
					
					  <input id="END_NODEID" name="inputData.END_NODEID"  class="nui-dictcombobox nui-form-input"  textField="ACTIVITYINSTNAME" valueField="ACTIVITYINSTID"   
              nullItemText="请选择" emptyText="请选择" showNullItem="true"  style="width:200px;" required="true" /> 
  				</td>
	      	</tr>
	
	      	<tr id="APPOINT_ACTIVITYINSTID" >
	      		<th class="nui-form-label"><label for="type$text">线的名称：</label></th>
	      		<td colspan="4">
	      			<input name="inputData.LINE_NAME" id="LINE_NAME"  class="nui-textbox" style="width: 100%;" required="true" />
	      		</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">排序：</label></th>
				<td colspan="4">
					<input name="inputData.SORT" id=SORT  class="nui-textbox" style="width: 100%;" />
  				</td>
	      	</tr>
		</table>
	    <div class="nui-toolbar" style="padding:0px;"   borderStyle="border:0;">
			<table width="100%">
		    	<tr>
		        	<td style="text-align:center;">
		          		<a class="nui-button" iconCls="icon-save" id="saveButtorn"  onclick="saveData">保存</a>
		          		<span style="display:inline-block;width:25px;"></span>
		          		<a class="nui-button" iconCls="icon-cancel" onclick="CloseWindow('cancel')">取消</a>
		        	</td>
		      	</tr>
		   	</table>
		</div>
	</div>
<script type="text/javascript">
    nui.parse();
    var fileTypeForm = new nui.Form("fileTypeForm");
    
    
    var START_NODEID = nui.get("START_NODEID");
    var END_NODEID = nui.get("END_NODEID");
    
     //与父页面的方法2对应：页面间传输json数据
    function setFormData(data){
                   
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);
        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
             var json = infos.record;
             var form = new nui.Form("#fileTypeForm");//将普通form转为nui的form
             form.setData(json);
             
             
               var PROCESS_NAME = nui.get("PROCESS_NAME").getValue();
		  
		        $.ajax({
						url : "com.gotop.xmzg.files.processMaintain.queryNode.biz.ext",
						type : 'POST',
						data : 'PROCESS_NAME='+PROCESS_NAME,
						cache : false,
						async : false,
						dataType : 'json',
						success : function(text) {
                         var obj = nui.decode(text.resultList);
                         START_NODEID.load(obj);
                         END_NODEID.load(obj);
                         
                         START_NODEID.setValue(json.inputData.START_NODEID);
                        END_NODEID.setValue(json.inputData.END_NODEID);
						}
					});
              
 
         }
    }   
 
 
  START_NODEID.on("valuechanged", function (e) {
		     nui.get("START_NODENAME").setValue(e.selected.ACTIVITYINSTNAME);
		      });  
		      
		      END_NODEID.on("valuechanged", function (e) {
		     nui.get("END_NODENAME").setValue(e.selected.ACTIVITYINSTNAME);
		      });  
        
    
    //关闭添加窗口
 	function CloseWindow(action){
  		if(window.CloseOwnerWindow) 
    		return window.CloseOwnerWindow(action);
  		else
    		return window.close();
    }
    //保存数据
    function saveData(){
    	fileTypeForm.validate();            
        if (fileTypeForm.isValid() == false) return;
        var inputData = fileTypeForm.getData(true,true);
        var json = nui.encode(inputData);
        var URL="com.gotop.xmzg.files.processMaintain.editProcessMaintain.biz.ext";
        $.ajax({
        	url: URL,
            type: 'POST',
            data: json,
            cache: false,
            contentType:'text/json',
            success: function (text){
                var returnJson = nui.decode(text);
				if(returnJson.flag == "1"){
					nui.alert("保存成功", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							CloseWindow("saveSuccess");
						}
					});
				}else if(returnJson.exception == null && returnJson.flag == "exist"){
					nui.alert("档案种类已存在！");
				}else{
					nui.alert("保存失败", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							//CloseWindow("saveFailed");
						}
					});
				}
		    }
  		});   
    }
 
</script>
</body>
</html>
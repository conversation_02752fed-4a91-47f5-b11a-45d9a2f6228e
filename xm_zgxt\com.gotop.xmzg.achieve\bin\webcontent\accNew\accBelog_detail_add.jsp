<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): xwl
  - Date: 2019-04-23 11:41:00
  - Description:
-->
<head>
<%@include file="/coframe/dict/common.jsp"%>
</head>
<body>
  <div id="form1" style="padding-top:5px;">
    <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
       <tr id="khgxTr">
        <th class="nui-form-label"><label for="map.type$text">客户关系类型：</label></th>
        <td colspan="3" >    
      		<input id="NEW_TAB_CUST_RELA" name="map.NEW_TAB_CUST_RELA"  class="nui-dictcombobox"  style="width:150px;" valueField="dictID" 
	        textField="dictName" dictTypeId="JF_GRCKKHGXLX" required="true"/>
        </td> 
      </tr>
      <tr id="kssjTr">
        <th class="nui-form-label"><label for="map.type$text">认领终止开始时间：</label></th>
        <td colspan="3" >    
      		<input id="NEW_TAB_BEGIN" name="map.NEW_TAB_BEGIN"  class="nui-datepicker"  style="width:150px;" required="true" allowInput="false" format="yyyyMMdd" />
        </td> 
      </tr>
    </table>     
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a id="save" class="nui-button" iconCls="icon-save" onclick="onOk">保存</a>&nbsp;&nbsp;
	          <a id="ok" class="nui-button" iconCls="icon-save" onclick="onOk2">确定</a>&nbsp;&nbsp;
	          <span style="display:inline-block;width:25px;"></span>&nbsp;&nbsp;
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  </div>

  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);
    
    var url = "com.gotop.xmzg.achieve.accNew.accBelong_add_claims.biz.ext";
    var objs = [];
    function setFormData(data){                   
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);
        objs = infos.objs;
        
        if(infos.pageType == "updates"){
        	$("#kssjTr").hide();
        	$("#ok").hide();
        	url = "com.gotop.xmzg.achieve.accNew.accRela_updates.biz.ext";
        	if(objs != null) nui.get("NEW_TAB_CUST_RELA").setValue(objs[0].TAB_CUST_RELA);
        }
        
        if(infos.pageType == "claim_terNot"){
        	$("#khgxTr").hide();
        	$("#save").hide();
	   		nui.get("NEW_TAB_BEGIN").setMinDate(new Date());
	   		nui.get("NEW_TAB_BEGIN").setMaxDate(new Date());
	   		var info = objs[0];
	   		info.type = "RLZZ";
	   		nui.ajax({
	            url: "com.gotop.xmzg.achieve.accNew.accBelong_add_zydate_get.biz.ext",
	            type: 'POST',
	            data:nui.encode({"info":info}),
	            cache: false,
	            contentType:'text/json',
	            success: function (text) {
	            	console.log(text.res);
	            	if(text.res != null ){
            			nui.get("NEW_TAB_BEGIN").setMinDate(text.res.min);
            			nui.get("NEW_TAB_BEGIN").setMaxDate(text.res.max);
	            	}
			    }
	       });
	   }
    }
    function onOk2(){
    	 CloseWindow("ok");
    }
    
    function GetData() {
    	return nui.get("NEW_TAB_BEGIN").getValue();        
    }
    
    function onOk(){
    	
        form.validate();
        if (form.isValid() == false) return;
        var load= nui.loading("正在保存请稍侯...","温馨提示 =^_^="); //显示遮罩层
        
        var NEW_TAB_CUST_RELA = nui.get("NEW_TAB_CUST_RELA").getValue();
        for(var i = 0 ; i < objs.length ; i++){
			objs[i].NEW_TAB_CUST_RELA = NEW_TAB_CUST_RELA;
			objs[i].num = objs.length;
			objs[i].tgnum = objs.length;
        }
		//提交数据
        nui.ajax({
       		url: url,
           	type: "post",
           	data: nui.encode({"objs":objs}),
           	contentType:'text/json',
           	success: function (text) {
        	   nui.hideMessageBox(load);  //隐藏遮罩层
        	   var code = text.code;
        	   var msg = text.msg;
        	   
        	   if(code != "1"){
        		  	nui.alert(msg, "系统提示", function(action){
						if(action == "ok" || action == "close"){
							//CloseWindow("saveFailed");
						}
					});
	           }else{
          			nui.alert("保存成功", "系统提示", function(action){
						if(action == "ok"){
							console.log("11");
							CloseWindow("saveSuccess");
						}
					});
	          }
           }
       });
    }
    
    function onCancel(){
        CloseWindow("close");
    }
    
    function CloseWindow(action){
     	var flag = form.isChanged();
		if(window.CloseOwnerWindow) 
		    return window.CloseOwnerWindow(action);
		else
		    return window.close();
    }
  </script>
</body>
</html>
<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-01-05 11:41:00
  - Description:
-->
<head>
<%-- <%@include file="/coframe/tools/skins/common.jsp" %> --%>
<%@include file="/coframe/dict/common.jsp"%>
<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
	    .nui-form-label{
	    	width: 135px;
	    }
    </style>
</head>
<body>
<!-- JF_POSITION -->
<div id="form1" style="padding-top:5px;">
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
   	  <input id="TIDD_ID" name = "map.TIDD_ID" class="nui-hidden" />
   	  <input id="TID_CODE" name = "map.TID_CODE" class="nui-hidden" />
      <tr>
        <th class="nui-form-label"><label for="map.type$text">业务条线：</label></th>
        <td colspan="3" >
        	<input id="TIP_NAME" name = "map.TIP_NAME" enabled="false" class="nui-textbox" style="width:100%;" />
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text">指标：</label></th>
        <td colspan="3" >  
        	<input id="TI_NAME" name = "map.TI_NAME" enabled="false" class="nui-textbox" style="width:100%;" />
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text">指标细项：</label></th>
        <td colspan="3" >  
        	<input id="TID_NAME" name = "map.TID_NAME" enabled="false" class="nui-textbox" style="width:100%;"/>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>积分标准：</label></th>
        <td colspan="3" >  
        	<input class="nui-spinner" id="TIDD_PROPORTION" name="map.TIDD_PROPORTION" style="width:100%;" minValue="0" maxValue="99999999" format="#,0.00" required="true"/>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>开始时间：</label></th>
        <td colspan="3" >  
        	<input id="TIDD_BEGIN" name = "map.TIDD_BEGIN" class="nui-monthpicker"  style="width:100%;" required="true" format="yyyyMM" onvalidation="comparedate"/>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>结束时间：</label></th>
        <td colspan="3" >  
        	<input id="TIDD_END" name = "map.TIDD_END" class="nui-monthpicker"  style="width:100%;" required="true" format="yyyyMM" onvalidation="comparedate"/>
        </td> 
      </tr>
    </table>
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save" onclick="onOk">保存</a>
	          <span style="display:inline-block;width:10px;"></span>
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  </div>

  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);
	
    var saveUrl = "com.gotop.xmzg.achieve.indicators.indicatorsDetail_dt_add.biz.ext";
    function setData(data){     	    
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);
        //表单数据回显
        var json = infos.record;
	    form.setData(json);
        
        if(infos.pageType == "edit"){
	        saveUrl = "com.gotop.xmzg.achieve.indicators.indicatorsDetail_dt_upd.biz.ext";
	        nui.get("TIDD_BEGIN").setEnabled(false); 
	        nui.get("TIDD_BEGIN").setValue(json.map.TIDD_BEGIN.substr(0,4)+"-"+json.map.TIDD_BEGIN.substr(4,6));
	        nui.get("TIDD_END").setValue(json.map.TIDD_END.substr(0,4)+"-"+json.map.TIDD_END.substr(4,6));
        }
        
        if(infos.pageType == "add"){
        	var datetime = new Date();
        	var year = datetime.getFullYear();
        	var month = datetime.getMonth() + 1;
        	var TIDD_BEGIN = null;
        	if(month == '1' || month == '2' || month == '3') TIDD_BEGIN = year+"-01";
			if(month == '4' || month == '5' || month == '6') TIDD_BEGIN = year+"-04";
			if(month == '7' || month == '8' || month == '9') TIDD_BEGIN = year+"-07";
			if(month == '10' || month == '11' || month == '12') TIDD_BEGIN = year+"-10";
	        nui.get("TIDD_BEGIN").setValue(TIDD_BEGIN);
	        nui.get("TIDD_END").setValue('2100-12');
	        //nui.get("TIDD_BEGIN").setMinDate(TIDD_BEGIN);
	        nui.get("TIDD_END").setMinDate(TIDD_BEGIN);
	        
        }
    }
    
    function onOk(){
      	saveData();
    }
    
    //提交
    function saveData(){   
      form.validate();
      if(form.isValid()==false) return;
      
      var data = form.getData(true,true);
      var json = nui.encode(data);
      $.ajax({
        url:saveUrl,
        type:'POST',
        data:json,
        cache:false,
        contentType:'text/json',
        success:function(text){
            var returnJson = nui.decode(text);
			if(returnJson.exception == null && returnJson.iRtn == 1){
				nui.alert("操作成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveSuccess");
					}
				});
			}else if(returnJson!= null && returnJson.msg != null && returnJson.iRtn != 1){
				nui.alert(returnJson.msg, "系统提示");
			}else{
				nui.alert("操作失败", "系统提示");
			}
        }
      });
    }
    
   
    function onCancel(){
      CloseWindow("cancel");
    }
    
    function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
	
	function comparedate(e){
    	var bDate = nui.get("TIDD_BEGIN").getFormValue();
    	var eDate = nui.get("TIDD_END").getFormValue();
    	if(e.isValid){
          if(bDate > eDate){
            e.errorText="结束时间必须大于开始时间";
            e.isValid=false;
          }else
          {
          e.errorText="";
          e.isValid=true;
          }
        }
        
    }
  </script>
</body>
</html>
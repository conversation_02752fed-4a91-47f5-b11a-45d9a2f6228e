<?xml version="1.0" encoding="UTF-8"?>
<sca:definitions xmlns="http://www.osoa.org/xmlns/sca/1.0"
                    targetNamespace="http://tuscany.apache.org/xmlns/sca/1.0"
                    xmlns:sca="http://www.osoa.org/xmlns/sca/1.0"
                    xmlns:tuscany="http://tuscany.apache.org/xmlns/sca/1.0">

 <!-- WS Security POLICY SETS -->
 <!--
 <sca:policySet name="reference-authentication-client"
 	provides="authentication" 
 	appliesTo="sca:reference/sca:binding.ws">--> <!-- authentication verification transmit data the with username and password that user provide-->
 	<!--
	<tuscany:wsConfigParam>-->
		<!-- process the output data-->
		<!--
 		<parameter name="OutflowSecurity"> 
 			<action>
 				<items>UsernameToken</items>
 				<user>userName</user>
				<passwordCallbackClass>com.primeton.engine.composite.client.AuthClientPWCBHandler</passwordCallbackClass>-->     <!--related handler -->
				<!--
                <passwordType>PasswordText</passwordType>--> <!-- password transfer by cleartext-->
               <!--
			   </action>
    	</parameter>
 	</tuscany:wsConfigParam>
 </sca:policySet>
 -->
 
 <!-- A policyset that uses WS Policy --> 
 <!--
 <sca:policySet name="reference-integrity-client"
 	provides="integrity"
 	appliesTo="sca:reference/sca:binding.ws">-->  <!-- integrity validate with certificate -->
	<!--
 	<wsp:Policy wsu:Id="SignOnly" 
 		xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" 
 		xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy">
		<wsp:ExactlyOne>
			<wsp:All>
				<sp:AsymmetricBinding xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
					<wsp:Policy>
						<sp:InitiatorToken>
							<wsp:Policy>
								<sp:X509Token sp:IncludeToken="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy/IncludeToken/AlwaysToRecipient">
									<wsp:Policy>
										<sp:WssX509V3Token10/>
									</wsp:Policy>
								</sp:X509Token>
							</wsp:Policy>
						</sp:InitiatorToken>
						<sp:RecipientToken>
							<wsp:Policy>
								<sp:X509Token sp:IncludeToken="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy/IncludeToken/Never">
									<wsp:Policy>
										<sp:WssX509V3Token10/>
									</wsp:Policy>
								</sp:X509Token>
							</wsp:Policy>
						</sp:RecipientToken>
						<sp:AlgorithmSuite>
							<wsp:Policy>
								<sp:TripleDesRsa15/>--><!-- encryption by RSA -->
								<!--
							</wsp:Policy>
						</sp:AlgorithmSuite>
						<sp:Layout>
							<wsp:Policy>
								<sp:Strict/>
							</wsp:Policy>
						</sp:Layout>
						<sp:IncludeTimestamp/>
						<sp:OnlySignEntireHeadersAndBody/>
					</wsp:Policy>
				</sp:AsymmetricBinding>
				<sp:Wss10 xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
					<wsp:Policy>
						<sp:MustSupportRefKeyIdentifier/>
						<sp:MustSupportRefIssuerSerial/>
					</wsp:Policy>
				</sp:Wss10>
				<sp:SignedParts xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
					<sp:Body/>
				</sp:SignedParts>
	
				<ramp:RampartConfig xmlns:ramp="http://ws.apache.org/rampart/policy">--> <!-- Get infomation by rampart configtion-->
				<!--
					<ramp:user>userName</ramp:user>
					<ramp:encryptionUser>userName</ramp:encryptionUser>
					<ramp:passwordCallbackClass>com.primeton.engine.composite.client.InClientPWCBHandler</ramp:passwordCallbackClass>--> <!--related handler -->
				<!--
					<ramp:signatureCrypto>
						<ramp:crypto provider="org.apache.ws.security.components.crypto.Merlin">-->
							<!-- type of safety certificate -->
							<!--
							<ramp:property name="org.apache.ws.security.crypto.merlin.keystore.type">JKS</ramp:property>-->
							<!-- safety certificate -->
							<!--
							<ramp:property name="org.apache.ws.security.crypto.merlin.file">wangjq.jks</ramp:property>-->
							<!-- keystore -->
							<!--
							<ramp:property name="org.apache.ws.security.crypto.merlin.keystore.password">000000</ramp:property>
						</ramp:crypto>
					</ramp:signatureCrypto>
				</ramp:RampartConfig>
			</wsp:All>
		</wsp:ExactlyOne>
	</wsp:Policy>
 </sca:policySet>
-->
<!--
 <sca:policySet name="reference-confidentiality-client"
      provides="sca:confidentiality" alais="output authenticate"
      appliesTo="sca:reference/sca:binding.ws">-->  <!-- confidentiality encrypt  the transfer data-->
	  <!--
      <description>description of authenticate</description>
      <tuscany:wsConfigParam>-->
			 <!-- process the out data -->
			 <!--
             <parameter name="OutflowSecurity">
                 <action>
                      <items>Timestamp Signature Encrypt</items>
                      <user>userName</user>
                      <encryptionUser>userName</encryptionUser>
                      <passwordCallbackClass>com.primeton.engine.composite.client.ConOutClientPWCBHandler</passwordCallbackClass>--> <!--related handler -->
					  <!--
                      <signaturePropFile>com/primeton/engine/composite/client/security.properties</signaturePropFile>--> <!-- digitally signed file -->
					  <!--
                      <signatureKeyIdentifier>DirectReference</signatureKeyIdentifier>
                      <encryptionKeyIdentifier>SKIKeyIdentifier</encryptionKeyIdentifier> -->
					  <!--
                 </action>
           </parameter>
		   -->
		   <!--
            <parameter name="InflowSecurity">
                 <action>
                      <items>Timestamp Signature Encrypt</items>
                      <passwordCallbackClass>com.primeton.engine.composite.client.ConInClientPWCBHandler</passwordCallbackClass>--> <!--related handler -->
					  <!--
                      <signaturePropFile>com/primeton/engine/composite/client/security.properties</signaturePropFile>--> <!-- digitally signed file -->
					  <!--
                 </action>
           </parameter>
      </tuscany:wsConfigParam>
 </sca:policySet>
-->
 </sca:definitions>

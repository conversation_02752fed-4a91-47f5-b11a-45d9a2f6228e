<%@page pageEncoding="UTF-8"%>	
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2019-06-04 14:51:16
  - Description:
-->
<head>
	<title>指标明细新增</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%@include file="/achieve/init.jsp"%>
	<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
</head>
<body>
  <div id="prameter" style="padding-top:5px;"> 
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table" align="center"> 
    <tbody>
     <tr> 
      <th class="nui-form-label" style="width:20%"><font class="b_red">*</font>业务条线：</th> 
      <td style="width:30%">
      	<div id="tip_code" name="tip_code" class="nui-combobox" allowInput="false" 
      		dataField="list" textField="TIP_NAME" valueField="TIP_CODE"
      		emptyText="请选择" onvaluechanged="onTipCodeChanged"
      		url="com.gotop.xmzg.achieve.indicators.indicators_plate_choice.biz.ext" 
      		style="width:200px;" required="true">
      		<div property="columns">
		        <div header="条线代码" field="TIP_CODE" width="60"></div>
		        <div header="条线名称" field="TIP_NAME" width="120"></div>
		    </div>
		</div>
      </td>
      <th class="nui-form-label" style="width:20%"><font class="b_red">*</font>指标代码：</th>
      <td style="width:30%">
      	<div id="ti_code" name="ti_code" class="nui-combobox" allowInput="false" 
	      	dataField="list" textField="TI_NAME" valueField="TI_CODE" style="width:200px;"
	      	emptyText="请选择" required="true"
	      	>
	     	<div property="columns">
		        <div header="指标代码" field="TI_CODE" width="60"></div>
		        <div header="指标名称" field="TI_NAME" width="120"></div>
		    </div>
		</div>
      </td>
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>指标细项代码：</th> 
      <td>
      <input class="nui-hidden" id="tid_start" name="tid_start" value="1"/>
      <input class="nui-textbox" id="tid_code" name="tid_code" style="width:200px;" required="true"/></td>
      <th class="nui-form-label"><font class="b_red">*</font>指标细项名称：</th> 
      <td><input class="nui-textbox" id="tid_name" name="tid_name" style="width:200px;" required="true"/></td> 
     </tr>
     <tr> 
      <th class="nui-form-label">指标细项说明：</th>
      <td colspan="3"><input class="nui-textbox" id="tid_remark" name="tid_remark" style="width:600px;"/></td>
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>是否可预约业绩：</th> 
      <td>
      	<input class="nui-dictcombobox" id="tid_results" valueField="dictID" textField="dictName" 
			dictTypeId="JF_RESULTS" name="tid_results" value="0" required="true"
			style="width:200px;" onvaluechanged="on_tid_results"
			/>
      </td>
      <th class="results nui-form-label"><font class="b_red">*</font>预约是否审核：</th> 
      <td class="results">
      	<input class="nui-dictcombobox" id="tid_results_auth" valueField="dictID" textField="dictName" 
      		style="width:200px;" name="tid_results_auth"
			dictTypeId="JF_RESULTS_AUTH" required="true" value="0"/>
      </td> 
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>数据处理机制：</th> 
      <td>
      	<input class="nui-dictcombobox" id="tid_processmode" valueField="dictID" textField="dictName" 
      		style="width:200px;"
			dictTypeId="JF_PROCESSMODE" name="tid_processmode"  value="1" required="true"/>
      </td>
      <th class="nui-form-label"><font class="b_red">*</font>数据来源：</th> 
      <td>
      	<input class="nui-dictcombobox" id="tid_datasource" valueField="dictID" textField="dictName" 
      		style="width:200px;"
			dictTypeId="JF_DATASOURCE" name="tid_datasource"  value="1" required="true"/>
      </td>  
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>指标单位：</th> 
      <td>
      	<input class="nui-dictcombobox" id="tid_unit" valueField="dictID" textField="dictName" 
      		style="width:200px;"
			dictTypeId="JF_UINT" name="tid_unit"  value="1" required="true"/>
      </td>
      <th class="nui-form-label"><font class="b_red">*</font>基数：</th> 
      <td>
      	<input class="nui-spinner" id="tid_base" name="tid_base" style="width:200px;" minValue="0" maxValue="99999999" format="#,0.00" required="true"/>
      </td> 
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>积分标准：</th> 
      <td>
      	<input class="nui-spinner" id="tid_proportion" name="tid_proportion" style="width:200px;" minValue="-99999999" maxValue="99999999" format="#,0.00" required="true"/>
      </td>
      <th class="nui-form-label">固定值：</th> 
      <td>
      	<input class="nui-spinner" id="tid_fixed" name="tid_fixed" style="width:200px;" value="0" minValue="0" maxValue="99999999" format="#,0.00"/>
      </td>
     </tr>
     <tr> 
      <th class="nui-form-label">最小值：</th> 
      <td>
      	<input class="nui-spinner" id="tid_min" name="tid_min" style="width:200px;" value="0" minValue="0" maxValue="99999999" format="#,0.00"/>
      </td>
      <th class="nui-form-label">最大值：</th> 
      <td>
      	<input class="nui-spinner" id="tid_max" name="tid_max" style="width:200px;" value="0" minValue="0" maxValue="99999999" format="#,0.00"/>
      </td>
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>数据类型：</th> 
      <td>
      	<input class="nui-dictcombobox" id="tid_datatype" valueField="dictID" textField="dictName" 
      		style="width:200px;"
			dictTypeId="JF_DATATYPE" name="tid_datatype"  value="0" required="true"/>
      </td>
      <th class="nui-form-label"><font class="b_red">*</font>时段标识：</th> 
      <td>
      	<input class="nui-dictcombobox" id="tid_timeframe" valueField="dictID" textField="dictName" 
      		style="width:200px;"
			dictTypeId="JF_TIMEFRAME" name="tid_timeframe"  value="0" required="true"/>
      </td>
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>计算频率：</th> 
      <td>
      	<input class="nui-dictcombobox" id="tid_frequency" valueField="dictID" textField="dictName" 
      		style="width:200px;"
			dictTypeId="JF_FREQUENCY" name="tid_frequency"  value="0" required="true"/>
      </td>
      <th class="nui-form-label"><font class="b_red">*</font>币种：</th> 
      <td>
      	<input class="nui-dictcombobox" id="tid_currency" valueField="dictID" textField="dictName" 
      		style="width:200px;"
			dictTypeId="JF_CURRENCY" name="tid_currency"  value="0" required="true"/>
      </td>
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>统计口径：</th> 
      <td>
      	<input class="nui-dictcombobox" id="tid_cal" valueField="dictID" textField="dictName" 
      		style="width:200px;"
			dictTypeId="JF_CAL" name="tid_cal"  value="0" required="true"/>
      </td>
      <th class="nui-form-label"><font class="b_red">*</font>指标类型：</th> 
      <td>
      	<input class="nui-dictcombobox" id="tid_type" valueField="dictID" textField="dictName" 
      		style="width:200px;"
			dictTypeId="JF_IND_TYPE" name="tid_type"  value="0" required="true"/>
      </td> 
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>是否设置保护期：</th> 
      <td>
      	<input class="nui-dictcombobox" id="tid_protection" valueField="dictID" textField="dictName" 
			dictTypeId="JF_YES_NO" name="tid_protection"  value="0" required="true"
			style="width:200px;"
			onvaluechanged="on_tid_protection"
			/>
      </td>
      <th class="nui-form-label"><font class="b_red">*</font>保护期天数：</th> 
      <td>
      	<input class="nui-spinner" id="tid_protection_date" name="tid_protection_date" minValue="0" maxValue="99999999" style="width:200px;" value="30"/>
      </td> 
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>是否参与积分计算：</th> 
      <td>
      	<input class="nui-dictcombobox" id="tid_integral_calcul" valueField="dictID" textField="dictName" 
      		style="width:200px;"
			dictTypeId="JF_YES_NO" name="tid_integral_calcul"  value="0" required="true"/>
      </td>
      <th class="nui-form-label">排序：</th> 
      <td>
      	<input class="nui-spinner" id="tid_sorting" name="tid_sorting" style="width:200px;" value="0"/>
      </td> 
     </tr>
    </tbody>
   </table> 
   <div class="nui-toolbar" style="padding:0px;" borderstyle="border:0;"> 
    <table width="100%"> 
     <tbody>
      <tr> 
       	<td style="text-align:center;">
       		<input class="nui-hidden" id="orgs"/>
	       	<a class="nui-button" iconcls="icon-save" onclick="seve">提交</a> 
	       	<span style="display:inline-block;width:10px;"></span> 
	       	<a class="nui-button" iconcls="icon-cancel" onclick="onCancel">取消</a>
       	</td> 
      </tr> 
     </tbody>
    </table> 
   </div> 
  </div>

  <script type="text/javascript">
  	nui.parse();
  	nui.get("tid_protection_date").disable();
  	nui.get("tid_results_auth").setValue("");
  	$(".results").hide();
  	nui.get("tid_results_auth").setRequired(false);
  	//获取机构集
  	function onTipCodeChanged(e){
  		var orgs = e.selected.TIP_ORGCODES;
  		nui.get("orgs").setValue(orgs);
  		var json = nui.encode({tip_code:nui.get("tip_code").getValue(),type:2});
	   	//提交数据
        nui.ajax({
           url: "com.gotop.xmzg.achieve.indicators.indicators_choice.biz.ext",
           type: "post",
           data: json,
           contentType:'text/json',
           success: function (text) {
        	  var obj = text.list;
              nui.get("ti_code").setData(obj);
           }
       });
  	}
	
  	function on_tid_results(e){
  		var falt = false;
  		if(e.value == 1){
  			$(".results").show();
  			falt = true;
  		}else{
  			$(".results").hide();
  			falt = false;
  			nui.get("tid_results_auth").setValue("");
  		}
  		nui.get("tid_results_auth").setRequired(falt);
  	}
  	
  	//是否保护期为1时，必填 缺省值30天
  	function on_tid_protection(e){
  		if(e.value == 1){
  			nui.get("tid_protection_date").enable();
  		}else{
  			nui.get("tid_protection_date").disable();
  			nui.get("tid_protection_date").setValue("30");
  		}
  	}
  	
  	
  	function seve(){
  		var form = new nui.Form("#prameter");
		form.validate();
		if (form.isValid() == false) return;
		var num = nui.get("tid_proportion").getValue();
		if(num == "0"){
			nui.alert("积分系数不能为0");
			return;
		}
		contain(function(){
			var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
			//提交数据
  	        nui.ajax({
  	       		url: "com.gotop.xmzg.achieve.indicators.indicators_judge_add.biz.ext",
  	           	type: "post",
  	           	data: nui.encode({type:2,t_code:nui.get("tid_code").getValue()}),
  	           	contentType:'text/json',
  	           	success: function (text) {
  	        	   nui.hideMessageBox(load);  //隐藏遮罩层
  	        	   var code = text.code;
  	        	   var msg = text.msg;
  	        	   if(code != "0000"){
  	        		  	nui.alert(msg, "系统提示");
  		           }else{
						//提交数据
				        nui.ajax({
				       		url: "com.gotop.xmzg.achieve.indicators.indicators_detail_add.biz.ext",
				           	type: "post",
				           	data: nui.encode({"obj":form.getData()}),
				           	contentType:'text/json',
				           	success: function (text) {
				        	   nui.hideMessageBox(load);  //隐藏遮罩层
				        	   var code = text.code;
				        	   var msg = text.msg;
				        	   if(code != "1"){
				        		  	nui.alert(msg, "系统提示");
					           }else{
				          			nui.alert(msg, "系统提示", function(action){
										if(action == "ok"){
											CloseWindow("saveSuccess");
										}
									});
					          }
				           }
				       });
  		         	}
  	           	}
  	        });
		});
  	}
  	
  </script>
</body>
</html>
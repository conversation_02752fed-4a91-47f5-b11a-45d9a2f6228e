<%@page pageEncoding="UTF-8"%>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<%@page import="java.text.SimpleDateFormat"%>
<%@page import="java.util.*"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-01-05 17:04:11
  - Description:
-->
<%
UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");

Date date = new Date();
SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
Calendar c= Calendar.getInstance();
c.setTime(date);
c.add(Calendar.MONTH, -1);//往前一个月
String startDate = df.format(c.getTime());

Calendar c1= Calendar.getInstance();
c1.setTime(date);
String endDate = df.format(c1.getTime());
%>

<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>操作日志查询</title>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	     <div class="nui-hidden"  name="queryData.userOrgId" id="userOrgId"></div>
		<div class="nui-hidden"  name="queryData.userId"  id="userId" ></div>
		<div id="flag" class="nui-hidden"  name="queryData.flag" ></div>
	    <table class="table" style="width:100%;">
	       
			<tr>
			    <th class="tit" >操作人：</th>
				<td>
					<input id="empname"  class="nui-textbox" name="queryData.empname" style="width:150px;" vtype="maxLength:25;"/>
				</td>
			    <th class="tit" >操作模块：</th>
				<td>
					<input id="ope_mod"  class="nui-textbox" name="queryData.ope_mod" style="width:150px;" vtype="maxLength:25;"/>
				</td>	
				<th class="tit" style="width:120px;"  >操作时间：</th>
				<td> 
					<input id="queryData.startDate" class="nui-datepicker" name="queryData.apply_time1"  style="width:110px;" required="true" dateFormat="yyyy-MM-dd" allowInput="false" value="<%=startDate%>"/>
	                 ~
	                <input id="queryData.endDate" class="nui-datepicker" name="queryData.apply_time2"  style="width:110px;" required="true" dateFormat="yyyy-MM-dd" allowInput="false" value="<%=endDate%>" onvalidation="comparedate"/>
				</td> 	
				<td></td>	
				<td></td>	
				<th rowspan="2" class="btn-wrap">
					<input class="nui-button" text="查询" iconCls="icon-search" onclick="search"/>&nbsp;&nbsp;
					<input class="nui-button" text="重置" iconCls="icon-reset" onclick="clean"/>&nbsp;&nbsp;
					<input class="nui-button" text="导出报表" iconCls="icon-download" onclick="excel"/>
				</th>
			</tr>
	    </table>
	  </div>
    </div>
  </div>
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.userInfoManage.wj.operateLog.select_operate_log.biz.ext" 
	  sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" >
	    <div property="columns" >
		  <div field="OPE_MOD" headerAlign="center"  align="center">操作模块</div>
		  <div field="EMPNAME" headerAlign="center"  align="center" >操作人姓名</div>
		  <div field="ORGNAME" headerAlign="center"  align="center" >操作人所属机构</div> 
		  <div field="OPE_CONTENT" headerAlign="center"  align="center" width=200>操作内容</div>
		  <div field="OPE_REVER1" headerAlign="center"  align="center">备注1</div>
		  <div field="OPE_REVER2" headerAlign="center"  align="center">备注2</div>
	      <div field="OPE_DATE" headerAlign="center"  align="center" >操作时间</div>
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">
      nui.parse();
      
      //判断当前登录人是否是sysadmin、admin
      $.ajax({
		        url:"com.gotop.userInfoManage.wj.operateLog.isEmpSysadminAndAmin.biz.ext",
		        type:'POST',
		        data:'',
		        cache:false,
		        async:false,
        		dataType:'json',
		        success:function(text){
		          //如果是
		          if(text.flag == "1"){
		           nui.get("flag").setValue("yes");
		          }
		        }
		 });
      
	    var  userOrgId="<%=userObject.getUserOrgId()%>";
		var  userId="<%=userObject.getUserId()%>";
		nui.get("userOrgId").setValue(userOrgId);
	    nui.get("userId").setValue(userId);
	    
	    var grid = nui.get("datagrid1");
	    //search();
	    var form = new nui.Form("#form1");
		var data = form.getData(true,true);
	    //grid.load(data);
	    
	     function search(){
	       var form = new nui.Form("#form1");
			   form.validate();
			   if (form.isValid() == false) return;
	       var data = form.getData(true,true);
	       grid.load(data);
	    }
 //比较两个时间  time1，time2均为日期类型  
//判断两个时间段是否相差 m 个月
function completeDate2(time1 , time2 , m)
{
	var diffyear = time2.getFullYear() - time1.getFullYear() ;
	var diffmonth = diffyear * 12 + time2.getMonth() - time1.getMonth() ;
	if(diffmonth < 0 ){
		return false ;
	}
 
	var diffDay = time2.getDate() - time1.getDate() ;
	if(diffmonth < m || (diffmonth == m && diffDay <= 0)){
		
		if(diffmonth == m && diffDay == 0){
			var timeA = time1.getHours()*3600+60*time1.getMinutes()+time1.getSeconds();
			var timeB = time2.getHours()*3600+60*time2.getMinutes()+time2.getSeconds();
			if(timeB-timeA > 0){
				return false;
			}
		}
		return true ;
	}
	return false ;
}
	    
 //时间判断开始时间不能大于结束时间
    function comparedate(e){
    //debugger;
      var startDate = nui.get("queryData.startDate").getFormValue();
      var endDate = nui.get("queryData.endDate").getFormValue();
      
      var regEx = new RegExp("\\-","gi");
       startVal=startDate.replace(regEx,"/");
       endVal=endDate.replace(regEx,"/");
       //var s1=Date.parse(startVal);
       //var s2=Date.parse(endVal);
      var startTime =new Date(startVal);
      var endTime = new Date(endVal); 
	  if(endDate!=""){
        if(e.isValid){
          if(startDate>endDate){
            e.errorText="结束日期不能小于开始日期";
            e.isValid=false;
          }else
          {
	          if(completeDate2(startTime,endTime,12)==true){
	           e.errorText="";
	           e.isValid=true;
	          }else{
	            e.errorText="日期跨度不能大于一年";
	            e.isValid=false;
	          }
          }
        }
      }
    }    
 

   
//导出Excel
function excel(){
	      
	var form=new nui.Form("form1");
    form.validate();
    if (form.isValid() == false) return;
	var data=form.getData(true, true);
	var fileName="操作日志查询";
	var queryPath="com.gotop.userInfoManage.wj.operateLog.query_t_sysope_log";
	var columns=grid.getBottomColumns();
	columns=columns.clone();
	for(var i=0;i<columns.length;i++){
		var column=columns[i];
		if(!column.field){
			columns.removeAt(i);
		}else{
			var c={header:column.header,field:column.field };
			columns[i]=c;
		}
	}
	columns=nui.encode(columns);
	data=nui.encode(data);
	var url="<%=request.getContextPath()%>/util/exportExcel/exportExcel.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data;
	 window.location.replace(encodeURI(url));
	 
	 //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
     setMask();
} 

	function clean(){
	    //不能用form.reset(),否则会把隐藏域信息清空
       nui.get("ope_mod").setValue("");
       nui.get("empname").setValue("");
       nui.get("queryData.startDate").setValue("");
       nui.get("queryData.endDate").setValue("");
	}
    
//设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)	
function setMask(){
	 
	 var a= nui.loading("正在操作中,请稍等...","提示"); //显示遮罩层
	 
	 var icount = setInterval(function(){  
	
		  if(document.attachEvent){   //IE浏览器
	
		 	if(document.readyState=='interactive'){

	              nui.hideMessageBox(a);  //隐藏遮罩层
	              clearTimeout(icount);
	        }
		 }else{ //谷歌浏览器
		 	if(document.readyState=='complete'){

	              nui.hideMessageBox(a);  //隐藏遮罩层
	              clearTimeout(icount);
	        }
		 } 
	 
	 }, 1); 
	 
}
  </script>
</body>
</html>
<%@page import="com.eos.data.datacontext.UserObject"%>
<%@page import="org.gocom.components.coframe.tools.IConstants" %>
<%@page import="java.util.HashMap" %>
<%@page import="java.util.Map" %>
<%@page import="com.pfpj.foundation.database.DatabaseExt" %>
<%@page import="com.primeton.ext.engine.component.LogicComponentFactory" %>
<%@page import="com.eos.engine.component.ILogicComponent" %>
<%@page pageEncoding="UTF-8"%>
<!-- 
  - Author(s): wj
  - Date: 2018-08-31 09:58:59
  - Description: 为了记录注销日志   以后要是不用这个的话，可以将index_20180831bf。jsp这个的内容复制过来
-->
<%
	String skin = "default";
	Object userObj = session.getAttribute("userObject");
	String userid="";
	if(userObj != null){
		UserObject userObject = (UserObject)userObj;
		userid = userObject.getUserId();
		if(userObject.getAttributes().get(IConstants.MENU_TYPE) != null){
			skin = (String)userObject.getAttributes().get(IConstants.MENU_TYPE);
		}
	}
	
	String ip = null;
	if (request.getHeader("x-forwarded-for") == null) {
		ip = request.getRemoteAddr();
	}
	//System.out.println("登陆的ip地址是"+ip);
    // String sql = "org.gocom.components.coframe.userInfoManage.userInfoManage.insert_sysLog";
	//	Map<String, Object> map = new HashMap<String, Object>();
	//	map.put("OPE_MOD", "登陆");//操作模块
	//	map.put("OPE_PER", userid);//操作人员
	//	map.put("OPE_CONTENT", "["+ip+"]"+"登陆系统");//操作内容
	//	DatabaseExt.executeNamedSql("default", sql, map);
	
	//Java里调用biz,发送即将超时通知到未阅通知表里
	HashMap<String, Object> map2 = new HashMap<String, Object>();
	String OPE_MOD="登陆";//操作模块
	String OPE_CONTENT="["+ip+"]"+"登陆系统";//操作内容
	try{
		 String componentName = "org.gocom.components.coframe.userInfoManage.userInfoManage";// 逻辑构件名称
	     String operationName = "addSysLog";// 逻辑流名称
	     ILogicComponent logicComponent = LogicComponentFactory.create(componentName);
	     int size = 2;  //表示1个参数
	     Object[] params = new Object[size];// 逻辑流的输入参数
	     params[0] = OPE_MOD;
	     params[1] = OPE_CONTENT;
	     logicComponent.invoke(operationName, params);
	}catch(Throwable e){
	}
	
	response.sendRedirect(request.getContextPath() + "/skins/"+skin+"/index.jsp");
 %>
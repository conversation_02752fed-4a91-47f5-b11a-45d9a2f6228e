<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): yangzhou
  - Date: 2013-03-21 11:24:50
  - Description:
-->
<head>
<title>员工基本信息修改</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<%@include file="/coframe/tools/skins/common.jsp" %>
<%@page import="com.eos.system.utility.StringUtil"%>
<script type="text/javascript" src="<%=contextPath%>/coframe/org/js/org_common.js"></script>
<style type="text/css">
    fieldset
    {
        border:solid 1px #aaa;
    }        
    .hideFieldset
    {
        border-left:0;
        border-right:0;
        border-bottom:0;
    }
    .hideFieldset .fieldset-body
    {
        display:none;
    }
</style>
</head>
<body>

<div style="padding-top:5px;overflow:hidden">
	<div id="form1">
	    <input class="nui-hidden" name="employee.ORGID" id="orgid"/>
		<input class="nui-hidden" name="employee.EMPID" id="empid"/>
		<input class="nui-hidden" name="employee.USERID" id="empuserid"/>
	    <input class="nui-hidden" name="employee.STATUS" id="userstatus"/>
	    
	    <input class="nui-hidden" name="employee.OLD_EMPNAME" id="old_empname"/>
	    <input class="nui-hidden" name="employee.OLD_EMPCODE" id="old_empcode"/>
	     
		<table style="width:100%;table-layout:fixed;" class="nui-form-table" >
			<tr>
				<td class="nui-form-label"><label for="empname$text">员工姓名：</label></td>
				<td><input style="width:100%" id="empname" class="nui-textbox" name="employee.EMPNAME" required="true" vtype="maxLength:50"/></td>
				<td class="nui-form-label"><label for="empcode$text">员工号：</label></td>
				<td><input style="width:100%" id="empcode" class="nui-textbox" name="employee.EMPCODE" readonly = "true" required="true" vtype="maxLength:30"/></td>
			</tr>		
			<tr class="odd">
				<td class="nui-form-label"><label for="gender$text">性别：</label></td>
				<td><input style="width:100%"  id="gender" name="employee.GENDER" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox" dictTypeId="COF_GENDER" /></td>
				<td class="nui-form-label"><label for="birthdate$text">出生日期：</label></td>
				<td><input style="width:100%" id="birthdate" name="employee.BIRTHDATE" class="nui-datepicker" allowInput="false"/></td>
			</tr>				
			<tr>
				<td class="nui-form-label"><label for="cardtype$text">证件类型：</label></td>
				<td><input style="width:100%" id="cardtype" name="employee.CARDTYPE" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox" dictTypeId="COF_CARDTYPE" /></td>
				<td class="nui-form-label"><label for="cardno$text">证件号码：</label></td>
				<td><input style="width:100%" id="cardno" name="employee.CARDNO" class="nui-textbox" vtype="maxLength:20"/></td>
			</tr>				
			<tr class="odd">
				<td class="nui-form-label"><label for="indate$text">入职日期：</label></td>
				<td><input style="width:100%"  id="indate" name="employee.INDATE" class="nui-datepicker" allowInput="false"/></td>
				<td class="nui-form-label"><label for="outdate$text">离职日期：</label></td>
				<td><input style="width:100%" id="outdate" name="employee.OUTDATE" class="nui-datepicker" onvalidation="onOutdateValidation" allowInput="false" />
				</td>
			</tr>				
			<tr>
				<td class="nui-form-label"><label for="empstatus$text">人员状态：</label></td>
				<td><input style="width:100%"  id="empstatus" name="employee.EMPSTATUS" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox" dictTypeId="COF_EMPSTATUS"   onvaluechanged="getstatus" required="true"/></td>
				<td class="nui-form-label"><label for="mobileno$text">手机号码：</label></td>
				<td><input style="width:100%" id="mobileno" name="employee.MOBILENO" class="nui-textbox" vtype="maxLength:14" required="true"/></td>
			</tr>
			<tr  class="odd">
				<td class="nui-form-label"><label for="oaddress$text">办公地址：</label></td>
				<td colspan="3"><input id="oaddress" name="employee.OADDRESS" class="nui-textbox nui-form-input" style="width:80%;"  vtype="maxLength:255"/></td>
			</tr>
			<tr>	
				<td class="nui-form-label"><label for="ozipcode$text">办公室邮编：</label></td>
				<td><input id="ozipcode" name="employee.OZIPCODE" class="nui-textbox nui-form-input" vtype="int;rangeLength:0,10" /></td>
				<td class="nui-form-label"><label for="faxno$text">传真号码：</label></td>
				<td><input id="faxno" name="employee.FAXNO" class="nui-textbox nui-form-input"  vtype="maxLength:14"/></td>
			</tr>
			<tr  class="odd">
				<td class="nui-form-label"><label for="otel$text">办公室电话：</label></td>
				<td><input id="otel" name="employee.OTEL" class="nui-textbox nui-form-input"  vtype="phone;rangeLength:0,20"/></td>
				<td class="nui-form-label"><label for="party$text">政治面貌：</label></td>
				<td><input id="party" name="employee.PARTY" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox nui-form-input" dictTypeId="COF_PARTYVISAGE" /></td>
			</tr>	
			<tr  class="odd">
				<td class="nui-form-label"><label for="oaddress$text">已分配角色：</label></td>
				<td colspan="3"><input id="role" name = "map.ROLE_CODE"  class="nui-textboxlist nui-form-input"   allowInput="false" readOnly="true" disable="true"  style="width:80%;height:50px"/></td>
			</tr>
			<!-- <tr class="odd">
				<td class="nui-form-label"><label for="sortno$text">排列顺序：</label></td>
				<td><input style="width:100%" id="sortno" class="nui-textbox" name="employee.sortno" vtype="int" /></td>
				<td></td>
				<td></td>
			</tr>			 -->	
		</table>
	</div>
</div>
<div  id="btn_div" class="nui-toolbar" style="text-align:center;padding-top:5px;padding-bottom:5px;" 
    borderStyle="border:0;">
    <a class="nui-button" style="width:60px;" iconCls="icon-save" onclick="update">保存</a>
    <span style="display:inline-block;width:25px;"></span>
    <a class="nui-button" iconCls="icon-reload" style="width:100px;" onclick="resetPassWord">重置密码</a>
</div>
</div>

<script type="text/javascript">
	 nui.parse();
	 var form = new nui.Form("#form1");
     form.setChanged(false);
	 
	 //与父页面的方法2对应：页面间传输json数据
    function setFormData(data){
                   
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);

        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
        
	      //表单数据回显
             var json = infos.record;
             var form = new nui.Form("#form1");//将普通form转为nui的form
             form.setData(json);
             
            var role = nui.get("role");                    
            role.setValue(json.employee.ROLE_CODE);
            role.setText(json.employee.ROLE_NAME);
            
            var empname = nui.get("empname").getValue();
			var empcode = nui.get("empcode").getValue();
			    nui.get("old_empname").setValue(empname);
			    nui.get("old_empcode").setValue(empcode);
            
            //判断当前登录人是否是系统管理员
	    		$.ajax({
			        url:"org.gocom.components.coframe.userInfoManage.userInfoManage.isSysadmin.biz.ext",
			        type:'POST',
			        data:'',
			        cache:false,
			        async:false,
	        		dataType:'json',
			        success:function(text){
			          //如果不是系统管理员/超级管理员
			          if(text.flag == "2"){
			           var cardtype = mini.get("cardtype");
			           var cardno = mini.get("cardno");
			           var empcode = mini.get("empcode");
			           
			           //不让修改证件类型和证件号码
                       cardtype.disable();
                       cardno.disable();
                       
                       //不让修改员工号
			            empcode.disable();
			          }
			        }
			 });
         }
    }
	 
	
	
    function update(){   
      var empstatus=nui.get("empstatus").getValue();
       if(empstatus=="on"){
           nui.get("userstatus").setValue("1");
       }else{
           nui.get("userstatus").setValue("9");
       }
      form.validate();
      if(form.isValid()==false) return;
        
      var data = form.getData(true,true);
      var json = nui.encode(data);
      $.ajax({
        url:"org.gocom.components.coframe.userInfoManage.userInfoManage.update_userInfo.biz.ext",
        type:'POST',
        data:json,
        cache:false,
        contentType:'text/json',
        success:function(text){
            var returnJson = nui.decode(text);
			if(returnJson.exception == null && returnJson.iRtn == 1){
			
				nui.alert("修改成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
					 
						CloseWindow("saveSuccess");
					}
				});
			}else{
				nui.alert("修改失败", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveFailed");
					}
				});
			}
        }
      });
   
    }
    
	 

    function CloseWindow(action) {
        if (action == "close" && form.isChanged()) {
            if (confirm("数据被修改了，是否先保存？")) {
                return false;
            }
        }
        if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
        else window.close();            
    }
    
    function cancel(e) {
        CloseWindow("cancel");
    }
	
	
	function resetPassWord(){
		var data = form.getData(true,true);
		var json = nui.encode(data);
		if(confirm("确定重置密码吗？")){
			$.ajax({
				url:'org.gocom.components.coframe.userInfoManage.userInfoManage.resetPassword.biz.ext',
				type:'post',
				contentType:'text/json',
				cache:'false',
				data:json,
				success:function(){
				    //记录日志
				    var old_empname = nui.get("old_empname").getValue();
				    var old_empcode = nui.get("old_empcode").getValue();
				    var OPE_MOD = "重置密码";  
					var OPE_CONTENT = "重置[工号:"+old_empcode+" 姓名:"+old_empname+"]"+"密码";
			        var json = nui.encode({OPE_MOD:OPE_MOD,OPE_CONTENT:OPE_CONTENT});
				    $.ajax({
				        url:"org.gocom.components.coframe.userInfoManage.userInfoManage.addSysLog.biz.ext",
				        type:'POST',
				        data:json,
				        cache:false,
				        contentType:'text/json'
				      });
				      
					nui.alert("密码已重置");				
				},
				error:function(){
					nui.alert("密码重置失败");
				}				
			});
		}
	}
	//校验日期
	function onOutdateValidation(e){
       	var o = form.getData();
       	var org = o.employee || {};
		if(org.outdate && org.indate && org.outdate<=org.indate){
			e.errorText = "离职日期必须大于入职日期";
			e.isValid = false;
		}
  
	}
	
	
    
	 	//选择离职状态
    function getstatus(){
       	var empstatus=nui.get("empstatus").getValue();
       if(empstatus=="on"){
           nui.get("userstatus").setValue("1");
       }else{
           nui.get("userstatus").setValue("9");
       }	
    }  
</script>

</body>
</html>

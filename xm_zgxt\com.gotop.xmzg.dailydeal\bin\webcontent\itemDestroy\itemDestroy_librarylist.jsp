<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-05-23 12:01:11
  - Description:根据库存核销
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>单册核销(库存)</title>
<%

	java.text.SimpleDateFormat formatter = new java.text.SimpleDateFormat("yyyy/MM/dd"); 
    java.util.Date currentTime = new java.util.Date();//得到当前系统时间 
    String str_date = formatter.format(currentTime); //将日期时间格式化 
 %>
</head>
<body style="width:100%;overflow:hidden;">
<%
 
 	 //Object[] result =(Object[])request.getAttribute("itR");
 	 Object itR = (Object)request.getAttribute("itR"); 
 	 String str1 ="";
 	 
 	 if(itR!=null){
 	 	String str3 = (String)itR;
 	 	if(!"".equals(str3)){
 	 		str1=str3;
 	 	}
 	 }
 	 //System.out.println(str1);
	
%>  
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	  <div id="c_orgid" class="nui-hidden"  name="queryData.c_orgid" ></div>
	    <table class="table" style="width:100%;">
	      <tr >
	            <th class="tit" >机构名称：</th>
				<td>
					<input class="nui-buttonedit" style="width:150px" name="queryData.orgid"  id="queryData.orgid" allowInput="false"onbuttonclick="onButtonEdit"  />
				</td>
		     
				<th class="tit">单册代码：</th>
				<td>
					<input id="item_no" name="queryData.item_no" class="nui-textbox" style="width:150px;" vtype="maxLength:50"/>
				</td>
				
				<th class="tit">单册名称：</th>
				<td>
					<input id="item_name" name="queryData.item_name" class="nui-textbox" style="width:150px;" vtype="maxLength:100"/>
				</td>	
				
			    <th  class="btn-wrap">
					<a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;
	            <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
				</th>	  
	        </tr>
	    </table>
	  </div>
    </div>
  </div>
  
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
             <a class="nui-button" iconCls="icon-ok" onclick="update">核销</a> 
             <input class="nui-button" text="导出待核销记录" iconCls="icon-download" onclick="excel"/>
             <a class="nui-button" iconCls="icon-upload" onclick="importGrant">批量核销导入</a>
             <a class="nui-button" iconCls="icon-collapse" onclick="viewlog">导入日志</a>
		     <!-- <a id="update" class="nui-button" iconCls="icon-edit" onclick="update">修改</a> 
		     <a class="nui-button" iconCls="icon-remove" onclick="remove">删除</a>-->
           </td>
        </tr>
      </table>
    </div>
 
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.dailydeal.itemDestroy.query_itemDestroy.biz.ext"
	  sizeList=[5,10,20,50,100]  pageSize="10">
	    <div property="columns" >
	     <!--<div field="DESTROY_TIME" headerAlign="center" align="center">核销日期</div>
	      <div field="ORGNAME" headerAlign="center" align="center">机构名称</div>
	      <div field="ITEM_NO" headerAlign="center" align="center">单册代码</div>
	      <div field="ITEM_NAME" headerAlign="center" align="center">单册名称</div>
	      <div field="DESTROYNUM" headerAlign="center" align="center">核销数量</div>
	      <div field="DESTROY_REASON" headerAlign="center" align="center" renderer="destroyType">核销原因</div>-->    
	      <div type="checkcolumn"></div>
	      <div field="ORGNAME" headerAlign="center" align="center">机构名称</div> 
	      <div field="ITEM_NO" headerAlign="center" align="center">单册代码</div>
	      <div field="NO" headerAlign="center" align="center">编号</div>
	      <div field="ITEM_NAME" headerAlign="center" align="center">单册名称</div>
	      <div field="UNIT" headerAlign="center" align="center">单位</div>
	      <div field="PRE_TAX_PRICE" headerAlign="center" align="center" width=120>税前单价</div>
	      <div field="TAX_PRICE" headerAlign="center" align="center" width=120>含税单价</div>
	      <div field="NUM" headerAlign="center" align="center">库存</div> 
	    </div>
	 </div>
  </div>
  
  <div id="importWindow" class="nui-window" title="导入excel" style="width:650px;" 
    showModal="true" allowResize="true" allowDrag="true">    
   <div title="批量核销导入" >
    	<form id="import_dict_form" action="com.gotop.xmzg.util.importExcelItenDestroy.flow" method="post" enctype="multipart/form-data">
	       <input class="nui-hidden" id="aa"  name="_eosFlowAction"  />
	       <input class="nui-hidden" id="bb"  name="excelFile"  />
	       <input class="nui-hidden" id="IMPORT_NAME"  name="map/IMPORT_NAME"  />
	       <input class="nui-hidden" id="IMPORT_TYPE"  name="map/IMPORT_TYPE"  />
	       <input class="nui-hidden" id="RUN_TYPE"  name="map/RUN_TYPE"  />
	       <input class="nui-hidden" id="MCODE"  name="map/MCODE"/>
	       <input class="nui-hidden" id="BT_ROW"  name="map/BT_ROW"/>
	       <input class="nui-hidden" id="START_ROW"  name="map/START_ROW"/>
	       
	       <input class="nui-hidden" id="ORGID"  name="map/ORGID"/>
        <table border="0" style="width:500px;height:100px;" align="center">
			<tr>
				<td colspan="2" align="center">
				<!-- <a  style="color:blue" href="#" onclick="templateDown()">导入模板下载</a>	
				<br> -->
				 <font style="color:red">导入成功后.在【导入日志】中查看导入情况</font>	
				 <br>
				<font style="color:red">
                                                                        只有数据全部无误时才会导入库表
                </font>	
				</td>		
			</tr>
			
			<tr>
				<td width="35%" align="right">核销日期:</td>
				<td><span>
				 <input id="destroy_time" name = "map/DESTROY_TIME" class="nui-monthpicker"  style="width:150px;" allowInput="true" format="yyyy/MM/dd"/> 
			     <!--<input id="assess_date" class="nui-datepicker" name="map/ASSESS_DATE"  allowInput="false" style="width:150px;" format="yyyyMMdd"/>-->
			</tr>
			
			<tr>
				<td width="35%" align="right">请选择您要导入的Excel文件:</td>
				<td><span id="uploadSpan"><input type="file" id="dict_file" name="empItemFile" size="60" style="width:250px;"></span></td>
			</tr>
			<tr align="center">
				<td colspan="2">
					<a class="nui-button" onclick="startUpload();">导入</a>&nbsp;
					<a class="nui-button" onclick="resetImport();">重置</a>&nbsp;
					<a class="nui-button" onclick="cancelImport();">取消</a>
				</td>
			</tr>
        </table>
        </form>
   </div>
</div>	
  
  <script type="text/javascript">
    nui.parse();
    var grid = nui.get("datagrid1");    
    
     var  s_OrgId="";
    var  s_OrgName="";
    
     //初始化c_orgid ,若当前登录的是单册管理员，则查询所有机构  ，若不是则当前登录人所在机构
    $.ajax({
		        url:"com.gotop.xmzg.report.reportQuery.judge_orgid.biz.ext",
		        type:'POST',
		        data:'',
		        cache:false,
		        async:false,
        		dataType:'json',
		        success:function(text){
		           //alert(text.c_orgid);
		          nui.get("c_orgid").setValue(text.c_orgid);
		          nui.get("ORGID").setValue(text.c_orgid);
		          
		          nui.get("queryData.orgid").setValue(text.c_orgid);
                  nui.get("queryData.orgid").setText(text.c_orgname);
                  s_OrgId = text.c_orgid;
                  s_OrgName = text.c_orgname;
		        }
		 });
		 
     var importWindow = nui.get("importWindow");
    var a;
    
    
     var str2 ="<%=str1 %>";
    	if(str2!=null && str2!=""){
    		nui.alert(str2, "系统提示", function(action){
    		            nui.hideMessageBox(a);  //隐藏遮罩层
						if(action == "ok" || action == "close"){
							/* CloseWindow("saveSuccess"); */
							grid.reload();
						}
					});
    	}
       var MCODE = "M002";  
       var json = nui.encode({MCODE:MCODE});
	   $.ajax({
	        url:"com.gotop.xmzg.util.Excel.queryConfigInfoByID.biz.ext",
	        type:'POST',
	        data:json,
	        cache:false,
	        contentType:'text/json',
	        success:function(text){
	          obj = nui.decode(text);
	          //import_dict_form.setData(obj);
	          //import_dict_form.setChanged(false);
	          //debugger;
	          nui.get("IMPORT_NAME").setValue(obj.map.IMPORT_NAME);
	          nui.get("IMPORT_TYPE").setValue(obj.map.IMPORT_TYPE);
	          nui.get("RUN_TYPE").setValue(obj.map.RUN_TYPE);
	          nui.get("MCODE").setValue(obj.map.MCODE);
	          nui.get("BT_ROW").setValue(obj.map.BT_ROW);   
	          nui.get("START_ROW").setValue(obj.map.START_ROW);    
	         
	        }
	      });
	      
	      
    var form = new nui.Form("#form1");
	var data = form.getData(true,true);
    
    //grid.load(data);
    
    //机构树回显
     function onButtonEdit(e) {
            var btnEdit = this;
            nui.open({
                <%--  url:"<%=request.getContextPath() %>/kpiCheckManage/ProOrgKpiManage/Prokpiorg_tree.jsp",  //初始未展开的机构树，加载快  --%>
                url:"<%=request.getContextPath() %>/report/tree/ReportOrg_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            
                        }
                    }
                }
            });            
             
        }     
    
    
    function search(){

       var form = new nui.Form("#form1");
            form.validate();
            if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
    
   function update(){
       var row = grid.getSelected();
       if(row!=null){
       
	       nui.open({
	          url:"<%=request.getContextPath() %>/dailydeal/itemDestroy/itemDestroy_update.jsp",
	          title:'编辑',
	          width:350,
	          height:400,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             
	             //方法1：后台查询一次，获取信息回显
	             /* var data = row;
	             iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
	             
	             //方法2：直接从页面获取，不用去后台获取
	               var data = {pageType:"edit",record:{map:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }else{
           nui.alert("请选中一条记录！");
       }
    }
    
    
    function selectionChanged(){
       var rows = grid.getSelecteds();
       if(rows.length>1){
           nui.get("update").disable();
       }else{
           nui.get("update").enable();
       }
    }
    
    
    
   
    function destroyType(e){
      	    return nui.getDictText("DESTROY_TYPE", e.row.DESTROY_REASON);
    }
    
    
    //重置
    function clean(){
	   //不能用form.reset(),否则会把隐藏域信息清空
	   nui.get("queryData.orgid").setValue(s_OrgId);
       nui.get("queryData.orgid").setText(s_OrgName);
       
       nui.get("item_no").setValue("");
       nui.get("item_name").setValue("");
     }
     
   
       //时间判断开始时间不能大于结束时间
      function comparedate(e){
      var startDate = nui.get("queryData.startDate").getFormValue();
      var endDate = nui.get("queryData.endDate").getFormValue();
      if(startDate!="")
	    startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
	  if(endDate!=""){
		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
        if(e.isValid){
          if(startDate>endDate){
            e.errorText="结束日期必须大于或等于开始日期";
            e.isValid=false;
          }else
          {
          e.errorText="";
          e.isValid=true;
          }
        }
      }
    } 
     
    //初始界面导入的按键
        function importGrant (){
        	resetImport();  
        	nui.get("destroy_time").setValue("<%=str_date%>");      
       		importWindow.show();
        
        }
        
	//重置
        function resetImport() {
			var html=document.getElementById('uploadSpan').innerHTML; 
			document.getElementById('uploadSpan').innerHTML=html; 
        }
    	//导入
        function startUpload() {
        
            var destroy_time = nui.get("destroy_time").getFormValue();
	        if(destroy_time==""){
	         nui.alert("请选择核销日期！");
             return false;
	        }
       
	        var form = $("#import_dict_form");
	        var excelFile = $("#dict_file").val();
	        
	        if (excelFile == "") {
				nui.alert("请选择文件！");
				return;
			}
			var reg = /.xls$/;
			if (!reg.test(excelFile))
			{
				nui.alert('请选择Excel格式(*.xls)文件！');
				return;
			}
			
			excelFile = excelFile.substr(excelFile.lastIndexOf("\\") + 1);
			//if(!confirm("是否确认导入？")){
			//		return false;
	       //  }
	         nui.alert("是否确定导入?", "系统提示", function(action){
					if(action == "close"){
						return false;
						
					}else{
					 nui.get("aa").setValue("importFile");
				     nui.get("bb").setValue(excelFile);
					 form.submit();
					 a= nui.loading("正在操作中,请稍等...","提示");
					}
		     });
			
            //form.action = "com.gotop.zkpz.excel.branch_import.flow?_eosFlowAction=importFile&excelFile=" + excelFile;		
			
        }
        
        //取消
         function cancelImport() {
    	   importWindow.hide();
        }
        
        function templateDown(){
	      var url="<%=request.getContextPath() %>/util/empRelieveGuard/templateDownload.jsp?filename=itemCatalog.xls&filename2=单册核销导入模板.xls";
	      window.location.replace(encodeURI(url));
	    }
     function viewlog()
     {
	       nui.open({
	          url:"<%=request.getContextPath() %>/util/empRelieveGuard/importResult_log.jsp",
	          title:'导入日志',
	          width:1000,
	          height:500,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             
	             //方法1：后台查询一次，获取信息回显
	             /* var data = row;
	             iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
	             
	             //方法2：直接从页面获取，不用去后台获取
	               var data = {MCODE:MCODE};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }
       
    //导出Excel
function excel(){
	      
	var form=new nui.Form("form1");
	//form.validate();
    //if (form.isValid() == false) return;
	var data=form.getData(true, true);
	var fileName="待核销记录";
	var queryPath="com.gotop.xmzg.dailydeal.itemDestroy.query_library";
	var columns=grid.getBottomColumns();
	columns=columns.clone();
	for(var i=0;i<columns.length;i++){
		var column=columns[i];
		if(!column.field){
			columns.removeAt(i);
		}else{
			var c={header:column.header,field:column.field };
			columns[i]=c;
		}
	}
	columns=nui.encode(columns);
	data=nui.encode(data);
     var url="<%=request.getContextPath()%>/util/exportExcel/exportExcel_destroy.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data;
	 window.location.replace(encodeURI(url));
	 
	 //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
     setMask();
} 

  //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)	
function setMask(){
	 
	 var a= nui.loading("正在操作中,请稍等...","提示"); //显示遮罩层
	 
	 var icount = setInterval(function(){  
	
		  if(document.attachEvent){   //IE浏览器
	
		 	if(document.readyState=='interactive'){

	              nui.hideMessageBox(a);  //隐藏遮罩层
	              clearTimeout(icount);
	        }
		 }else{ //谷歌浏览器
		 	if(document.readyState=='complete'){

	              nui.hideMessageBox(a);  //隐藏遮罩层
	              clearTimeout(icount);
	        }
		 } 
	 
	 }, 1); 
	 
} 
     
    
  </script>
</body>
</html>
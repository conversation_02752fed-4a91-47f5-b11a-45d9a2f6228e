<%@page import="com.eos.server.dict.DictManager"%>
<%@page import="java.io.File"%>
<%@page import="java.util.Enumeration"%>
<%@page import="java.text.SimpleDateFormat"%>
<%-- <%@page import="com.oreilly.servlet.multipart.DefaultFileRenamePolicy"%>--%>
 <%@page import="com.oreilly.servlet.MultipartRequest"%>  
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8" import="java.util.*"%>
    	
<%@include file="/coframe/tools/skins/common.jsp" %>
<% 	
	 HashMap resultMap = new HashMap();	
	 
	  
	//String uploadPath = "/fileUpload/upload";
	
	
	//获取当前时间，以便于建立当天的文件夹
	//	String nowTime="";
	//	Date date=new Date();  
	//    SimpleDateFormat formatter=new SimpleDateFormat("yyyyMMdd");  
	//    nowTime=formatter.format(date); 
	    
	    
	    
	//String saveDirectory = session.getServletContext().getRealPath("/fileUpload/upload");
	String saveDirectory = DictManager.getDictName("NOTICE_UPLOAD_DIR","02");
	
	//原先双机模式下，数据字典NOTICE_UPLOAD_DIR的内容为：	/fileshare/notice_upload
	//现在临时使用单机模式，故将数据字典NOTICE_UPLOAD_DIR的内容修改为项目所在路径/fileUpload/upload
	
	
	//saveDirectory=saveDirectory+"/"+nowTime;
	
	String newImgPath = "";  
	
	File file=new File(saveDirectory);
	if(!file.exists()&&!file.isDirectory())
	{
	  file.mkdirs();//创建目录    file.mkdir();//创建文件夹

	}
	
	System.out.println("saveDirectory               "+saveDirectory);
	
	 MultipartRequest multi = new MultipartRequest(request,saveDirectory,
		100 * 1024 * 1024, "UTF-8");
	 
	//如果有上传文件, 则保存到数据内
	Enumeration files = multi.getFileNames();
	while (files.hasMoreElements()) {
		String name = (String)files.nextElement();

		
		File f = multi.getFile(name);
		
		//System.out.println("f               "+f.toString());
		
		//给文件定义唯一标识名
		String	efilename = new Date().getTime()+"";  
		
		if(f!=null){
		
		    //imgPath为原文件名  
		    String imgPath = f.getName(); 
                            
            //文件后缀  
            String extention= imgPath.substring(imgPath.indexOf("."));
                   
              
            //新的文件名(日期+后缀)  
            newImgPath = efilename + extention;  
              
            File newf = new File(saveDirectory + "/" + newImgPath);  
            //重命名
            f.renameTo(newf);  
//System.out.println("newf               "+newf.toString());
		
			//读取上传后的项目文件, 导入保存到数据中    (真名)
			String fileName = multi.getFilesystemName(name);
			resultMap.put("fileName",fileName);
			
			//文件唯一标识名
			//resultMap.put("uniquefileName",nowTime+"/"+newImgPath);
			resultMap.put("uniquefileName",newImgPath);
			
			//response.getWriter().write(fileName +"("+new Date()+")");    //可以返回一个JSON字符串, 在客户端做更多处理		
			String title = request.getParameter("TITLE");
			resultMap.put("title",title);
			
			String content = request.getParameter("CONTENT");
			resultMap.put("content",content);
			
			String org_range = request.getParameter("ORG_RANGE");
			resultMap.put("org_range",org_range);
			
			String remark = request.getParameter("REMARK");
			resultMap.put("remark",remark);

			

			resultMap.put("succflag","1");			
		}else
		{
	    	resultMap.put("fileName","");
	    	resultMap.put("uniquefileName","");
	    	
		    String title = request.getParameter("TITLE");
			resultMap.put("title",title);
			
			String content = request.getParameter("CONTENT");
			resultMap.put("content",content);
			
			String org_range = request.getParameter("ORG_RANGE");
			resultMap.put("org_range",org_range);
			
            String remark = request.getParameter("REMARK");
			resultMap.put("remark",remark);
			
			resultMap.put("succflag","1");
		}
	} 
%>
<html xmlns="http://www.w3.org/1999/xhtml">

<head>	  
	  <script type="text/javascript" > 
	 	var succflag='<%=resultMap.get("succflag") %>';
	 	//var title='<%=resultMap.get("title") %>';
	 	//var content='<%=resultMap.get("content") %>';
	 	//var org_range='<%=resultMap.get("org_range") %>';
	 	var fileName='<%=resultMap.get("fileName") %>';
	 	var uniqueFileName='<%=resultMap.get("uniquefileName") %>';
	 	//var remark='<%=resultMap.get("remark") %>';
	 	
	 	
	 	

      	/* $(function(){ */
  			if(succflag=="1"){
  			debugger;
  			var ddata=<%=request.getParameter("data") %>;
  			ddata.map.FPATH=fileName;
  			ddata.map.UNIQUEFPATH=uniqueFileName;
  			var json=nui.encode(ddata);
  			//alert("文件上传成功");
  			/* var data={map:{}};
  			data.map.TITLE=title;
  			data.map.CONTENT=content;
  			data.map.ORG_RANGE=org_range;
  			data.map.FPATH=fileName;
  			data.map.UNIQUEFPATH=uniqueFileName;
  			data.map.REMARK=remark; */

  			//var json = nui.encode(data);
            $.ajax({
                url:"com.gotop.lnjxkh.notice.notice.add_notice.biz.ext",
                type:'POST',
                data:json,
                cache:false,
                contentType:'text/json',
                success:function(text){
                   var returnJson = nui.decode(text);
					if(returnJson.exception == null){
					
					if(returnJson.iRtn == 1){
                          CloseWindow("saveSuccess");
                        }else{
                          CloseWindow("saveFailed");
                        } 
					//CloseWindow("saveSuccess");
					 //nui.alert("提交成功11");
						/* nui.alert("提交成功", "系统提示", function(action){
							if(action == "ok" || action == "close"){
								CloseWindow("saveSuccess");
							}
						}); */
					}else{
					CloseWindow("saveFailed");
						/* nui.alert("提交失败", "系统提示", function(action){
							if(action == "ok" || action == "close"){
							
								CloseWindow("saveFailed");
							}
						}); */
					}
				}
                   /* var returnJson = nui.decode(text);
                   if(returnJson.exception == null){
                        alert("提交成功");
                        //window.history.go(-2) ;
                     }else{
                         alert("提交失败");
                         window.history.go(-2) ;
                        }
                   } */
             });

  			//setTimeout(self.location=document.referrer,30000);

  			}
       /*  }); */

     function CloseWindow(action){
     
       if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
       
	  </script>
</head>
<body  >  

</body>
</html>	  
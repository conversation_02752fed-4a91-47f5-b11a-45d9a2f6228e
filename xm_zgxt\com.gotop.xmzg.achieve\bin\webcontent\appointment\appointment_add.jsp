<%@page pageEncoding="UTF-8"%>	
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2019-06-04 14:51:16
  - Description:
-->
<head>
	<title>业绩预约录入</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%@include file="/achieve/init.jsp"%>
	<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
</head>
<body>
  <div id="prameter" style="padding-top:5px;"> 
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table" align="center"> 
    <tbody>
     <tr> 
      <input id="TAA_ID" name = "TAA_ID" class="mini-hidden"/>
      <input id="queryData.TAA_STATUS" name = "queryData.TAA_STATUS" class="mini-hidden"/>
     
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>客户经理：</th> 
      <td><input id="TAA_EMP" name = "TAA_EMP"  class="nui-buttonedit"  allowInput="false" onbuttonclick="selectEmp" style="width:150px;" required = "true"  emptyText="请选择..."/></td> 
     </tr>
     
      <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>预约机构：</th> 
      <td><input id="TAA_ORG" name = "TAA_ORG"  class="nui-combobox"  allowInput="false" style="width:150px;" required = "true" emptyText="请选择..."
       dataField="datas" textField="ORGNAME" valueField="ORGCODE" /></td> 
      </tr>
     
  <!--<tr> 
      <th class="nui-form-label"><font class="b_red">*</font>指标明细：</th> 
      <td><input id="TID_CODE" name="TID_CODE" class="nui-combobox"  textField="TEXT" valueField="ID" dataField="list" style="width:300px;" required = "true"/></td> 
     </tr>-->
     
     <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>业务条线：</label></th>
        <td colspan="3" >  
        	<div id="TIP_CODE" name="TIP_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TIP_NAME" valueField="TIP_CODE" 
		    	url="com.gotop.xmzg.achieve.appointment.tip_get.biz.ext" multiSelect="false" allowInput="false" showNullItem="false" emptyText="请选择..."
		    	onvaluechanged="onTipChanged" required="true">     
			    <div property="columns">
			        <div header="业务条线代码" field="TIP_CODE" width="60"></div>
			        <div header="业务条线名称" field="TIP_NAME" width="120"></div>
			    </div>
		    </div>
		</div>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>指标：</label></th>
        <td colspan="3" >  
        	<div id="TI_CODE" name="TI_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TI_NAME" valueField="TI_CODE" 
		    	multiSelect="false" allowInput="false" showNullItem="false" emptyText="请先选择业务条线..." required="true"
		    	onvaluechanged="onTiChanged">     
			    <div property="columns">
			        <div header="指标代码" field="TI_CODE" width="60"></div>
			        <div header="指标名称" field="TI_NAME" width="120"></div>
			    </div>
		    </div>
		</div>
        </td> 
      </tr>
      <tr id="tidTr">
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>指标细项：</label></th>
        <td colspan="3" >  
        	<div id="TID_CODE" name="TID_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TID_NAME" valueField="TID_CODE" 
		    	multiSelect="false" allowInput="false" showNullItem="false" emptyText="请先选择指标..." required="true">     
			    <div property="columns">
			        <div header="指标细项代码" field="TID_CODE" width="60"></div>
			        <div header="指标细项名称" field="TID_NAME" width="120"></div>
			    </div>
		    </div>
		</div>
        </td> 
      </tr>
    
     <tr> 
      <th class="nui-form-label">产品编号：</th> 
      <td><input class="nui-textbox" id="TAA_BUSI_NO" name="TAA_BUSI_NO" style="width:150px;"/></td> 
     </tr>
      <tr> 
      <th class="nui-form-label">客户类型：</th> 
      <td><input class="nui-textbox" id="TAA_CUST_TYPE" name="TAA_CUST_TYPE" style="width:150px;"/></td> 
     </tr>
      <tr> 
      <th class="nui-form-label">客户名称：</th> 
      <td><input class="nui-textbox" id="TAA_CUST_NAME" name="TAA_CUST_NAME" style="width:150px;"/></td> 
     </tr>
       <tr> 
      <th class="nui-form-label">证件类型：</th> 
      <td><input class="nui-dictcombobox" valueField="dictName" textField="dictName" id="TAA_CARD_TYPE" name="TAA_CARD_TYPE" dictTypeId="COF_CARDTYPE" style="width:150px;"/></td> 
     </tr>
     <tr> 
      <th class="nui-form-label">证件号码：</th> 
      <td><input class="nui-textbox" id="TAA_CARD_NO" name="TAA_CARD_NO" style="width:150px;"/></td> 
     </tr>
      <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>预约起始日：</th> 
      <td><input class="nui-datepicker" id="TAA_START_DATE" name="TAA_START_DATE" format="yyyy-MM-dd" valueformat="yyyyMMdd" style="width:150px;" required = "true" renderer="setdate" onvalidation="comparedate"/></td> 
     </tr>
       <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>预约结束日：</th> 
      <td><input class="nui-datepicker" id="TAA_END_DATE" name="TAA_END_DATE"  format="yyyy-MM-dd" valueformat="yyyyMMdd" style="width:150px;" required = "true" renderer="setdate" onvalidation="comparedate"/> </td> 
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>预约业绩：</th> 
      <td><input class="nui-textbox" id="TAA_NUM" name="TAA_NUM" style="width:150px;" vtype="range:0.01,9999999999.99"  required = "true"/></td> 
     </tr>
      <tr> 
      <!--<th class="nui-form-label">审核人员：</th> 
      <td><input   class="nui-buttonedit"  allowInput="false" onbuttonclick="selectEmp" id="TAA_APP_EMP" name="TAA_APP_EMP" style="width:150px;"/></td> 
     </tr>-->
    </tbody>
   </table> 
   <div class="nui-toolbar" style="padding:0px;" borderstyle="border:0;"> 
    <table width="100%"> 
     <tbody>
      <tr> 
       	<td style="text-align:center;"> 
	       	<a class="nui-button" iconcls="icon-search" onclick="seve">提交审核</a> 
	       	<span style="display:inline-block;width:25px;"></span> 
	       	<a class="nui-button" iconcls="icon-save" onclick="save">保存草稿</a> 
	       	<span style="display:inline-block;width:25px;"></span> 
	       	<a class="nui-button" iconcls="icon-cancel" onclick="onCancel">取消</a>
       	</td> 
      </tr> 
     </tbody>
    </table> 
   </div> 
  </div>

  <script type="text/javascript">
  	nui.parse();
  	var org = nui.get("TAA_ORG");
  	org.setEnabled(false);
  	var emp = nui.get("TAA_EMP");
  	
  	  $.ajax({
		       url: "com.gotop.xmzg.achieve.appointment.appointment_qry_auth.biz.ext",
                        type: "post",
                        success: function (text) {
                     	   var code = text.code;
                     	   var msg = text.msg;
                     	   var obj = text.obj;
                     	   console.log(code);
                     	   console.log(msg);
                     	   console.log(obj);
                     	   if(code == "2"){
                     	    var emp = nui.get("TAA_EMP");
							emp.setEnabled(false);
							emp.setValue(obj.emp_code);
							emp.setText(obj.EMPNAME);
							var org = nui.get("TAA_ORG");
							org.setEnabled(false);
							org.setValue(obj.ORGCODE);
							org.setText(obj.ORGNAME);
						   }
		      		}
		    }); 
   		
  	function seve(){
  		var form = new nui.Form("#prameter");
		form.validate();
		if (form.isValid() == false) return;
		var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
       
		//提交数据
        nui.ajax({
       		url: "com.gotop.xmzg.achieve.appointment.appointment_add.biz.ext",
           	type: "post",
           	data: nui.encode({"obj":form.getData(true,true)}),
           	contentType:'text/json',
           	success: function (text) {
        	   nui.hideMessageBox(load);  //隐藏遮罩层
        	   var code = text.code;
        	   var msg = text.msg;
        	   if(code != "1"){
        		  	nui.alert(msg, "系统提示");
	           }else{
          			nui.alert(msg, "系统提示", function(action){
						if(action == "ok"){
							CloseWindow("saveSuccess");
						}
					});
	          }
           }
       });
  	}
  	
  	function save(){
  		var form = new nui.Form("#prameter");
		form.validate();
		if (form.isValid() == false) return;
		var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
       
		//提交数据
        nui.ajax({
       		url: "com.gotop.xmzg.achieve.appointment.appointment_save.biz.ext",
           	type: "post",
           	data: nui.encode({"obj":form.getData(true,true)}),
           	contentType:'text/json',
           	success: function (text) {
        	   nui.hideMessageBox(load);  //隐藏遮罩层
        	   var code = text.code;
        	   var msg = text.msg;
        	   if(code != "1"){
        		  	nui.alert(msg, "系统提示");
	           }else{
          			nui.alert(msg, "系统提示", function(action){
						if(action == "ok"){
							CloseWindow("saveSuccess");
						}
					});
	          }
           }
       });
  	}
			var type ='3';
  		 function selectEmp(e){
        	var ele = e.sender.id;
    		var emp = nui.get(ele);
            nui.open({
                url:"<%=request.getContextPath()%>/achieve/acc/accEmptree.jsp?type="+type,
                title: "选择人员",
                width: 600,
                height: 400,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = emp.getValue();
                	//frame.contentWindow.setData(data);
                	frame.contentWindow.setTreeCheck(data.value);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            emp.setValue(data.ORGID);
                            emp.setText(data.ORGNAME);
                            
					              var url = "com.gotop.xmzg.achieve.appointment.org_get.biz.ext?code="+data.ORGID;
					                //获取机构
					              $.ajax({
					             	  url: url,
					          	 	  type:'POST',						
					                  success: function (text1){
					                  	console.log(text1);
					              		var returnJson = nui.decode(text1);
					              		var result = returnJson.datas;
					              		console.log(result);
					              		org.setValue(result[0].ORGCODE);
					              		org.setText(result[0].ORGNAME);
					            	   }
					  				});
         					 
                        }
                    }

                }
            });
    	}
    	
    	//机构树回显
     function OrgonButtonEdit(e){
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/files/archiveList/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            //将值机构id变成机构code
                            var data={orgId:data.ID};
					        var json = nui.encode(data);
					        var URL="com.gotop.xmzg.files.fileLedger.getOrgcode.biz.ext";
					        $.ajax({
					        	url: URL,
					            type: 'POST',
					            data: json,
					            async:false,
					            cache: false,
					            contentType:'text/json',
					            success: function (text){
					                var returnJson = nui.decode(text);
					                var result = returnJson.resultList;
					                btnEdit.setValue(result[0].ORGCODE);
							    }
					  		}); 
                        }
                    }
                }
            });            
             
        } 
        
      //时间判断开始时间不能大于结束时间
      function comparedate(e){
      var startDate = nui.get("TAA_START_DATE").getFormValue();
      var endDate = nui.get("TAA_END_DATE").getFormValue();
      if(startDate!="")
	    startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
	  if(endDate!=""){
		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
        if(e.isValid){
          if(startDate>endDate){
            e.errorText="结束日期必须大于或等于开始日期";
            e.isValid=false;
          }else
          {
          e.errorText="";
          e.isValid=true;
          }
        }
      }
    } 
    
     function onProductChanged(e){
    	productChanged(e.value);
    }
    function productChanged(val) {
		 if(val == 1){
		 	nui.get("TC_PRODUCTNAME").setRequired(true);
			nui.get("TC_PRODUCT_TABLE").setRequired(true);
			nui.get("TC_PRODUCT_FIELD").setRequired(true);
			nui.get("TC_ORGFIELD").setRequired(true);
		 }else{
		 	nui.get("TC_PRODUCTNAME").setRequired(false);
			nui.get("TC_PRODUCT_TABLE").setRequired(false);
			nui.get("TC_PRODUCT_FIELD").setRequired(false);
			nui.get("TC_ORGFIELD").setRequired(false);
		 }
    }
    
    function onTypeChanged(e) {
		 typeChanged(e.value);
    }
    
    function typeChanged(val) {
		 if(val == 2){
		 	$("#tidTr").show();
		 }else{
		 	$("#tidTr").hide();
		 }
    }
    
    var tip = nui.get("TIP_CODE");
    var ti = nui.get("TI_CODE");
    var tid = nui.get("TID_CODE");
    
    function onTipChanged(e,val){
    	console.log("tipchange");
        ti.setValue("");
        tid.setValue("");
        if(!val) val = tip.getValue();
        var url = "com.gotop.xmzg.achieve.appointment.ti_get.biz.ext?code=" + val;
        ti.setUrl(url);
    }
    
    
   /*  function onTiChanged(e,val){
        tid.setValue("");
        if(!val) val = ti.getValue();
        var url = "com.gotop.xmzg.achieve.configClaPro.tid_get.biz.ext?code=" + val;
        tid.setUrl(url);
    }  */
    
       function onTiChanged(e,val){
        tid.setValue("");
        if(!val) val = ti.getValue();
        var url = "com.gotop.xmzg.achieve.appointment.tid_get.biz.ext?code=" + val;
        tid.setUrl(url);
    } 
    
    
    
    
  </script>
</body>
</html>
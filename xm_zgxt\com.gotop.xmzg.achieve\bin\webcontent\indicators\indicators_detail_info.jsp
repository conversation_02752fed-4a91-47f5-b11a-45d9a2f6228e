<%@page pageEncoding="UTF-8"%>	
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2019-06-04 14:51:16
  - Description:
-->
<head>
	<title>指标明细</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%@include file="/achieve/init.jsp"%>
	<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
</head>
<body>
  <div id="prameter" style="padding-top:5px;"> 
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table" align="center"> 
    <tbody>
     <tr> 
      <th class="nui-form-label" style="width:25%">条线代码：</th> 
      <td style="width:25%">
      	<input class="nui-textbox" id="tip_code" name="tip_code" style="width:200px;" />
      </td>
      <th class="nui-form-label" style="width:25%">条线名称：</th> 
      <td style="width:25%"><input class="nui-textbox" id="tip_name" name="tip_name" style="width:200px;" /></td> 
     </tr>
     <tr> 
      <th class="nui-form-label">指标代码：</th> 
      <td>
      	<input class="nui-textbox" id="ti_code" name="ti_code" style="width:200px;" />
      </td>
      <th class="nui-form-label">指标名称：</th> 
      <td><input class="nui-textbox" id="ti_name" name="ti_name" style="width:200px;" /></td> 
     </tr>
     <tr> 
      <th class="nui-form-label" style="width:25%">积分系数：</th> 
      <td>
      	<input class="nui-textbox" id="ti_integral" name="ti_integral" style="width:200px;" />
      </td>
      <th class="nui-form-label" style="width:25%">排序：</th> 
      <td><input class="nui-textbox" id="ti_sorting" name="ti_sorting" style="width:200px;" /></td> 
     </tr>
     <tr> 
      <th class="nui-form-label" style="width:25%">指标说明：</th> 
      <td>
      	<input class="nui-textbox" id="ti_remark" name="ti_remark" style="width:200px;" />
      </td>
      <th class="nui-form-label" style="width:25%">是否启用：</th> 
      <td><input class="nui-textbox" id="ti_start" name="ti_start" style="width:200px;" /></td> 
     </tr>
    </tbody>
   </table>
   </div>
   <div class="nui-fit" style="padding-top:5px;">
	 <div id="bw_grid" class="nui-datagrid"
			 style="height: 100%;"
			 idField="id" 
			 totalField="page.count"  
			 showPageInfo="true"
			 multiSelect="true"  
			 pageSize="20"
			 sizeList=[5,10,20,50,100]
	         dataField="list"
			 url="com.gotop.xmzg.achieve.indicators.indicators_detail_info.biz.ext"
			 >
	    <div property="columns" >
		  	<div field="TID_CODE" headerAlign="center" align="center" allowSort="true" >指标细项代码</div>
			<div field="TID_NAME" headerAlign="center" align="center" allowSort="true" >指标细项名称</div>
			<div field="TID_REMARK" headerAlign="center" align="center" allowSort="true" >指标细项说明</div>
			<div field="TID_RESULTS" headerAlign="center" align="center" allowSort="true" renderer="tid_results">是否可预约业绩</div>
			<div field="TID_RESULTS_AUTH" headerAlign="center" align="center" allowSort="true" renderer="tid_results_auth">预约是否审核</div>
			<div field="TID_PROCESSMODE" headerAlign="center" align="center" allowSort="true" renderer="tid_processmode">数据处理机制</div>
			<div field="TID_DATASOURCE" headerAlign="center" align="center" allowSort="true" renderer="tid_datasource">数据来源</div>
			<div field="TID_UNIT" headerAlign="center" align="center" allowSort="true" renderer="tid_unit">指标单位</div>
			<div field="TID_BASE" headerAlign="center" align="center" allowSort="true" >基数</div>
			<div field="TID_PROPORTION" headerAlign="center" align="center" allowSort="true" >积分标准</div>
			<div field="TID_DATATYPE" headerAlign="center" align="center" allowSort="true" renderer="tid_datatype">数据类型</div>
			<div field="TID_TIMEFRAME" headerAlign="center" align="center" allowSort="true" renderer="tid_timeframe">时段标识</div>
			<div field="TID_FREQUENCY" headerAlign="center" align="center" allowSort="true" renderer="tid_frequency">计算频率</div>
			<div field="TID_CURRENCY" headerAlign="center" align="center" allowSort="true" renderer="tid_currency">币种</div>
			<div field="TID_CAL" headerAlign="center" align="center" allowSort="true" renderer="tid_cal">统计口径</div>
			<div field="TID_TYPE" headerAlign="center" align="center" allowSort="true" renderer="tid_type">指标类型</div>
			<div field="TID_PROTECTION" headerAlign="center" align="center" allowSort="true" renderer="tid_protection">是否设置保护期</div>
			<div field="TID_PROTECTION_DATE" headerAlign="center" align="center" allowSort="true" >保护期天数 </div>
			<div field="TID_INTEGRAL_CALCUL" headerAlign="center" align="center" allowSort="true" renderer="tid_integral_calcul">是否参与积分计算</div>
			<div field="TID_SORTING" headerAlign="center" align="center" allowSort="true" >排序</div>
			<div field="TID_START" headerAlign="center" align="center" allowSort="true" renderer="tid_start">是否启用</div>
	    </div>
	 </div>
  </div>
   <div class="nui-toolbar" style="padding:0px;" borderstyle="border:0;"> 
    <table width="100%"> 
     <tbody>
      <tr> 
       	<td style="text-align:center;"> 
	       	<a class="nui-button" iconcls="icon-cancel" onclick="onCancel">关闭</a>
       	</td> 
      </tr> 
     </tbody>
    </table> 
   </div> 
  <script type="text/javascript">
  	nui.parse();
  	var grid = nui.get("bw_grid");
  	var form = new mini.Form("prameter");
  	init();
  	function init(){
  		var fields = form.getFields();                
        for (var i = 0, l = fields.length; i < l; i++) {
            var c = fields[i];
            if (c.setReadOnly) c.setReadOnly(true);     //只读
            if (c.setIsValid) c.setIsValid(true);      //去除错误提示
            if (c.addCls) c.addCls("asLabel");          //增加asLabel外观
        }
  	}
  	
  	
	//查询
    function search() {
      	grid.load({"obj":form.getData()});
	}
  	
  	
  	function setData(data){
  		//跨页面传递的数据对象，克隆后才可以安全使用
        data = nui.clone(data);
        data = lowerJson(data);
        form.setData(data);
        search();
  	}
  	//是否可预约业绩
    function tid_results(e){
		var tid_results = e.record.TID_RESULTS;
		return nui.getDictText("JF_RESULTS",tid_results);
	}
  	//预约是否审核
    function tid_results_auth(e){
		var tid_results_auth = e.record.TID_RESULTS_AUTH;
		return nui.getDictText("JF_RESULTS_AUTH",tid_results_auth);
	}
  	//数据处理机制
    function tid_processmode(e){
		var tid_processmode = e.record.TID_PROCESSMODE;
		return nui.getDictText("JF_PROCESSMODE",tid_processmode);
	}
  	//数据来源
    function tid_datasource(e){
		var tid_datasource = e.record.TID_DATASOURCE;
		return nui.getDictText("JF_DATASOURCE",tid_datasource);
	}
  	//指标单位
    function tid_unit(e){
		var tid_unit = e.record.TID_UNIT;
		return nui.getDictText("JF_UINT",tid_unit);
	}
  	//数据类型
    function tid_datatype(e){
		var tid_datatype = e.record.TID_DATATYPE;
		return nui.getDictText("JF_DATATYPE",tid_datatype);
	}
  	//时段标识
    function tid_timeframe(e){
		var tid_timeframe = e.record.TID_TIMEFRAME;
		return nui.getDictText("JF_TIMEFRAME",tid_timeframe);
	}
  	//计算频率
    function tid_frequency(e){
		var tid_frequency = e.record.TID_FREQUENCY;
		return nui.getDictText("JF_FREQUENCY",tid_frequency);
	}
  	//币种
    function tid_currency(e){
		var tid_currency = e.record.TID_CURRENCY;
		return nui.getDictText("JF_CURRENCY",tid_currency);
	}
  	//统计口径
    function tid_cal(e){
		var tid_cal = e.record.TID_CAL;
		return nui.getDictText("JF_CAL",tid_cal);
	}
  	//统计口径
    function tid_type(e){
		var tid_type = e.record.TID_TYPE;
		return nui.getDictText("JF_IND_TYPE",tid_type);
	}
  	//是否设置保护期 
    function tid_protection(e){
		var tid_protection = e.record.TID_PROTECTION;
		return nui.getDictText("JF_YES_NO",tid_protection);
	}
  	//是否参与积分计算
    function tid_integral_calcul(e){
		var tid_integral_calcul = e.record.TID_INTEGRAL_CALCUL;
		return nui.getDictText("JF_YES_NO",tid_integral_calcul);
	}
  	//状态
    function tid_start(e){
		var tid_start = e.record.TID_START;
		return nui.getDictText("JF_STATE",tid_start);
	}
  </script>
</body>
</html>
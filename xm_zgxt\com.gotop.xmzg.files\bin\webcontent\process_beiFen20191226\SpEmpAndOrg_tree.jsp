<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title></title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>     
    <link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/coframe/tools/icons/icon.css"/>
    <style type="text/css">
    html,body
    {
        padding:0;
        margin:0;
        border:0;     
        width:100%;
        height:100%;
        overflow:hidden;   
    }
    </style>
</head>
<% 
	String ACTIVITYDEFID = request.getParameter("ACTIVITYDEFID");
	String PROCESS_NAME = request.getParameter("PROCESS_NAME");
	String APPLY_ORGID = request.getParameter("APPLY_ORGID");
	String PROCESSINSTID = request.getParameter("PROCESSINSTID");
	
 %>
<body>
    <div class="nui-toolbar" style="text-align:center;line-height:30px;" 
        borderStyle="border-left:0;border-top:0;border-right:0;">
          <label >名称：</label>
          <input id="key" class="nui-textbox" style="width:150px;" onenter="onKeyEnter"/>
          <a class="nui-button" style="width:60px;" onclick="search()">查询</a>
    </div>
    
      <div id="form1">
       <input id="ACTIVITYDEFID" class="nui-hidden" name="map.ACTIVITYDEFID" value="<%=ACTIVITYDEFID %>"/>
		<input id="PROCESS_NAME" class="nui-hidden" name="map.PROCESS_NAME"  value="<%=PROCESS_NAME %>" />
		<input  id="APPLY_ORGID"  class="nui-hidden" name="map.APPLY_ORGID"   value="<%=APPLY_ORGID %>" />
      </div>
    
    <div class="nui-fit">
    <!-- 没有机构角色配置的时候 -->
        <!-- <ul id="dataTree" class="nui-tree" style="width:98%;height:98%;padding:5px;background:#fafafa;margin-top:5px;" 
		    showTreeIcon="true" dataField="dataList" textField="text" idField="id" parentField="pid"
		     resultAsTree="false" showCheckBox="true"
		     onbeforeload="onBeforeTreeLoad"
		    checkRecursive="true" 
		    expandOnLoad="0"
		    onbeforenodeselect="beforenodeselect" 
		    allowSelect="true" enableHotTrack="true"
		    url="com.gotop.xmzg.files.process.selectEmpOrgZtree.biz.ext"
		    > 
		</ul> -->
		
		<!-- 根据机构角色配置表来 -->
		<ul id="dataTree" class="nui-tree" style="width:98%;height:98%;padding:5px;background:#fafafa;margin-top:5px;" 
		    showTreeIcon="true" dataField="dataList" textField="text" idField="id" parentField="pid"
		     resultAsTree="false" showCheckBox="true"
		     onbeforeload="onBeforeTreeLoad"
		    checkRecursive="true" 
		    expandOnLoad="0"
		    onbeforenodeselect="beforenodeselect" 
		    allowSelect="true" enableHotTrack="true"
		    url="com.gotop.xmzg.files.process.selectEmpOrgRoleZtree.biz.ext?ACTIVITYDEFID=<%=ACTIVITYDEFID %>&PROCESS_NAME=<%=PROCESS_NAME %>&APPLY_ORGID=<%=APPLY_ORGID %>&PROCESSINSTID=<%=PROCESSINSTID %>"
		    > 
		</ul>
    </div>                
    <div class="nui-toolbar" style="text-align:center;padding-top:8px;padding-bottom:8px;" 
        borderStyle="border-left:0;border-bottom:0;border-right:0;">
        <a class="nui-button" style="width:60px;" onclick="onOk()">确定</a>
        <span style="display:inline-block;width:25px;"></span>
        <a class="nui-button" style="width:60px;" onclick="onCancel()">取消</a>
    </div>

</body>
<script type="text/javascript">
    nui.parse();
	
	var tree = nui.get("dataTree");
	
	
	/* var form = new nui.Form("#form1");
       var data = form.getData(true,true);
       tree.setUrl=""
       tree.load(data); */

    //tree.expandLevel(0); //展开第一级  就是展示厦门分行及下的
	
	 
	/**
	 * 新增界面获取此iframe数据的方法
	 * return list 获取Check选中的多个节点
	 */
	function GetData() {

    	return tree.getCheckedNodes();         
    }
    
    function setTreeCheck(idStr) {
    	tree.setValue(idStr);
    } 
    
    //组织机构树的加载事件
 	    function onBeforeTreeLoad(e) {
 	    
		e.params.nodeId = e.node.realId; 
    } 
    
    function beforenodeselect(e) {

    //组织机构不能选 
    if (e.node.nodeType=="OrgOrganization") {
        e.cancel = true;
    }
}
    
    function search() {
        var key = nui.get("key").getValue();
        if(key == ""){
            tree.clearFilter();
        }else{
            key = key.toLowerCase();
            tree.filter(function (node) {
                var text = node.text ? node.text.toLowerCase() : "";
                if (text.indexOf(key) != -1) {
                    return true;
                }
            });
        }
    }
 
    function onKeyEnter(e) {
        search();
    }
    
    function onNodeDblClick(e) {
        onOk();
    }
    
    function CloseWindow(action) {
        if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
        else window.close();
    }

    function onOk() {

        CloseWindow("ok");        
    }
    function onCancel() {
        CloseWindow("cancel");
    }

    
</script>
</html>

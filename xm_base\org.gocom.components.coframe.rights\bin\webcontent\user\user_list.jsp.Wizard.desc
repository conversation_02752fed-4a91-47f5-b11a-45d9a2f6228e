<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wizards><wizard id="4914d690-de3b-454c-b3ab-2f2400c09373"><attribute name="rowLength" type="Integer" value="2"/><attribute name="isPagination" type="Boolean" value="true"/><attribute name="action" value=""/><attribute name="entityNameLowerCase" value="cappartyauth"/><attribute name="title" value=""/><attribute name="rel_tables" type="array"/><attribute name="target" value="_self"/><attribute name="currentRow" type="Integer" value="1"/><attribute name="criteriaName" value="criteria"/><attribute name="entityName" value="CapPartyauth"/><attribute name="pageLength" type="Integer" value="10"/><entityTable name="orderEntityTable"/><attribute name="isPageCount" type="Boolean" value="true"/><attribute name="PK" type="array"><array value="roleId"/><array value="roleType"/><array value="partyId"/><array value="partyType"/></attribute><attribute name="checkType" value="blur"/><entityTable name="entityTable"><field><attribute name="displayModel" value="editModel"/><attribute name="configOperator" value="="/><attribute name="component_type" value="queryform"/><attribute name="fieldColSpan" type="Integer" value="1"/><attribute name="xpath" value="criteria/_expr[1]/"/><attribute name="config_size" type="Integer" value="0"/><attribute name="fieldName" value="partyId"/><attribute name="fieldDataType" value="String"/><attribute name="entityName" value="CapPartyauth"/><attribute name="criteriaName" value="criteria"/><attribute name="fieldDisplayName;" value="参与者ID"/><attribute name="configFieldName" value="partyId"/><tagElement displayName="参与者ID" name="configControlType" showAllAttr="false" tagName="h:text"><attribute name="value" value=""/><attribute name="readonly" value=""/><attribute name="styleClass" value=""/><attribute name="tabindex" value=""/><attribute name="validateAttr" value=""/><attribute name="maxlength" value=""/><attribute name="title" value=""/><attribute name="disabled" value=""/><attribute name="property" value="criteria/_expr[1]/partyId"/><attribute name="style" value=""/><attribute name="extAttr" value=""/><attribute name="size" value=""/></tagElement><attribute name="configDataType" value="String"/><attribute name="fieldControlType" value="h:text"/><attribute name="isCustomProperty" type="Boolean" value="false"/><attribute name="isEdit" type="Boolean" value="false"/></field></entityTable><attribute name="pageFlow" value=""/><attribute name="formWidth" value="100%"/><attribute name="isNoRepeat" type="Boolean" value="false"/><attribute name="selectedEntity" value="org.gocom.components.coframe.rights.dataset.CapPartyauth"/><attribute name="formName" value="form1"/></wizard><wizard id="3e609c97-536b-49d0-927b-865c07ce9ac6"><attribute name="rowLength" type="Integer" value="2"/><attribute name="isPagination" type="Boolean" value="true"/><attribute name="action" value=""/><attribute name="entityNameLowerCase" value="capuser"/><attribute name="title" value=""/><attribute name="rel_tables" type="array"/><attribute name="target" value="_self"/><attribute name="currentRow" type="Integer" value="1"/><attribute name="criteriaName" value="criteria"/><attribute name="entityName" value="CapUser"/><attribute name="pageLength" type="Integer" value="10"/><entityTable name="orderEntityTable"/><attribute name="isPageCount" type="Boolean" value="true"/><attribute name="PK" type="array"><array value="operatorId"/></attribute><attribute name="checkType" value="blur"/><entityTable name="entityTable"><field><attribute name="displayModel" value="editModel"/><attribute name="configOperator" value="="/><attribute name="component_type" value="queryform"/><attribute name="fieldColSpan" type="Integer" value="1"/><attribute name="xpath" value="criteria/_expr[1]/"/><attribute name="config_size" type="Integer" value="0"/><attribute name="fieldName" value="status"/><attribute name="fieldDataType" value="String"/><attribute name="entityName" value="CapUser"/><attribute name="criteriaName" value="criteria"/><attribute name="fieldDisplayName;" value="正常，挂起，注销，锁定..."/><attribute name="configFieldName" value="status"/><tagElement displayName="正常，挂起，注销，锁定..." name="configControlType" showAllAttr="false" tagName="d:radio"><attribute name="dictTypeId" value="COF_EMPSTATUS"/><attribute name="property" value="criteria/_expr[1]/status"/></tagElement><attribute name="configDataType" value="String"/><attribute name="fieldControlType" value="d:radio"/><attribute name="isCustomProperty" type="Boolean" value="false"/><attribute name="isEdit" type="Boolean" value="false"/></field></entityTable><attribute name="pageFlow" value=""/><attribute name="formWidth" value="100%"/><attribute name="isNoRepeat" type="Boolean" value="false"/><attribute name="selectedEntity" value="org.gocom.components.coframe.rights.dataset.CapUser"/><attribute name="formName" value="form1"/></wizard></wizards>
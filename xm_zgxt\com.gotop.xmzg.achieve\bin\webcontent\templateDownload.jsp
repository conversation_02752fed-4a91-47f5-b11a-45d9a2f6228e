<%@page pageEncoding="UTF-8" contentType="text/html; charset=UTF-8" %>
<%@page import="java.io.File"%>
<%@page import="java.util.HashMap"%>
<%@page import="java.util.*"%>
<%@page import="com.eos.server.dict.DictManager"%>
<%@page import="java.io.FileInputStream"%>
<%@page import="java.io.OutputStream"%>
<%@page import="java.net.URLEncoder"%>

<%@include file="/coframe/tools/skins/common.jsp" %>
<%

  HashMap resultMap = new HashMap();

  java.util.Calendar timea = null;
  java.util.Date ad = null;
  try {
     

	//获取数据字典中设定的存放文件夹   比如: /app/err_excel/
    //String dictVir = DictManager.getDictName("CMMS_MB_DOWNLOAD_DIR","02");
    //String filePath = session.getServletContext().getRealPath("/"+fileDir);
   // fileName = fileName.replaceAll("\\+", "%20").replaceAll("%28", "\\(").replaceAll("%29", "\\)").replaceAll("%3B", ";").replaceAll("%40", "@").replaceAll("%23", "\\#").replaceAll("%26", "\\&");
    String dictVir=request.getParameter("filePath");
    String filePath = session.getServletContext().getRealPath(dictVir);
    String fileName =request.getParameter("filename");   //获取JSP传入的文件名称  
    
    filePath=filePath+"/"+fileName;
   //System.out.println("filePath:"+filePath);
   //System.out.println("fileName:"+fileName);
    
    String filename2 =request.getParameter("filename2");   //获取JSP传入的文件名称  
    System.out.println("========================fileName2:"+filename2);
    //String filename2="KPI员工绩效合同导入模板.xls";

    	File file=new File(filePath);
		if(file.exists()){
		 //  System.out.println("ok");
		
			response.reset();//可以加也可以不加
		
			response.setContentType("application/x-download");
			response.addHeader("Content-Disposition","attachment;filename=" + new String( filename2.getBytes("utf-8"), "ISO8859-1" ));
			FileInputStream fis = null;
			OutputStream outputStream = null;
			try{
				ad = new java.util.Date();//开始时间
				timea = java.util.Calendar.getInstance();
				fis = new FileInputStream(filePath);
				outputStream = response.getOutputStream();
				byte[] b = new byte[8096];
				int i = 0;
				while ((i = fis.read(b,0,b.length)) !=-1)
				{outputStream.write(b, 0, i);}
				java.util.Date bd = new java.util.Date();//结束时间
	        	java.util.Calendar timeb = java.util.Calendar.getInstance();
	        	timea.setTime(ad);
	        	timeb.setTime(bd);
				outputStream.flush();response.flushBuffer();
				out.clear();
	    		out=pageContext.pushBody();
		
				resultMap.put("succflag","1");	
					
			}catch(Exception e){
			resultMap.put("succflag","2");
			}finally{
			if (outputStream != null) outputStream.close();     
	        if (fis != null) fis.close();   
	    	
	        }
	        }
	         else{
	        
	      	 resultMap.put("succflag","4");
	      	
	        } 
		}catch(Exception e){
	      resultMap.put("succflag","3");
	      }finally{
		  }
	  
	  %>
	  
<html xmlns="http://www.w3.org/1999/xhtml">
<head>	  
	  <script type="text/javascript" >
	 	var time = 4;  
	 	var succflag=<%=resultMap.get("succflag") %>
  		$(function(){
  			if(succflag=="4"){
  			alert("该文件不存在");

  			setTimeout(self.location=document.referrer,30000);

  			}
        });
   
       
       
	  </script>
</head>
<body  onload="returnUrlByTime()">  
<!-- <span id="layer">3</span>秒后，转入输入界面。</b>   -->

</body>
</html>	  
	  
	  
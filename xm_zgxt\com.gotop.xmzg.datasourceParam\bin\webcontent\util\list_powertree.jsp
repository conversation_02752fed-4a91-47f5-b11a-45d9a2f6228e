<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%@ page import="com.eos.data.datacontext.UserObject"%>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<%
		UserObject userObject = (UserObject) request.getSession().getAttribute("userObject");
	%>
    <title></title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=EDGE">
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>     
    <link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/coframe/tools/icons/icon.css"/>
    <style type="text/css">
    html,body
    {
        padding:0;
        margin:0;
        border:0;     
        width:100%;
        height:100%;
        overflow:hidden;   
    }
    </style>
</head>
<body>
    <div class="nui-toolbar" style="text-align:center;line-height:30px;" 
        borderStyle="border-left:0;border-top:0;border-right:0;">
          <label >名称：</label>
          <input id="key" class="nui-textbox" style="width:150px;" onenter="onKeyEnter"/>
          <a class="nui-button" style="width:60px;" onclick="search()">查询</a>
    </div>
    <div class="nui-fit">
        <ul id="dataTree" class="nui-tree" style="width:98%;height:98%;padding:5px;background:#fafafa;margin-top:5px;" 
		    showTreeIcon="true" textField="nodeName" dataField="treeNodes"
		    idField="nodeId" parentField="parentId" resultAsTree="false" showCheckBox="true"
		    expandOnLoad="0" ondrawnode="onDrawNode" allowSelect="true" enableHotTrack="true"
		    > 
		</ul>
    </div>                
    <div class="nui-toolbar" style="text-align:center;padding-top:8px;padding-bottom:8px;" 
        borderStyle="border-left:0;border-bottom:0;border-right:0;">
        <a class="nui-button" style="width:60px;" onclick="onOk()">确定</a>
        <span style="display:inline-block;width:25px;"></span>
        <a class="nui-button" style="width:60px;" onclick="onCancel()">取消</a>
    </div>

</body>
<script type="text/javascript">
    nui.parse();
	var userId= <%=userObject.getUserId()%>;
	var tree = nui.get("dataTree");
	$(function() {
		nui.parse();
	    var tree = nui.get("dataTree");
	     tree.setCheckRecursive(false);
		tree.load("com.gotop.fileManger.hyq.entity.listPermManager.queryOrgInfoByType.biz.ext");
	});
	
	/**
	 *  显示父节点checkbox
	 */
 	function onDrawNode(e) {
		var tree = e.sender;
		var node = e.node;
		var isLeaf = tree.isLeaf(node);
	} 
	/**
	 * 新增界面获取此iframe数据的方法
	 * return list 获取Check选中的多个节点
	 */
	function GetData() {
		return tree.getCheckedNodes();
    }
    
    function setTreeCheck(idStr) {
    	tree.setValue(idStr);
    } 
    
    function search() {
        var key = nui.get("key").getValue();
        if(key == ""){
            tree.clearFilter();
        }else{
            key = key.toLowerCase();
            tree.filter(function (node) {
                var text = node.nodeName ? node.nodeName.toLowerCase() : "";
                if (text.indexOf(key) != -1) {
                    return true;
                }
            });
        }
    }
 
    function onKeyEnter(e) {
    	
        search();
    }
    
    function onNodeDblClick(e) {
        onOk();
    }
    
    function CloseWindow(action) {
        if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
        else window.close();
    }

    function onOk() {

        CloseWindow("ok");        
    }
    function onCancel() {
        CloseWindow("cancel");
    }

    
</script>
</html>

<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-05-29 16:10:11
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>认领配置</title>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <table class="table" style="width:100%;">       
	        <tr>
	    
				<th class="tit" >认领类型：</th>
				<td>
					<input id="TC_INDIC_TYPE" name="queryData.TC_INDIC_TYPE" dictTypeId="JF_FPLX"  class="nui-dictcombobox" valueField="dictID" textField="dictName" emptyText="全部"  showNullItem="true" nullItemText="全部" style="width:100%;"/>
				</td>
				<th  class="tit">业务条线：</th>
				<td>
				    <div id="TIP_CODE" name="queryData.TIP_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TIP_NAME" valueField="TIP_CODE" 
				    	url="com.gotop.xmzg.achieve.configClaPro.tip_get.biz.ext" multiSelect="false" allowInput="false" showNullItem="false" nullItemText="全部" emptyText="全部"
				    	onvaluechanged="onTipChanged">     
					    <div property="columns">
					        <div header="业务条线代码" field="TIP_CODE" width="60"></div>
					        <div header="业务条线名称" field="TIP_NAME" width="120"></div>
					    </div>
				    </div>
				</td>
			</tr>
				<tr>	
				<th  class="tit">指标：</th>
				<td >
					<div id="TI_CODE" name="queryData.TI_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TI_NAME" valueField="TI_CODE" 
				    	multiSelect="false" allowInput="true" showNullItem="false" nullItemText="全部" emptyText="全部"
				    	onvaluechanged="onTiChanged">     
					    <div property="columns">
					        <div header="指标代码" field="TI_CODE" width="60"></div>
					        <div header="指标名称" field="TI_NAME" width="120"></div>
					    </div>
				    </div>
				</td>
				<th  class="tit">指标细项：</th>
				<td >
					<div id="TID_CODE" name="queryData.TID_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TID_NAME" valueField="TID_CODE" 
				    	multiSelect="false" allowInput="true" showNullItem="false" nullItemText="全部" emptyText="全部">     
					    <div property="columns">
					        <div header="指标细项代码" field="TID_CODE" width="60"></div>
					        <div header="指标细项名称" field="TID_NAME" width="120"></div>
					    </div>
				    </div>
				</td>
				
				<th>
				  <a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;
				  <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
				</th>
			</tr>
	    </table>
	  </div>
    </div>
  </div>
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
		     <a class="nui-button" iconCls="icon-add" onclick="add">增加</a>
		     <a id="update" class="nui-button" iconCls="icon-edit" onclick="update">修改</a>
		     <a class="nui-button" iconCls="icon-remove" onclick="remove">删除</a>
           </td>
        </tr>
      </table>
    </div>
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;"
	  url="com.gotop.xmzg.achieve.configClaPro.claim_list.biz.ext" multiSelect="true"
	  sizeList=[5,10,20,50,100]  pageSize="10">
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      <div field="TC_INDIC_TYPE" headerAlign="center" align="center" renderer="dict">认领类型</div>
	      <div field="TIP_NAME" headerAlign="center" align="center">业务条线名称</div>
	      <div field="TI_NAME" headerAlign="center" align="center" >指标名称</div>
	      <div field="TID_NAME" headerAlign="center" align="center" >指标细项名称</div>
	      <div field="TC_TYPE" headerAlign="center" align="center" renderer="dict1">认领方式</div>
	      <div field="TC_AUTH" headerAlign="center" align="center" renderer="dict2">是否审核</div>
	      <div field="TC_ACROSSORG" headerAlign="center" align="center" renderer="dict3">是否跨机构</div>
	      
	      <div field="TC_RL_DATE" headerAlign="center" align="center">认领截至日</div>
	      <div field="TC_RL_CYCLE" headerAlign="center" align="center" renderer="dict5">认领周期</div>
	      
	      <div field="TC_ZY_DATE" headerAlign="center" align="center">转移截至日</div>
	      <div field="TC_ZY_CYCLE" headerAlign="center" align="center" renderer="dict5">转移周期</div>
	      
	      <div field="TC_RLZZ_DATE" headerAlign="center" align="center">认领终止截至日</div>
	      <div field="TC_RLZZ_CYCLE" headerAlign="center" align="center" renderer="dict5">认领终止周期</div>
	      
	      <div field="TC_PRODUCT_EXIST" headerAlign="center" align="center" renderer="dict4">判断产品是否存在</div>
	      <div field="TC_PRODUCTNAME" headerAlign="center" align="center" >产品名称</div>
	      <div field="TC_PRODUCT_TABLE" headerAlign="center" align="center" >产品对应数据库表名</div>
	      <div field="TC_PRODUCT_FIELD" headerAlign="center" align="center" >产品字段名</div>
	      <div field="TC_ORGFIELD" headerAlign="center" align="center" >机构字段名</div>
	      <div field="TC_CREATETIME" headerAlign="center" align="center">创建时间</div>
	      <div field="CREATEORGNAME" headerAlign="center" align="center">创建机构</div>	  
		  <div field="CREATORNAME" headerAlign="center" align="center">创建人</div>
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">  
    nui.parse();
 	
    var grid = nui.get("datagrid1");
    var form = new nui.Form("#form1");
    search();
    
    function search(){
       form.validate();
       if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
     
     function add(){
      nui.open({
          url:"<%=request.getContextPath() %>/achieve/configClaPro/claim_form.jsp",
          title:'新增',
          width:500,
          height:600,
          onload:function(){
          	var iframe = this.getIFrameEl();
	             var data = {pageType:"add"};
                  iframe.contentWindow.setData(data);
          },
          ondestroy:function(action){
             if(action=="saveSuccess"){
	            grid.reload();
	         }
          }
       });
    }
    
      function update(){
      var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
	       nui.open({
	          url:"<%=request.getContextPath() %>/achieve/configClaPro/claim_form.jsp",
	          title:'编辑',
	          width:500,
	          height:600,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             var data = {pageType:"edit",record:{map:rows[0]}};
                  iframe.contentWindow.setData(data);
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }
    }
    
    
 function remove(){
      var rows = grid.getSelecteds();
     if(rows.length > 0){
         nui.confirm("确定删除选中记录？","系统提示",
           function(action){
             if(action=="ok"){
	           var json = nui.encode({maps:rows});
	           var a= nui.loading("正在执行中,请稍等...","提示");
		        $.ajax({
		          url:"com.gotop.xmzg.achieve.configClaPro.claim_delete.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	nui.hideMessageBox(a);
		            var result = text.result;
					if(result !=null && result.code==1){
						nui.alert(result.msg, "系统提示", function(action){
							grid.reload();
						});
					}else{
						nui.alert(result.msg, "系统提示");
						grid.unmask();
					}
		          }
		        });
		     }
		   });
      }else{
        nui.alert("请选中一条记录！");
      }
    }
    
    
  function CloseWindow(action){
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
  
   	//重置
    function clean(){
	   form.reset();
    }
    
    var ti = nui.get("TI_CODE");
    var tid = nui.get("TID_CODE");
    function onTipChanged(e){
        ti.setValue("");
        tid.setValue("");
        var url = "com.gotop.xmzg.achieve.configClaPro.ti_get.biz.ext?code=" + e.value;
        ti.setUrl(url);
    }
    
    function onTiChanged(e){

        tid.setValue("");
        var url = "com.gotop.xmzg.achieve.configClaPro.tid_get.biz.ext?code=" + e.value;
        tid.setUrl(url);
    }
    
    function dict(e){
    	return nui.getDictText("JF_FPLX", e.value);
    }
    function dict1(e){
    	return nui.getDictText("JF_RLFS", e.value);
    }
    function dict2(e){
    	return nui.getDictText("JF_SFSP", e.value);
    }
    function dict3(e){
    	return nui.getDictText("JF_KJG", e.value);
    }
    function dict4(e){
    	return nui.getDictText("JF_YES_NO", e.value);
    }
    function dict5(e){
    	return nui.getDictText("JF_RL_CYCLE", e.value);
    }
  </script>
</body>
</html>
<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page import="java.util.List"%>
<%@ page import="com.eos.data.datacontext.UserObject" %>	
<%@page import="org.gocom.components.coframe.tools.tab.TabPageManager"%>
<%@page import="org.gocom.components.coframe.tools.tab.TabPage"%>
<% List<TabPage> tabList = TabPageManager.INSTANCE.getTabPageList("AuthTab");
   //List<TabPage> tabList2 = TabPageManager.INSTANCE.getTabPageList("AuthTab");
   
   //不显示 表单、视图、数据实体tab、工作组、机构、岗位
   for(int i=0; i<tabList.size(); i++){
   		TabPage tab = tabList.get(i);
   		if( ("表单".equals(tab.getTitle())) || ("视图".equals(tab.getTitle())) || ("数据实体".equals(tab.getTitle())) || ("模块".equals(tab.getTitle())) || ("工作组".equals(tab.getTitle())) || ("岗位".equals(tab.getTitle())) || ("机构".equals(tab.getTitle())) || ("员工".equals(tab.getTitle())) || ("功能".equals(tab.getTitle()))){
	      tabList.remove(i);
	      i--;
	   }
   }
   
   UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>
<html>
<!-- 
  - Author(s): wj
  - Date: 2018-08-17 12:01:11
  - Description: 分管授权管理
-->
<%@page import="com.eos.foundation.eoscommon.ResourcesMessageUtil"%>
<head>
	<title>角色授权</title>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
	<%@include file="/coframe/tools/skins/common.jsp" %>
</head>
<body>
<style>
#authTabs .mini-tabs-bodys{
	padding:0px;
}
</style>
<div class="nui-fit" style="width:100%; height:100%; padding:0px 0px 0px 0px;">
<div id="layout1" class="nui-layout" style="width:100%;height:100%;">
	<div id="region1" region="west" title="角色授权" showHeader="true" class="sub-sidebar" 
	width="400" allowResize="false">

		<div class="search-condition">
			<div class="list">
				<div id="form1">
					<table id="table1" class="table">
						<tr>
							<td  >
								<input class="nui-textbox" name="map.roleCode" emptyText="角色代码" style="width:120px;" />
							</td>
							<td  >
								<input class="nui-textbox" name="map.roleName" emptyText="角色名称" style="width:120px;"/>
							</td>
							<td></td>
							<td >
								<input class="nui-button" iconCls="icon-search" text="查询" onclick="search" />
							</td>
						</tr>
					</table>
				</div>
			</div>
		</div>	
		
		<div class="nui-fit" style="padding:0px 5px 5px 5px;">
			<div id="roleGrid" class="nui-datagrid" style="width:100%;height:99%;" dataField="resultList"
				url="org.gocom.components.coframe.userInfoManage.roleAuth.query_AuthRole.biz.ext"
				idField="ROLE_ID" multiSelect="true" allowAlternating="true" showPager="false"
				showReloadButton="false" showPageSize="false" showPageInfo="false"
				ondrawcell="drawRoleAuthConfig"  onload="myLoad">
				<div property="columns">
					<div field="ROLE_CODE" width="38%">角色代码</div>
					<div field="ROLE_NAME" width="38%">角色名称</div>
					<div name="roleAuthConfig" width="24%">授权配置</div>
				</div>
			</div>
		</div>
	 </div>
    <div title="center" region="center" style="border:0;padding-left:5px;padding-top:5px;">
		<div id="authTabs" class="nui-tabs  bg-toolbar" activeIndex="0" style="width:100%;height:100%;" onactivechanged="changeTab">
			<%	for(TabPage tab : tabList){ 
			%>      
					<div id="<%= tab.getId() %>" name="<%= tab.getId() %>" title="<%= tab.getTitle() %>" url=""></div>
			<%	 } %>
			<div id="userInfo" name="userInfo" title="员工" url=""></div>
			<%-- <div id="funcTab" name="funcTab" title="功能" url=""></div>
			<div id="formTab" name="formTab" title="表单" url=""></div>
			<div id="viewTab" name="viewTab" title="视图" url=""></div>
			<div id="empTab" name="empTab" title="员工" url=""></div>
			<div id="orgTab" name="orgTab" title="机构" url=""></div>
			<div id="positionTab" name="positionTab" title="岗位" url=""></div> --%>
		</div>
	</div>
</div>
</div>
</body>
</html>
<script type="text/javascript">
	nui.parse();
	var form1 = new nui.Form("#form1");
	var roleGrid = nui.get("roleGrid");
	var authTabs = nui.get("authTabs");
	roleGrid.load();
	var authTabArray = [];
	<%	for(TabPage tab : tabList){ %>
			var <%= tab.getId() %> = {name:"<%= tab.getId() %>", title:"<%= tab.getTitle() %>", path:"<%=request.getContextPath() %><%= tab.getUrl() %>"};
			authTabArray.push(<%= tab.getId() %>);
	<%	} %>
	var userInfo = {name:"userInfo", title:"员工", path:"<%=request.getContextPath() %>/userInfoManage/roleAuth/employee_auth.jsp"};
			authTabArray.push(userInfo);
	<%-- 设置Tab的URL及参数 --%>
	function setUrlParam(url,paramObj){
		if(!url){
			return url;
		}
		var params = [];
		for(var pop in paramObj){
			params.push(pop + "=" + paramObj[pop]);
		}
		var paramStr = params.join("&");
		if(url.indexOf("?")>=0){
			return url + "&" + paramStr;
		}else{
			return url + "?" + paramStr;
		}
	}

	<%-- 装载选定角色相应的tab页 --%>
	function reloadTab(paramObj){
		for(var i = 0; i < authTabArray.length; i++){
			var authTabElem = authTabArray[i];
			var settingTab = authTabs.getTab(i);
			settingTab.url = setUrlParam(authTabElem.path, paramObj);
		}
		var currentTab = authTabs.getActiveTab();
		authTabs.reloadTab(currentTab);
	}

	function drawRoleAuthConfig(e){
		if(e.column.name == "roleAuthConfig"){
			//alert(e.record.roleId);
			e.cellHtml = "<a style='color:#1B3F91;text-decoration:underline;' href='#' id='"+e.record.ROLE_ID+"' onclick='authConfig(this)'>配置</a>";
		}
	}

	function authConfig(obj){
		var layout1 = nui.get("layout1");
		var paramObj = {roleId:obj.id};
		reloadTab(paramObj);
	}

	function changeTab(e){
		authTabs.reloadTab(e.tab);
	}
	
	function search(){
		var form1Data = form1.getData(false, true);
        roleGrid.load(form1Data);
	}
	
	function myLoad(e){
		
		var userName = "<%=userObject.getUserName() %>";
		
		/*
		if(userName != "sysadmin") {//当前登录用户不是sysadmin这个用户
		
			//如果角色列表里有超级管理员（角色编码为sysadmin）的记录，移除。因为普通系统管理员不能对配置超级管理员权限
			for(var i=0; i<e.data.length; i++){
				if(e.data[i].ROLE_CODE == "sysadmin"){
					e.data.splice(i,1);
				}
			}
		}*/ 
	}
</script>

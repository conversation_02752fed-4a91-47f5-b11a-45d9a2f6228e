<%@page pageEncoding="UTF-8"%>
<%@page import="net.sf.json.JSONObject"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): liuyl
  - Date: 2019-05-10 17:41:00
  - Description:
-->
<head>
<%@include file="/coframe/dict/common.jsp"%>
<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
</style>
</head>
<body>
  <div id="form1" style="padding-top:5px;">
    <input id="map.userName" name="map.userName" style="width:150px;" class="nui-hidden" />
    <input id="map.id" name="map.id" style="width:150px;" class="nui-hidden" />
    <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
     <tr>
      	<th align="center" colspan="8" style="font-size:18px;">数据源系统参数维护</th>
      </tr>
      <tr>
      	<th class="nui-form-label" style="width:150px;"><label for="map.type$text">系统名称：</label></th>
        <td colspan="2" >    
          <input id="map.systemName" name="map.systemName" style="width:150px;" class="nui-textbox asLabel" required="true" readOnly="true"/>
        </td> 
      	<th class="nui-form-label" style="width:150px;"><label for="map.type$text">登录网址：</label></th>
        <td colspan="4" >    
          <input id="map.systemUrl" name="map.systemUrl"  style="width:400px;" class="nui-textbox asLabel" required="true" readOnly="true"/>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label" style="width:150px;"><label for="map.type$text">维护部门：</label></th>
        <td colspan="6" >  
            <input id="btnEdit1" name = "map.managerOrg"  class="nui-buttonedit"  onbuttonclick="OrgonButtonEdit" allowInput="false" required="true"  style="width:150px;"/>      
            <input id="map.managerOrgName" name = "map.managerOrgName"  class="nui-hidden" />      
        </td> 
       </tr>
     <!--   <tr>
        <th class="nui-form-label" style="width:150px;"><label for="map.status$text">是否输入验证码：</label></th>
		<td colspan="7">
            <input class="nui-radiobuttonlist"  id="map.isEncry"  name="map.isEncry" data="[{id:'1',text:'是'},{id:'2',text:'否'}]"  textField="text" valueField="id" value="1" />
		           					
		</td>
      </tr> -->
      </div>
    </table>
    </br>
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save"  onclick="onOk">确定</a>&nbsp;&nbsp;
	        </td>
	      </tr>
	    </table>
	 </div>
  </div>

  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);

    
    
    //与父页面的方法2对应：页面间传输json数据
    function setFormData(data){                   
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);

        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
        	//表单数据回显
             var json = infos.record;
             var form = new nui.Form("#form1");//将普通form转为nui的form
             form.setData(json);
             nui.get("btnEdit1").setText(json.map.managerOrgName);
             nui.get("map.managerOrgName").setValue(json.map.managerOrgName);
             nui.get("map.id").setValue(json.map.id);
         }
    }
    //机构树回显
    function OrgonButtonEdit(e) {
        var btnEdit = this;
        nui.open({
            url:"<%=request.getContextPath() %>/datasourceParam/util/list_powertree.jsp",
            showMaxButton: false,
            title: "选择树",
            width: 350,
            height: 350,
            onload: function () {
	              var iframe = this.getIFrameEl();  
	                //var editTextBox = findEditTextBox(e.sender.defaultValue);
	              var idStr = nui.get("btnEdit1").getValue();
	              iframe.contentWindow.setTreeCheck(idStr);
	            },
            ondestroy: function (action) {                    
                if (action == "ok") {
                 var iframe = this.getIFrameEl();
	                    var chooseList = iframe.contentWindow.GetData();
	                    chooseList = nui.clone(chooseList);
	                    
	                      var data = {};
	                      var orgIds = [];
	                      var orgNames = [];
		                  for(var i=0;i<chooseList.length;i++){
		                  		orgIds.push(chooseList[i].ORGID);
		                  		orgNames.push(chooseList[i].ORGNAME);
		                  }
		                  data.orgId = orgIds.join(",");
		                  data.orgName = orgNames.join(",");
		            
		            if (data) {
                        btnEdit.setValue(data.orgId);
                        btnEdit.setText(data.orgName);
                        nui.get("map.managerOrgName").setValue(data.orgName);
                    }      
		                  
                }
            }
        });            
     }
    
    //确定修改
    function onOk(){
      saveData();
    }
    
    function saveData(){  
	      form.validate();
	      if(form.isValid()==false) return;
 
	      var data = form.getData(false,true);
	      var json = nui.encode(data);
	      $.ajax({
	        url:"com.gotop.xmzg.datasourceParam.dataSourceParam.uptDataSourceParam.biz.ext",
	        type:'POST',
	        data:json,
	        cache:false,
	        contentType:'text/json',
	        success:function(text){
	            var returnJson = nui.decode(text);
				if(returnJson.exception == null && returnJson.flag=="1"){	
					nui.alert("修改成功", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							CloseWindow("saveSuccess");
						}
					});
				}else{
					nui.alert("修改失败", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							CloseWindow("saveFailed");
						}
					});
				}
	        }
	      });
    }
    
    
     function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }

 //刷新数据字典缓存
   function refreshDictCache(){
    	nui.ajax({
			url: "org.gocom.components.coframe.dict.DictManager.refreshDictCache.biz.ext",
			type: "post",
			cache: false,
			contentType: 'text/json',
			success: function (json) {
				if(json.status == 'success'){
				location.reload();
			}
			//else nui.alert("刷新缓存失败！");
			},
			error: function () {
				//nui.alert("刷新缓存失败！");
				location.reload();
			}
		});
    }     
     

  </script>
</body>
</html>

<!-- $Header: svn://**************:7029/svn/xm_project/xm_zgxt/com.gotop.fileManger.jw/src/webcontent/upload.jsp 1466 2018-08-14 02:34:48Z jw $-->
<%@page import="java.io.File"%>
<%@page import="java.util.Enumeration"%>
<%@page import="com.oreilly.servlet.multipart.DefaultFileRenamePolicy"%>
<%@page import="com.oreilly.servlet.MultipartRequest"%>
<%@page import="com.gotop.xm.util.DocConverterUtil"%>
<%@page import="com.gotop.xm.util.FileUtil"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" import="java.util.*"%>
<%
	String fileno= request.getParameter("fileno");
	System.out.println("---->"+fileno);
	String dir= request.getParameter("dir");
	System.out.println("---->"+dir);
	String path= request.getParameter("path");
	System.out.println("---->"+path);
	String previewname= request.getParameter("previewname");
	System.out.println("---->"+previewname);
	//创建upload文件夹
	String upload = "upload";
	String saveUpload = session.getServletContext().getRealPath("/" + upload);
	File uploads = new File(saveUpload);
	FileUtil.judeDirExists(uploads);
	//创建upload文件夹
	//创建目录文件夹
	String uploadPath = "upload/"+dir;
	String saveDirectory = session.getServletContext().getRealPath("/" + uploadPath);
	System.out.println(saveDirectory);
	File file = new File(saveDirectory);
	FileUtil.judeDirExists(file);
	//创建目录文件夹
	//每个文件最大100m,最多10个文件,所以...
	int maxPostSize = 10 * 100 * 1024 * 1024;
	MultipartRequest multi = new MultipartRequest(request,saveDirectory, maxPostSize, "UTF-8");

	//如果有上传文件, 则保存到数据内
	Enumeration files = multi.getFileNames();
	while (files.hasMoreElements()) {
		String name = (String) files.nextElement();
		File f = multi.getFile(name);
		if (f != null) {
			try {
				//读取上传后的项目文件, 导入保存到数据中
				String fileName = multi.getFilesystemName(name);
				// 获取需要转换的文件名,将路径名中的'\'替换为'/'
				String converfilename = saveDirectory + "/" + fileName;
				// 调用转换类DocConverter,并将需要转换的文件传递给该类的构造方法
				DocConverterUtil d = new DocConverterUtil(converfilename,fileno,path,previewname);
				// 调用conver方法开始转换，先执行doc2pdf()将office文件转换为pdf;再执行pdf2swf()将pdf转换为swf;			
				d.conver();
				response.getWriter().write("ok");
			} catch (Exception e) {
				e.printStackTrace();
				response.getWriter().write("error");
			}
		}
	}
%>

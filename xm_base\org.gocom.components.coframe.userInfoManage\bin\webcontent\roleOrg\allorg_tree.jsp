<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%
	String contextPath=request.getContextPath();
%>

<script type="text/javascript" src="<%=contextPath%>/common/nui/nui.js"></script>
<html xmlns="http://www.w3.org/1999/xhtml">
<!-- 
  - Author(s): wj
  - Date: 2018-08-16 12:01:11
  - Description:单选机构树  所有机构
-->
<head>
    <title></title>
 
  	<style type="text/css">
    html,body
    {
        padding:0;
        margin:0;
        border:0;     
        width:100%;
        height:100%;
        overflow:hidden;   
    }
    </style>
</head>
<body>
    <div class="nui-toolbar" style="text-align:center;line-height:30px;" 
        borderStyle="border-left:0;border-top:0;border-right:0;">
          <label >名称：</label>
          <input id="key" class="nui-textbox" style="width:150px;" onenter="onKeyEnter"/>
          <a class="nui-button" style="width:60px;" onclick="search()">查询</a>
    </div>
    <div class="nui-fit">
        
        <ul id="tree1" class="nui-tree" style="width:100%;height:100%;" dataField="treeNodes"
            showTreeIcon="true" textField="TEXT" idField="ID" parentField="PID" resultAsTree="false"  
            expandOnLoad="0" onnodedblclick="onNodeDblClick" expandOnDblClick="false" 
            >
        </ul>
    
    </div>                
    <div class="nui-toolbar" style="text-align:center;padding-top:8px;padding-bottom:8px;" 
        borderStyle="border-left:0;border-bottom:0;border-right:0;">
        <a class="nui-button" style="width:60px;" onclick="onOk()">确定</a>
        <span style="display:inline-block;width:25px;"></span>
        <a class="nui-button" style="width:60px;" onclick="onCancel()">取消</a>
    </div>

</body>
</html>
<script type="text/javascript">
    nui.parse();

    var tree = nui.get("tree1");

    
    tree.load("org.gocom.components.coframe.userInfoManage.roleOrg.get_OrgTree.biz.ext");

    function GetData() {
        var node = tree.getSelectedNode();
        return node;
    }
    
    function search() {
        var key = nui.get("key").getValue();
        if(key == ""){
            tree.clearFilter();
        }else{
            key = key.toLowerCase();
            tree.filter(function (node) {
                var text = node.TEXT ? node.TEXT.toLowerCase() : "";
                if (text.indexOf(key) != -1) {
                    return true;
                }
            });
        }
    }
    
    function setTreeCheck(idStr) {
    	tree.setValue(idStr);
    }
    
    function onKeyEnter(e) {
        search();
    }
    function onNodeDblClick(e) {
        onOk();
    }
    
    function CloseWindow(action) {
        if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
        else window.close();
    }

    function onOk() {
    
      /* $.ajax({
			        url:"com.gotop.zkpz.orgStock.orgStockManage.query_selectTree.biz.ext",
			        type:'POST',
			        cache:false,
			        contentType:'text/json',
			        success:function(text){
			      		var json = nui.encode(text);
			            alert(json);
				    }
			       });*/
      /*  var node = tree.getSelectedNode();
        if (node && tree.isLeaf(node) == false) {
            alert("不能选中父节点");
            return;
        } */

        CloseWindow("ok");    
    }
    function onCancel() {
        CloseWindow("cancel");
    }

    
</script>
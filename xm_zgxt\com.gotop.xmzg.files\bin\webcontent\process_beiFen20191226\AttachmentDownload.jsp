<%@page pageEncoding="UTF-8" contentType="text/html; charset=UTF-8" %>
<%@page import="java.io.File"%>
<%@page import="java.util.HashMap"%>
<%@page import="java.util.*"%>
<%@page import="java.io.FileInputStream"%>
<%@page import="java.io.OutputStream"%>
<%@page import="java.net.URLEncoder"%>

<%@include file="/coframe/tools/skins/common.jsp" %>
<%
//  org.gotop.abframe.dataContext.MUOUserSession muo = (org.gotop.abframe.dataContext.MUOUserSession)session.getAttribute("userIntegrated");
  HashMap resultMap = new HashMap();;
//  Attachment attachment = null; 
  java.util.Calendar timea = null;
  java.util.Date ad = null;
  try {
       String fileid = request.getParameter("fileid");
	//   attachment = new Attachment();
	//   resultMap = attachment.downloadAttachmentInweb(fileid);
	//	resultMap.put("empid",muo.getEmpid());
	//	resultMap.put("orgcode",muo.getOrgcode());
	//	String filePath = (String) resultMap.get("filePath");
	String path=request.getParameter("filepath");//获取数据库中的文件路径
	String realPath =request.getSession().
                getServletContext().getRealPath("/");//获取服务器上的地址
    String filePath = realPath+ path;
    String fileName =request.getParameter("filename");   //获取数据库中的文件名称  
    System.out.println(fileid+":"+path+":"+realPath+":"+fileName);
    	File file=new File(filePath);
		if(file.exists()){
			// String filePath = "F:\\tt.txt";
			//	String fileName = (String) resultMap.get("fileName");
			//String fileName ="测试.txt";
			//fileName = URLEncoder.encode(fileName, "UTF-8");
			response.reset();//可以加也可以不加
			//response.setCharacterEncoding("gb2312");
			response.setContentType("application/x-download");
			response.addHeader("Content-Disposition","attachment;filename=" + new String( fileName.getBytes("gb2312"), "ISO8859-1" ));
			FileInputStream fis = null;
			OutputStream outputStream = null;
			try{
				ad = new java.util.Date();//开始时间
				timea = java.util.Calendar.getInstance();
				fis = new FileInputStream(filePath);
				outputStream = response.getOutputStream();
				byte[] b = new byte[8096];
				int i = 0;
				while ((i = fis.read(b,0,b.length)) !=-1)
				{outputStream.write(b, 0, i);}
				java.util.Date bd = new java.util.Date();//结束时间
	        	java.util.Calendar timeb = java.util.Calendar.getInstance();
	        	timea.setTime(ad);
	        	timeb.setTime(bd);
				outputStream.flush();//response.flushBuffer();
				out.clear();
	    		out=pageContext.pushBody();
			//resultMap.put("ttime",timeb.getTimeInMillis()-timea.getTimeInMillis());//下载用时
				resultMap.put("succflag","1");	
					
			}catch(Exception e){
			resultMap.put("succflag","2");
			}finally{
			if (outputStream != null) outputStream.close();     
	        if (fis != null) fis.close();   
	    	
	        }
	        }
	         else{
	        
	      	 resultMap.put("succflag","4");
	      	
	        } 
		}catch(Exception e){
	      resultMap.put("succflag","3");
	      }finally{
		  //attachment.addDownLoadLog(resultMap);//记录下载日志
		  }
	  
	  %>
	  
<html xmlns="http://www.w3.org/1999/xhtml">
<head>	  
	  <script type="text/javascript" >
	 	var time = 4;  
	 	var succflag=<%=resultMap.get("succflag") %>
  		$(function(){
  			if(succflag=="4"){
  			alert("该文件不存在");

  			setTimeout(self.location=document.referrer,30000);

  			}
        	//setTimeout(history.back(),30000);
        });
    /* 	function returnUrlByTime() {  
  
         setTimeout(returnUrlByTime, 1000);  
        time = time - 1;  
        document.getElementById("layer").innerHTML = time;  
        }
       */
       
       
	  </script>
</head>
<body  onload="returnUrlByTime()">  
<!-- <span id="layer">3</span>秒后，转入输入界面。</b>   -->

</body>
</html>	  
	  
	  
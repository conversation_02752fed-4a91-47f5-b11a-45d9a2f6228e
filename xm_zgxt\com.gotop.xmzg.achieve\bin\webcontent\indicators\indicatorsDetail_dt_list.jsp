<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-05-29 16:10:11
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>指标细项积分配置</title>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition" style="display: none;">
   <div class="list">
	  <div id="form1">
	     <input class="nui-hidden" name="obj.TID_CODE" id="TID_CODE">
	     <input class="nui-hidden" name="obj.TID_NAME" id="TID_NAME">
	     <input class="nui-hidden" name="obj.TIP_NAME" id="TIP_NAME">
	     <input class="nui-hidden" name="obj.TI_NAME" id="TI_NAME">
	  </div>
    </div>
  </div>
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
		     <a class="nui-button" iconCls="icon-add" onclick="add">新增</a>
             <a class="nui-button" iconCls="icon-edit" onclick="update">修改</a>
             <!-- <a class="nui-button" iconCls="icon-no" onclick="ter">终止</a> -->
             <a class="nui-button" iconCls="icon-remove" onclick="remove">删除</a>
           </td>
        </tr>
      </table>
    </div>
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="list" class="nui-datagrid" style="width:100%;height:100%;"
	  url="com.gotop.xmzg.achieve.indicators.indicatorsDetail_dt_list.biz.ext" multiSelect="true"
	  sizeList=[5,10,20,50,100]  pageSize="10">
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      <div field="TIDD_ID" headerAlign="center" align="center">ID</div>
	      <div field="TIP_NAME" headerAlign="center" align="center">业务条线</div>
	      <div field="TI_NAME" headerAlign="center" align="center">指标</div>
	      <div field="TID_NAME" headerAlign="center" align="center">指标细项</div>
	      <div field="TIDD_PROPORTION" headerAlign="center" align="center" >积分标准</div>
	      <div field="TIDD_BEGIN" headerAlign="center" align="center" >开始时间</div>
	      <div field="TIDD_END" headerAlign="center" align="center" >结束时间</div>
	      <div field="EMPNAME" headerAlign="center" align="center" >创建人</div>
	      <div field="TIDD_CREATE_TIME" headerAlign="center" align="center" >创建时间</div>
	    </div>
	 </div>
  </div>
      <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">关闭</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  <script type="text/javascript">  
    nui.parse();
 	
 	var datetime = new Date();
 	var year = datetime.getFullYear();
    var month = datetime.getMonth() + 1;
 	var cq = year+""+getq(month);
 	
    var grid = nui.get("datagrid1");
    var form = new nui.Form("#form1");
    
    function setData(data){
    	data = nui.clone(data);
    	//console.log(data);
    	nui.get("TID_CODE").setValue(data.TID_CODE);
    	nui.get("TID_NAME").setValue(data.TID_NAME);
    	nui.get("TI_NAME").setValue(data.TI_NAME);
    	nui.get("TIP_NAME").setValue(data.TIP_NAME);
    	search();
    }
    
    function search(){
       form.validate();
       if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
    
    function add(){
       var map = form.getData(true,true).obj;
       nui.open({
          url:"<%=request.getContextPath() %>/achieve/indicators/indicatorsDetail_dt_form.jsp",
          title:'新增',
          width:500,
          height:400,
          onload:function(){
             var iframe = this.getIFrameEl();
             var data = {pageType:"add",record:{map:map}};
              iframe.contentWindow.setData(data);
          },
          ondestroy:function(action){
             if(action=="saveSuccess"){
                grid.reload();
             }
          }
       });
    }
    
    function update(){
      var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{   	  
	       nui.open({
	          url:"<%=request.getContextPath() %>/achieve/indicators/indicatorsDetail_dt_form.jsp",
	          title:'编辑',
	          width:500,
	          height:400,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             var data = {pageType:"edit",record:{map:rows[0]}};
                  iframe.contentWindow.setData(data);
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }
    }
    function getq(month){
    	var cq=null;
    	if(month == '1' || month == '2' || month == '3') cq = 1;
		if(month == '4' || month == '5' || month == '6') cq = 2;
		if(month == '7' || month == '8' || month == '9') cq = 3;
		if(month == '10' || month == '11' || month == '12') cq = 4;
		
		return cq;
    }
    function remove(){
      var rows = grid.getSelecteds();
     if(rows.length > 0){
         nui.confirm("确定删除选中的积分标准？","系统提示",
           function(action){
             if(action=="ok"){
	           for(var i = 0 ; i < rows.length ; i++){
	           	  var bq = rows[i].TIDD_BEGIN.substr(0,4)+getq(parseInt(rows[i].TIDD_BEGIN.substr(4,6)));
	      	   	  if(cq != bq){
	      	   	    nui.alert("id为"+rows[i].TIDD_ID+"的开始时间与当前时间不在同一季度无法删除！");
	      	   	  	return ;
	      	   	  }
	      	   }
	      	   
	           var json = nui.encode({maps:rows});
	           var a= nui.loading("正在执行中,请稍等...","提示");
		        $.ajax({
		          url:"com.gotop.xmzg.achieve.indicators.indicatorsDetail_dt_del.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	nui.hideMessageBox(a);
		            var result = text.result;
					if(result !=null && result.code==1){
						nui.alert(result.msg, "系统提示", function(action){
							grid.reload();
						});
					}else{
						nui.alert(result.msg, "系统提示");
						grid.unmask();
					}
		          }
		        });
		     }
		   });
      }else{
        nui.alert("请选中一条记录！");
      }
    }
    
  function onCancel(){
      CloseWindow("cancel");
    }
  function CloseWindow(action){
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
    
   	//重置
    function clean(){
	   form.reset();
    }
 
  </script>
</body>
</html>
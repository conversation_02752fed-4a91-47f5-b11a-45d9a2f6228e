<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2019-06-04 14:51:16
  - Description:
-->
<head>
	<title>业绩预约信息查询</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%@include file="../init.jsp"%>
</head>

<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="send_bw" class="nui-form">
	    <table class="table" style="width:100%;">
	        <tr>
	       		<th class="tit">预约起始日期：</th>
				<td><input id="queryData.TAA_START_DATE" name="queryData.TAA_START_DATE" class="nui-datepicker"  style="width:150px;" allowInput="false" format="yyyy-MM-dd"/></td>
		        
		        <th class="tit">客户经理：</th> 
				<td><input id="queryData.TAA_EMP" name = "queryData.TAA_EMP"  class="nui-buttonedit"  allowInput="false" onbuttonclick="selectEmp" style="width:150px;"/></td>
		     
		      <!--  
		        <th class="tit">指标明细：</th>
				<td><input id="queryData.TID_CODE" name="queryData.TID_CODE" class="nui-combobox"  textField="TEXT" valueField="ID" dataField="list" style="width:300px;"/></td>
				-->
				
				<th  class="tit">业务条线：</th>
				<td>
				    <div id="TIP_CODE" name="queryData.TIP_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TIP_NAME" valueField="TIP_CODE" 
				    	url="com.gotop.xmzg.achieve.configClaPro.tip_get.biz.ext" multiSelect="false" allowInput="false" showNullItem="false" nullItemText="全部" emptyText="全部"
				    	onvaluechanged="onTipChanged">     
					    <div property="columns">
					        <div header="业务条线代码" field="TIP_CODE" width="60"></div>
					        <div header="业务条线名称" field="TIP_NAME" width="120"></div>
					    </div>
				    </div>
				</td>
				</tr>
				<tr>	
				<th  class="tit">指标：</th>
				<td >
					<div id="TI_CODE" name="queryData.TI_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TI_NAME" valueField="TI_CODE" 
				    	multiSelect="false" allowInput="true" showNullItem="false" nullItemText="全部" emptyText="全部"
				    	onvaluechanged="onTiChanged">     
					    <div property="columns">
					        <div header="指标代码" field="TI_CODE" width="60"></div>
					        <div header="指标名称" field="TI_NAME" width="120"></div>
					    </div>
				    </div>
				</td>
				<th  class="tit">指标细项：</th>
				<td >
					<div id="TID_CODE" name="queryData.TID_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TID_NAME" valueField="TID_CODE" 
				    	multiSelect="false" allowInput="true" showNullItem="false" nullItemText="全部" emptyText="全部">     
					    <div property="columns">
					        <div header="指标细项代码" field="TID_CODE" width="60"></div>
					        <div header="指标细项名称" field="TID_NAME" width="120"></div>
					    </div>
				    </div>
				</td>
				
				<th class="tit">客户名称：</th>
				<td><input id="queryData.TAA_CUST_NAME" name = "queryData.TAA_CUST_NAME"  class="nui-textbox"  style="width:150px;"/></td>
				
				<th class="tit" width="120">审核状态：</th>
				<td><input id="queryData.TAA_STATUS" name = "queryData.TAA_STATUS" valueField="dictID" textField="dictName" dictTypeId="JF_SHZG" class="nui-dictcombobox"  style="width:150px;"/></td>
				
				<th><a class="nui-button"  iconCls="icon-search" onclick="search">查询</a></th>
				<th><a class="nui-button" iconCls="icon-reset"  onclick="clean">重置</a></th>
		    </tr>
	    </table>
	  </div>
    </div>
</div>
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
             <a class="nui-button" iconCls="icon-add" onclick="add">录入</a>
             <a class="nui-button" iconCls="icon-edit" onclick="update">修改</a>
             <a class="nui-button" iconCls="icon-remove" onclick="del">删除</a>
             <a class="nui-button" iconCls="icon-node" onclick="submit">批量提交</a>
             <!-- <a class="nui-button" iconCls="icon-edit" onclick="update">审核</a>-->
           </td>
        </tr>
      </table>
    </div>
  
  <div class="nui-fit">
	 	 <div id="bw_grid" class="nui-datagrid"
			 style="height: 100%;"
			 idField="id" 
			 multiSelect="true"  
			 pageSize="20"
			 sizeList=[5,10,20,50,100]
	         dataField="resultList"
			 url="com.gotop.xmzg.achieve.appointment.appointment_list.biz.ext" 
			 >
			 <div field="TAA_ID" headerAlign="center" align="center" allowSort="true" >TAA_ID</div>
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      <div field="TAA_ORG" headerAlign="center" align="center" allowSort="true" >预约机构代号</div>
		  <div field="ORGNAME" headerAlign="center" align="center" allowSort="true" >预约机构名称</div>
		  <div field="TAA_EMP" headerAlign="center" align="center" allowSort="true" >客户经理代号</div>
		  <div field="EMPNAME" headerAlign="center" align="center" allowSort="true" >客户经理名称</div>
		  <div field="TIP_NAME" headerAlign="center" align="center" allowSort="true">业务条线名称</div>
	      <div field="TI_NAME" headerAlign="center" align="center" allowSort="true">指标名称</div>
		  <div field="TID_NAME" headerAlign="center" align="center" allowSort="true">指标细项名称</div>
		  <div field="TAA_CUST_TYPE" headerAlign="center" align="center" allowSort="true" >客户类型</div>
		  <div field="TAA_CUST_TYPE" headerAlign="center" align="center" allowSort="true" >客户类型</div>
		  <div field="TAA_CUST_NAME" headerAlign="center" align="center" allowSort="true" >客户名称</div>
		  <div field="TAA_CARD_TYPE" headerAlign="center" align="center" allowSort="true">证件类型</div>
		  <div field="TAA_CARD_NO" headerAlign="center" align="center" allowSort="true" >证件号码</div>
		  <div field="TAA_START_DATE" headerAlign="center" align="center" allowSort="true" >预约起始日</div>
		  <div field="TAA_END_DATE" headerAlign="center" align="center" allowSort="true">预约结束日</div>
		  <div field="TAA_NUM" headerAlign="center" align="center" allowSort="true" >业绩值</div>
		  <!--<div field="TAA_REMARK" headerAlign="center" align="center" allowSort="true">预约备注信息</div>
		  <div field="TAA_CHECK_FLAG" headerAlign="center" align="center" allowSort="true" >是否匹配</div>-->
		  <div field="TAA_STATUS" headerAlign="center" align="center" allowSort="true" renderer="statusRender">审核状态</div>
	    </div>
	 </div>
  </div>
  
 <script type="text/javascript">
    nui.parse();
    
    var grid = nui.get("bw_grid");
	var form = nui.get("send_bw");
	
	var ti = nui.get("TI_CODE");
    var tid = nui.get("TID_CODE");
    
    function onTipChanged(e){
        ti.setValue("");
        tid.setValue("");
        var url = "com.gotop.xmzg.achieve.configClaPro.ti_get.biz.ext?code=" + e.value;
        ti.setUrl(url);
    }
    
    function onTiChanged(e){

        tid.setValue("");
        var url = "com.gotop.xmzg.achieve.configClaPro.tid_get.biz.ext?code=" + e.value;
        tid.setUrl(url);
    }
	
	//打开录入页面
	function add(){
		//权限验证
               /*     nui.ajax({
                        url: "com.gotop.xmzg.achieve.appointment.appointment_qry_auth.biz.ext",
                        type: "post",
                        success: function (text) {
                     	   var code = text.code;
                     	   var msg = text.msg;
                     	   console.log(code);
                     	   console.log(msg);
                     	   if(code == '2'){
								  nui.alert(msg, "系统提示", function(action){});
							}else{
								var obj = text.obj;
								console.log(obj); */
								nui.open({
										url:bactpath+"/achieve/appointment/appointment_add.jsp",
										title:"业绩预约录入",
										width:600,
										height:500,
										onload:function(){},
										ondestroy:function(action){
											if(action=="saveSuccess"){
								                grid.reload();
								            }
										}
									});
						/* 	}
                        }
                    }); */
	
	}
	
	function statusRender(e) {
			if (e.value == "0") {
				return "待审核";
			}else if(e.value == "1"){
				return "已审核";
			}else if(e.value == "2"){
				return "审核不通过";
			}else if(e.value == "9"){
				return "草稿";
			}
	}
	
	//查询
    function search() {
    	var form = new nui.Form("#send_bw");
 		form.validate();
      	if (form.isValid() == false) return;
      	grid.load(form.getData(true,true));
	}
	
	//转换时间
    function setdate(e){
 		var date = e.record.TI_CREATETIME;
 		if(!isNullOrEmpty(date) && date.length == 14){
 			return changeDate(date);
 		}else{
 			return "";
 		}
 	}
 	
 		//打开修改页面
	function update(){
		var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
			
			if(rows[0].TAA_STATUS=='1'||rows[0].TAA_STATUS=='0'){
				nui.alert("该记录已提交审核无法修改");
			}else{
		
			nui.open({
			url:bactpath+"/achieve/appointment/appointment_update.jsp",
				title:"业绩预约修改",
				width:600,
				height:500,
				onload:function(){
					var iframe = this.getIFrameEl();
 	      	    	iframe.contentWindow.setData(rows[0]);
				},
				ondestroy:function(action){
					if(action=="saveSuccess"){
		                grid.reload();
		            }
				}
			});
			
			}
			
		}
	}
	
 		//人员树回显
        function selectEmp(e){
        	var ele = e.sender.id;
    		var emp = nui.get(ele);
            nui.open({
                url:"<%=request.getContextPath()%>/files/archiveList/empTree.jsp?type=emp",
                title: "选择人员",
                width: 600,
                height: 400,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = emp.getValue();
                	//frame.contentWindow.setData(data);
                	frame.contentWindow.setTreeCheck(data.value);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);
                        debugger;    //必须
                        if (data) {
                            emp.setValue(data.nodeId);
                            emp.setText(data.nodeName);
                            //将值人员id转换成人员code
                            var data={empId:data.nodeId};
					        var json = nui.encode(data);
					        var URL="com.gotop.xmzg.files.fileLedger.getEmpcode.biz.ext";
					        $.ajax({
					        	url: URL,
					            type: 'POST',
					            data: json,
					            async:false,
					            cache: false,
					            contentType:'text/json',
					            success: function (text){
					                var returnJson = nui.decode(text);
					                var result = returnJson.resultList;
					                emp.setValue(result[0].EMPCODE);
							    }
					  		});
                        }
                    }

                }
            });
    	}  
	
	//重置
	  function clean(){
	   var form = new nui.Form("#send_bw");
	   form.clear();
       }
       
       //删除
	function del(){
		var rows = grid.getSelecteds();
		if(rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
			nui.confirm("确定删除以下选择的数据？", "确定？",
	           function (action) {
	             if (action == "ok") {
	            	 var arr = [];
	            	 for(var i = 0;i<rows.length;i++){
	            	 	if(rows[i].TAA_STATUS=='1'||rows[i].TAA_STATUS=='2'||rows[0].TAA_STATUS=='0'){
						nui.alert("包含已提交审核记录无法删除");
						return;
						}else{arr.push(rows[i].TAA_ID);}
	            	 }
	            	 var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
	            	 //提交数据
                     nui.ajax({
                        url: "com.gotop.xmzg.achieve.appointment.appointment_del.biz.ext",
                        type: "post",
                        data: nui.encode({"arr":arr}),
                        contentType:'text/json',
                        success: function (text) {
                           nui.hideMessageBox(load);  //隐藏遮罩层
                     	   var code = text.code;
                     	   var msg = text.msg;
                     	   nui.alert(msg, "系统提示", function(action){});
                     	   grid.reload();
                        }
                    });
	             }
			});
			
		}
	}
	
	 //批量提交
	function submit(){
		var rows = grid.getSelecteds();
		if(rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
			nui.confirm("确定要提交以下数据？", "确定？",
	           function (action) {
	             if (action == "ok") {
	            	 var arr = [];
	            	 for(var i = 0;i<rows.length;i++){
	            	 	if(rows[i].TAA_STATUS=='1'||rows[i].TAA_STATUS=='0'){
						nui.alert("选中记录包含已提交审核的记录，请重新选择");
						return;
						}else{arr.push(rows[i].TAA_ID);}
	            	 }
	            	 var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
	            	 //提交数据
                     nui.ajax({
                        url: "com.gotop.xmzg.achieve.appointment.appointment_submit.biz.ext",
                        type: "post",
                        data: nui.encode({"arr":arr}),
                        contentType:'text/json',
                        success: function (text) {
                           nui.hideMessageBox(load);  //隐藏遮罩层
                     	   var code = text.code;
                     	   var msg = text.msg;
                     	   nui.alert(msg, "系统提示", function(action){});
                     	   grid.reload();
                        }
                    });
	             }
			});
			
		}
	}
	
  </script>
</body>

</html>
<%@page import="com.gotop.xmzg.files.FilesDownloadUtils"%>
<%@page import="java.io.File"%>
<%@page import="com.eos.server.dict.DictManager"%>
<%@page import="com.pfpj.foundation.database.DatabaseExt"%>
<%@page import="java.util.HashMap"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false" %>
<% 
	//测试本地固定文件地址
	// String path =System.getProperty("catalina.home")+File.separator+"webapps/default/uploads/files/"+request.getParameter("fileName");
	/*  String path1 = System.getProperty("catalina.home"); //获取当前项目tomcat路径
	if(path1==null){
		String rootpath =  this.getClass().getClassLoader().getResource("/").getPath();
		path1 = rootpath.substring(0, rootpath.length() - 17);
	}
	System.out.println("下载文件路径1----"+path1); */
	
 	//从数据表获取
	String AFFILIATED_ID = request.getParameter("fileId");
	System.out.println("附件Id---"+AFFILIATED_ID);
	//获取配置路径
	Object[] objs = DatabaseExt.queryByNamedSql("default", "com.gotop.xmzg.files.fileList.query_Files_affi", AFFILIATED_ID);
	String dirPath = "";//路径
	String dirName = "";//名称
    HashMap<String,Object> obj = null;
    	if(objs.length != 0){
    		obj = (HashMap<String,Object>)objs[0];
    		dirPath = (String) obj.get("AFFILIATED_ADDRESS");
    		dirName = (String) obj.get("AFFILIATED_NAME");
    	}else{
    		System.out.println("路径不存在");
    	}
   // String path=dirPath+File.separator+request.getParameter("fileName");
    //String path=path1+File.separator+"webapps/default/"+dirPath;
    String path=dirPath;
    System.out.print("----下载路径----"+path);
   	//下载文件
   	File file = new File(path);
   	String exists = null;
   	if(file.exists()){
   		exists = "true";
		//普通文件下载方式
		FilesDownloadUtils.downloadFile2(path,dirName,response);
	 	out.clear();  
     	out = pageContext.pushBody();
   	}else{
   		exists = "false";
   	}
%>	

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<title>文件下载</title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
</head>
<body>
	<script type="text/javascript">
		var exists = "<%=exists%>";
		if(exists == "false"){
			alert("文件不存在");
		}
	</script>
</body>
</html>
<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): lyl
  - Date: 2019-11-25
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<script type="text/javascript" src="<%=request.getContextPath() %>/files/process/js/swfupload/swfupload.js"></script>
<script type="text/javascript" src="<%=request.getContextPath() %>/files/process/js/swfupload/handlers.js"></script>

<title>待审核清单</title>
</head>
<body style="margin:0px;width:100%;overflow:hidden;" >
	<div class="search-condition">
		<div class="list">
			<div id="filesForm">
				<div class="nui-hidden"  name="condition.userOrgId" id="userOrgId"></div>
				<div class="nui-hidden"  name="condition.userId"  id="userId" ></div>
		   		<table class="table" style="width:100%;">
		      		<tr>
						<td align="right">档案种类：</td>
						<td>
							<input id="files_type" class="nui-dictcombobox nui-form-input" name="queryData.FILES_TYPE"  emptyText="请选择"
          					valueField="dictID" textField="dictName" dictTypeId="FILES_TYPE" showNullItem="true" nullItemText="请选择"/>
						</td>
						<td align="right">档案名称：</td>
						<td>
							<input name="queryData.FILES_NAME" class="nui-textbox" style="width:180px;"/>
						</td>
			        	<td align="right">客户/产品经理名称：</td>
						<td>
							<input name="queryData.EMPNAME"  class="nui-textbox" style="width:180px;"/>
						</td>
						<td rowspan="12" class="btn-wrap">
							<a class="nui-button" iconCls="icon-search" onclick="searchData();">查询</a>
							<a class="nui-button" iconCls="icon-reset"  onclick="clean()"/>重置</a>
						</td>
				
					</tr>
		    	</table>
		  	</div>
		</div>
	</div>
	<div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      	<table style="width:100%;">
        	<tr>
           		<td style="width:100%;">
			     	<a class="nui-button" iconCls="icon-add" onclick="agree()" >同意</a>
					<a class="nui-button" iconCls="icon-edit" onclick="back();">退回</a> 
					<a class="nui-button" iconCls="icon-node" onclick="fileDetail();">档案明细</a>  
	             	<a class="nui-button" iconCls="icon-node" onclick="operateDetail">操作明细</a>   
           		</td>
        	</tr>
      	</table>
    </div>
    <!-- 档案主表信息 -->
  	<div class="nui-fit" id="fieldset1">
	 	<div id="grid"  dataField="resultList" idField="dicttypeid" class="nui-datagrid" style="width:100%;height:100%;" 
	 	allowCellWrap = "true"
	  	url="com.gotop.xmzg.files.fileList.queryAuditFileList.biz.ext" sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" >
	    	<div property="columns">
	    		<div type="checkcolumn"></div>
	    		<div field="INFORMATION_ID" class="nui-hidden" visible="false">ID</div>
		        <div field="FILES_TYPE_NAME" headerAlign="center">档案种类</div>
		        <div field="FILES_NAME" headerAlign="center">档案名称</div>
		        <div field="DEAL_NAME" headerAlign="center">归属机构</div>
		        <div field="EMP_NAME" headerAlign="center">客户经理</div>
		        <div field="SUB_TIME" headerAlign="center">提交时间</div>
		        <div field="AFFILIATED_NAMES" headerAlign="center" renderer="onActionRender">附件</div>
		    </div>
	 	</div>
  	</div>
  	<div id="addAcsWindow" class="nui-window" title="新增附件" style="width:650px;" 
	    showModal="true" allowResize="true" allowDrag="true">    
	   <div >
	    	<form id="acsForm" method="post" enctype="multipart/form-data">
		     <input class="nui-hidden" name="map.INFORMATION_ID" id="map.INFORMATION_ID"/>
	        <table border="0" style="width:500px;height:100px;" align="center">
				
				 <tr>
			        <th class="nui-form-label"><label for="map.device_serial$text">上传附件：</label></th>
			        <td colspan="7"  >
			           <div style="display: inline; border: solid 1px #7FAAFF; background-color: #C5D9FF; padding: 2px;width:200px;">   
				            <span id="spanButtonPlaceholder1"></span>
				         </div>
						<div id="divFileProgressContainer1"></div>
				        <div id="thumbnails">
				             <table id="infoTable" border="1" width="700" style="display: inline; border: none; padding: 2px;margin-top:8px;"> 
				            
				            </table>
				        </div>
				        
				        <div id="listbox1" class="nui-listbox" style="width:100%;height:80px;" textField="AFFILIATED_NAME" valueField="AFFILIATED_ID" 
					           dataField="resultList"  onvaluechanged="onListBoxValueChanged">
					    </div>
				        
			        </td>
      			</tr>
      			<tr align="center">
					<td colspan="2">
						<a class="nui-button" onclick="startUpload()">确认</a>&nbsp;
					</td>
				</tr>
	        </table>
	        </form>
	   </div>
	</div>
</body>
</html>
<script type="text/javascript">
	nui.parse();
	var form = new nui.Form("filesForm");
	var grid = nui.get("grid");
	var data = form.getData(true,true);
	grid.load(data);
	
	function searchData(){	
		var form = new nui.Form("filesForm");
		var data = form.getData(true,true);
    		grid.load(data);
    }
    
     window.onload = function () {
    		onload2();
    		onload3();
    		
    	};
    	
    //新增附件  
   	var addAcsWindow = nui.get("addAcsWindow");
    
    //新增附件	
    function addAccessory(value){
   		var row = grid.getRow(value);
   		var INFORMATION_ID = row.INFORMATION_ID;
   		nui.get("map.INFORMATION_ID").setValue(INFORMATION_ID);
    	//显示新增附件窗口 
   		addAcsWindow.show();
    }
    
  
	
    //审核同意
    function agree(){
    	var rows = grid.getSelecteds();
      	if(rows.length > 0){
	        nui.confirm("确定审核同意选中档案？","系统提示",function(action){
	        	if(action=="ok"){
		        	var spStatus = '3';//同意审核，审批状态变更为已审核
		      		for (var i=0;i<rows.length;i++){
			        		rows[i].SP_STATUS = spStatus;
		        		}
		            var json = nui.encode({upDatas:rows});
		           	var a= nui.loading("正在审核中,请稍等...","提示");
		           	var URL="com.gotop.xmzg.files.fileList.updateFileStatus.biz.ext";
			        	$.ajax({
			          		url:URL,
			          		type:'POST',
			          		data:json,
			          		cache: false,
			          		contentType:'text/json',
			          		success:function(text){
				          		nui.hideMessageBox(a);
				            	var returnJson = nui.decode(text);
								if(returnJson.flag == 1){
									nui.alert("审核通过", "系统提示", function(action){
										searchData();
									});
								}else{
									nui.alert("操作失败", "系统提示");
									grid.unmask();
								}
			          		}
			        	});
		     	}
			});
      	}else{
        	nui.alert("请至少选中一条记录！");
      	}
    }
    //审核退回
    function back(){
    	var rows = grid.getSelecteds();
      	if(rows.length > 0){
	        nui.confirm("确定退回选中档案？","系统提示",function(action){
	        	if(action=="ok"){
		        	var spStatus = '1';//审核退回，审批状态变更为待复核
		      		for (var i=0;i<rows.length;i++){
			        		rows[i].SP_STATUS = spStatus;
		        		}
		            var json = nui.encode({upDatas:rows});
		           	var a= nui.loading("正在审核中,请稍等...","提示");
		           	var URL="com.gotop.xmzg.files.fileList.updateFileStatus.biz.ext";
			        	$.ajax({
			          		url:URL,
			          		type:'POST',
			          		data:json,
			          		cache: false,
			          		contentType:'text/json',
			          		success:function(text){
				          		nui.hideMessageBox(a);
				            	var returnJson = nui.decode(text);
								if(returnJson.flag == 1){
									nui.alert("已退回档案，请重新复核", "系统提示", function(action){
										searchData();
									});
								}else{
									nui.alert("操作失败", "系统提示");
									grid.unmask();
								}
			          		}
			        	});
		     	}
			});
      	}else{
        	nui.alert("请至少选中一条记录！");
      	}
    }
   
    
    //重置查询信息
	function clean(){
	   	//不能用form.reset(),否则会把隐藏域信息清空
	   	form.reset();  
    }
    
    //机构树回显
     function OrgonButtonEdit(e){
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/files/archiveList/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            
                        }
                    }
                }
            });            
             
        }   
        
      //操作列：查看/新增附件
      function onActionRender(e){
      		var row  = grid.getRow(e.rowIndex);
      		var s =e.value;
      		var d = row.AFFILIATED_IDS;
      		 if(s==null){
	           	s="<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\"  onclick=\"addAccessory("+e.rowIndex+")\">"+"新增附件"+"</a>";
      		}else{
      			var names = s.split(",");
      			var ids = d.split(",");
      			var a = [];
      			for(var i=0;i<names.length;i++){
		           var s1=	"<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\"  onclick=\"downAccessory(\'"+ids[i]+"\',\'"+names[i]+"\')\">"+names[i]+"</a>";
		           a.push(s1);
	            }
	            s = a; 
      		} 
           	return s;
      }
        
    //档案明细
     function fileDetail(){
    	var rows = grid.getSelecteds();
    	var row = grid.getSelected();
    	var detail = '';
    	if(rows.length>1){
    		return nui.alert("请只选择一条记录进行明细查看！","提示");
    	}
       	if(row!=null){
       		var fy = row.FILES_TYPE;
       		//综合类档案
       		if(noun(fy)){
       			other(fy,row);
       		}else {     //基础类档案
       			oneToNine(row,fy);
       		}
       	}
    }
    
     function oneToNine(row,fy){
    	var TABLE_NAME = getDetailTableName(fy);
	       		//获取档案详情
	       		var data={queryData:{INFORMATION_ID:row.INFORMATION_ID,FILES_TYPE:row.FILES_TYPE,TABLE_NAME:TABLE_NAME}};
	       		var json = nui.encode(data);
			    $.ajax({
			        url:"com.gotop.xmzg.files.archiveList.getDetails.biz.ext",
			        type:'POST',
			        data:json,
			        cache:false,
			        async:false,
			        contentType:'text/json',
			        success:function(text){
			        	obj = nui.decode(text.resultList);
			        	var detail = obj[0];
			        	debugger;
			        	row = detail;
			    	}
				});
		    nui.open({
		    	url:"<%=request.getContextPath()%>/files/fileLedger/archiveDetail.jsp",
	          	title:'档案详情',
	          	width:1150,
          		height:580,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            //方法1：后台查询一次，获取信息回显
		            /* var data = row;
		            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
		            //方法2：直接从页面获取，不用去后台获取
		            var filesType=row.FILES_TYPE;
		            var data={};
		            if(filesType == '01'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row,editData1:row}};
					}else if(filesType == '02'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row,editData2:row}};
					}else if(filesType == '03'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row,editData3:row}};
					}else if(filesType == '04'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row,editData4:row}};
					}else if(filesType == '05'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row,editData5:row}};
					}else if(filesType == '06'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row,editData6:row}};
					}else if(filesType == '09'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row,editData9:row}};
					}else{
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		        	
		        }
		    });
    }
    
     function other(fy,row){
		if(fy == '15'){
			//信审档案详情界面
			path = "<%=request.getContextPath()%>/files/archiveList/xsDetail.jsp";
		}else {
			//综合类档案的其它档案详情界面
			var path = "<%=request.getContextPath()%>/files/archiveList/otherDetail.jsp";
		}
		
	    nui.open({
	    	url:path,
          	title:'编辑',
          	width:1150,
      		height:580,
          	onload:function(){
	        	var iframe = this.getIFrameEl();
	            //方法1：后台查询一次，获取信息回显
	            /* var data = row;
	            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
	            //方法2：直接从页面获取，不用去后台获取
	            var filesType=row.FILES_TYPE;
	            var data = {pageType:'edit',filesType:filesType,record:{editData:row}};
            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	        },
	        ondestroy:function(action){
	        	
	        }
	    });
    }
    
    
    //根据档案种类获取详情表名
	function getDetailTableName(filesType){
		if(filesType == '01'){
				return 'T_FILES_RETAIL_CREDIT';
			}else if(filesType == '02'){
				return 'T_FILES_RETAIL_DISBURSE';
			}else if(filesType == '03'){
				return 'T_FILES_PERSON_CREDIT';
			}else if(filesType == '04'){
				return 'T_FILES_PERSON_DISBURSE';
			}else if(filesType == '05'){
				return 'T_FILES_BILL_HONOUR';
			}else if(filesType == '06'){
				return 'T_FILES_BILL_DISCOUNT';
			}else if(filesType == '07'){
				return 'T_FILES_COOPERATE_ORG';
			}else if(filesType == '08'){
				return 'T_FILES_REFUSE_LOAN';
			}else if(filesType == '09'){
				return 'T_FILES_CREDIT';
			}
	}
	
	
	//操作明细
   function operateDetail(){
       var row = grid.getSelected();
       var rows = grid.getSelecteds();
       if(rows.length>1){
    		return nui.alert("请只选择一条记录进行查看！","提示");
    	}
       if(row!=null){
       var INFORMATION_ID=row.INFORMATION_ID;
	       nui.open({
	          url:"<%=request.getContextPath() %>/files/archiveList/operationLog.jsp?infoId="+INFORMATION_ID,
	          title:'档案操作流水',
	          width:1200,
	          height:550,
	          onload:function(){
	             /* var iframe = this.getIFrameEl();
	               var data = {pageType:"edit",record:{map:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法 */
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	             }
	          }
	       });
       }else{
           nui.alert("请选中一条记录！");
       }
    }
    
    //附件文本框
     function onListBoxValueChanged(e) {
	            var listbox = e.sender;
	            var value = listbox.getValue();
	            var items=listbox.getSelecteds();
	            var filepath=items[0].AFFILIATED_ADDRESS;
	            var jsontemp=listbox.getData();
	            nui.confirm("确定删除该附件？","系统提示",function(action){
           			// var json = nui.encode({AFFILIATED_ID:value,AFFILIATED_ADDRESS:filepath});
           			 var json = {AFFILIATED_ID:value,AFFILIATED_ADDRESS:filepath};
		             if(action=="ok"){ 
		             	arr.push(json);
		             	//arr.push("AFFILIATED_ID",AFFILIATED_ID);
		             	//arr.push("AFFILIATED_ADDRESS",AFFILIATED_ADDRESS);
		             	
				        listbox.removeItems(items);
				     } else{
					     listbox.load(jsontemp);
				     }
		            
				   });
	        }
	//下载附件
	function downAccessory(id,name){
       	var fileId = id;
       	var fileName = name;
       	var url="<%=request.getContextPath()%>/files/fileList/downloadFile.jsp?fileId="+fileId+"&fileName="+fileName;
		window.location.replace(url);
	  }
	  
	  
	   //附件上传
        
    	var files=new Array();
		var pics=new Array;
		var swfu1;
		function onload2() {
			swfu1 = new SWFUpload({
				upload_url: "com.gotop.xmzg.files.process.simuUpLoad.biz.ext",
				use_query_string:true,
				// File Upload Settings
				file_size_limit : "10 MB",	// 文件大小控制
				file_types : "*.dwg;*.psd;*.rtf;*.eml;*.doc;*.xls;*.mdb;*.ps;*.pdf;*.docx;*.xlsx;*.rar;*.wav;*.avi;*.rm;*.mpg;*.mpg;*.mov;*.asf;*.mid;*.gz",
				file_types_description : "Web Files",//指定在文件选取窗口中显示的文件类型描述
				file_upload_limit : "0",//把该属性设为0时表示不限制文件的上传数量
								
				file_queue_error_handler : fileQueueError,
				/*fileQueueError:当文件添加到上传队列失败时触发此事件，
				失败的原因可能是文件大小超过了你允许的数值、文件是空的或者文件队列已经满员了等。*/
				
				
				file_dialog_complete_handler : fileDialogComplete,//选择好文件后提交
				
				file_queued_handler : fileQueued,//当一个文件被添加到上传队列时会触发此事件，提供的唯一参数为包含该文件信息的file object对象
				upload_progress_handler : uploadProgress,
				upload_error_handler : uploadError,
				upload_success_handler : uploadSuccess,
				upload_complete_handler : uploadComplete,
				button_placeholder_id : "spanButtonPlaceholder1",//指定一个dom元素,该dom元素在swfupload实例化后会被Flash按钮代替，这个dom元素相当于一个占位符
				button_width: 150,
				button_height: 18,
				button_text : '请选择文件... ',
				button_text_style : ' .button { font-family: Helvetica, Arial, sans-serif; font-size: 12pt; } .buttonSmall { font-size: 10pt;}',
				button_text_top_padding: 0,
				button_text_left_padding: 18,
				button_window_mode: SWFUpload.WINDOW_MODE.TRANSPARENT, //允许Flash按钮透明显示
				button_cursor: SWFUpload.CURSOR.HAND,//鼠标以手形显示
				
				// Flash Settings
				flash_url : "<%=request.getContextPath() %>/files/process/js/swfupload/swfupload.swf",

				custom_settings : {
					upload_target : "divFileProgressContainer1",
					info_target:"infoTable"
				},
				// Debug Settings
				debug: false  //是否显示调试窗口
			});
		};
		var swfu2;
		function onload3() {
			swfu2 = new SWFUpload({
				upload_url: "com.gotop.xmzg.files.process.simuUpLoad.biz.ext",
				use_query_string:true,
				// File Upload Settings
				file_size_limit : "10 MB",	// 文件大小控制
				file_types : "*.jpg;*.gif;*.png;*.bmp",
				file_types_description :"Web Image Files",//指定在文件选取窗口中显示的文件类型描述
				file_upload_limit : "0",//把该属性设为0时表示不限制文件的上传数量
								
				file_queue_error_handler : fileQueueError,
				/*fileQueueError:当文件添加到上传队列失败时触发此事件，
				失败的原因可能是文件大小超过了你允许的数值、文件是空的或者文件队列已经满员了等。*/
				
				
				file_dialog_complete_handler : fileDialogComplete,//选择好文件后提交
				
				file_queued_handler : fileQueued2,
				upload_progress_handler : uploadProgress2,
				upload_error_handler : uploadError,
				upload_success_handler : uploadSuccess,
				upload_complete_handler : uploadComplete2,
				button_placeholder_id : "spanButtonPlaceholder2",//指定一个dom元素,该dom元素在swfupload实例化后会被Flash按钮代替，这个dom元素相当于一个占位符
				button_width: 150,
				button_height: 18,
				button_text : '请选择文件... ',
				button_text_style : ' .button { font-family: Helvetica, Arial, sans-serif; font-size: 12pt; } .buttonSmall { font-size: 10pt;}',
				button_text_top_padding: 0,
				button_text_left_padding: 18,
				button_window_mode: SWFUpload.WINDOW_MODE.TRANSPARENT, //允许Flash按钮透明显示
				button_cursor: SWFUpload.CURSOR.HAND,//鼠标以手形显示
				
				// Flash Settings
				flash_url : "<%=request.getContextPath() %>/files/process/js/swfupload/swfupload.swf",

				custom_settings : {
					upload_target : "divFileProgressContainer2",
					info_target:"infoTable2"
				},
				// Debug Settings
				debug: false  //是否显示调试窗口
			});
		};
		function fileDialogComplete(e){
			   this.startUpload();//文件上传到临时文件夹
			}
		
		function uploadSuccess ( file, server, received){
			    files.push(nui.decode(server).file);//把文件对象放入一个数组，等待业务数据同时提交
			    console.log("附件上传成功："+files);
			} 
		
		//新增附件上传
		function startUpload(){
			var form = new nui.Form("acsForm");
			form.validate(); 
	        if(form.isValid()==false) return false;
	        var data = form.getData(false,true);
	        data.files=files;
	        var json = nui.encode(data);
	        console.log("新增附件--"+nui.encode(json));
         	var a= nui.loading("正在提交中,请稍等...","提示");
        $.ajax({
                url:"com.gotop.xmzg.files.fileList.addAccessory.biz.ext",
                type:'POST',
                data:json,
                cache:false,
                contentType:'text/json',
                success:function(text){
                nui.hideMessageBox(a); //关闭加载提示（即正在提交中，请稍等）
                   var returnJson = nui.decode(text);
                   if(returnJson.exception == null){
                        files.splice(0, files.length);//文件上传成功清空文件对象数组避免重复提交
                        alert("提交成功");
                        window.location.href="<%=request.getContextPath() %>/files/fileList/toAuditList.jsp";
                     }else{
                         alert("提交失败");
                         window.history.go(-1) ;
                        }
                   }
             });
		  
    	}
    	
    function onCancel(){
      //CloseWindow("cancel");
       window.history.go(-1) ;
    }
    
    function noun(fy){
		if(fy == '10'){
			return true;
		}else if(fy == '11'){
			return true;
		}else if(fy == '12'){
			return true;
		}else if(fy == '13'){
			return true;
		}else if(fy == '14'){
			return true;
		}else if(fy == '15'){
			return true;
		}else {
			return false;
		}
	}
    	
</script>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): JW
  - Date: 2018-07-25 11:08:29
  - Description:
-->
<!-- $Header: svn://**************:7029/svn/xm_project/xm_zgxt/com.gotop.fileManger.jw/src/webcontent/filegl_list.jsp 1935 2018-09-12 03:44:13Z jw $ -->
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
 %>
<head>
<title>文件列表浏览和查询</title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=EDGE">
<%@include file="/coframe/tools/skins/common.jsp"%>
<script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
<style>
#table1 .tit {
	height: 10px;
	line-height: 10px;
}

#table1 td {
	height: 10px;
	line-height: 10px;
}

#filelists tr,#filelists td {
	text-align: center;
}
</style>
</head>
<body>
	<div class="nui-splitter" style="width: 100%; height: 100%;">
		<div size="240" showCollapseButton="true">
			<input id="parentListId" name="parentListId" class="nui-hidden" />
			<div class="nui-toolbar" style="padding: 2px; border-top: 0; border-left: 0; border-right: 0;">
				<span style="padding-left: 5px;">名称：</span> <input id="meun_box" class="nui-textbox" style="width: 100px;" onenter="onKeyEnter" /> <a class="nui-button"
					iconCls="icon-search" plain="true" onclick="search('meun_box')">查找</a>
			</div>
			<div class="nui-fit">
				<!--
							showTreeIcon:显示节点图标
							textField:节点文本字段
							idField:值字段
							parentField:父节点字段
						-->
				<ul id="menu_tree" class="nui-tree" style="width: 100%;" showTreeIcon="true" resultAsTree="true" expandOnLoad="0" onnodeclick="onNodeClick" idField="LISTID"
					textField="LISTNAME" dataField="nodes">
				</ul>
			</div>
		</div>
		<div showCollapseButton="true">
			<div class="search-condition">
				<div class="list">
					<div id="form1">
						<div>
								<label>文件名：</label> 
								<input id="filename" class="nui-textbox" style="width: 15%;"/> 
								<label>关键字：</label> 
								<input id="filekey" class="nui-textbox" style="width: 15%;"/>
								<label>目录：</label> 
								<input id="filePath" class="nui-textbox" style="width: 15%;"/>
								<label>状态：</label>
								<input id="status" name="status" class="nui-combobox" emptyText="全部"
									showNullItem="true" nullItemText="全部"/>
								<a class="nui-button" style="width: 8%;" iconCls="icon-search" onclick="search1">查询</a>
						</div>
					</div>
				</div>
			</div>
			<div class="nui-toolbar" style="padding: 2px; border-top: 0; border-left: 0; border-right: 0;">
				<table style="width:100%;">
			      <tr>
			        <td style="width:100%;">
						<a class="nui-button" iconCls="icon-add" plain="true" onclick="addRow()">新增文件</a>
						<a class="nui-button" iconCls="icon-search" plain="true" onclick="authority()">设置权限</a>
						<a class="nui-button" iconCls="icon-edit" plain="true" onclick="updateRow()">修改文件信息</a>
						<a class="nui-button" iconCls="icon-folderopen" plain="true" onclick="updateMeun()">修改所属目录</a>
						<a class="nui-button" iconCls="icon-remove" plain="true" onclick="delfile()">删除文件</a>
						<a class="nui-button" iconCls="icon-upload" plain="true" onclick="ccfile()">重传文件</a>
						<a class="nui-menubutton" plain="true" menu="#popupMenu">修改状态</a>
					</td>
				 </tr>
    			</table>						
				<ul id="popupMenu" class="nui-menu" style="display:none;">
				    <li iconCls="icon-ok" onclick="updatefilestatus(1)">正常</li>
		            <li iconCls="icon-no" onclick="updatefilestatus(0)">停用</li>	            				 
				</ul>
			</div>
			<div id="region1" class="nui-fit">
				<input id="menu_id" class="nui-hidden"/> <input id="ids" class="nui-hidden" />
				<div id="filelists" class="nui-datagrid" style="width: 100%; height: 100%;" allowCellSelect="true" allowCellEdit="true" multiSelect="true" pageSize="10"
					sizeList="[5,10,20,30]" onpagechanged="onPageChanged" allowAlternating="true">
					<div property="columns">
						<div type="checkcolumn"></div>
						<div field="FILEID" visible="false"></div>
						<div field="PREVIEWNAME" visible="false"></div>
						<div field="REALFILENAME" visible="false"></div>
						<div field="ONLYNAME" visible="false"></div>
						<div field="LISTID" visible="false"></div>
						<div field="FILENAME" width="120" headerAlign="center" allowSort="true">文件名</div>
						<div field="FILENO" width="120" headerAlign="center" allowSort="true">文号</div>
						<div field="FILEKEY" width="120" headerAlign="center" allowSort="true">关键字</div>
						<div field="UPLOADNAME" width="120" headerAlign="center" allowSort="true">上传人员</div>
						<div field="FILEPATH" width="120" headerAlign="center" allowSort="true" renderer="onGenderRenderer">所属目录</div>
						<div field="FILESTATUS" width="120" headerAlign="center" allowSort="true" renderer="onStatusRenderer">文件状态</div>
						<div field="CREATETIME" width="120" headerAlign="center" allowSort="true">上传时间</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<ul id="contextMenu" class="nui-contextmenu">
	    <li iconCls="icon-search" onclick="authority">修改权限</li>
	    <li iconCls="icon-edit" onclick="updateRow">修改文件信息</li>
		<li iconCls="icon-upload" onclick="ccfile">重新上传</li>
		<li iconCls="icon-remove" onclick="delfile">删除文件</li>
		<li >
		<span >修改状态</span>
			<ul>
			    <li iconCls="icon-ok" onclick="updatefilestatus(1)">正常</li>
	            <li iconCls="icon-no" onclick="updatefilestatus(0)">停用</li>	            
			</ul>
		</li>
	</ul>
	<script type="text/javascript">
	 nui.parse();
     var tree = nui.get("menu_tree");
	var userid="<%=userObject.getUserId()%>";
	var orgid="<%=userObject.getUserOrgId()%>";
	var permId="";
	$(document).ajaxError(function(evt, req, settings){
	    if(req && (req.status === 401)){ 
	    	nui.alert('登陆超时！请重新登陆！', '提示', function() {
	    		window.location="/default/coframe/auth/login/login.jsp";
	        });
	    	return false; 
	    }		
	});
	</script>
	
	<script src="<%= request.getContextPath() %>/js/meun.js" type="text/javascript"></script>
	<script type="text/javascript">
		nui.parse();
		menulist();
		var username="<%=userObject.getUserName()%>";
		var tpath="<%= request.getContextPath() %>";
		//文件列表
		var file = nui.get("filelists");
		//目录底下所选id集合
		var ides = nui.get("ids");
		//文件名称查询条件
		var filename = nui.get("filename");
		//文件关键字查询条件
		var filekey = nui.get("filekey");
		//文件关键字查询条件
		var filePath = nui.get("filePath");
		//点击目录信息
		var menuid = nui.get("menu_id");
		//文件状态
		var Opinions = [{ id: "1", text: '正常' }, { id: "0", text: '停用'}];
		nui.get("status").load(Opinions);
		var status1=nui.get("status");
		//文件状态
		var menuval="";
		//判断当前操作是不是模糊查询
		var opr="";
		//树左键点击触发事件
		 function onNodeClick(e){
		 	opr="";//清楚模糊查询标志
			var id=e.node.LISTID;
			menuval={id:id,name:e.node.LISTNAME,pid:e.node.LISTPID};
			menuid.setValue(menuval);
			var ids="";
			if(id!=0){
				/* //获取子目录
				var children = e.node.children;
				ids+=e.node.LISTID+",";//获取到1审批资料查询
				if(isNull(children)){
					for ( var i=0;i<children.length;i++) {
						ids+=children[i].LISTID+",";
						var childs=children[i].children;
						if(isNull(childs)){
							ids+=isChildren(childs);
						}
					}
				}
				ides.setValue(ids); */
				
				ides.setValue(id);				
				if(userid!=1&&userid!=2){
					if(menuid.getValue().id==0){
						nui.alert("无权操作当前目录");
						return false;
					}else{
			  		 if(!qryListAuth(userid,orgid,menuid.getValue().id,"1")){
				    		file.setTotalCount(0);
							file.setData("");
							return false;
					 	}
					 } 
				}
				filelist(0,id, 0, file.pageSize,"","","","");
			}			
		}
		 
		//数据加载前发生（设置分页，页数改变时）
		 file.on("beforeload", function (e) {
	            e.cancel = true;
	            var pageIndex = e.data.pageIndex; 
	            var pageSize = e.data.pageSize;
	            var listid=ides.getValue();
	            if(opr=="search"){
	            	filelist(1,0, pageIndex, pageSize,filename.getValue(),filekey.getValue(),status1.getValue(),filePath.getValue());
	            }else{
	            	if(isNull(listid)){
	            		filelist(0,listid, pageIndex, pageSize,filename.getValue(),filekey.getValue(),status1.getValue(),filePath.getValue());
	      			}
	            }
	            
	      });
		//条件查询按钮事件
		 function search1() {
	        /*  var listid=ides.getValue();	    
	         if(isNull(listid)){
	        	 filelist(0,listid, 0, file.pageSize,filename.getValue(),filekey.getValue(),status1.getValue(),filePath.getValue());
	         }else{
	         	 filelist(1,0, 0, file.pageSize,filename.getValue(),filekey.getValue(),status1.getValue(),filePath.getValue());
	         	 //nui.alert("请选择目录后在操作", "系统提示", function(action){});
	         } */
	         opr="search";
			 filelist(1,0, 0, file.pageSize,filename.getValue(),filekey.getValue(),status1.getValue(),filePath.getValue());
		 }
		//加载文件列表数据
		/* function filelist(listid,page,size,filenames,filekey,status){
			var json={"listId":listid,"page":page,"size":size,
				"fileName":filenames,"fileKey":filekey,
				"uploadName":"","fileStatus":status,
				"permId":permId,"userId":userid,
						"instId":orgid};
			$.ajax({
				type : 'POST',
				url : "com.gotop.fileManger.hyq.entity.fileBrowser.queryFile.biz.ext",
				dataType : "json",//返回的格式为json
				data : json,
				contentType : "application/x-www-form-urlencoded",
				success : function(data) {

					if(data.msg.resCode=="1"){
					//console.log(data.msg);
						file.setTotalCount(data.msg.count);
						file.setData(data.msg.file);
						file.setPageSize(size);
						file.setPageIndex(page);
					}else{
						file.setTotalCount(0);
						file.setData("");
						file.setPageSize(size);
						file.setPageIndex(page);
					}
				}
			});
		} */
		function filelist(type,listid,page,size,filename,filekey,status,filepath){
				var url="";
				var req=new Object();
				if(type==0){
					//点击当前目录
					url="com.gotop.fileManger.hyq.entity.fileBrowser.queryFile.biz.ext";
					req.listId=listid;
				}else{
					//模糊查询
					req.type="1";
					url="com.gotop.fileManger.hyq.entity.fileBrowser.queryAllAuthFile.biz.ext";			
				}
				req.page=page;
				req.size=size;
				req.filePath=filepath;
				req.fileName=filename;
				req.fileKey=filekey;
				req.fileStatus=status;
				req.uploadName="";
				req.userId=userid;
				req.instId=orgid;
				var json = nui.encode({"req":req}); //序列化成JSON
			$.ajax({
				type : 'POST',
				url : url,
				dataType : "json",//返回的格式为json
				data : json,
				contentType : "application/json",
				success : function(data) {
					
					if(data.msg.resCode=="1"){
						file.setTotalCount(data.msg.count);
						file.setData(data.msg.file);
						file.setPageSize(size);
						file.setPageIndex(page);
					}else{
						file.setTotalCount(0);
						file.setData("");
						file.setPageSize(size);
						file.setPageIndex(page);
					}
				}
			});
		}
		//设置文件状态
		function onStatusRenderer(e) {
			return nui.getDictText("FILE_STATUS",e.value);
		}
		
		
		
		//文件添加
		function addRow() {			
			if(isNull(menuid.getValue())&&opr!="search"){
				if(menuid.getValue().id!=0){
					if(userid!=1&&userid!=2){
						if(menuid.getValue().id==0){
							nui.alert("无权操作当前目录");
							return false;
						}else{
				  		 var permId="1";
				  		 if(!qryListAuth(userid,orgid,menuid.getValue().id,permId)){
				    		nui.alert("无权操作当前目录");
				    		return false;
						 	}
						 } 
					}
					nui.open({
		                url: "<%= request.getContextPath() %>/jw/filegl_add3.jsp",
		                width: 620, height: 500,title:"文件添加",
		                onload: function () {
		                	var iframe = this.getIFrameEl();
		                    iframe.contentWindow.SetData(menuid.getValue(),datalist,username);
		                },
		                ondestroy: function (action) {
		                	
		                	file.reload();
		                }
		            });
				}else{
					nui.alert("根目录不能添加文件");
				}
			}else{
				nui.alert("请选择目录");
			}
    	}
		//移动文件方法
		function updateMeun() {
			var currentId="";
			var listId=menuid.getValue().id;
			if(userid!=1&&userid!=2){
				 if(listId!="undefined"&&listId!=""&&listId!=null){
				 	if(listId==0){
						nui.alert("无权操作当前目录");
						return false;
					}else{
				  		 var permId="1";
				  		 if(!qryListAuth(userid,orgid,listId,permId)){
				    		nui.alert("当前目录无操作权限");
				    		return false;
						 }
					} 
				}
		 	} 
			if(isNull(menuid.getValue())&&opr!="search"){	
			var rows = file.getSelecteds();
			if(rows.length > 0){
				var addr = [];
				var oidpatharr=[];
				var oidpath="";
				for(var i=0; i<rows.length;i++){	
					//判断目录权限
					if(userid!=1&&userid!=2){
						 if(listId=="undefined"||listId==""||listId==null){
						 	currentId=rows[i].LISTID;
						 	if(currentId==0){
								nui.alert("无权操作当前目录");
								return false;
							}else{
						  		 var permId="1";
						  		 if(!qryListAuth(userid,orgid,currentId,permId)){
						    		nui.alert("所属目录无操作权限，请重新查询");
						    		return false;
								 }
							} 
						}
		 			} 				
	            	addr.push(rows[i].FILEID);
	            	oidpatharr.push(rows[i].FILEPATH);
	            }
				if(oidpatharr.length>0){
					var temp=rows[0].FILEPATH;
					for(var i=0; i<oidpatharr.length;i++){
						if(temp.indexOf(oidpatharr[i])!=0){
							nui.alert("多个文件移动时，请选择相同目录下的文件移动！");
							return false;
						}
					}
					oidpath=temp;
				}							
				nui.open({
	                url: "<%= request.getContextPath() %>/jw/update_menu.jsp",
	                width: 620, height: 170,title:"目录修改",
	                onload: function () {
	                	var iframe = this.getIFrameEl();
	                    iframe.contentWindow.SetData(addr,oidpath);
	                },
	                ondestroy: function (action) {
	                	
	                	file.reload();
	                }
	            });
				<%-- var nodes = [];
	            var fileName="";
	            for(var i=0; i<rows.length;i++){
	        		fileId=rows[i].FILEID;
	            	nodes.push(rows[i].FILEID);
	            	if(i>0&&i<=10){
		         		fileName+=",";
		        	}
		        	fileName+=rows[i].FILENAME;
	            } 
				nui.open({
	                url: "<%= request.getContextPath() %>/jw/update_menu.jsp",
	                width: 620, height: 170,title:"目录修改",
	                onload: function () {
	                	var iframe = this.getIFrameEl();
	                    iframe.contentWindow.SetData(menuid.getValue(),nodes,fileName);
	                },
	                ondestroy: function (action) {
	                	file.reload();
	                }
	            }); --%>
				}else{
					nui.alert("请选中一条记录");
				}
			}else{
				nui.alert("请选择目录");
			}
    	}
		
		//文件修改
		function updateRow() {
			if(isNull(menuid.getValue())&&opr!="search"){
				var rows = file.getSelecteds();			
				if(rows.length > 1){
					nui.alert("请选中一条记录");		
				}else if(rows.length > 0){
					//判断当前目录是否有修改权限
					var currentId="";
					var listId=menuid.getValue().id;
					if(userid!=1&&userid!=2){
						if(listId=="undefined"||listId==""||listId==null){
							currentId=rows[0].LISTID;
						}else{
							currentId=listId;
						}
						if(currentId==0){
								nui.alert("无权操作当前目录");
								return false;
						}else{
					  		 var permId="1";
					  		 if(!qryListAuth(userid,orgid,currentId,permId)){
					    		nui.alert("所属目录无操作权限，请重新查询");
					    		return false;
							 }
						} 
				 	} 
					var fileu=new Object();
					fileu.fileId=rows[0].FILEID;
					fileu.fileNo=rows[0].FILENO;
					fileu.fileKey=rows[0].FILEKEY;
					fileu.fileName=rows[0].FILENAME;
					fileu.realFileName=rows[0].REALFILENAME;
					fileu.onlyName=rows[0].ONLYNAME;
					fileu.file=rows[0].ONLYNAME;
					fileu.filePath=rows[0].FILEPATH;
					var json={"file":fileu};
					nui.open({
		                url: "<%= request.getContextPath() %>/jw/filegl_update2.jsp",
		                width: 620, height: 270,title:"文件修改",
		                onload: function () {
		                	var iframe = this.getIFrameEl();
		                    iframe.contentWindow.SetData(json);
		                },
		                ondestroy: function (action) {
		                	
		                	file.reload();
		                }
		            });
				}else{
					nui.alert("请选中一条记录");
				}
			}else{
				nui.alert("请选择目录");
			}
    	}
		//文件重传
		function ccfile(){
			if(isNull(menuid.getValue())&&opr!="search"){
				var rows = file.getSelecteds();			
				if(rows.length > 1){
					nui.alert("请选中一条记录");		
				}else if(rows.length > 0){
					//判断当前目录是否有修改权限
					var currentId="";
					var listId=menuid.getValue().id;
					if(userid!=1&&userid!=2){
						if(listId=="undefined"||listId==""||listId==null){
							currentId=rows[0].LISTID;
						}else{
							currentId=listId;
						}
						if(currentId==0){
								nui.alert("无权操作当前目录");
								return false;
						}else{
					  		 var permId="1";
					  		 if(!qryListAuth(userid,orgid,currentId,permId)){
					    		nui.alert("所属目录无操作权限，请重新查询");
					    		return false;
							 }
						} 
				 	}
					var fileu=new Object();
					fileu.fileId=rows[0].FILEID;
					fileu.filePath=rows[0].FILEPATH;
					fileu.realFilename=rows[0].REALFILENAME;
					fileu.onlyName=rows[0].ONLYNAME;
					fileu.fileName=rows[0].FILENAME;
					var json={"file":fileu};
					nui.open({
		                url: "<%= request.getContextPath() %>/jw/update_file.jsp",
		                width: 620, height: 170,title:"文件重传",
		                onload: function () {
		                	var iframe = this.getIFrameEl();
		                    iframe.contentWindow.SetData(json);
		                },
		                ondestroy: function (action) {
		                	
		                	file.reload();
		                }
		            });
				}else{
					nui.alert("请选中一条记录");
				}
			}else{
				nui.alert("请选择目录");
			}
		}
		//文件删除
		function delfile(){			
			var currentId="";
			var listId=menuid.getValue().id;
			if(userid!=1&&userid!=2){
				 if(listId!="undefined"&&listId!=""&&listId!=null){
				 	if(listId==0){
						nui无权操作当前目录lert("无权操作当前目录");
						return false;
					}else{
				  		 var permId="1";
				  		 if(!qryListAuth(userid,orgid,listId,permId)){
				    		nui.alert("当前目录无操作权限");
				    		return false;
						 }
					} 
				}
		 	}
			if(isNull(menuid.getValue())&&opr!="search"){
	            var rows = file.getSelecteds();
	            var nodes = [];
	            var fileName="";//文件名
	            var realFilename="";//真实文件名
	            var path=[];
	            for(var i=0; i<rows.length;i++){
	            	//判断目录权限
					if(userid!=1&&userid!=2){
						 if(listId=="undefined"||listId==""||listId==null){
						 	currentId=rows[i].LISTID;
						 	if(currentId==0){
								nui.alert("无权操作当前目录");
								return false;
							}else{
						  		 var permId="1";
						  		 if(!qryListAuth(userid,orgid,currentId,permId)){
						    		nui.alert("所属目录无操作权限，请重新查询");
						    		return false;
								 }
							} 
						}
		 			}	
	        		fileId=rows[i].FILEID;
	            	nodes.push(rows[i].FILEID);
	            	if(i==rows.length-1){
		        		realFilename+=rows[i].REALFILENAME;
		         		fileName+=rows[i].FILENAME;
	            	}else{
	            		realFilename+=rows[i].REALFILENAME+",";
		         		fileName+=rows[i].FILENAME+",";
	            	}
	            	path.push(rows[i].FILEPATH);
	            } 
	            if(path.length>0){
					var temp=rows[0].FILEPATH;
					for(var i=0; i<path.length;i++){
						if(temp.indexOf(path[i])!=0){
							nui.alert("多个文件修改时，请选择相同目录下的文件修改！");
							return false;
						}
					}
					path=temp;
				}
	             if (rows.length > 0) {
	            	nui.confirm("该文件将被删除，确定？","删除确认",function(action){
		            	if(action!="ok") return;
		            	var json = nui.encode({nodes:nodes});
	                    $.ajax({
	                        url: "com.gotop.fileManger.jw.action.filegl.delfile.biz.ext",
			                type: 'POST',
			                data: json,
			                cache: false,
			                contentType:'text/json',
	                        success: function (data) {
	                        	
	                        	if(data.msg.resCode=='1'){
	                        		content="["+username+"]"+"删除文件["+fileName+"]成功";
				 					sys_log(content,pathdecode(path+"["+fileName+"]"));
	                        		nui.alert("删除成功", "系统提示", function(action) {
	                					if (action == "ok" || action == "close") {         
	                						file.reload();
	                					}
	                				});
	                            }else{                                                
	                            	content="["+username+"]"+"删除文件["+fileName+"]失败";
				 					//sys_log(content,fileName);
				 					sys_log(content,"");
	                            	nui.alert("删除失败", "系统提示", function(action) {
	                					if (action == "ok" || action == "close") {
	                						file.reload();
	                					}
	                				});
	                            }
	                        	
	                        }
	                    });
	                });
	            } else {
	                nui.alert("请选中一条记录");
	            };
			}else{
				nui.alert("请选择目录");
			}
		}
		
		//文件状态修改
		function updatefilestatus(filestatus){
			var currentId="";
			var listId=menuid.getValue().id;
			if(userid!=1&&userid!=2){
				 if(listId!="undefined"&&listId!=""&&listId!=null){
				 	if(listId==0){
						nui.alert("无权操作当前目录");
						return false;
					}else{
				  		 var permId="1";
				  		 if(!qryListAuth(userid,orgid,listId,permId)){
				    		nui.alert("当前目录无操作权限");
				    		return false;
						 }
					} 
				}
		 	}
			if(isNull(menuid.getValue())&&opr!="search"){
				var rows = file.getSelecteds();
				var nodes = [];
				var boot=true;
				var fileName="";//真实文件名
				var realFilename="";//唯一文件名
				var path=[];
	            for(var i=0; i<rows.length;i++){
	        		//判断目录权限
					if(userid!=1&&userid!=2){
						 if(listId=="undefined"||listId==""||listId==null){
						 	currentId=rows[i].LISTID;
						 	if(currentId==0){
								nui.alert("无权操作当前目录");
								return false;
							}else{
						  		 var permId="1";
						  		 if(!qryListAuth(userid,orgid,currentId,permId)){
						    		nui.alert("所属目录无操作权限，请重新查询");
						    		return false;
								 }
							} 
						}
		 			}	
	        		fileId=rows[i].FILEID;
	            	nodes.push(rows[i].FILEID);
	            	if(rows[i].FILESTATUS==filestatus){
	            		boot=false;
	            	}
	            	if(i==rows.length-1){
	            		fileName+=rows[i].FILENAME;
	    	        	realFilename+=rows[i].REALFILENAME;
	            	}else{
	            		fileName+=rows[i].FILENAME+",";
	    	        	realFilename+=rows[i].REALFILENAME+",";
	            	}
	            	path.push(rows[i].FILEPATH);            	
	            }
	            if(path.length>0){
					var temp=rows[0].FILEPATH;
					for(var i=0; i<path.length;i++){
						if(temp.indexOf(path[i])!=0){
							nui.alert("多个文件修改时，请选择相同目录下的文件修改！");
							return false;
						}
					}
					path=temp;
				}
				if(rows.length > 0){
					if(boot){
						nui.confirm("是否修改","修改确认",function(action){
			            	if(action!="ok") return;
			            	updfilestrue(filestatus,nodes,fileName,realFilename,path);
						});
					}else{
						nui.confirm("文件中包含不同状态的文件是否要全部修改","修改确认",function(action){
			            	if(action!="ok") return;
			            	updfilestrue(filestatus,nodes,fileName,realFilename,path);
						});
					}
				
				}else{
					nui.alert("请选中一条记录");
				};
			}else{
				nui.alert("请选择目录");
			}
		}
		//修改状态ajax代码
		function updfilestrue(filestatus,nodes,fileName,realFilename,path){
			var json=nui.encode({"fileStatus":filestatus,"fileId":nodes});
			var start="";
			if(filestatus==0){
				start="停用";
			}else{
				start="正常";
			}
			$.ajax({
				type : 'POST',
				url : "com.gotop.fileManger.jw.action.filegl.updateFileStatus.biz.ext",
				dataType : "json",//返回的格式为json
				data : json,
				contentType:'text/json',
				success : function(data) {
					
					sys_log("["+username+"]"+"修改文件["+fileName+"]状态为["+start+"]",pathdecode(path+"["+fileName+"]"));//日志
					file.reload();
				}
			});
		}
		
		
		//修改权限
		function authority(){
			//判断当前目录是否有修改权限
			var currentId="";
			
			if(isNull(menuid.getValue())&&opr!="search"){
				var rows = file.getSelecteds();			
				if(rows.length > 1){
					nui.alert("请选中一条记录");		
				}else if(rows.length > 0){
					var listId=menuid.getValue().id;
					if(userid!=1&&userid!=2){
						if(listId=="undefined"||listId==""||listId==null){
							currentId=rows[0].LISTID;
						}else{
							currentId=listId;
						}
						if(currentId==0){
								nui.alert("无权操作当前目录");
								return false;
						}else{
					  		 var permId="1";
					  		 if(!qryListAuth(userid,orgid,currentId,permId)){
					    		nui.alert("所属目录无操作权限，请重新查询");
					    		return false;
							 }
						} 
				 	} 
					nui.open({
		                url: "<%= request.getContextPath() %>/jw/filegl_authority2.jsp",
								width : 620,
								height : 320,
								title : "权限修改",
								onload : function() {
									var iframe = this.getIFrameEl();
									iframe.contentWindow.SetData(rows[0].FILEID,rows[0].ONLYNAME,rows[0].FILENAME);
								},
								ondestroy : function(action) {
									
									file.reload();
								}
							});
				} else {
					nui.alert("请选中一条记录");
				}
			}else{
				nui.alert("请选择目录");
			}
		}

		/**
		 * 日志操作记录
		 * @param mod 模块
		 * @param content 内容
		 */
		function sys_log(content, name) {
			var json = nui.encode({
				"mod" : "文件模块",
				"content" : content,
				"name" : name
			}); //序列化成JSON
			$.ajax({
				url : "com.gotop.xmzg.util.Excel.syslog.biz.ext",
				type : "post",
				data : json,
				cache : false,
				contentType : 'text/json',
				success : function(data) {
					
				}
			});
		}
		window.onload = function () {
            $("#region1").bind("contextmenu", function (e) {
                var menu = nui.get("contextMenu");
                menu.showAtPos(e.pageX, e.pageY);
                return false;
            });
        }
	</script>
</body>
</html>
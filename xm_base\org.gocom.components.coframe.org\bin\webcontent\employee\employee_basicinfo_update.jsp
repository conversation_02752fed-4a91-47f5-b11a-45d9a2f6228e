<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): yangzhou
  - Date: 2013-03-21 11:24:50
  - Description:
-->
<head>
<title>员工基本信息修改</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<%@include file="/coframe/tools/skins/common.jsp" %>
<%@page import="com.eos.system.utility.StringUtil"%>
<script type="text/javascript" src="<%=contextPath%>/coframe/org/js/org_common.js"></script>
<style type="text/css">
    fieldset
    {
        border:solid 1px #aaa;
    }        
    .hideFieldset
    {
        border-left:0;
        border-right:0;
        border-bottom:0;
    }
    .hideFieldset .fieldset-body
    {
        display:none;
    }
</style>
</head>
<body>

<div style="padding-top:5px;overflow:hidden">
<form id="form">
	<div id="form1">
		<input class="nui-hidden" name="employee.orgid" />
		<input class="nui-hidden" name="employee.empid" id="empid"/>
		<input class="nui-hidden" name="employee.userId" id="empuserid"/>
		<input class="nui-hidden" name="user.userId" id="userId"/>
		<input class="nui-hidden" name="user.operatorId" id="operatorid"/>
		<input class="nui-hidden"  name="user.enddate" id="enddate" />
        <input class="nui-hidden"  name="user.startdate" id="startdate" />
	    <input class="nui-hidden" name="user.userName" id="userName"/>
	    <input class="nui-hidden"  id="userstatus"/>
	     
		<table style="width:100%;table-layout:fixed;" class="nui-form-table" >
			<tr>
				<td class="nui-form-label"><label for="empname$text">员工姓名：</label></td>
				<td><input style="width:100%" id="empname" class="nui-textbox" name="employee.empname" required="true" vtype="maxLength:50"/></td>
				<td class="nui-form-label"><label for="empcode$text">员工号：</label></td>
				<td><input style="width:100%" id="empcode" class="nui-textbox" name="employee.empcode" required="true" readonly = "true" vtype="maxLength:30" allowInput="false"/></td>
			</tr>		
			<tr class="odd">
				<td class="nui-form-label"><label for="gender$text">性别：</label></td>
				<td><input style="width:100%"  id="gender" name="employee.gender" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox" dictTypeId="COF_GENDER" /></td>
				<td class="nui-form-label"><label for="birthdate$text">出生日期：</label></td>
				<td><input style="width:100%" id="birthdate" name="employee.birthdate" class="nui-datepicker" allowInput="false"/></td>
			</tr>				
			<tr>
				<td class="nui-form-label"><label for="cardtype$text">证件类型：</label></td>
				<td><input style="width:100%" id="cardtype" name="employee.cardtype" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox" dictTypeId="COF_CARDTYPE" /></td>
				<td class="nui-form-label"><label for="cardno$text">证件号码：</label></td>
				<td><input style="width:100%" id="cardno" name="employee.cardno" class="nui-textbox" vtype="maxLength:20"/></td>
			</tr>				
			<tr class="odd">
				<td class="nui-form-label"><label for="indate$text">入职日期：</label></td>
				<td><input style="width:100%"  id="indate" name="employee.indate" class="nui-datepicker" allowInput="false"/></td>
				<td class="nui-form-label"><label for="outdate$text">离职日期：</label></td>
				<td><input style="width:100%" id="outdate" name="employee.outdate" class="nui-datepicker" onvalidation="onOutdateValidation" allowInput="false" />
				</td>
			</tr>				
			<tr>
				<td class="nui-form-label"><label for="empstatus$text">人员状态：</label></td>
				<td><input style="width:100%"  id="empstatus" name="employee.empstatus" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox" dictTypeId="COF_EMPSTATUS"   onvaluechanged="getstatus" required="true"/></td>
				<td class="nui-form-label"><label for="mobileno$text">手机号码：</label></td>
				<td><input style="width:100%" id="mobileno" name="employee.mobileno" class="nui-textbox" vtype="maxLength:14" required="true"/></td>
			</tr>
			<tr  class="odd">
				<td class="nui-form-label"><label for="oaddress$text">办公地址：</label></td>
				<td colspan="3"><input id="oaddress" name="employee.oaddress" class="nui-textbox nui-form-input" style="width:80%;"  vtype="maxLength:255"/></td>
			</tr>
			<tr>	
				<td class="nui-form-label"><label for="ozipcode$text">办公室邮编：</label></td>
				<td><input id="ozipcode" name="employee.ozipcode" class="nui-textbox nui-form-input" vtype="int;rangeLength:0,10" /></td>
				<td class="nui-form-label"><label for="faxno$text">传真号码：</label></td>
				<td><input id="faxno" name="employee.faxno" class="nui-textbox nui-form-input"  vtype="maxLength:14"/></td>
			</tr>
			<tr  class="odd">
				<td class="nui-form-label"><label for="otel$text">办公室电话：</label></td>
				<td><input id="otel" name="employee.otel" class="nui-textbox nui-form-input"  vtype="phone;rangeLength:0,20"/></td>
				<td class="nui-form-label"><label for="party$text">政治面貌：</label></td>
				<td><input id="party" name="employee.party" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox nui-form-input" dictTypeId="COF_PARTYVISAGE" /></td>
			</tr>	
			<!-- <tr class="odd">
				<td class="nui-form-label"><label for="sortno$text">排列顺序：</label></td>
				<td><input style="width:100%" id="sortno" class="nui-textbox" name="employee.sortno" vtype="int" /></td>
				<td></td>
				<td></td>
			</tr>			 -->	
		</table>
	</div>
	<!-- <div id="form3" style="padding-top:5px;width:100%">	
		<fieldset border="0" style="width:100%">
			<input class="nui-hidden" name="user.operatorId" />
			<input class="nui-hidden" name="user.password" />
	        <legend><label><input type="checkbox" checked="checked" id="userRefCheckbox" onclick="toggleFieldSet(this, 'form3')" hideFocus/>&nbsp;用户信息</label></legend>
	        <div class="fieldset-body">
	            <table style="width:100%;table-layout:fixed;" class="nui-form-table" >
					<tr>
						<td class="nui-form-label"><label for="userId$text">用户登录名：</label></td>
						<td><input style="width:100%"  id="userId" class="nui-textbox" name="user.userId" required="true" vtype="maxLength:30"/></td>
						<td class="nui-form-label"><label for="userstatus$text">用户状态：</label></td>
						<td><input style="width:100%" id="userstatus" name="user.status" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox" dictTypeId="COF_USERSTATUS" value="1"/></td>
					</tr>
					<tr class="odd">
						<td class="nui-form-label"><label for="authmode$text">认证模式：</label></td>
						<td><input style="width:100%"  id="authmode" name="user.authmode" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox" dictTypeId="COF_AUTHMODE" value="local"/></td>
						<td class="nui-form-label"><label for="invaldate$text">密码失效日期：</label></td>
						<td><input id="invaldate" class="nui-datepicker" name="user.invaldate"  style="width:100%"/></td>
					</tr>	
					<tr>
						<td class="nui-form-label"><label for="startdate$text">有效开始时间：</label></td>
						<td><input style="width:100%"  id="startdate" name="user.startdate" class="nui-datepicker" /></td>
						<td class="nui-form-label"><label for="enddate$text">有效截止时间：</label></td>
						<td ><input style="width:100%" id="enddate" name="user.enddate" class="nui-datepicker" onvalidation="onEnddateValidation" />
					</td>
					<tr class="odd">
						<td class="nui-form-label"><label for="menutype$text">菜单风格：</label></td>
						<td ><input style="width:100%"  id="menutype" name="user.menutype" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox" dictTypeId="COF_SKINLAYOUT" value="default"  /></td >
					</tr>	
				</table>
	        </div>
		</fieldset>
    </div> -->
</form>
</div>
<div  id="btn_div" class="nui-toolbar" style="text-align:center;padding-top:5px;padding-bottom:5px;" 
    borderStyle="border:0;">
    <a class="nui-button" style="width:60px;" iconCls="icon-save" onclick="update">保存</a>
    <span style="display:inline-block;width:25px;"></span>
    <a class="nui-button" id="resetBtn_01" style="width:60px;" iconCls="icon-reset" onclick="resetForm">重置</a>
    <span style="display:inline-block;width:25px;"></span>
    <a class="nui-button" iconCls="icon-reload" style="width:100px;" onclick="resetPassWord">重置密码</a>
</div>
</div>

<script type="text/javascript">
	 nui.parse();
	 
	 var is_hidden = "<%=StringUtil.htmlFilter(request.getParameter("is_hidden")) %>";
	 
	 if(is_hidden=="true"){
   
    	$("#btn_div").attr("style","display:none");
    }
	 
	 (function(){
		if(window.parent.getCurrentNode){
			var node = window.parent.getCurrentNode();
			window['parentNode'] = node;
		}
	 })();
	 
    var form = new nui.Form("#form");
	var form1 = new nui.Form("#form1");
/* 	var form3 = new nui.Form("#form3"); */

    function update() {
    
	//	var empcode = nui.get("empcode").getValue();
	    //nui.get("userId").setValue(empcode);
	   // nui.get("empuserid").setValue(empcode);
		var empname = nui.get("empname").getValue();
	    nui.get("userName").setValue(empname);
    	var data = {};
       	//校验
		form1.validate();
        if (form1.isValid()==false) return;
        var data = form.getData(true,true);
        if(nui.get("userstatus").getValue()!=""&&nui.get("userstatus").getValue()!=null){
         data.user.status= nui.get("userstatus").getValue();
        }
        	//data = form1Data;
       /*  if($("#userRefCheckbox")[0].checked){
        	form3.validate();
        	if (form3.isValid()==false) return;
        	//提交所有数据
        	data = form.getData(true,true);
        }else{
        	//只提交emp的数据
        	var form1Data = form1.getData(true,true);
        	data = form1Data;
        } */
        var json = nui.encode(data);
        
        $.ajax({
            url: "org.gocom.components.coframe.org.employee.updateEmployee.biz.ext",
            type: 'POST',
            data: json,
            cache: false,
            contentType:'text/json',
            success: function (text) {
            	var response = text.response;
            	if(response){
	            	if(response.flag){
	            		if(window.isCloseWindow){
            	            nui.alert("保存成功");
		            		CloseWindow("ok");
		            		return;
	            		}
	            		window['formData']=data;
	            		//重新载入
	            		var data = form1.getData();
	            		SetData({empid:data.employee.empid});
	            		//刷新其父节点
	            		if(window.parent){
	            		    nui.alert("保存成功");
	            			window.parent.refreshParentNode();
	            		}
	            		return;
	            	}else{
            	   nui.alert("新增失败，请联系管理员");
	            		return;
	            	}
            	}
            	nui.alert("修改失败，请联系管理员");
            },
            error: function (jqXHR, textStatus, errorThrown) {
            	   nui.alert("新增失败，请联系管理员");
                CloseWindow();
            }
        });
    }

    ////////////////////
    //标准方法接口定义
    function SetData(data) {
    	if(data.action=="update"){
    		window.isCloseWindow = true;
    		showCancelBtn();
    	}
        //跨页面传递的数据对象，克隆后才可以安全使用
        data = nui.clone(data);
		var json = nui.encode({template:data});
        $.ajax({
            url: "org.gocom.components.coframe.org.employee.getEmployee.biz.ext",
            type: 'POST',
            data: json,
            cache: false,
            contentType:'text/json',
            cache: false,
            async: false,//非异步，即同步
            success: function (data) {
                var o = nui.decode(data);
                form1.setData(o);
                window['formData1'] = o;
                nui.get("operatorid").setValue(o.employee.operatorid);
                nui.get("userId").setValue(o.employee.userid) 
           /*      alert(nui.get("operatorid").getValue());
                alert(nui.get("userid").getValue());
                alert(nui.get("empid").getValue()) ; */
                
                //loadUser(o.employee);
            }
        });
    }
    
   

    function CloseWindow(action) {
        if (action == "close" && form.isChanged()) {
            if (confirm("数据被修改了，是否先保存？")) {
                return false;
            }
        }
        if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
        else window.close();            
    }
    
    function cancel(e) {
        CloseWindow("cancel");
    }
	
	function resetForm(){
		//form.reset();
		var data = window['formData'];
		var data1 = window['formData1'];
		/* var data3 = window['formData3']; */
		if(data1){
			form1.setData(data1);
		}
		/* if(data3){
			form3.setData(data3);
		} */
		if(data){
			form.setData(data);
		}
		if(!data1 ){
			form.reset();
		}
	}
	function resetPassWord(){
		var data = form.getData(true,true);
		var json = nui.encode(data);
		if(confirm("确定重置密码吗？")){
			$.ajax({
				url:'org.gocom.components.coframe.org.updateEmp.resetPassword.biz.ext',
				type:'post',
				contentType:'text/json',
				cache:'false',
				data:json,
				success:function(){
					alert("密码已重置");				
				},
				error:function(){
					alert("密码重置失败");
				}				
			});
		}
	}
	//校验日期
	function onOutdateValidation(e){
       	var o = form.getData();
       	var org = o.employee || {};
		if(org.outdate && org.indate && org.outdate<=org.indate){
			e.errorText = "离职日期必须大于入职日期";
			e.isValid = false;
		}
        var indate = nui.get("indate").getValue();
        var outdate = nui.get("outdate").getValue();
        nui.get("enddate").setValue(outdate);
        nui.get("startdate").setValue(indate);
  
	}
	
	function onEnddateValidation(e){
       	var o = form.getData();
       	var org = o.user || {};
		if(org.enddate && org.startdate && org.enddate<=org.startdate){
			e.errorText = "失效日期必须大于生效日期";
			e.isValid = false;
		}
	}
	
    var bootPath = "<%=request.getContextPath() %>";
    
    //选择机构
    function selectOrg(e) {
        var btnEdit = this;
        nui.open({
            url: bootPath + "/coframe/org/employee/select_manageorg_tree.jsp",
            showMaxButton: false,
            title: "选择员工",
            width: 350,
            height: 350,
            ondestroy: function (action) {            
                if (action == "ok") {
                    var iframe = this.getIFrameEl();
                    var data = iframe.contentWindow.GetData();
                    data = nui.clone(data);
                    if (data) {
                        btnEdit.setValue(data.id);
                        btnEdit.setText(data.text);
                    }
                }
            }
        });            
    }    
    
    function showCancelBtn(){
    	$("#cancelBtn_01").show();
    	$("#resetBtn_01").hide();
    }
    
  	//展开，折叠
    function toggleFieldSet(ck, id) {
        var dom = document.getElementById(id);
        if(ck.checked){
        	dom.className = "";//展开
        }else{
        	dom.className = "hideFieldset";
        }
    }
	 	//选择离职状态
    function getstatus(){
       	var empstatus=nui.get("empstatus").getValue();
       if(empstatus=="on"){
           nui.get("userstatus").setValue("1");
       }else{
           nui.get("userstatus").setValue("9");
       }	
    }  
</script>

</body>
</html>


<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"  %>
<%@page import="com.gotop.xmzg.files.importWorld"%>
<%@page import="java.io.File"%>
<%@page import="java.util.HashMap"%>
<%@page import="java.util.*"%>
<%@page import="com.eos.server.dict.DictManager"%>
<%@page import="java.io.FileInputStream"%>
<%@page import="java.io.OutputStream"%>
<%@page import="java.net.URLEncoder"%>

<%@include file="/coframe/tools/skins/common.jsp" %>
<% 
   //xml的文件地址
    String dictVir="/files/borrow";
    String filePath = session.getServletContext().getRealPath(dictVir);
    //生成的world存放地址
    String saveDirectoryVir="/uploads/borrowWorld/";
    String saveDirectory = session.getServletContext().getRealPath(saveDirectoryVir);
	 importWorld.exportWorld(request,response,filePath,saveDirectory);
	 out.clear();  
     out = pageContext.pushBody();
%>	


<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): zhanghongkai
  - Date: 2019-05-09 15:57:02
  - Description:
-->
<head>
	<%@include file="/coframe/tools/skins/common.jsp" %>
	<title>选择角色</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
</head>
<body>
	<div class="nui-toolbar" style="text-align:center;line-height:30px;" borderStyle="border:0;">
		<label>角色名称：</label>
		<input id="roleName" class="nui-textbox" style="width:150px;" onenter="onKeyEnter"/>
		<a class="nui-button" style="width:60px;" onclick="search()">查询</a>
    </div>
    <div class="nui-fit">
		<div id="grid" class="nui-datagrid" style="width:100%;height:100%;" 
            url="com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.getRoleList.biz.ext"
            dataField="resultList" multiSelect="true" onload="setSelects"
            onSelect="onSelect" onDeSelect="onDeSelect">
			<div property="columns">
				<div type="checkcolumn"></div>
                <div field="ROLE_CODE" align="center" headerAlign="center">角色代码</div>
                <div field="ROLE_NAME" align="center" headerAlign="center">角色名称</div>          
            </div>
        </div>
    </div>                
    <div class="nui-toolbar" style="text-align:center;padding-top:8px;padding-bottom:8px;" borderStyle="border:0;">
		<a class="nui-button" style="width:60px;" onclick="onOk()">确定</a>
        <span style="display:inline-block;width:25px;"></span>
        <a class="nui-button" style="width:60px;" onclick="onCancel()">取消</a>
    </div>
	
	<script type="text/javascript">
	    nui.parse();
	
	    var grid = nui.get("grid");
	
	    grid.load();
	    
	    var values = [];
	    
	    function onSelect(e){
	    	var row = e.record;
	    	var isOk = true;
	    	for(var i=0;i<values.length;i++){
	    		if(row.ROLE_CODE == values[i]){
	    			isOk = false;
	    		}
	    	}
	    	if(isOk){
	    		values.push(row.ROLE_CODE);
	    	}
	    }
	    
	    function onDeSelect(e){
	    	var row = e.record;
	    	for(var i=0;i<values.length;i++){
	    		if(row.ROLE_CODE == values[i]){
	    			values.splice(i,1);
	    		}
	    	}
	    }
	    
	    function setData(data){
	    	data = nui.clone(data);
	    	if(data.value != null && data.value != ""){
	    		values = data.value.split(",");
	    	}
	    }
	    
	    function setSelects(e){
	    	if(values != null && values.length != 0){
	    		var data = e.data;
		    	for(var i=0;i<data.length;i++){
		    		for(var j=0;j<values.length;j++){
		    			if(data[i].ROLE_CODE == values[j]){
		    				grid.setSelected(data[i]);
		    			}
		    		}
		    	}
	    	}
	    }
	
	    function getData() {
	        var rows = [];
	    	var data = [];
	    	$.ajax({
	    		url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.getRoleList2.biz.ext",
	    		type:"post",
	    		async:false,
	    		success:function(text){
	    			data = text.resultList;
	    		}
	    	});
	    	for(var i=0;i<data.length;i++){
	    		for(var j=0;j<values.length;j++){
	    			if(data[i].ROLE_CODE == values[j]){
	    				rows.push(data[i]);
	    			}
	    		}
	    	}
	        return rows;
	    }
	    
	    function search() {
	        var roleName = nui.get("roleName").getValue();
	        grid.load({ queryData: {roleName:roleName} });
	    }
	    
	    function onKeyEnter(e) {
	        search();
	    }
	    //////////////////////////////////
	    function CloseWindow(action) {
	        if (window.CloseOwnerWindow){
	        	return window.CloseOwnerWindow(action);
	        }else{
	        	window.close();
	        }
	    }
	
	    function onOk() {
	        CloseWindow("ok");
	    }
	    
	    function onCancel() {
	        CloseWindow("cancel");
		}
	</script>
</body>
</html>
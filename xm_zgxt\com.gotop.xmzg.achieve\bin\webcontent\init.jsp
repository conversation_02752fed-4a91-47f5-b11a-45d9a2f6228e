<%@page pageEncoding="UTF-8"%>
<style type="text/css">
	.b_red{
		color:red;
		vertical-align: middle;
    	display: inline-block;
	}
</style>
<!-- 公共部分内容 -->
<script>
	/**
	 * 是否为空
	 * @param aObject
	 * @returns {Boolean} true 为空 false 不为空
	 */
	 function isNullOrEmpty() {
		var args = Array.prototype.slice.call(arguments);
		var aObject = args[0];
		if (typeof aObject == "undefined") {
			return true;
		}
		if (aObject == null || aObject == "null") {
			return true;
		}
		if (typeof aObject == "string") {
			if ($.trim(aObject) == "")
				return true;
		}
		if (aObject instanceof Array && aObject.length == 0) {
			return true;
		}
		return false;
	};
	
	//获取当前路径的根路径
	var bactpath='<%= request.getContextPath() %>';
	
	function onCancel(){
      	CloseWindow("cancel");
    }
  
  	function CloseWindow(action){
    	if(window.CloseOwnerWindow) 
      		return window.CloseOwnerWindow(action);
    	else
      		return window.close();
  	}
	
	//对象的key值转大写
    function upperJson(str){
    	for(var key in str){
    		str[key.toUpperCase()]=str[key];
    		delete(str[key]);
    	}
    	return str;
    }
    //对象的key值转小写
    function lowerJson(str){
    	for(var key in str){
    		str[key.toLowerCase()]=str[key];
    		delete(str[key]);
    	}
    	return str;
    }
    //是否包含中文
    function file_check(o){
    	var str = o.substr(o.lastIndexOf("\\")+1);
    	if(escape(str).indexOf("%u") != -1){
    		nui.alert("上传文件不能包含文字，请修改后在上传");
    		return true;
    	}else{
    		return false;
    	}
    }
    //转换时间格式
 	function changeDate(str){
 		var y = str.substring(0,4);
 		var m = str.substring(4,6);
 		var d = str.substring(6,8);
 		var h = str.substring(8,10);
 		var mi = str.substring(10,12);
 		var s = str.substring(12,14);
 		return y+"-"+m+"-"+d+" "+h+":"+mi+":"+s;
 	}
 	//判断是否又权限
	function contain(back){
		var load= nui.loading("校验中...","温馨提示 =^_^="); //显示遮罩层
		//提交数据
        nui.ajax({
       		url: "com.gotop.xmzg.achieve.indicators.contain.biz.ext",
           	type: "post",
           	data: nui.encode({"tip_orgcodes":nui.get("orgs").getValue()}),
           	contentType:'text/json',
           	success: function (text) {
           		nui.hideMessageBox(load);  //隐藏遮罩层
           		var code = text.code;
           		var msg = text.msg;
           		if(code == "9"){
           			nui.alert("用户超时请重新登录");
           			return;
           		}else if(code == "11"){
           			nui.alert(msg);
           			return;
           		}else if(code == "10"){
           			back();
           			return;
           		}else{
           			nui.alert("系统异常");
           			return;
           		}
            }
       });
	}
</script>
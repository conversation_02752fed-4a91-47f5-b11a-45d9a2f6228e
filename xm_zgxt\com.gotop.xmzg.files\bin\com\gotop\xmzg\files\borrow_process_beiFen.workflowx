<?xml version="1.0" encoding="UTF-8"?>
<workflowProcess productVersion="6.1" schemaVersion="6.0">
  <processHeader>
    <processBasicInfo>
      <processId>com.gotop.xmzg.files.borrow_process_beiFen</processId>
      <processName>borrow_process_beifen</processName>
      <priority>60</priority>
      <author>Administrator</author>
      <department>psbc</department>
      <description></description>
    </processBasicInfo>
    <dataFields>
      <dataField>
        <name>audit_res</name>
        <dataType>
          <typeClass>primitive</typeClass>
          <typeValue>String</typeValue>
        </dataType>
        <initialValue></initialValue>
        <description></description>
        <isArray>false</isArray>
      </dataField>
      <dataField>
        <name>isContinue</name>
        <dataType>
          <typeClass>primitive</typeClass>
          <typeValue>String</typeValue>
        </dataType>
        <initialValue></initialValue>
        <description></description>
        <isArray>false</isArray>
      </dataField>
      <dataField>
        <name>prorole</name>
        <dataType>
          <typeClass>sdo</typeClass>
          <typeValue>com.pfpj.workflow.data.WFParticipant</typeValue>
        </dataType>
        <initialValue></initialValue>
        <description></description>
        <isArray>true</isArray>
      </dataField>
      <dataField>
        <name>participantID</name>
        <dataType>
          <typeClass>primitive</typeClass>
          <typeValue>String</typeValue>
        </dataType>
        <initialValue></initialValue>
        <description></description>
        <isArray>false</isArray>
      </dataField>
    </dataFields>
    <triggerEvents/>
    <timeLimit>
      <isTimeLimitSet>false</isTimeLimitSet>
      <calendarSet>
        <initType>appoint</initType>
        <calendarInfo>
          <resourceType>business-calendar</resourceType>
          <resourceID>default</resourceID>
          <resourceName>默认日历</resourceName>
          <parameters/>
        </calendarInfo>
      </calendarSet>
    </timeLimit>
    <procStarterLists>
      <processStarterType>all</processStarterType>
    </procStarterLists>
    <parameters/>
    <splitTransaction>false</splitTransaction>
    <longProcess>true</longProcess>
    <bizEntityInfo>
      <bizEntityName></bizEntityName>
      <relevantKey></relevantKey>
    </bizEntityInfo>
    <calendarInfo>
      <resourceType>business-calendar</resourceType>
      <resourceID>default</resourceID>
      <resourceName>默认日历</resourceName>
      <parameters/>
    </calendarInfo>
    <extendNodes><extendNode><key>IsForWardBiz</key><value>false</value><desc></desc></extendNode><extendNode><key>openLog</key><value>false</value><desc></desc></extendNode></extendNodes>
  </processHeader>
  <transitions>
    <transition>
      <from>startActivity</from>
      <to>manualActivity</to>
      <isDefault>true</isDefault>
      <priority>60</priority>
      <displayName></displayName>
      <type>simpleCondition</type>
      <bendPoints/>
    </transition>
    <transition>
      <from>manualActivity</from>
      <to>manualActivity1</to>
      <isDefault>true</isDefault>
      <priority>60</priority>
      <displayName></displayName>
      <type>simpleCondition</type>
      <bendPoints/>
    </transition>
    <transition>
      <from>manualActivity</from>
      <to>finishActivity</to>
      <isDefault>false</isDefault>
      <priority>60</priority>
      <displayName>流程办结</displayName>
      <type>simpleCondition</type>
      <simpleCondition>
        <leftValueType>variable</leftValueType>
        <leftValue>isContinue</leftValue>
        <compType>EQ</compType>
        <rightValue>false</rightValue>
        <rightValueType>constant</rightValueType>
      </simpleCondition>
      <bendPoints>
        <bendPoint>
          <w1>3</w1>
          <h1>89</h1>
          <w2>-866</w2>
          <h2>88</h2>
        </bendPoint>
        <bendPoint>
          <w1>870</w1>
          <h1>82</h1>
          <w2>1</w2>
          <h2>81</h2>
        </bendPoint>
      </bendPoints>
    </transition>
    <transition>
      <from>manualActivity1</from>
      <to>manualActivity2</to>
      <isDefault>true</isDefault>
      <priority>60</priority>
      <displayName></displayName>
      <type>simpleCondition</type>
      <bendPoints/>
    </transition>
    <transition>
      <from>manualActivity1</from>
      <to>manualActivity</to>
      <isDefault>false</isDefault>
      <priority>60</priority>
      <displayName>审核不通过</displayName>
      <type>simpleCondition</type>
      <simpleCondition>
        <leftValueType>variable</leftValueType>
        <leftValue>audit_res</leftValue>
        <compType>EQ</compType>
        <rightValue>n</rightValue>
        <rightValueType>constant</rightValueType>
      </simpleCondition>
      <bendPoints>
        <bendPoint>
          <w1>1</w1>
          <h1>-51</h1>
          <w2>166</w2>
          <h2>-51</h2>
        </bendPoint>
        <bendPoint>
          <w1>-166</w1>
          <h1>-52</h1>
          <w2>-1</w2>
          <h2>-52</h2>
        </bendPoint>
      </bendPoints>
    </transition>
    <transition>
      <from>manualActivity2</from>
      <to>manualActivity3</to>
      <isDefault>true</isDefault>
      <priority>60</priority>
      <displayName></displayName>
      <type>simpleCondition</type>
      <bendPoints/>
    </transition>
    <transition>
      <from>manualActivity2</from>
      <to>manualActivity1</to>
      <isDefault>false</isDefault>
      <priority>60</priority>
      <displayName>审核不通过</displayName>
      <type>simpleCondition</type>
      <simpleCondition>
        <leftValueType>variable</leftValueType>
        <leftValue>audit_res</leftValue>
        <compType>EQ</compType>
        <rightValue>n</rightValue>
        <rightValueType>constant</rightValueType>
      </simpleCondition>
      <bendPoints>
        <bendPoint>
          <w1>1</w1>
          <h1>66</h1>
          <w2>196</w2>
          <h2>66</h2>
        </bendPoint>
        <bendPoint>
          <w1>-194</w1>
          <h1>68</h1>
          <w2>1</w2>
          <h2>68</h2>
        </bendPoint>
      </bendPoints>
    </transition>
    <transition>
      <from>manualActivity3</from>
      <to>manualActivity4</to>
      <isDefault>true</isDefault>
      <priority>60</priority>
      <displayName></displayName>
      <type>simpleCondition</type>
      <bendPoints/>
    </transition>
    <transition>
      <from>manualActivity3</from>
      <to>manualActivity2</to>
      <isDefault>false</isDefault>
      <priority>60</priority>
      <displayName>审核不通过</displayName>
      <type>simpleCondition</type>
      <simpleCondition>
        <leftValueType>variable</leftValueType>
        <leftValue>audit_res</leftValue>
        <compType>EQ</compType>
        <rightValue>n</rightValue>
        <rightValueType>constant</rightValueType>
      </simpleCondition>
      <bendPoints>
        <bendPoint>
          <w1>2</w1>
          <h1>-46</h1>
          <w2>215</w2>
          <h2>-46</h2>
        </bendPoint>
        <bendPoint>
          <w1>-214</w1>
          <h1>-46</h1>
          <w2>-1</w2>
          <h2>-46</h2>
        </bendPoint>
      </bendPoints>
    </transition>
    <transition>
      <from>manualActivity4</from>
      <to>finishActivity</to>
      <isDefault>true</isDefault>
      <priority>60</priority>
      <displayName></displayName>
      <type>simpleCondition</type>
      <bendPoints/>
    </transition>
    <transition>
      <from>manualActivity4</from>
      <to>manualActivity3</to>
      <isDefault>false</isDefault>
      <priority>60</priority>
      <displayName>审核不通过</displayName>
      <type>simpleCondition</type>
      <simpleCondition>
        <leftValueType>variable</leftValueType>
        <leftValue>audit_res</leftValue>
        <compType>EQ</compType>
        <rightValue>n</rightValue>
        <rightValueType>constant</rightValueType>
      </simpleCondition>
      <bendPoints>
        <bendPoint>
          <w1>5</w1>
          <h1>57</h1>
          <w2>182</w2>
          <h2>57</h2>
        </bendPoint>
        <bendPoint>
          <w1>-173</w1>
          <h1>59</h1>
          <w2>4</w2>
          <h2>59</h2>
        </bendPoint>
      </bendPoints>
    </transition>
  </transitions>
  <activities>
    <activity>
      <activityId>startActivity</activityId>
      <activityName>开始</activityName>
      <description></description>
      <splitType>XOR</splitType>
      <joinType>XOR</joinType>
      <priority>60</priority>
      <activityType>start</activityType>
      <splitTransaction>false</splitTransaction>
      <implementation>
        <startActivity>
          <formFields/>
        </startActivity>
      </implementation>
      <isStartActivity>true</isStartActivity>
      <nodeGraphInfo>
        <color>16777215</color>
        <height>32</height>
        <width>32</width>
        <x>-15</x>
        <y>171</y>
        <lookAndFeel>classic</lookAndFeel>
        <fontName>System</fontName>
        <fontSize>12</fontSize>
        <fontWidth>550</fontWidth>
        <foreColor>0</foreColor>
        <isItalic>0</isItalic>
        <isUnderline>0</isUnderline>
        <textHeight>60</textHeight>
      </nodeGraphInfo>
    </activity>
    <activity>
      <activityId>finishActivity</activityId>
      <activityName>结束</activityName>
      <description></description>
      <splitType>XOR</splitType>
      <joinType>XOR</joinType>
      <priority>60</priority>
      <activityType>finish</activityType>
      <splitTransaction>false</splitTransaction>
      <activateRule>
        <activateRuleType>directRunning</activateRuleType>
      </activateRule>
      <isStartActivity>false</isStartActivity>
      <nodeGraphInfo>
        <color>16777215</color>
        <height>32</height>
        <width>32</width>
        <x>960</x>
        <y>171</y>
        <lookAndFeel>classic</lookAndFeel>
        <fontName>System</fontName>
        <fontSize>12</fontSize>
        <fontWidth>550</fontWidth>
        <foreColor>0</foreColor>
        <isItalic>0</isItalic>
        <isUnderline>0</isUnderline>
        <textHeight>60</textHeight>
      </nodeGraphInfo>
    </activity>
    <activity>
      <activityId>manualActivity</activityId>
      <activityName>借阅申请</activityName>
      <description></description>
      <splitType>XOR</splitType>
      <joinType>XOR</joinType>
      <priority>60</priority>
      <activityType>manual</activityType>
      <splitTransaction>false</splitTransaction>
      <activateRule>
        <activateRuleType>directRunning</activateRuleType>
        <applicationInfo>
          <actionType>service-component</actionType>
          <suppressJoinFailure>suppress</suppressJoinFailure>
          <application>
            <actionType>service-component</actionType>
            <applicationUri></applicationUri>
            <transactionType>join</transactionType>
            <exceptionStrategy>rollback</exceptionStrategy>
            <invokePattern>synchronous</invokePattern>
            <parameters/>
          </application>
          <assignments/>
        </applicationInfo>
      </activateRule>
      <implementation>
        <manualActivity>
          <allowAgent>true</allowAgent>
          <customURL>
            <isSpecifyURL>true</isSpecifyURL>
            <urlInfo>/files/process/process_apply.jsp</urlInfo>
            <urlType>webpage</urlType>
          </customURL>
          <resetUrl>
            <isSpecifyURL>false</isSpecifyURL>
            <urlType>presentation-logic</urlType>
          </resetUrl>
          <participants>
            <participantType>process-starter</participantType>
            <specialActivityID></specialActivityID>
            <specialPath></specialPath>
          </participants>
          <formFields/>
          <timeLimit>
            <isTimeLimitSet>false</isTimeLimitSet>
            <calendarSet>
              <initType>appoint</initType>
              <calendarInfo>
                <resourceType>business-calendar</resourceType>
                <resourceID>default</resourceID>
                <resourceName>默认日历</resourceName>
                <parameters/>
              </calendarInfo>
            </calendarSet>
          </timeLimit>
          <multiWorkItem>
            <isMulWIValid>false</isMulWIValid>
            <workitemNumStrategy>participant-number</workitemNumStrategy>
            <finishRule>all</finishRule>
            <finishRequiredPercent>0</finishRequiredPercent>
            <finishRquiredNum>0</finishRquiredNum>
            <isAutoCancel>false</isAutoCancel>
            <sequentialExecute>false</sequentialExecute>
          </multiWorkItem>
          <triggerEvents/>
          <rollBack>
            <applicationInfo>
              <actionType>service-component</actionType>
              <suppressJoinFailure>suppress</suppressJoinFailure>
              <application>
                <actionType>service-component</actionType>
                <applicationUri></applicationUri>
                <transactionType>join</transactionType>
                <exceptionStrategy>rollback</exceptionStrategy>
                <invokePattern>synchronous</invokePattern>
                <parameters/>
              </application>
              <assignments/>
            </applicationInfo>
          </rollBack>
          <freeFlowRule>
            <isFreeActivity>false</isFreeActivity>
            <freeRangeStrategy>freeWithinProcess</freeRangeStrategy>
            <isOnlyLimitedManualActivity>false</isOnlyLimitedManualActivity>
          </freeFlowRule>
          <resetParticipant>originalParticipant</resetParticipant>
          <notification>
            <isSendNotification>false</isSendNotification>
            <actionURL>
              <isSpecifyURL>false</isSpecifyURL>
              <urlType>presentation-logic</urlType>
            </actionURL>
            <participants>
              <participantType>process-starter</participantType>
              <specialActivityID></specialActivityID>
              <specialPath></specialPath>
            </participants>
            <isExpandParticipant>false</isExpandParticipant>
            <timeLimit>
              <isTimeLimitSet>false</isTimeLimitSet>
              <calendarSet>
                <initType>appoint</initType>
                <calendarInfo>
                  <resourceType>business-calendar</resourceType>
                  <resourceID>default</resourceID>
                  <resourceName>默认日历</resourceName>
                  <parameters/>
                </calendarInfo>
              </calendarSet>
            </timeLimit>
            <notificationImplType>workItem</notificationImplType>
          </notification>
        </manualActivity>
      </implementation>
      <extendNodes></extendNodes>
      <isStartActivity>false</isStartActivity>
      <nodeGraphInfo>
        <color>16777215</color>
        <height>45</height>
        <width>64</width>
        <x>75</x>
        <y>164</y>
        <lookAndFeel>classic</lookAndFeel>
        <fontName>System</fontName>
        <fontSize>12</fontSize>
        <fontWidth>550</fontWidth>
        <foreColor>0</foreColor>
        <isItalic>0</isItalic>
        <isUnderline>0</isUnderline>
        <textHeight>60</textHeight>
      </nodeGraphInfo>
    </activity>
    <activity>
      <activityId>manualActivity1</activityId>
      <activityName>借阅单位负责人审批</activityName>
      <description></description>
      <splitType>XOR</splitType>
      <joinType>XOR</joinType>
      <priority>60</priority>
      <activityType>manual</activityType>
      <splitTransaction>false</splitTransaction>
      <activateRule>
        <activateRuleType>directRunning</activateRuleType>
        <applicationInfo>
          <actionType>service-component</actionType>
          <suppressJoinFailure>suppress</suppressJoinFailure>
          <application>
            <actionType>service-component</actionType>
            <applicationUri></applicationUri>
            <transactionType>join</transactionType>
            <exceptionStrategy>rollback</exceptionStrategy>
            <invokePattern>synchronous</invokePattern>
            <parameters/>
          </application>
          <assignments/>
        </applicationInfo>
      </activateRule>
      <implementation>
        <manualActivity>
          <allowAgent>true</allowAgent>
          <customURL>
            <isSpecifyURL>true</isSpecifyURL>
            <urlInfo>/files/process/process_sp.jsp</urlInfo>
            <urlType>webpage</urlType>
          </customURL>
          <resetUrl>
            <isSpecifyURL>false</isSpecifyURL>
            <urlType>presentation-logic</urlType>
          </resetUrl>
          <participants>
            <participantType>relevantdata</participantType>
            <specialActivityID></specialActivityID>
            <specialPath>prorole</specialPath>
          </participants>
          <formFields/>
          <timeLimit>
            <isTimeLimitSet>false</isTimeLimitSet>
            <calendarSet>
              <initType>appoint</initType>
              <calendarInfo>
                <resourceType>business-calendar</resourceType>
                <resourceID>default</resourceID>
                <resourceName>默认日历</resourceName>
                <parameters/>
              </calendarInfo>
            </calendarSet>
          </timeLimit>
          <multiWorkItem>
            <isMulWIValid>false</isMulWIValid>
            <workitemNumStrategy>participant-number</workitemNumStrategy>
            <finishRule>all</finishRule>
            <finishRequiredPercent>0</finishRequiredPercent>
            <finishRquiredNum>0</finishRquiredNum>
            <isAutoCancel>false</isAutoCancel>
            <sequentialExecute>false</sequentialExecute>
          </multiWorkItem>
          <triggerEvents/>
          <rollBack>
            <applicationInfo>
              <actionType>service-component</actionType>
              <suppressJoinFailure>suppress</suppressJoinFailure>
              <application>
                <actionType>service-component</actionType>
                <applicationUri></applicationUri>
                <transactionType>join</transactionType>
                <exceptionStrategy>rollback</exceptionStrategy>
                <invokePattern>synchronous</invokePattern>
                <parameters/>
              </application>
              <assignments/>
            </applicationInfo>
          </rollBack>
          <freeFlowRule>
            <isFreeActivity>false</isFreeActivity>
            <freeRangeStrategy>freeWithinProcess</freeRangeStrategy>
            <isOnlyLimitedManualActivity>false</isOnlyLimitedManualActivity>
          </freeFlowRule>
          <resetParticipant>originalParticipant</resetParticipant>
          <notification>
            <isSendNotification>false</isSendNotification>
            <actionURL>
              <isSpecifyURL>false</isSpecifyURL>
              <urlType>presentation-logic</urlType>
            </actionURL>
            <participants>
              <participantType>process-starter</participantType>
              <specialActivityID></specialActivityID>
              <specialPath></specialPath>
            </participants>
            <isExpandParticipant>false</isExpandParticipant>
            <timeLimit>
              <isTimeLimitSet>false</isTimeLimitSet>
              <calendarSet>
                <initType>appoint</initType>
                <calendarInfo>
                  <resourceType>business-calendar</resourceType>
                  <resourceID>default</resourceID>
                  <resourceName>默认日历</resourceName>
                  <parameters/>
                </calendarInfo>
              </calendarSet>
            </timeLimit>
            <notificationImplType>workItem</notificationImplType>
          </notification>
        </manualActivity>
      </implementation>
      <extendNodes></extendNodes>
      <isStartActivity>false</isStartActivity>
      <nodeGraphInfo>
        <color>16777215</color>
        <height>45</height>
        <width>64</width>
        <x>240</x>
        <y>164</y>
        <lookAndFeel>classic</lookAndFeel>
        <fontName>System</fontName>
        <fontSize>12</fontSize>
        <fontWidth>550</fontWidth>
        <foreColor>0</foreColor>
        <isItalic>0</isItalic>
        <isUnderline>0</isUnderline>
        <textHeight>60</textHeight>
      </nodeGraphInfo>
    </activity>
    <activity>
      <activityId>manualActivity2</activityId>
      <activityName>对应业务部门负责人/资产保全部负责人</activityName>
      <description></description>
      <splitType>XOR</splitType>
      <joinType>XOR</joinType>
      <priority>60</priority>
      <activityType>manual</activityType>
      <splitTransaction>false</splitTransaction>
      <activateRule>
        <activateRuleType>directRunning</activateRuleType>
        <applicationInfo>
          <actionType>service-component</actionType>
          <suppressJoinFailure>suppress</suppressJoinFailure>
          <application>
            <actionType>service-component</actionType>
            <applicationUri></applicationUri>
            <transactionType>join</transactionType>
            <exceptionStrategy>rollback</exceptionStrategy>
            <invokePattern>synchronous</invokePattern>
            <parameters/>
          </application>
          <assignments/>
        </applicationInfo>
      </activateRule>
      <implementation>
        <manualActivity>
          <allowAgent>true</allowAgent>
          <customURL>
            <isSpecifyURL>true</isSpecifyURL>
            <urlInfo>/files/process/process_sp.jsp</urlInfo>
            <urlType>webpage</urlType>
          </customURL>
          <resetUrl>
            <isSpecifyURL>false</isSpecifyURL>
            <urlType>presentation-logic</urlType>
          </resetUrl>
          <participants>
            <participantType>relevantdata</participantType>
            <specialActivityID></specialActivityID>
            <specialPath>prorole</specialPath>
          </participants>
          <formFields/>
          <timeLimit>
            <isTimeLimitSet>false</isTimeLimitSet>
            <calendarSet>
              <initType>appoint</initType>
              <calendarInfo>
                <resourceType>business-calendar</resourceType>
                <resourceID>default</resourceID>
                <resourceName>默认日历</resourceName>
                <parameters/>
              </calendarInfo>
            </calendarSet>
          </timeLimit>
          <multiWorkItem>
            <isMulWIValid>false</isMulWIValid>
            <workitemNumStrategy>participant-number</workitemNumStrategy>
            <finishRule>all</finishRule>
            <finishRequiredPercent>0</finishRequiredPercent>
            <finishRquiredNum>0</finishRquiredNum>
            <isAutoCancel>false</isAutoCancel>
            <sequentialExecute>false</sequentialExecute>
          </multiWorkItem>
          <triggerEvents/>
          <rollBack>
            <applicationInfo>
              <actionType>service-component</actionType>
              <suppressJoinFailure>suppress</suppressJoinFailure>
              <application>
                <actionType>service-component</actionType>
                <applicationUri></applicationUri>
                <transactionType>join</transactionType>
                <exceptionStrategy>rollback</exceptionStrategy>
                <invokePattern>synchronous</invokePattern>
                <parameters/>
              </application>
              <assignments/>
            </applicationInfo>
          </rollBack>
          <freeFlowRule>
            <isFreeActivity>false</isFreeActivity>
            <freeRangeStrategy>freeWithinProcess</freeRangeStrategy>
            <isOnlyLimitedManualActivity>false</isOnlyLimitedManualActivity>
          </freeFlowRule>
          <resetParticipant>originalParticipant</resetParticipant>
          <notification>
            <isSendNotification>false</isSendNotification>
            <actionURL>
              <isSpecifyURL>false</isSpecifyURL>
              <urlType>presentation-logic</urlType>
            </actionURL>
            <participants>
              <participantType>process-starter</participantType>
              <specialActivityID></specialActivityID>
              <specialPath></specialPath>
            </participants>
            <isExpandParticipant>false</isExpandParticipant>
            <timeLimit>
              <isTimeLimitSet>false</isTimeLimitSet>
              <calendarSet>
                <initType>appoint</initType>
                <calendarInfo>
                  <resourceType>business-calendar</resourceType>
                  <resourceID>default</resourceID>
                  <resourceName>默认日历</resourceName>
                  <parameters/>
                </calendarInfo>
              </calendarSet>
            </timeLimit>
            <notificationImplType>workItem</notificationImplType>
          </notification>
        </manualActivity>
      </implementation>
      <extendNodes></extendNodes>
      <isStartActivity>false</isStartActivity>
      <nodeGraphInfo>
        <color>16777215</color>
        <height>45</height>
        <width>64</width>
        <x>435</x>
        <y>164</y>
        <lookAndFeel>classic</lookAndFeel>
        <fontName>System</fontName>
        <fontSize>12</fontSize>
        <fontWidth>550</fontWidth>
        <foreColor>0</foreColor>
        <isItalic>0</isItalic>
        <isUnderline>0</isUnderline>
        <textHeight>60</textHeight>
      </nodeGraphInfo>
    </activity>
    <activity>
      <activityId>manualActivity3</activityId>
      <activityName>分行授信档案管理部门负责人审核</activityName>
      <description></description>
      <splitType>XOR</splitType>
      <joinType>XOR</joinType>
      <priority>60</priority>
      <activityType>manual</activityType>
      <splitTransaction>false</splitTransaction>
      <activateRule>
        <activateRuleType>directRunning</activateRuleType>
        <applicationInfo>
          <actionType>service-component</actionType>
          <suppressJoinFailure>suppress</suppressJoinFailure>
          <application>
            <actionType>service-component</actionType>
            <applicationUri></applicationUri>
            <transactionType>join</transactionType>
            <exceptionStrategy>rollback</exceptionStrategy>
            <invokePattern>synchronous</invokePattern>
            <parameters/>
          </application>
          <assignments/>
        </applicationInfo>
      </activateRule>
      <implementation>
        <manualActivity>
          <allowAgent>true</allowAgent>
          <customURL>
            <isSpecifyURL>true</isSpecifyURL>
            <urlInfo>/files/process/process_sp.jsp</urlInfo>
            <urlType>webpage</urlType>
          </customURL>
          <resetUrl>
            <isSpecifyURL>false</isSpecifyURL>
            <urlType>presentation-logic</urlType>
          </resetUrl>
          <participants>
            <participantType>relevantdata</participantType>
            <specialActivityID></specialActivityID>
            <specialPath>prorole</specialPath>
          </participants>
          <formFields/>
          <timeLimit>
            <isTimeLimitSet>false</isTimeLimitSet>
            <calendarSet>
              <initType>appoint</initType>
              <calendarInfo>
                <resourceType>business-calendar</resourceType>
                <resourceID>default</resourceID>
                <resourceName>默认日历</resourceName>
                <parameters/>
              </calendarInfo>
            </calendarSet>
          </timeLimit>
          <multiWorkItem>
            <isMulWIValid>false</isMulWIValid>
            <workitemNumStrategy>participant-number</workitemNumStrategy>
            <finishRule>all</finishRule>
            <finishRequiredPercent>0</finishRequiredPercent>
            <finishRquiredNum>0</finishRquiredNum>
            <isAutoCancel>false</isAutoCancel>
            <sequentialExecute>false</sequentialExecute>
          </multiWorkItem>
          <triggerEvents/>
          <rollBack>
            <applicationInfo>
              <actionType>service-component</actionType>
              <suppressJoinFailure>suppress</suppressJoinFailure>
              <application>
                <actionType>service-component</actionType>
                <applicationUri></applicationUri>
                <transactionType>join</transactionType>
                <exceptionStrategy>rollback</exceptionStrategy>
                <invokePattern>synchronous</invokePattern>
                <parameters/>
              </application>
              <assignments/>
            </applicationInfo>
          </rollBack>
          <freeFlowRule>
            <isFreeActivity>false</isFreeActivity>
            <freeRangeStrategy>freeWithinProcess</freeRangeStrategy>
            <isOnlyLimitedManualActivity>false</isOnlyLimitedManualActivity>
          </freeFlowRule>
          <resetParticipant>originalParticipant</resetParticipant>
          <notification>
            <isSendNotification>false</isSendNotification>
            <actionURL>
              <isSpecifyURL>false</isSpecifyURL>
              <urlType>presentation-logic</urlType>
            </actionURL>
            <participants>
              <participantType>process-starter</participantType>
              <specialActivityID></specialActivityID>
              <specialPath></specialPath>
            </participants>
            <isExpandParticipant>false</isExpandParticipant>
            <timeLimit>
              <isTimeLimitSet>false</isTimeLimitSet>
              <calendarSet>
                <initType>appoint</initType>
                <calendarInfo>
                  <resourceType>business-calendar</resourceType>
                  <resourceID>default</resourceID>
                  <resourceName>默认日历</resourceName>
                  <parameters/>
                </calendarInfo>
              </calendarSet>
            </timeLimit>
            <notificationImplType>workItem</notificationImplType>
          </notification>
        </manualActivity>
      </implementation>
      <extendNodes></extendNodes>
      <isStartActivity>false</isStartActivity>
      <nodeGraphInfo>
        <color>16777215</color>
        <height>45</height>
        <width>64</width>
        <x>648</x>
        <y>164</y>
        <lookAndFeel>classic</lookAndFeel>
        <fontName>System</fontName>
        <fontSize>12</fontSize>
        <fontWidth>550</fontWidth>
        <foreColor>0</foreColor>
        <isItalic>0</isItalic>
        <isUnderline>0</isUnderline>
        <textHeight>60</textHeight>
      </nodeGraphInfo>
    </activity>
    <activity>
      <activityId>manualActivity4</activityId>
      <activityName>分行授信档案管理员确认</activityName>
      <description></description>
      <splitType>XOR</splitType>
      <joinType>XOR</joinType>
      <priority>60</priority>
      <activityType>manual</activityType>
      <splitTransaction>false</splitTransaction>
      <activateRule>
        <activateRuleType>directRunning</activateRuleType>
        <applicationInfo>
          <actionType>service-component</actionType>
          <suppressJoinFailure>suppress</suppressJoinFailure>
          <application>
            <actionType>service-component</actionType>
            <applicationUri></applicationUri>
            <transactionType>join</transactionType>
            <exceptionStrategy>rollback</exceptionStrategy>
            <invokePattern>synchronous</invokePattern>
            <parameters/>
          </application>
          <assignments/>
        </applicationInfo>
      </activateRule>
      <implementation>
        <manualActivity>
          <allowAgent>true</allowAgent>
          <customURL>
            <isSpecifyURL>true</isSpecifyURL>
            <urlInfo>/files/process/FilesAdmin_sp.jsp</urlInfo>
            <urlType>webpage</urlType>
          </customURL>
          <resetUrl>
            <isSpecifyURL>false</isSpecifyURL>
            <urlType>presentation-logic</urlType>
          </resetUrl>
          <participants>
            <participantType>relevantdata</participantType>
            <specialActivityID></specialActivityID>
            <specialPath>prorole</specialPath>
          </participants>
          <formFields/>
          <timeLimit>
            <isTimeLimitSet>false</isTimeLimitSet>
            <calendarSet>
              <initType>appoint</initType>
              <calendarInfo>
                <resourceType>business-calendar</resourceType>
                <resourceID>default</resourceID>
                <resourceName>默认日历</resourceName>
                <parameters/>
              </calendarInfo>
            </calendarSet>
          </timeLimit>
          <multiWorkItem>
            <isMulWIValid>false</isMulWIValid>
            <workitemNumStrategy>participant-number</workitemNumStrategy>
            <finishRule>all</finishRule>
            <finishRequiredPercent>0</finishRequiredPercent>
            <finishRquiredNum>0</finishRquiredNum>
            <isAutoCancel>false</isAutoCancel>
            <sequentialExecute>false</sequentialExecute>
          </multiWorkItem>
          <triggerEvents/>
          <rollBack>
            <applicationInfo>
              <actionType>service-component</actionType>
              <suppressJoinFailure>suppress</suppressJoinFailure>
              <application>
                <actionType>service-component</actionType>
                <applicationUri></applicationUri>
                <transactionType>join</transactionType>
                <exceptionStrategy>rollback</exceptionStrategy>
                <invokePattern>synchronous</invokePattern>
                <parameters/>
              </application>
              <assignments/>
            </applicationInfo>
          </rollBack>
          <freeFlowRule>
            <isFreeActivity>false</isFreeActivity>
            <freeRangeStrategy>freeWithinProcess</freeRangeStrategy>
            <isOnlyLimitedManualActivity>false</isOnlyLimitedManualActivity>
          </freeFlowRule>
          <resetParticipant>originalParticipant</resetParticipant>
          <notification>
            <isSendNotification>false</isSendNotification>
            <actionURL>
              <isSpecifyURL>false</isSpecifyURL>
              <urlType>presentation-logic</urlType>
            </actionURL>
            <participants>
              <participantType>process-starter</participantType>
              <specialActivityID></specialActivityID>
              <specialPath></specialPath>
            </participants>
            <isExpandParticipant>false</isExpandParticipant>
            <timeLimit>
              <isTimeLimitSet>false</isTimeLimitSet>
              <calendarSet>
                <initType>appoint</initType>
                <calendarInfo>
                  <resourceType>business-calendar</resourceType>
                  <resourceID>default</resourceID>
                  <resourceName>默认日历</resourceName>
                  <parameters/>
                </calendarInfo>
              </calendarSet>
            </timeLimit>
            <notificationImplType>workItem</notificationImplType>
          </notification>
        </manualActivity>
      </implementation>
      <extendNodes></extendNodes>
      <isStartActivity>false</isStartActivity>
      <nodeGraphInfo>
        <color>16777215</color>
        <height>45</height>
        <width>64</width>
        <x>825</x>
        <y>164</y>
        <lookAndFeel>classic</lookAndFeel>
        <fontName>System</fontName>
        <fontSize>12</fontSize>
        <fontWidth>550</fontWidth>
        <foreColor>0</foreColor>
        <isItalic>0</isItalic>
        <isUnderline>0</isUnderline>
        <textHeight>60</textHeight>
      </nodeGraphInfo>
    </activity>
  </activities>
  <notes/>
  <resource/>
</workflowProcess>
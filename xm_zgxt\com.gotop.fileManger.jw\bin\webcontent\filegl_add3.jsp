<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="com.eos.data.datacontext.UserObject"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- $Header: svn://**************:7029/svn/xm_project/xm_zgxt/com.gotop.fileManger.jw/src/webcontent/filegl_add3.jsp 1935 2018-09-12 03:44:13Z jw $ -->
<!-- 
  - Author(s): JW
  - Date: 2018-07-25 11:08:29
  -   
-->
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
 %>
<head>
<title>文件添加</title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=EDGE">
<%@include file="/coframe/tools/skins/common.jsp"%>
<script src="<%=request.getContextPath()%>/common/nui/nui.js" type="text/javascript"></script>
<style type="text/css">
html,body {
	padding: 0;
	margin: 0;
	border: 0;
	height: 100%;
	overflow-y: auto;
}

.errorText{
	color:red;
	font-size: 12px;
}
</style>
</head>
<body>
	<fieldset style="border: solid 1px #aaa; padding: 3px;">
		<legend>文件基本信息</legend>
		<div style="padding: 5px;">
			<form id="filefrom" method="post">
				<table border="0" cellpadding="1" cellspacing="2" align="center" style="width: 100%">
					<tr>
						<td style="width: 15%; text-align: right;">文件名：</td>
						<td style="width: 70%"><input id="fileName" name="file.fileName" class="nui-textbox nui-form-input" required="true" style="width: 100%;" onblur="checkFileName"/></td>
						<td style="width: 15%;" id="filename_error" class="errorText">
							<input id="errorname" class="nui-hidden"/>
						</td>
					</tr>
					<tr>
						<td style="width: 15%; text-align: right;">文号：</td>
						<td style="width: 70%"><input id="fileNo" name="file.fileNo" class="nui-textbox nui-form-input" style="width: 100%;" onblur="checkFileNo"/></td>
						<td style="width: 15%;" id="fileno_error" class="errorText">
							<input id="errorno" class="nui-hidden"/>
						</td>
					</tr>
					<tr>
						<td style="width: 15%; text-align: right;">关键字：</td>
						<td style="width: 70%"><input name="file.fileKey" class="nui-textbox nui-form-input"  style="width: 100%;" /></td>
						<td style="width: 15%;"></td>
					</tr>
					<tr>
						<td style="width: 15%; text-align: right;">所属目录：</td>
						<td style="width: 70%">
							<input id="listId" class="nui-textbox nui-form-input" required="true" style="width: 100%;" allowInput="false"/>
							<input id="listIds" name="file.listId" class="nui-hidden"/>
							<input id="listName" class="nui-hidden"/>							
							</td>
						<td style="width: 15%;"></td>
					</tr>
				</table>
				<input id="uploadname" name="file.uploadName" class="nui-hidden"/>
				<input id="realFileName" name="file.realFileName" class="nui-hidden"/> 
				<input id="filePath" name="file.filePath" class="nui-hidden"/> 
				<input id="previewName" name="file.previewName" class="nui-hidden"/> 
				<input id="onlyName" name="file.onlyName" class="nui-hidden"/> 
				<input name="file.fileStatus" class="nui-hidden" value="1" />
				<input id="fileId" class="nui-hidden" value=""/>
			</form>
			<form id="upload" method="post" enctype="multipart/form-data">
				<table border="0" cellpadding="1" cellspacing="2" align="center" style="width: 100%">
					<tr>
						<td style="width: 15%; text-align: right;">文件：</td>
						<td style="width: 70%"><input class="nui-htmlfile" name="Fdata" id="uploadfile" style="width: 100%;" onfileselect="onFileSelect" /></td>
						<td style="width: 15%;"></td>
					</tr>
				</table>
			</form>
		</div>
	</fieldset>
	<div style="padding: 5px;">
		<table border="0" cellpadding="1" cellspacing="2" align="center" style="width: 100%">
			<tr>
				<td colspan="3" align="center">
					<div id="radio_powerType" class="nui-radiobuttonlist" repeatItems="1" repeatLayout="table" repeatDirection="vertical"
						 textField="DICTNAME" valueField="DICTID" dataField="msg.dict" value="1" style="font-size: 13px" onvaluechanged="onValuechanged"
			        	 url="com.gotop.fileManger.jw.action.filegl.ditcfind.biz.ext?dicttypeid=FILE_JURISDICTION_TYPE" value="0"></div>
				</td>
			</tr>
			<tr>
				<td style="width: 15%; text-align: right;">已分配人员：</td>
				<td style="width: 70%; height:40px;" id="check">
					<input id="check_empBox" class="nui-textboxlist belongCheckInput" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
					<input id="down_empBox" class="nui-textboxlist belongDownInput" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
				</td>
				<td style="width: 15%">
	            	<a value="emp" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
	            </td>
			</tr>
			<tr>
				<td style="width: 15%; text-align: right;">已分配角色：</td>
				<td style="width: 70%; height:40px;" id="down">
					<input id="check_roleBox" class="nui-textboxlist belongCheckInput" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
					<input id="down_roleBox" class="nui-textboxlist belongDownInput" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
				</td>
				<td style="width: 15%">
	            	<a value="role" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
	            </td>
			</tr>
			<tr>
				<td style="width: 15%; text-align: right;">已分配机构：</td>
				<td style="width: 70%; height:40px;" id="update">
					<input id="check_orgBox" class="nui-textboxlist belongCheckInput" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
					<input id="down_orgBox" class="nui-textboxlist belongDownInput" value="5425" text="厦门分行" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
				</td>
				<td style="width: 15%">
	            	<a value="org" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
	            </td>
			</tr>
			<tr>
				<td style="width: 15%; text-align: right;">已分配群组：</td>
				<td style="width: 70%; height:40px;" >
					<input id="check_groupBox" class="nui-textboxlist belongCheckInput" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
					<input id="down_groupBox" class="nui-textboxlist belongDownInput" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
				</td >
				<td style="width: 15%">
	            	<a value="group" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
	            </td>
			</tr>
		</table>
	</div>
	<div class="nui-toolbar" style="text-align:center;padding-top:5px;padding-bottom:5px;" borderStyle="border:0;">
	    <a class="nui-button" style="width:60px;" iconCls="icon-save" onclick="submitForm()">保存</a>
	    <span style="display:inline-block;width:25px;"></span>
	    <a class="nui-button" style="width:60px;" iconCls="icon-cancel" onclick="onCancel()">取消</a>
	</div> 
	
	<script src="<%= request.getContextPath() %>/js/jquery.form.js" type="text/javascript"></script>
	
	<script type="text/javascript">
		nui.parse();
			
		/* 0（预览） 1（修改） 2（删除） 3（下载）  */
		var username="<%=userObject.getUserName()%>";
		var radio_powerType = nui.get("radio_powerType");
		var oldCheckPower=[],oldDownPower=[];
		var checkInputs = $(".belongCheckInput");
		var downInputs = $(".belongDownInput");
		//路径
		var path="<%=request.getContextPath()%>";
		var userid="<%=userObject.getUserId()%>";
		var form = new nui.Form("#filefrom");
		var fileinfo = nui.get("#uploadfile");

		//标准方法接口定义
		function SetData(data, datalist, username) {
			//跨页面传递的数据对象，克隆后才可以安全使用
			data = nui.clone(data);
			listsuperior(data.id);
			nui.get("uploadname").setValue(username);
		}
		
		//文件浏览按钮触发事件
		function onFileSelect(e){				
			/* var path=fileinfo.value;								
			filename=path.substring(path.lastIndexOf("\\")+1,path.length);
			nui.get("fileName").setValue(filename); */
			//checkFileName(filename);
		} 
		//文件添加事件
		function submitForm() {	
			var data=fileinfo.getValue();
			var error=nui.get("errorname").getValue(); 
			var fileName=nui.get("fileName").getValue(); 
			var errorno=nui.get("errorno").getValue();
			if(data==null||data==""||data==undefined){
				nui.alert("请选择上传文件");
				return;
			}else if(error){
				nui.alert("文件名重复，请修改文件名");
				return;
			}else if(fileName==null||fileName==""||fileName==undefined){
				nui.alert("请填写文件名");
				return;
			}else if(errorno){
				nui.confirm("文号重复，是否要保存数据？","保存确认",function(action){
					if(action!="ok") return;
					FileUpload();
				})
			}else{
				nui.confirm("是否要保存数据？","保存确认",function(action){
					if(action!="ok") return;
					FileUpload();
				})
			}
			
		}
		//查询当前目录上级目录
		function listsuperior(listid){
			var addr="";
			var json = nui.encode({"listid":listid}); //序列化成JSON
			$.ajax({
				url : "com.gotop.fileManger.hyq.entity.listManager.findList.biz.ext",
				type : "post",
				data : json,
				cache : false,
				contentType : 'text/json',
				success : function(data) {
					
					var obj = data.msg;
					if (obj.resCode == "1") {
						var meun=obj.list;
						nui.get("listIds").setValue(meun[0].LISTID);						
						for(var i=meun.length-1;i>=0;i--){
							addr+=meun[i].LISTNAME+"/";
						}
						nui.get("listId").setValue(addr); 
					}else{
						nui.alert("目录树查询失败");
					} 
				}
			});	
		}
		
		/**
		 * 往textboxlist中添加选择的数据
		 * @params powerType 权限类型
		 * @params boxType	    根据点击按钮的类型 添加到不同box里面
		 * @params list 	    获取Check选中的节点集合
		 */
		var map =["emp", "role", "org", "group"];
		function putDataTextBox(powerType, boxType, list){
			var text = "",value = "";
			var isEmp = (boxType == "emp"),isGroup = (boxType == "group");
			
			var editTextBox = findEditTextBox(powerType, boxType);
			for (var i = 0; i < list.length; i++) {
				var node = list[i];
				if (node.nodeType == "OrgOrganization" && isEmp) continue;
				if (node.nodeType != "Group" && isGroup) continue;
				if (i == list.length -1) {
					value += node["nodeId"];
					text  += node["nodeName"];
				} else {
					value += node["nodeId"] + ",";
					text  += node["nodeName"] + ",";
				}
			}
			if (value.endsWith(",")) value = strSplitLast(value);
			if (text.endsWith(",")) text = strSplitLast(text);
			editTextBox.setValue(value);
			editTextBox.setText(text);
		}
		
		var powerTypeName =['check','down'];
		function findEditTextBox(powerType, boxType) {
		    
	    	var boxId = powerTypeName[powerType]+"_"+boxType+"Box";
	    	return nui.get(boxId);
	    }
		
		function getSaveData(fileNum) {
	    	var checkList=[],downList=[];
			for (var i=0; i < 4; i++) {
	        	checkList[i] = nui.get(powerTypeName[0]+"_"+map[i]+"Box").getValue();
	        	downList[i] = nui.get(powerTypeName[1]+"_"+map[i]+"Box").getValue();
	        }
			var json = {fileNum:fileNum,
						oldCheckList:oldCheckPower,
						oldDownList:oldDownPower,
						newCheckList:checkList,
	            		newDownList:downList};
	        return json;	
	    }   
		
		/* 弹出权限设置界面  */
		var powerType,boxType;
		function onClick(e) {
			powerType = radio_powerType.getValue();
			boxType = e.sender.defaultValue;
			nui.open({
	        	url: path + "/hyq/list_powertree.jsp?type="+boxType,
	        	title: "权限配置",
	            iconCls: "icon-edit", 
	            width: 350, 
	            height: 350,
	            onload: function () {
	                var iframe = this.getIFrameEl();  
	                var editTextBox = findEditTextBox(powerType, boxType);
	                iframe.contentWindow.setTreeCheck(editTextBox.getValue());
	            },
	            ondestroy: function (action) {
					if (action == "ok") {
						
	                    var iframe = this.getIFrameEl();
	                    var chooseList = iframe.contentWindow.GetData();
	                    chooseList = nui.clone(chooseList);
	                    if (chooseList) {
	                    	putDataTextBox(powerType, boxType, chooseList, "nodeId", "nodeName");
	                    }
	                }
	            }
	        });
		}
		
		var hideInputs = [checkInputs,downInputs];
		function onValuechanged() {
			var value = radio_powerType.getValue();
			for (var i=0; i<2; i++) {
				if (value == i){
					hideInputs[i].show();
				} else {
					hideInputs[i].hide();
				}
			}
		}
		onValuechanged();
		
		/* 保存数据  */
	    function saveData(fileNum){
			var data = getSaveData(fileNum);
	      	$.ajax({
	        	url:"com.gotop.fileManger.jw.action.filegl.addFilePowerInfo.biz.ext",
	        	type:'POST',
	            data:data,
	            traditional: true,
	            success:function(data){
	            	
	 	        	if(data.msg=="success"){
	 	        		nui.alert("保存成功", "系统提示", function(action){
	                         if(action == "ok" || action == "close"){
	                             CloseWindow("saveFailed");
	                   		}
	                    });
	            	}else{
	             		nui.alert("保存失败", "系统提示", function(action){
	                         if(action == "ok" || action == "close"){

	                   		}
	                    });
	             	} 
	             }
	         }); 
	     }
		
		//判断当前字符串是否以str结束
	    if (typeof String.prototype.endsWith != 'function') {
	       String.prototype.endsWith = function (str){
	          return this.slice(-str.length) == str;
	       };
	    }
	    /* 切割ID字符串最后一位 */
	    function strSplitLast(obj) {
	    	return (obj).substring(0, obj.length-1);
	    }
	    function CloseWindow(action) {
	        if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
	        else window.close();
	    }
	    /* 取消 */
	    function onCancel() {
	        CloseWindow("cancel");
	    }		
	</script>
	<script src="<%= request.getContextPath() %>/js/filecs2.js" type="text/javascript"></script>
</body>
</html>

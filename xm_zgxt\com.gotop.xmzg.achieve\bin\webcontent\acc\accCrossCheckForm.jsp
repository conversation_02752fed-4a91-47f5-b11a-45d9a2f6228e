<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): wsd
  - Date: 2022-02-27 08:34:30
  - Description:
-->
<head>
<title>业绩账户分润审核</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
    <%@include file="/coframe/tools/skins/common.jsp" %>
</head>
<body>
<div id="form1" style="padding-top:5px;">
    <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
    <input id="taa_pre_id" name = "formData.taa_pre_id" class="nui-hidden"/>
    <input id="taa_id" name = "formData.taa_id" class="nui-hidden"/>
    <input id="flag" name = "formData.flag" class="nui-hidden"/>
    <input id="taa_emp" name = "formData.taa_emp" class="nui-hidden"/>
    <input id="taa_change_type" name = "formData.taa_change_type" class="nui-hidden"/>
    <input id="taa_busi_org" name = "formData.taa_busi_org" class="nui-hidden"/>
    <input id="taa_app_id" name = "formData.taa_app_id" class="nui-hidden"/>
     <input id="taa_zb_code" name = "formData.taa_zb_code" class="nui-hidden"/>
     <input id="f_taa_busi_no" name = "formData.taa_busi_no" class="nui-hidden"/>
     
     <input id="tac_rl_type" name = "q.tac_rl_type" class="nui-hidden"/>
     <input id="tac_zb_code" name = "q.tac_zb_code" class="nui-hidden"/>
     <input id="tac_busi_no" name = "q.tac_busi_no" class="nui-hidden"/>
     <tr>
        <th class="nui-form-label"><label>产品编号：</label></th>
        <td colspan="2" >  
         	<input id=taa_busi_no name = "formData.taa_busi_no" class="nui-textbox" readonly="readonly" style="width:150px" />  
        </td>
         <th class="nui-form-label"><label>产品名称：</label></th>
        <td colspan="2" >  
         	<input id="taa_busi_name" name = "taa_busi_name" class="nui-textbox asLabel" readonly="readonly" style="width:150px" />  
        </td>
      </tr>
      <tr>
        <th class="nui-form-label"><label>认领类型：</label></th>
        <td colspan="2" >  
         	<input id="taa_rl_type" name = "formData.taa_rl_type" class="nui-dictcombobox asLabel" readonly="readonly" disabled="true" style="width:150px;" valueField="dictID" textField="dictName" dictTypeId="JF_FPLX"/>  
        </td>
         <th class="nui-form-label"><label>指标或指标细项：</label></th>
        <td colspan="2" >  
         	<input id="taa_zb_name" name = "taa_zb_name" class="nui-textbox asLabel" readonly="readonly" style="width:150px" />  
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label>归属客户经理：</label></th>
        <td colspan="4" >  
         	<input id="taa_emp_name" name = "taa_emp_name" class="nui-textbox asLabel" readonly="readonly" style="width:150px" />  
        </td>
        
      </tr>
      <tr id="typeShow" style="display:none;"> 
        <th class="nui-form-label"><label>客户类型：</label></th>
        <td colspan="4" >  
			<input id="dd_cust_type" name = "formData.dd_cust_type" class="nui-dictcombobox asLabel" readonly="readonly" style="width:150px;" valueField="dictID" 
	        textField="dictName" dictTypeId="JF_GRCKKHLX2" required="true" onvaluechanged="typeChange"/>
	    </td> 
        </tr>
    </table>
    
    
    <div class="nui-panel" title="审核后分润信息"  style="width:100%;height:180px;" 
    	showToolbar="true" showCloseButton="false" showFooter="true"
	>
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.achieve.acc.queryAccCrossListApp.biz.ext" showPager="false" allowCellEdit="true" allowCellSelect="true" multiSelect="true" 
        editNextOnEnterKey="true"  editNextRowCell="true" oncellbeginedit="OnCellBeginEdit" oncellvalidation="onCellValidation">
	  
	    <div property="columns" >
	      <div field="taa_cr_empname" name="taa_cr_empname" headerAlign="center" align="center">分润客户经理
	      <!-- <input property="editor" class="nui-textbox" style="width:100%;" minWidth="150" required="true"/> -->
	       		<input property="editor" class="nui-textbox" style="width:100%;" minWidth="150"  />
	      </div>
	      <div field="taa_rate" headerAlign="center" align="center">分润比例（单位:%）
	      	<input property="editor" class="nui-textbox" style="width:100%;" minWidth="150"/>
	      </div>
	      <div field="taa_begin" align="center" vtype="required" headerAlign="center" renderer="dateStr">开始时间 
            <input property="editor"  class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd" onclick="selDate"/>
        </div>
        <div field="taa_end" align="center" vtype="required" headerAlign="center" renderer="dateStr">结束时间
            <input property="editor" class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd" />
        </div>
	    </div>
	</div>
	</div>
	
	
	<div class="nui-panel" title="当前分润信息"  style="width:100%;height:180px;" 
    	showToolbar="true" showCloseButton="false" showFooter="true" >
	 <div id="datagrid2" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.achieve.acc.queryAccCrossListGd.biz.ext" showPager="false" allowCellSelect="true" multiSelect="true" 
       >
	  <div name="accProfitsDiv" class="nui-hidden" ></div>
	    <div property="columns" >
	      <div field="tac_cr_empname" headerAlign="center" align="center" >分润客户经理
	       		<input property="editor" class="nui-textbox" style="width:100%;" minWidth="150"  />
	      </div>
	      <div field="tac_rate" vtype="int;range:1,100;" headerAlign="center" align="center">分润比例（单位:%）
	      <input property="editor" class="nui-textbox" style="width:100%;" minWidth="150" required="true" value="0"/>
	      </div>
	      <div field="tac_begin" align="center" vtype="required" headerAlign="center" renderer="dateStr">开始时间 
            <input property="editor" name="new_tac_begin"  class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd" onclick="selDate"/>
        </div>
        <div field="tac_end" align="center" vtype="required" headerAlign="center" renderer="dateStr">结束时间
            <input property="editor" class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd" />
        </div>
	    </div>
	 </div>
  </div>
  
	<div class="nui-panel" title="历史分润信息"  style="width:100%;height:180px;" 
    	showToolbar="true" showCloseButton="false" showFooter="true">
	 <div id="datagrid3" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.achieve.acc.queryAccCrossListLs.biz.ext" showPager="false" allowCellSelect="true" multiSelect="true" 
       >
	  
	  <div name="accProfitsDiv" class="nui-hidden" ></div>
	    <div property="columns" >
	      <div field="tac_cr_empname" headerAlign="center" align="center" >分润客户经理
	       		<input property="editor" class="nui-textbox" style="width:100%;" minWidth="150"  />
	      </div>
	      <div field="tac_rate" vtype="int;range:1,100;" headerAlign="center" align="center">分润比例（单位:%）
	      <input property="editor" class="nui-textbox" style="width:100%;" minWidth="150" required="true" value="0"/>
	      </div>
	      <div field="tac_begin" align="center" vtype="required" headerAlign="center" renderer="dateStr">开始时间 
            <input property="editor" name="new_tac_begin"  class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd" onclick="selDate"/>
        </div>
        <div field="tac_end" align="center" vtype="required" headerAlign="center" renderer="dateStr">结束时间
            <input property="editor" class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd" />
        </div>
	    </div>
	 </div>
  </div>
	
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save" onclick="onOk(1)">审核通过</a>
	          <span style="display:inline-block;width:25px;"></span>
	           <a class="nui-button" iconCls="icon-save" onclick="onOk(2)">审核不通过</a>
	          <span style="display:inline-block;width:25px;"></span>
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel()">关闭</a>
	        </td>
	      </tr>
	    </table>
	 </div>
	<script type="text/javascript">
    	nui.parse();
    	var form = new nui.Form("#form1");
    	var grid = nui.get("datagrid1");
    	var grid2 = nui.get("datagrid2");
		var grid3 = nui.get("datagrid3");
		
    	function CloseWindow(action){
      		if(window.CloseOwnerWindow) 
        		return window.CloseOwnerWindow(action);
      		else
        		return window.close();
    	}
    	
    	function onCancel(){
    		CloseWindow("cancel");
    	}
    	
    	function onOk(flag){
    		nui.get("flag").setValue(flag);
    		var data = form.getData(true,true);
            var json = nui.encode(data);

    		$.ajax({
                url: "com.gotop.xmzg.achieve.acc.checkPlAccCross.biz.ext",
                type: 'POST',
                data:json,
                cache: false,
                contentType:'text/json',
                success: function (json) {
                var returnJson = nui.decode(json);
				if(returnJson.exception == null && returnJson.iRtn == 0){
					nui.alert("操作成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("ok");
					}
				});
				}else{
					nui.alert(returnJson.msg, "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("fail");
					}
				});
			}
			}
       }); 
    	}
    	
    	//初始化表单数据
    	function setFormData(data) {
			var info = nui.clone(data);
			console.log(data);
			var taa_type = info.taa_type;
			//alert('2222');
			nui.get("taa_busi_no").setValue(info.taa_busi_no);
			nui.get("taa_busi_name").setValue(info.taa_busi_name);
			nui.get("taa_rl_type").setValue(info.taa_rl_type);
			nui.get("taa_zb_code").setValue(info.taa_zb_code);
			nui.get("taa_zb_name").setValue(info.taa_zb_name);
			nui.get("f_taa_busi_no").setValue(info.taa_busi_no);
			/* nui.get("taa_change_type").setValue(info.taa_change_type); */
			nui.get("taa_busi_org").setValue(info.taa_busi_org);
			
			nui.get("taa_emp").setValue(info.taa_emp);
			nui.get("taa_emp_name").setValue(info.taa_emp_name);
			nui.get("taa_app_id").setValue(info.taa_app_id);
			
			nui.get("tac_rl_type").setValue(info.taa_rl_type);
			nui.get("tac_busi_no").setValue(info.taa_busi_no);
			var zbCode = info.taa_zb_code;
			nui.get("tac_zb_code").setValue(info.taa_zb_code);
			//公司贷款利差收入 11003002，贸易融资表外12006001 票据承兑12007001需要先维护客户类型
		    if(zbCode == '11003002' || zbCode == '12006001' || zbCode == '12007001' ){
		    	$("#typeShow").show();
		    	nui.get("dd_cust_type").setValue(info.dd_cust_type);
			}

			search();
			
		}
		
		//查询
    	function search(){
       		//获取form中的表单数据
    		var formData = form.getData(true,true);
       		grid.load({"formData":formData.formData});

       		grid2.load({"queryData":formData.q});
       		grid3.load({"queryData":formData.q});
    	}
		
		function dateStr(e){
	    	var endDate = e.value;
	    	if(endDate instanceof Date){
	    		endDate = nui.formatDate ( endDate, 'yyyyMMdd' );
	    	}
	    	if(endDate!="" && endDate!=null && endDate.length > 10){
	    		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
	    	}
	    	return endDate;
	    }
    </script>
</body>
</html>
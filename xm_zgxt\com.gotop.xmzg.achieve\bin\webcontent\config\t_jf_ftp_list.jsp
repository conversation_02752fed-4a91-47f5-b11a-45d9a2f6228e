<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-05-29 16:10:11
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>ftp配置</title>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <table class="table" style="width:100%;">
			<tr>
				<th  class="tit">业务类型：</th>
				<td >
					<input name="queryData.TJF_BUSI_TYPE"  class="nui-dictcombobox" valueField="dictID" textField="dictName" emptyText="全部" dictTypeId="JF_FTP_BUSI" showNullItem="true" nullItemText="全部" style="width:150px;"/>
				</td>
					
				<th  class="tit">日期：</th>
				<td >
					<input id="queryData.TJF_START" name = "queryData.TJF_START" class="nui-datepicker"  style="width:150px;" allowInput="false" format="yyyyMMdd" onvalidation="comparedate" />~
					<input id="queryData.TJF_END" name = "queryData.TJF_END" class="nui-datepicker"  style="width:150px;"  allowInput="false" format="yyyyMMdd" onvalidation="comparedate"/>
				</td>
				<th>
				  <a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;
				  <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
				</th>
			</tr>
	    </table>
	  </div>
    </div>
  </div>
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
		     <a class="nui-button" iconCls="icon-edit" onclick="imp">导入</a>
           </td>
        </tr>
      </table>
    </div>
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;"
	  url="com.gotop.xmzg.achieve.config.t_jf_ftp_list.biz.ext" multiSelect="true"
	  sizeList=[5,10,20,50,100]  pageSize="10">
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      
	      <div field="TJF_BUSI_TYPE" headerAlign="center" align="center" renderer="busitype">业务类型</div>
	      <div field="TJF_START" headerAlign="center" align="center">开始时间</div>
	      <div field="TJF_END" headerAlign="center" align="center">结束时间</div>
	      <div field="TJF_TYPE" headerAlign="center" align="center" renderer="ontype">类型</div>
	      <div field="TJF_CYCLE" headerAlign="center" align="center" >期限（年）</div>
	      <div field="TJF_FTP" headerAlign="center" align="center" >ftp（%）</div>
	      <div field="EMPNAME" headerAlign="center" align="center" >操作人</div>
	      <div field="TJF_CREATE_TIME" headerAlign="center" align="center" dateFormat="yyyy-MM-dd HH:mm:ss">创建日期</div>
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">  
    nui.parse();
 	
    var grid = nui.get("datagrid1");
    var form = new nui.Form("#form1");
    search();
    
    function search(){
       form.validate();
       console.log(form.isValid() )
       if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
     
    function imp(){
		nui.open({
	        url:"<%=request.getContextPath()%>/achieve/config/t_jf_ftp_imp.jsp",
	        title: "导入ftp数据",
	        width: 550,
	        height: 280,
	        ondestroy: function (action) {
	    		grid.reload();
	        }
	    });	
	}
  function CloseWindow(action){
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
  
   	//重置
    function clean(){
	   form.reset();
    }
    
    function ontype(e){
	   return e.value == '0' ? '固定' : e.value == '1' ? '浮动' : e.value;
    }
 	function busitype(e){
 		return nui.getDictText("JF_FTP_BUSI", e.value);
 	}
 	//时间判断开始时间不能大于结束时间
    function comparedate(e){
    //debugger;
      var startDate = nui.get("queryData.TJF_START").getFormValue();
      var endDate = nui.get("queryData.TJF_END").getFormValue();
      if(startDate!="" && startDate.length > 8)
	    startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
	  if(endDate!="" && startDate.length > 8)
		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);

	  if(startDate != null && startDate !=  "" &&  endDate != "" && endDate != null) {
		      if(startDate>endDate){
		        e.errorText="结束日期必须大于开始日期";
		        e.isValid=false;
		      }else {
		          e.errorText="";
		          e.isValid=true;
		      }
        }
    } 
  </script>
</body>
</html>
<%@page pageEncoding="UTF-8"%>	
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2019-06-04 14:51:16
  - Description:
-->
<head>
	<title>业绩预约审核</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%@include file="/achieve/init.jsp"%>
	<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
</head>
<body>
  <div id="prameter" style="padding-top:5px;"> 
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table" align="center"> 
    <tbody>
   	 	 	<input id="ORGNAME" name = "ORGNAME" class="mini-hidden"/>
   	  		<input id="EMPNAME" name = "EMPNAME" class="mini-hidden"/>
   	  	  	<input id="TID_NAME" name = "TID_NAME" class="mini-hidden"/>
    <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>预约机构：</th> 
      <td><input id="TAA_ORG" name = "TAA_ORG"  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;" /></td> 
      <td><input id="TAA_ID"  name = "TAA_ID" class="mini-hidden" /></td>
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>客户经理：</th> 
      <td><input id="TAA_EMP" name = "TAA_EMP"  class="nui-buttonedit"  allowInput="false" onbuttonclick="selectEmp" style="width:150px;"/></td> 
     </tr>
    <!-- <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>指标明细：</th> 
      <td><input allowInput="false"  id="TID_CODE" name="TID_CODE" class="nui-combobox"  textField="TEXT" valueField="ID" dataField="list" style="width:300px;"/></td> 
     </tr>-->
       <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>业务条线：</label></th>
        <td colspan="3" >  
        	<div id="TIP_CODE" name="TIP_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TIP_NAME" valueField="TIP_CODE" 
		    	url="com.gotop.xmzg.achieve.configClaPro.tip_get.biz.ext" multiSelect="false" allowInput="false" showNullItem="false" emptyText="请选择..."
		    	onvaluechanged="onTipChanged" required="true">     
			    <div property="columns">
			        <div header="业务条线代码" field="TIP_CODE" width="60"></div>
			        <div header="业务条线名称" field="TIP_NAME" width="120"></div>
			    </div>
		    </div>
		</div>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>指标：</label></th>
        <td colspan="3" >  
        	<div id="TI_CODE" name="TI_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TI_NAME" valueField="TI_CODE" 
		    	multiSelect="false" allowInput="false" showNullItem="false" emptyText="请先选择业务条线..." required="true"
		    	onvaluechanged="onTiChanged">     
			    <div property="columns">
			        <div header="指标代码" field="TI_CODE" width="60"></div>
			        <div header="指标名称" field="TI_NAME" width="120"></div>
			    </div>
		    </div>
		</div>
        </td> 
      </tr>
      <tr id="tidTr">
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>指标细项：</label></th>
        <td colspan="3" >  
        	<div id="TID_CODE" name="TID_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TID_NAME" valueField="TID_CODE" 
		    	multiSelect="false" allowInput="false" showNullItem="false" emptyText="请先选择指标..." required="true">     
			    <div property="columns">
			        <div header="指标细项代码" field="TID_CODE" width="60"></div>
			        <div header="指标细项名称" field="TID_NAME" width="120"></div>
			    </div>
		    </div>
		</div>
        </td> 
      </tr>
      
     <tr> 
      <th class="nui-form-label">产品编号：</th> 
      <td><input allowInput="false"  class="nui-textbox" id="TAA_BUSI_NO" name="TAA_BUSI_NO" style="width:150px;"/></td> 
     </tr>
      <tr> 
      <th class="nui-form-label">客户类型：</th> 
      <td><input allowInput="false" class="nui-textbox" id="TAA_CUST_TYPE" name="TAA_CUST_TYPE" style="width:150px;"/></td> 
     </tr>
      <tr> 
      <th class="nui-form-label">客户名称：</th> 
      <td><input allowInput="false" class="nui-textbox" id="TAA_CUST_NAME" name="TAA_CUST_NAME" style="width:150px;"/></td> 
     </tr>
       <tr> 
      <th class="nui-form-label">证件类型：</th> 
      <td><input allowInput="false" class="nui-textbox" id="TAA_CARD_TYPE" name="TAA_CARD_TYPE" style="width:150px;"/></td> 
     </tr>
     <tr> 
      <th class="nui-form-label">证件号码：</th> 
      <td><input allowInput="false" class="nui-textbox" id="TAA_CARD_NO" name="TAA_CARD_NO" style="width:150px;"/></td> 
     </tr>
       <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>预约起始日：</th> 
      <td><input allowInput="false" class="nui-textbox" id="TAA_START_DATE" name="TAA_START_DATE" format="yyyy-MM-dd"  style="width:150px;" required = "true" renderer="setdate"/></td> 
     </tr>
       <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>预约结束日：</th> 
      <td><input allowInput="false" class="nui-textbox" id="TAA_END_DATE" name="TAA_END_DATE"  format="yyyy-MM-dd"  style="width:150px;" required = "true" renderer="setdate"/></td> 
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>预约业绩：</th> 
      <td><input allowInpuht="false" class="nui-textbox" id="TAA_NUM" name="TAA_NUM" style="width:150px;" required = "true"/></td> 
     </tr>
     <!-- <tr> 
      <th class="nui-form-label">审核人员：</th> 
      <td><input  allowInput="false"  class="nui-buttonedit"  allowInput="false" onbuttonclick="selectEmp" id="TAA_APP_EMP" name="TAA_APP_EMP" style="width:150px;"/></td> 
     </tr>-->
    </tbody>
   </table> 
   <div class="nui-toolbar" style="padding:0px;" borderstyle="border:0;"> 
    <table width="100%"> 
     <tbody>
      <tr> 
       	<td style="text-align:center;"> 
	       	<a class="nui-button" iconcls="icon-search" onclick="seve">审核通过</a> 
	       	<span style="display:inline-block;width:25px;"></span> 
	       	<a class="nui-button" iconcls="icon-cancel" onclick="onCancel">审核不通过</a>
       	</td> 
      </tr> 
     </tbody>
    </table> 
   </div> 
  </div>

  <script type="text/javascript">
  	nui.parse();
  	
  		function setData(data){
  		//跨页面传递的数据对象，克隆后才可以安全使用
        data = nui.clone(data);
        var form = new nui.Form("#prameter");
        form.setData(data);
        nui.get("TAA_ORG").setValue(data.TAA_ORG);
        nui.get("TAA_EMP").setValue(data.TAA_EMP);
        nui.get("TID_CODE").setValue(data.TID_CODE);
        nui.get("TI_CODE").setValue(data.TI_CODE);
        nui.get("TIP_CODE").setValue(data.TIP_CODE);
        
        nui.get("TAA_ORG").setText(data.ORGNAME);
        nui.get("TAA_EMP").setText(data.EMPNAME);
        nui.get("TID_CODE").setText(data.TID_NAME);
        nui.get("TI_CODE").setText(data.TI_NAME);
        nui.get("TIP_CODE").setText(data.TIP_NAME);
        
        nui.get("TIP_CODE").setEnabled(false);
         nui.get("TI_CODE").setEnabled(false);
          nui.get("TID_CODE").setEnabled(false);
  	}
  	
  	//审核通过
  	function seve(){
  		var form = new nui.Form("#prameter");
		form.validate();
		if (form.isValid() == false) return;
		var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
		//提交数据
        nui.ajax({
       		url: "com.gotop.xmzg.achieve.appointment.appointment_update_1.biz.ext",
           	type: "post",
           	data: nui.encode({"obj":form.getData()}),
           	contentType:'text/json',
           	success: function (text) {
        	   nui.hideMessageBox(load);  //隐藏遮罩层
        	   var code = text.code;
        	   var msg = text.msg;
        	   if(code != "1"){
        		  	nui.alert(msg, "系统提示");
	           }else{
          			nui.alert(msg, "系统提示", function(action){
						if(action == "ok"){
							CloseWindow("saveSuccess");
						}
					});
	          }
           }
       });
  	}
  	
  	//审核不通过
  		function onCancel(){
  		var form = new nui.Form("#prameter");
		form.validate();
		if (form.isValid() == false) return;
		var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
		//提交数据
        nui.ajax({
       		url: "com.gotop.xmzg.achieve.appointment.appointment_update_2.biz.ext",
           	type: "post",
           	data: nui.encode({"obj":form.getData()}),
           	contentType:'text/json',
           	success: function (text) {
        	   nui.hideMessageBox(load);  //隐藏遮罩层
        	   var code = text.code;
        	   var msg = text.msg;
        	   if(code != "1"){
        		  	nui.alert(msg, "系统提示");
	           }else{
          			nui.alert(msg, "系统提示", function(action){
						if(action == "ok"){
							CloseWindow("saveSuccess");
						}
					});
	          }
           }
       });
  	}
  	
  	    function onTypeChanged(e) {
		 typeChanged(e.value);
    }
    
    function typeChanged(val) {
		 if(val == 2){
		 	$("#tidTr").show();
		 }else{
		 	$("#tidTr").hide();
		 }
    }
    
    var tip = nui.get("TIP_CODE");
    var ti = nui.get("TI_CODE");
    var tid = nui.get("TID_CODE");
    function onTipChanged(e,val){
        ti.setValue("");
        tid.setValue("");
        if(!val) val = tip.getValue();
        var url = "com.gotop.xmzg.achieve.configClaPro.ti_get.biz.ext?code=" + val;
        ti.setUrl(url);
    }
    
    function onTiChanged(e,val){
        tid.setValue("");
        if(!val) val = ti.getValue();
        var url = "com.gotop.xmzg.achieve.configClaPro.tid_get.biz.ext?code=" + val;
        tid.setUrl(url);
    }
  
  </script>
</body>
</html>
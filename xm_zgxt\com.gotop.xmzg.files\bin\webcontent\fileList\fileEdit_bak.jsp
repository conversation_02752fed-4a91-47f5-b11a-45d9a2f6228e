<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%@page import="com.eos.data.datacontext.UserObject" %>
<%@include file="/coframe/dict/common.jsp"%>
<html>
<!-- 
  - Author(s): lyl
  - Date: 2019-12-03 09:36:10
  - Description:
-->
<head>
</head>
<%
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>
<body>
	<div id="fileForm" style="padding-top:5px;">
	    <input name="map.FILES_TYPE_OLD" id="FILES_TYPE_OLD"  class="nui-hidden" />
	    <input name="map.DEAL_ORG_OLD" id="DEAL_ORG_OLD"  class="nui-hidden" />
	    <input name="map.DEAL_NAME_OLD" id="DEAL_NAME_OLD"  class="nui-hidden" />
	    <input name="map.EMP_NAME_OLD" id="EMP_NAME_OLD"  class="nui-hidden" />
	    <input name="map.EMP_CODE_OLD" id="EMP_CODE_OLD"  class="nui-hidden" />
	    <input name="map.CUSTOMER_NAME_OLD" id="CUSTOMER_NAME_OLD"  class="nui-hidden" />
	    <input name="map.STORAGE_ADDRESS_OLD" id="STORAGE_ADDRESS_OLD"  class="nui-hidden" />
	    <input name="map.STORAGE_LOCATION_OLD" id="STORAGE_LOCATION_OLD"  class="nui-hidden" />
	    <input name="map.CHECK_TIME_OLD" id="CHECK_TIME_OLD"  class="nui-hidden" />
	    <input name="map.CHECK_TYPE_OLD" id="CHECK_TYPE_OLD"  class="nui-hidden" />
	    <input name="map.INFORMATION_ID" id="INFORMATION_ID"  class="nui-hidden" />
	    <input name="map.FILES_NAME" id="FILES_NAME"  class="nui-hidden" /><!-- 从档案主表获取  -->
	    <input name="map.FILES_NAME_NEW" id="FILES_NAME_NEW"  class="nui-hidden" /><!-- 编辑拼接  -->
	    <input name="map.EMPNAME" id="EMPNAME"  class="nui-hidden" />
	    <input name="map.BUSINESS_TYPE" id="BUSINESS_TYPE"  class="nui-hidden" /><!-- 拼接档案名称2 -->
	    <input name="map.CONTRACT_NUMBER" id="CONTRACT_NUMBER"  class="nui-hidden" /><!-- 拼接档案名称3 -->
	    <input name="map.START_TIME" id="START_TIME"  class="nui-hidden" /><!-- 拼接档案名称4 -->
	    
		<table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
		 	<tr>
				<th class="nui-form-label"><label for="type$text">档案种类：</label></th>
				<td >
					<input id="FILES_TYPE" class="nui-dictcombobox " name="map.FILES_TYPE"  emptyText="请选择" dictTypeId="FILES_TYPE"  style="width:150px;" onvalueChanged="filesNameShow" readOnly="true" required = "true" allowInput = "false"/>
  				</td>
	      	</tr>
	      	</tr>
	      		<tr>
	      		<th class="nui-form-label"><label for="type$text">原档案名称：</label></th>
	      		<td >
	      			<input name="map.FILES_NAME" id="FILES_NAME"  class="nui-textbox" style="width:150px;" readOnly="true"/>
	      		</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">归属机构：</label></th>
  				<td >
  					<input id="DEAL_ORG" name = "map.DEAL_ORG"  class="nui-buttonedit"   onbuttonclick="OrgonButtonEdit" style="width:150px;" required = "true" allowInput = "false" />
	      		</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">客户经理：</label></th>
	      		<td >
	      			<input name="map.EMP_NAME" id="EMP_NAME"  class="nui-buttonedit" style="width:150px;"  class="nui-buttonedit"   onbuttonclick="EmponButtonEdit" required = "true" allowInput = "false" />
	      		</td>
	      	</tr>
	      		<tr>
	      		<th class="nui-form-label"><label for="type$text">客户名字：</label></th>
	      		<td >
	      			<input name="map.CUSTOMER_NAME" id="CUSTOMER_NAME"  class="nui-textbox" style="width:150px;"/>
	      		</td>
	      	</tr>
	      	<tr>
				<th class="nui-form-label"><label for="type$text">库存地址：</label></th>
				<td >
					<input class="nui-dictcombobox" name="map.STORAGE_ADDRESS" id="STORAGE_ADDRESS"  emptyText="请选择" dictTypeId="FILES_STORAGE_ADDRESS"  style="width:150px;"/>
  				</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">货架号/箱号：</label></th>
	      		<td >
	      			<input name="map.STORAGE_LOCATION" id="STORAGE_LOCATION"  class="nui-textbox" style="width:150px;"/>
	      		</td>
	      	</tr>
	      	<tr id = "dh_1">
				<th class="nui-form-label"><label for="type$text">贷后查询日期：</label></th>
				<td >
					<input class="nui-datepicker" name="map.CHECK_TIME" id="CHECK_TIME"    style="width:150px;"/>
  				</td>
	      	</tr>
	      	<tr id = "dh_2">
	      		<th class="nui-form-label"><label for="type$text">贷后检查类型：</label></th>
	      		<td >
	      			<input name="map.CHECK_TYPE" id="CHECK_TYPE"  class="nui-dictcombobox"  dictTypeId="CHECK_TYPE"  emptyText="请选择"  style="width:150px;"/>
	      		</td>
	      	</tr>
		</table>
	    <div class="nui-toolbar" style="padding:0px;"   borderStyle="border:0;">
			<table width="100%">
		    	<tr>
		        	<td style="text-align:center;">
		          		<a class="nui-button" iconCls="icon-save" id="saveButtorn"  onclick="saveData">确定</a>
		          		<span style="display:inline-block;width:25px;"></span>
		          		<a class="nui-button" iconCls="icon-reset" onclick="clean()">重置</a>
		        	</td>
		      	</tr>
		   	</table>
		</div>
	</div>
<script type="text/javascript">
    nui.parse();
    var fileForm = new nui.Form("#fileForm");
    
    //与父页面的方法2对应：页面间传输json数据
    function setFormData(data){
                   
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);
        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
             var json = infos.record;
             json.map.FILES_TYPE_OLD = json.map.FILES_TYPE;
             json.map.DEAL_ORG_OLD = json.map.DEAL_ORG;
             json.map.DEAL_NAME_OLD = json.map.DEAL_NAME;
             json.map.EMP_CODE_OLD = json.map.EMPNAME;
             json.map.EMP_NAME_OLD = json.map.EMP_NAME;
             json.map.CUSTOMER_NAME_OLD = json.map.CUSTOMER_NAME;
             json.map.STORAGE_ADDRESS_OLD = json.map.STORAGE_ADDRESS;
             json.map.STORAGE_LOCATION_OLD = json.map.STORAGE_LOCATION;
             json.map.CHECK_TIME_OLD = json.map.CHECK_TIME;
             json.map.CHECK_TYPE_OLD = json.map.CHECK_TYPE;
             var form = new nui.Form("#fileForm");//将普通form转为nui的form
             form.setData(json);
             //档案名称
             nui.get("FILES_NAME_NEW").setValue(json.map.FILES_NAME);
             //机构回显
             nui.get("DEAL_ORG").setValue(json.map.DEAL_ORG);
             nui.get("DEAL_ORG").setText(json.map.DEAL_NAME);
             //人员回显
             var empCode = json.map.EMPNAME;//人员code
             showEmpName(empCode);
             
         }
    }
    
    //关闭添加窗口
 	function CloseWindow(action){
  		if(window.CloseOwnerWindow) 
    		return window.CloseOwnerWindow(action);
  		else
    		return window.close();
    }
    
     //重置查询信息
	function clean(){
	   	var ftype = nui.get("FILES_TYPE_OLD").getValue();
	   	var orgId = nui.get("DEAL_ORG_OLD").getValue();
	   	var orgName = nui.get("DEAL_NAME_OLD").getValue();
	   	var empcode = nui.get("EMP_CODE_OLD").getValue();
	   	var empname = nui.get("EMP_NAME_OLD").getValue();
	   	var custname = nui.get("CUSTOMER_NAME_OLD").getValue();
	   	var addr = nui.get("STORAGE_ADDRESS_OLD").getValue();
	   	var loc = nui.get("STORAGE_LOCATION_OLD").getValue();
	   	var ctime = nui.get("CHECK_TIME_OLD").getValue();
	   	var ctype = nui.get("CHECK_TYPE_OLD").getValue();
	   	fileForm.reset();
	   	
	   	nui.get("FILES_TYPE").setValue(ftype);
	   	nui.get("DEAL_ORG").setValue(orgId);
	   	nui.get("DEAL_ORG").setText(orgName);
	   	nui.get("EMP_NAME").setValue(empcode);
	   	nui.get("EMP_NAME").setText(empname);
	   	nui.get("CUSTOMER_NAME").setValue(custname);
	   	nui.get("STORAGE_ADDRESS").setValue(addr);
	   	nui.get("STORAGE_LOCATION").setValue(loc);
	   	nui.get("CHECK_TIME").setValue(ctime);
	   	nui.get("CHECK_TYPE").setValue(ctype);
	   	
	   	nui.get("FILES_TYPE_OLD").setValue(ftype);
	   	nui.get("DEAL_ORG_OLD").setValue(orgId);
	   	nui.get("DEAL_NAME_OLD").setValue(orgName);
	   	nui.get("EMP_CODE_OLD").setValue(empcode);
	   	nui.get("EMP_NAME_OLD").setValue(empname);
	   	nui.get("CUSTOMER_NAME_OLD").setValue(custname);
	   	nui.get("STORAGE_ADDRESS_OLD").setValue(addr);
	   	nui.get("STORAGE_LOCATION_OLD").setValue(loc);
	   	nui.get("CHECK_TIME_OLD").setValue(ctime);
	   	nui.get("CHECK_TYPE_OLD").setValue(ctype);
	   	
    }
    
    //机构树回显
     function OrgonButtonEdit(e){
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/files/archiveList/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                             //将值机构id变成机构code
                            var data={orgId:data.ID};
					        var json = nui.encode(data);
					        var URL="com.gotop.xmzg.files.fileLedger.getOrgcode.biz.ext";
					        $.ajax({
					        	url: URL,
					            type: 'POST',
					            data: json,
					            async:false,
					            cache: false,
					            contentType:'text/json',
					            success: function (text){
					                var returnJson = nui.decode(text);
					                var result = returnJson.resultList;
					                btnEdit.setValue(result[0].ORGCODE);
							    }
					  		}); 
                            
                        }
                    }
                }
            });            
             
        } 
    //人员树回显
     function EmponButtonEdit(e){
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/files/archiveList/empTree.jsp?type=emp",
                showMaxButton: false,
                title: "选择人员",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);
                        if (data) {
                         	btnEdit.setValue(data.nodeId);
                            btnEdit.setText(data.nodeName);
                            var data2={empId:data.nodeId};
					        var json = nui.encode(data2);
					        var URL="com.gotop.xmzg.files.fileLedger.getEmpcode.biz.ext";
					        $.ajax({
					        	url: URL,
					            type: 'POST',
					            data: json,
					            async:false,
					            cache: false,
					            contentType:'text/json',
					            success: function (text){
					                var returnJson = nui.decode(text);
					                var result = returnJson.resultList;
	                            	nui.get("EMPNAME").setValue(result[0].EMPCODE);
							    }
					  		});
                        }
                    }
                }
            });            
             
        } 
        
    // 人员信息回显
    function showEmpName(e){
    	var empCode = e;
    	nui.get("EMP_NAME").setValue(empCode);
    	nui.get("EMPNAME").setValue(empCode);
    	$.ajax({
				url:"com.gotop.xmzg.files.fileList.getEmpInfo.biz.ext",
				type:'post',
				data:nui.encode({empCode:empCode}),
				cache:false,
				async:false,
				contentType:'text/json',
				success: function (text) {
					var returnJson = nui.decode(text);
					var EMPNAME = returnJson.result.EMPNAME;
					nui.get("EMP_NAME").setText(EMPNAME);
					nui.get("EMP_NAME_OLD").setValue(EMPNAME);
				}
				
			});
    }
        
    function filesNameShow(){
    	var type = nui.get("FILES_TYPE").getValue();
    	//档案名称--根据档案类型拼接
    	var name = '';
    	if(type == '01'){
        	name = nui.get("CUSTOMER_NAME").getValue()+'-'
			+nui.get("BUSINESS_TYPE").getValue()+'-'
			+nui.get("CONTRACT_NUMBER").getValue();
        }else if(type == '02'){
        	name = nui.get("CUSTOMER_NAME").getValue()+'-'
			+nui.get("BUSINESS_TYPE").getValue()+'-'
			+nui.get("CONTRACT_NUMBER").getValue();
        }else if(type == '03'){
        	name = nui.get("CUSTOMER_NAME").getValue()+'-'
			+nui.get("BUSINESS_TYPE").getValue()+'-'
			+nui.get("START_TIME").getFormValue();
        }else if(type == '04'){
        	var temp4 = nui.get("CUSTOMER_NAME").getValue()+'-'
			+nui.get("BUSINESS_TYPE").getValue()+'-'
			+nui.get("CONTRACT_NUMBER").getValue();
        }else if(type == '05'){
        	name = nui.get("CUSTOMER_NAME").getValue()+'-票据承兑业务-'
			+nui.get("CONTRACT_NUMBER").getValue();
        }else if(type == '06'){
        	name = nui.get("CUSTOMER_NAME").getValue()+'-票据贴现业务-'
			+nui.get("CONTRACT_NUMBER").getValue();
        }else if(type == '07'){
        	name = '合作机构档案-'+nui.get("CUSTOMER_NAME").getValue()+'-'
			+nui.get("BUSINESS_TYPE").getValue();
        }else if(type == '08'){
        	name = '拒贷档案-'+nui.get("CUSTOMER_NAME").getValue()+'-'
			+nui.get("BUSINESS_TYPE").getValue();
        }else if(type == '09'){
        	name = '征信档案-'+nui.get("CUSTOMER_NAME").getValue()+'-'
			+nui.get("START_TIME").getFormValue();
        } if(type == '10'){
        	name = nui.get("CUSTOMER_NAME").getValue()+'-零售授信贷后档案-'
			+nui.get("CHECK_TIME").getFormValue();
        }else if(type == '11'){
        	name = nui.get("CUSTOMER_NAME").getValue()+'-零售支用贷后档案-'
			+nui.get("CHECK_TIME").getFormValue();
        }else if(type == '12'){
        	name = nui.get("CUSTOMER_NAME").getValue()+'-法人授信贷后档案-'
			+nui.get("CHECK_TIME").getFormValue();
        }else if(type == '13'){
        	name = nui.get("CUSTOMER_NAME").getValue()+'-法人支用贷后档案-'
			+nui.get("CHECK_TIME").getFormValue();
        }else if(type == '14'){
        	name = nui.get("CUSTOMER_NAME").getValue()+'-票据承兑贷后档案-'
			+nui.get("CHECK_TIME").getFormValue();
        }else if(type == '15'){
        	 name = nui.get("CUSTOMER_NAME").getValue()+'-信审档案-'
			+nui.get("CHECK_TIME").getFormValue();
        }
    	nui.get("FILES_NAME_NEW").setValue(name);
    }
    
    //保存修改过的数据
    function saveData(){
    	fileForm.validate();            
        if (fileForm.isValid() == false) return;
        var map = fileForm.getData(true,true);
        var json = nui.encode(map);
        var URL="com.gotop.xmzg.files.fileList.uptFileRecordList.biz.ext";
        $.ajax({
        	url: URL,
            type: 'POST',
            data: json,
            cache: false,
            contentType:'text/json',
            success: function (text){
                var returnJson = nui.decode(text);
				if(returnJson.flag == "1"){
					nui.alert("保存成功", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							CloseWindow("saveSuccess");
						}
					});
				}else if(returnJson.flag == "0"){
					nui.alert("保存失败", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							//CloseWindow("saveFailed");
						}
					});
				}
		    }
  		});   
    }
  
	
        
    
</script>
</body>
</html>
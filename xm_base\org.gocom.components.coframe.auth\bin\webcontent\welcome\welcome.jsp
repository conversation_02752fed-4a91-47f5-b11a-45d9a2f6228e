<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): shitf
  - Date: 2013-03-03 16:20:11
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>首页</title>
</head>
<body style="width:100%;">
  
  
		
		<div id="panel1" dataField="results" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="org.gocom.components.coframe.auth.Welcom.queryProToDoList.biz.ext"
	  showPager="false">
		    <div property="columns">
		    <div name="action1" header="标题"  width="37%"  renderer="onActionRenderer" ></div> 
		        <div header="申请人" field="EMPNAME" width="20%" ></div>
		        <div header="接收时间" field="STARTDATE" width="27%" allowSort="true" ></div>
		        <div header="上一处理人" field="DEAL_EMPNAME" width="15%"  ></div>
		       <!-- <div header="系统名称" name="sysname1" width="32%"  renderer="onActionRendererName"></div> -->
		    </div>
		</div>


		     <div id="panel2" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
			  url="com.gotop.xmzg.riskMoniter.remindSet.query_remindHomePage.biz.ext" sortMode="client" onrowclick="selectionChanged"
			  showPager="false">
		    <div property="columns">
		         <div field="remindName" headerAlign="center" align="center"  allowSort="true">提醒项目</div>
				 <div field="custType" headerAlign="center" align="center"  allowSort="true">提醒内容</div>
				 <div headerAlign="center" align="center"   allowSort="true"renderer="remindDateRender">提醒时间</div>
		    </div>
		</div>
		<div id="panel3" class="nui-listbox"  style="margin:-5px;"
		     dataField="resultList" url="org.gocom.components.coframe.auth.notice.select_infoApplyList.biz.ext"  allowResize="false">     
		    <div property="columns">
		    	<div name="TITLE" header="标题内容"  width="50%"  renderer="onActionInfo" ></div> 
		        <div header="发布人" field="EMPNAME" width="20%" ></div>
		        <div header="发布日期" field="TXNDATE" width="30%" ></div>
			    <%--<div header="阅读状态" field="READ_STATUS" width="15%" ></div>--%>
		    </div> 
		</div>
  
  		<div id="panel4" class="nui-listbox"  style="margin:-5px;"
		     dataField="resultList" url="org.gocom.components.coframe.auth.notice.query_noticeQueryPageList.biz.ext" >     
		    <div property="columns">
		    	<div name="TITLE" header="标题"  width="50%"  renderer="onActionRelease" ></div>
		    	<div header="发布人" field="EMPNAME" width="20%" ></div>
		    	<div header="发布时间" field="TXNDATE" width="30%" ></div>      	  
		    </div> 
		</div>

		<!-- <div id="panel4" dataField="messages" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
			  url="org.gocom.components.coframe.auth.Welcom.queryProUnviewedMessage.biz.ext"
			  showPager="false">   
		    <div property="columns">
		    <div name="action4" header="标题"  width="30%"  renderer="onActionRenderer" ></div> 
		        <div header="内容 " field="MESSAGE" width="45%" ></div>
		        <div header="发送者" field="SENDER" width="15%" ></div>
		        <div header="创建时间" field="CREATETIME2" width="25%" ></div>
		    </div>
		</div> -->
		

		<!-- <div id="panel5" dataField="messages" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
			  url="org.gocom.components.coframe.auth.Welcom.queryProViewedMessage.biz.ext"
			  showPager="false">   
		    <div property="columns">
		    <div name="action5" header="标题"  width="30%"  renderer="onActionRenderer" ></div> 
		        <div header="内容 " field="MESSAGE" width="45%" ></div>
		        <div header="发送者" field="SENDER" width="15%" ></div>
		        <div header="确认时间" field="CONFIRMTIME2" width="25%" ></div>
		    </div>
		</div> -->
		

  
  <link href="css/portal.css" rel="stylesheet" type="text/css" />
  <script src="js/Portal.js" type="text/javascript"></script>
  <script type="text/javascript">
  
    var portal = new nui.ux.Portal();
    /* portal.set({
        style: "width: 100%;height:400px",
        columns: [1050, "100%"]
    }); */
    
    portal.set({
        style: "width: 100%;height:400px",
        columns: ["50%", "50%"]
    });
    portal.render(document.body);

    //panel
    portal.setPanels([
        { column: 0, id: "p1", title: "流程待办", showCloseButton: false, showCollapseButton: true, body: "#panel1", height: 300 },
        { column: 0, id: "p2", title: "消息提醒", showCloseButton: false, showCollapseButton: true, body: "#panel2", height: 300 },
        
        { column: 1, id: "p3", title: "信息发布", showCloseButton: false, showCollapseButton: true, body: "#panel3", height: 300},
        { column: 1, id: "p4", title: "公告信息", showCloseButton: false, showCollapseButton: true, body: "#panel4", height: 300}
    ]); 
    /* portal.setPanels([
        { column: 0, id: "p1", title: "流程待办", showCloseButton: false, showCollapseButton: true, body: "#panel1", height: 230 },
        { column: 0, id: "p2", title: "流程已办（最近一周）", showCloseButton: false, showCollapseButton: true, body: "#panel2", height: 230 },

        { column: 1, id: "p4", title: "流程未阅通知", showCloseButton: false, showCollapseButton: true, body: "#panel4", height: 150},
        { column: 1, id: "p5", title: "流程已阅通知（最近一周）", showCloseButton: false, showCollapseButton: true, body: "#panel5", height: 150},
        { column: 1, id: "p6", title: "信息发布", showCloseButton: false, showCollapseButton: true, body: "#panel6", height: 150 }
        
    ]); */
    
   
    /* var bodyEl = portal.getPanelBodyEl("p2");
    bodyEl.appendChild(document.getElementById("Button2"));  */


    //获取配置的panels信息
    var panels = portal.getPanels();
      
    var grid1 = nui.get("panel1");  //待办任务
   // var grid4 = nui.get("panel4");  //未阅通知
 
    
    //grid1加载后再执行grid4
	grid1.load(null, function(){
	    grid1.sortBy("STARTDATE", "asc");
	    //grid4.load();
	});
  	 
     var grid2 = nui.get("panel2"); //已办任务
     grid2.load();
    
     /* var grid5 = nui.get("panel5");  //已阅通知
     grid5.load();   */
     
     
     function onActionRenderer(e){
	  	 var grid = e.sender;
	     var record = e.record;
	     
	     var s = "";
	     
	     if(e.column.name == "action1"){ //流程代办
	     //	var workItemName = record.WORKITEMNAME;
	     var noticeType=record.NOTICETYPE;
	     
	     
	    if(noticeType!=null&&noticeType!=""){
	     
	      if(noticeType=='NOTICE'){
	     	s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\" onclick=\"processNoticeSp("+record.INFOID+")\">"+record.NOTICENAME+"</a>";
	     	
	     }else if(noticeType=='INFO'){
	     	s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\" onclick=\"processInfoSp("+record.INFOID+")\">"+record.NOTICENAME+"</a>";
	    	     
	     }else if(noticeType=='BACKINFO'){
	     	     	s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\" onclick=\"processInfoBack("+record.INFOID+")\">"+record.NOTICENAME+"</a>";
	     	     
	     }else if(noticeType=='BACKNOTICE'){
	     	     	s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\" onclick=\"processNoticeBack("+record.INFOID+")\">"+record.NOTICENAME+"</a>";
	     
	}
	     }else{
	     
	    	var workItemName = record.PROCESSINSTNAME;
	     	var workItemId = record.WORKITEMID;
	     	s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"/default/bps/wfclient/task/dispatchTaskExecute.jsp?workItemID="+workItemId+"\">"+workItemName+"</a>";
	     }
	     }
     
	     return s;
	}
  

    
    
    function onActionInfo(e){
		var record = e.record;
		var title = record.TITLE;
	  	s = "<a class=\"dgBtn1\" style=\"color:blue\" href=\"javascript:void(0);\" onclick=\"infodetail("+e.rowIndex+")\">"+title+"</a>";
	  	return s;
	}
	
	function onActionRelease(e){
		var record = e.record;
		var title = record.TITLE;
	  	s = "<a class=\"dgBtn1\" style=\"color:blue\" href=\"javascript:void(0);\" onclick=\"releasedetail("+e.rowIndex+")\">"+title+"</a>";
	  	return s;
	}
	
	function infodetail(value){
    	var grid = nui.get("panel3");
   		var row= grid.data[value];
   		var info_id=row.INFO_ID;
    	nui.open({
				url: "<%=request.getContextPath() %>/notice/infoApply/infoApply_detail_1.jsp?info_id="+info_id,
	          	title:'详情',
	          	width:750,
          		height:600,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            var data = {pageType:"edit",record:{map:row}};
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){		        	
		               grid.load("org.gocom.components.coframe.auth.notice.select_infoApplyList.biz.ext");	             
		        }
		});
    }
    
    function releasedetail(value){
    	var grid = nui.get("panel4");
   		var row= grid.data[value];
   		var info_id=row.INFO_ID;
    	nui.open({
				url: "<%=request.getContextPath() %>/notice/noticeQueryPage/noticeQueryPage_detail.jsp?info_id="+info_id,
	          	title:'详情',
	          	width:750,
          		height:600,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            var data = {pageType:"edit",record:{map:row}};
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		        	if(action=="saveSuccess"){
		                //重定向
		                search();
		             }
		        }
		});
    }
    
     //提醒方式
	    function remindDateRender(e){
	    	var str = e.row.remindStartDate + "至" + e.row.remindEndDate;
	    	return str;
	    }
	    
	function selectionChanged(){
	       /* var rows = grid.getSelecteds();
	       if(rows.length>1){
	           nui.get("update").disable();
	       }else{
	           nui.get("update").enable();
	       } */
	       detail();
	    }    
	  //查看详情
	    function detail() {
			var row = grid2.getSelected();
			if(row!=null){
				nui.open({
		          url:"<%=request.getContextPath() %>/riskMoniter/remindSet/remindObj_detail.jsp",
		          title:'查看详情',
		          width:600,
		          height:400,
		          onload:function(){
		             var iframe = this.getIFrameEl();
		             
		             //方法1：后台查询一次，获取信息回显
		             /* var data = row;
		             iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 

		             //方法2：直接从页面获取，不用去后台获取
		             var data = {pageType:"detail",record:{map:row}};
	                 iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		          },
		          ondestroy:function(action){
		          
		          }
			    });
			}else{
				nui.alert("请选中一条记录！");
		  	}	
        }  
	    
  
  //首页点击事项链接，跳转操作
 <%--  function onActionRenderer(e){
  	
  	 var grid = e.sender;
     var record = e.record;
     
     var s = "";
     
     if(e.column.name == "action1"){ //流程代办
     //	var workItemName = record.WORKITEMNAME;
    var workItemName = record.PROCESSINSTNAME;
     	var workItemId = record.WORKITEMID;
     	s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"/default/bps/wfclient/task/dispatchTaskExecute.jsp?workItemID="+workItemId+"\">"+workItemName+"</a>";
     }else if(e.column.name == "action2"){  //流程已办
    
     	//var workItemName = record.workItemName;
     	var workItemName = record.processInstName;
     	    	
     //	s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"/default/bps/wfclient/task/taskList.jsp?taskType=finishedSelf\">"+workItemName+"</a>";
    s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\" onclick=\"viewyiban("+e.rowIndex+")\">"+workItemName+"</a> ";
    
     }else if(e.column.name == "action3"){  //消息提醒
     	var message = record.MESSAGE;
     	var addres_url = record.ADDRES_URL;
     	var id = record.ID;
     	s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"<%=request.getContextPath() %>"+addres_url+" \" onclick=\"myclick("+id+")\">"+message+"</a>";
     	//s = "<a class=\"dgBtn\" style=\"color:blue\" onclick=\"myclick("+record+")\" >"+message+"</a>";
     }else if(e.column.name == "action4"){  //未阅通知
     	var title = record.TITLE;
     	//s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"/default/bps/wfclient/task/notificationList.jsp?state=UNVIEWED\">"+title+"</a>";
      s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\" onclick=\"viewweiyue("+e.rowIndex+")\">"+title+"</a> ";
     }else if(e.column.name == "action5"){  //已阅通知
     	var title = record.TITLE;
     	var workitemid = record.NOTIFICATIONID;
     	//s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"/default/bps/wfclient/task/notificationList.jsp?state=VIEWED\">"+title+"</a>";
      s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\" onclick=\"viewyiyue("+workitemid+")\">"+title+"</a> ";
     }else if(e.column.name == "action6"){  //信息发布
     	var message = record.MESSAGE;
     	var addres_url = record.ADDRES_URL;
     	s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"<%=request.getContextPath() %>"+addres_url+" \">"+message+"</a>";
     }else{
     }
     
     return s;
  } --%>
  
  
  
  
  //首页绿色通道
  function onActionRendererName(e){
  	
  	 var grid = e.sender;
     var record = e.record;
     
     var s = "";
     
     if(e.column.name == "sysname1"){ //流程代办
     	var status = record.STATUS;  //绿色通道
     	var c_status = record.C_STATUS;  //初次上报，补充调查
     	var overtime_flag = record.OVERTIME_FLAG;  //时限是否超时的标志
     	var sysname = record.PROCESSINSTNAME;
     	  var processdefname=record.PROCESSDEFNAME; 
     	 if(processdefname=='com.gotop.credit.parameter.Credit_apply')
     	{ 
	     		     	
	     	 if(status=='1' && c_status==null)
	     	{
	     	s = "<span style=color:orange;>[0]</span>"+"<span style=color:green;>"+sysname+"</span>";
	     	}else if(status=='1' && c_status!=null)
	     	{
	     	s = "<span style=color:orange;>"+c_status+"</span>"+"<span style=color:green;>"+sysname+"</span>";
	     	}else if(overtime_flag=='1' && c_status==null)
	     	{
	     	s = "<span style=color:orange;>[0]</span>"+"<span style=color:red;>"+sysname+"</span>";
	     	}else if(overtime_flag=='1' && c_status!=null)
	     	{
	     	s = "<span style=color:orange;>["+c_status+"]</span>"+"<span style=color:red;>"+sysname+"</span>";
	     	}else if(overtime_flag=='2' && c_status==null)
	     	{
	     	s = "<span style=color:orange;>[0]</span>"+"<span style=color:#FFCC00;>"+sysname+"</span>";
	     	}else if(overtime_flag=='2' && c_status!=null)
	     	{
	     	s = "<span style=color:orange;>["+c_status+"]</span>"+"<span style=color:#FFCC00;>"+sysname+"</span>";
	     	}else if(status==null && c_status!=null)
	     	{
	     	s="<span style=color:orange;>["+c_status+"]</span>"+sysname;
	     	}else 
	     	{
	     	s="<span style=color:orange;>[0]</span>"+sysname;
	     	} 
     	
     	 }else
     	 {
     	 s=sysname;
     	 } 
     	 
     	 
     	
     }else if(e.column.name == "sysname2"){  //流程已办
     
     	var status = record.STATUS;
     	  var c_status = record.C_STATUS;
     	var sysname = record.processInstName;
     	
       var processdefname=record.PROCESSDEFNAME; 
     	 if(processdefname=='com.gotop.credit.parameter.Credit_apply')
     	{ 
	     	
	     	  if(status=='1' && c_status==null)
	     	{
	     	s = "<span style=color:orange;>[0]</span>"+"<span style=color:green;>"+sysname+"</span>";
	     	}else if(status=='1' && c_status!=null)
	     	{
	     	s = "<span style=color:orange;>"+c_status+"</span>"+"<span style=color:green;>"+sysname+"</span>";
	     	}else if(status==null && c_status!=null)
	     	{
	     	s="<span style=color:orange;>["+c_status+"]</span>"+sysname;
	     	}else 
	     	{
	     	s="<span style=color:orange;>[0]</span>"+sysname;
	     	} 
     	
     	 }else
     	 {
     	 s=sysname;
     	 }
     	
    
     }else
     {
     }
     return s;
     }
  
  
  
  function myclick(id){
	  
	$.ajax({
	        url:"org.gocom.components.coframe.auth.Welcom.updateMesStatus.biz.ext",
	        type:'POST',
	        data:'id='+id,
		    cache:false,
		    async:false,
        	dataType:'json',
	        success:function(text){
	          
	        }
	  });
	  
  }
  
    function view(value)
  {
  
      nui.open({
	       url:"<%=request.getContextPath() %>/bps/wfclient/task/dispatchTaskExecute.jsp?workItemID="+value,
	       title:'查看',
	       width:1050,
	       height:500,
	       onload:function(){
	       },
	       ondestroy:function(action){
	       if(action=="ok"){
	       
	          window.location.reload();
	        }
	          }
	       });
  }
  
    function viewyiban(value)
  {
   var grid = nui.get("panel2");
   var row= grid.data[value];
  var id=row.workItemID;
    nui.open({
				url: "<%=request.getContextPath() %>/myinformation/matters/task.jsp?ItemID="+id,
				title: "查看",
				width: 1050,
				height: 550,
				onload: function () {
					var iframe = this.getIFrameEl();
					if(iframe.contentWindow.initData) {
						iframe.contentWindow.initData(row, "self", true);
					}	
				},
				ondestroy: function (action){
					if (action == "ok") {
						taskListDataGridObj.load();
					} else if (action == "execute") {  
						doOperate(rowIndex, false);					
					}
				}
			});
 
  }
  
   function viewweiyue(value)
  {
   var grid = nui.get("panel4");
   var row= grid.data[value];
  var id=row.NOTIFICATIONID;
    nui.open({
	       url:"<%=request.getContextPath() %>/myinformation/matters/ProUnviewedMessage_view.jsp?ID="+id,
	       title:'查看',
	       width:850,
	       height:250,
	       onload:function(){
	       
	       var json = nui.encode({map:row});
	       $.ajax({
		          url:"com.gotop.iimp.myinformation.matters.addProViewedMessage.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	
					
		          }
		        });
	       },
	       ondestroy:function(action){
	       if(action=="cancel"){
	          window.location.reload();
	        }
	          }
	       }); 
 
  }
    
    
      function viewyiyue(value)
  {
 
    nui.open({
	       url:"<%=request.getContextPath() %>/myinformation/matters/ProViewedMessage_view.jsp?ID="+value,
	       title:'查看',
	       width:850,
	       height:250,
	       onload:function(){
	       },
	       ondestroy:function(action){
	       if(action=="cancel"){
	          window.location.reload();
	        }
	          }
	       }); 
  
  }
  
  
  
  function processNoticeSp(value){
    
    	nui.open({
				url: "<%=request.getContextPath() %>/notice/processNoticeSp/noticeApplySp.jsp",
	          	title:'公告审批',
	          	width:750,
          		height:650,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();	       
	            	iframe.contentWindow.setFormData(value);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		        	if(action=="saveSuccess"){
		                //重定向
		                grid1.load();
		             }
		        }
		});
    }
    
   
    
      function  processInfoSp(value){
    
    	nui.open({
				url: "<%=request.getContextPath() %>/notice/processNoticeSp/infoApplySp.jsp",
	          	title:'公告审批',
	          	width:750,
          		height:650,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();	       
	            	iframe.contentWindow.setFormData(value);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		        	if(action=="saveSuccess"){
		                //重定向
		                grid1.load();
		             }
		        }
		});
    }
    
    
    function processInfoBack(value){
    
    nui.open({
				url: "<%=request.getContextPath() %>/notice/processNoticeSp/infoBack.jsp",
	          	title:'公告审批',
	          	width:750,
          		height:650,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();	       
	            	iframe.contentWindow.setFormData(value);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		        
		                //重定向
		                grid1.load();
		           
		        }
		});
    }
  	
  	
  	
  	
  	    function processNoticeBack(value){
    
   		 nui.open({
				url: "<%=request.getContextPath() %>/notice/processNoticeSp/noticeBack.jsp",
	          	title:'公告审批',
	          	width:750,
          		height:650,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();	       
	            	iframe.contentWindow.setFormData(value);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		        
		                //重定向
		                grid1.load();
		           
		        }
		});
    }
  </script>
</body>
</html>
<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-01-05 11:41:00
  - Description:
-->
<head>
<%-- <%@include file="/coframe/tools/skins/common.jsp" %> --%>
<%@include file="/coframe/dict/common.jsp"%>
<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
</head>
<body>
  <div id="form1" style="padding-top:5px;">
    <input class="nui-hidden" name="map.REC_ID"/>
    <!--<input class="nui-hidden" name="map.EMPIDS" id="empids"/>->
     <!--<input class="nui-hidden" id="rad2" name="map.IS_WHOLEBANK"/>-->
     <div  class="nui-hidden"  id="c_role" ></div>
       <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
      
     <tr>
        <th class="nui-form-label"><label for="map.type$text">群组名称：</label></th>
        <td colspan="3" >  
         <input id="groupname" name = "map.GROUP_NAME" class="nui-textbox" vtype="maxLength:64" required="true" style="width:400px;"/>  
          
        </td> 
      </tr>
      
       <tr>
        <th class="nui-form-label"><label for="map.type$text">群组描述：</label></th>
        <td colspan="3" >  
         <input id="groupDetail" name = "map.GROUP_DETAIL" class="nui-textarea" vtype="maxLength:125" style="width:400px;height:100px"/>  
          
        </td> 
      </tr>
      
       <tr>
        <th class="nui-form-label"><label for="map.type$text">群组人员：</label></th>
        <td colspan="3" >  
         <input id="btnEdit1" name = "map.EMPIDS"  class="nui-textboxlist"   allowInput="false" required="true"  style="width:400px;height:100px"/><br/>  
         <a href="#" onclick="EmponButtonEdit()" style="color:blue;text-decoration:underline;">人员选择</a>    
         <a href="#" onclick="cleanEmp()" style="color:blue;text-decoration:underline;">清空</a></td>
        </td> 
      </tr>
      
      <tr id="text" style="display:none">
        <th class="nui-form-label"><label for="map.type$text">是否全行应用：</label></th>
        <td colspan="3" >  
        <!--   <input name="map.IS_WHOLEBANK2" type="radio" value="0"/>否&nbsp;
	      <input name="map.IS_WHOLEBANK2" type="radio" value="1" checked="checked"/>是&nbsp;   
	     -->
	     <input class="nui-dictradiogroup" dictTypeId="YESORNO" name="map.IS_WHOLEBANK"/>
      
        </td> 
      </tr>
      
    </table>
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save" onclick="onOk">保存</a>
	          <span style="display:inline-block;width:25px;"></span>
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  </div>

  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);
    
    //与父页面的方法2对应：页面间传输json数据
    function setFormData(data){
                   
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);

        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
        
	      //表单数据回显
             var json = infos.record;
             var form = new nui.Form("#form1");//将普通form转为nui的form
             form.setData(json);
             
             var rec_id = json.map.REC_ID;  
		       var json = nui.encode({rec_id:rec_id});
			   $.ajax({
			        url:"org.gocom.components.coframe.userInfoManage.groupManage.queryGroupInfoByID.biz.ext",
			        type:'POST',
			        data:json,
			        cache:false,
			        contentType:'text/json',
			        success:function(text){
			          obj = nui.decode(text);
			          //nui.get("empids").setValue(obj.xmap.EMPIDS);   
			          nui.get("btnEdit1").setValue(obj.xmap.EMPIDS); 
			          nui.get("btnEdit1").setText(obj.xmap.EMPNAMES);   
			         
			        }
			      });
			      
			  //判断当前登录人是否是系统管理员
	    		$.ajax({
			        url:"org.gocom.components.coframe.userInfoManage.groupManage.isSysadmin.biz.ext",
			        type:'POST',
			        data:'',
			        cache:false,
			        async:false,
	        		dataType:'json',
			        success:function(text){
			          //如果是系统管理员
			          if(text.flag == "1"){
			           document.getElementById("text").style.display = "";
			           nui.get("c_role").setValue("sysadmin");
			          }
			        }
			 });
	      
             
            
         }
    }
    
    //人员、机构回显
     function EmponButtonEdit() {
            var btnEdit1 = nui.get("btnEdit1");
            nui.open({
                url:"<%=request.getContextPath() %>/userInfoManage/groupManage/groupManage/EmpAndOrg_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                onload : function() {
					var iframe = this.getIFrameEl();
					iframe.contentWindow.setTreeCheck(btnEdit1.getValue());
				},
                ondestroy : function(action) {
							if (action == "ok") {
								var iframe = this.getIFrameEl();
								var list = iframe.contentWindow.GetData();
								list = nui.clone(list);
								if (list) {
									// 将树选中节点设置到box显示
									putDataTextBox(list);
								}
							}
						}
            });            
             
        }   
    /**
		 * 往textboxlist中添加选择的数据
		 * @params list 	 获取Check选中的节点集合
		 */
		function putDataTextBox(list){
			var text = "",value = "";
			var boxObj = nui.get("btnEdit1");
			for (var i = 0; i < list.length; i++) {
				var node = list[i];
				if (node.nodeType == "OrgOrganization") continue;
				if (i == list.length -1) {
					value += node["id"];
					text  += node["text"];
				} else {
					value += node["id"] + ",";
					text  += node["text"] + ",";
				}
			}
			if (value.endsWith(",")) value = strSplitLast(value);
			if (text.endsWith(",")) text = strSplitLast(text);
			boxObj.setValue(value);
			boxObj.setText(text);
		}
					
		//判断当前字符串是否以str结束
	     if (typeof String.prototype.endsWith != 'function') {
	       String.prototype.endsWith = function (str){
	          return this.slice(-str.length) == str;
	       };
	     }
	     
	     /* 切割ID字符串最后一位 */
		function strSplitLast(obj) {
			return (obj).substring(0, obj.length - 1);
		}
    
    
    function onOk(){
      saveData();
    }
    
    function saveData(){   
      //var iswholebank2 = $("input[name='map.IS_WHOLEBANK2']:checked").val();
      //nui.get("rad2").setValue(iswholebank2);
      form.validate();
      if(form.isValid()==false) return;
        
      var data = form.getData(false,true);
      var json = nui.encode(data);
      
      var c_role = nui.get("c_role").getValue();
          
      var a= nui.loading("正在检测登录人身份，请稍等...","提示");
      if(c_role != "sysadmin"){
           if(checkisExist(json) == '1')
		   {
		     nui.hideMessageBox(a);
		     nui.alert("该群组为全行群组，只有系统管理员才执行修改操作！");		     
		     return false;
		   }
      }
      nui.hideMessageBox(a);
      
      $.ajax({
        url:"org.gocom.components.coframe.userInfoManage.groupManage.update_groupManager.biz.ext",
        type:'POST',
        data:json,
        cache:false,
        contentType:'text/json',
        success:function(text){
            var returnJson = nui.decode(text);
			if(returnJson.exception == null && returnJson.iRtn == 1){
			
				nui.alert("修改成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
					 
						CloseWindow("saveSuccess");
					}
				});
			}else{
				nui.alert("修改失败", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveFailed");
					}
				});
			}
        }
      });
   
    }
    
    function onReset(){
      form.setData(obj);
      form.setChanged(false);
    } 
    
    function onCancel(){
      CloseWindow("cancel");
    }
    
     function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
    
    function cleanEmp(){
		 //nui.get("empids").setValue("");
		 nui.get("btnEdit1").setValue("");
		 nui.get("btnEdit1").setText("");
	}
	
	//判断该群组为全行群组时，只有系统管理员才执行修改操作
    function checkisExist(map){
      var vala;
      $.ajax({
        url:"org.gocom.components.coframe.userInfoManage.groupManage.checkIsExit.biz.ext",
        type: 'POST',
        data:map,
        cache: false,
        async:false,
        contentType:'text/json',
        success:function(text){
          vala = text.res;
        }
      });
      return vala;
    }
    
  </script>
</body>
</html>
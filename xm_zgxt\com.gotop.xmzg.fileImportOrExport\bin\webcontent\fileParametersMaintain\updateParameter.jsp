<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): zhanghongkai
  - Date: 2019-05-09 09:21:04
  - Description:
-->
<head>
	<%@include file="/coframe/tools/skins/common.jsp" %>
	<title>添加文件基本参数</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script type="text/javascript" src="<%=request.getContextPath() %>/js/jquery.form.js"></script>
</head>
<body>
	<div class="nui-fit">
		<form id="form" action="com.gotop.xmzg.fileImportOrExport.updateFileParameter.flow" method="post" enctype="multipart/form-data">
			<input id="ID" name="map/ID" class="nui-hidden">
			<input id="CHECK_TYPE" name="map/CHECK_TYPE" class="nui-hidden"><!-- 考评导入: 方案类型 -->
			<input id="FILENAME" name="map/TEMPLATE_NAME" class="nui-hidden">
			<input id="OPE_CONTENT"  class="nui-hidden" value="[<%=request.getRemoteAddr() %>]修改文件基本技术参数" name="map/OPE_CONTENT" />
			<table border="0" cellpadding="1" cellspacing="2" style="width:100%;table-layout:fixed;">
			    <tr>
			        <th style="width:150px;text-align:right;">文件类别名称：</th>
			        <td style="width:50%">
			            <input id="FILE_NAME" name="map/FILE_NAME" class="nui-textbox" style="width:100%;" required="true"
			            	onValidation="checkFileName"/>
			        </td>
			        <th style="width:150px;text-align:right;">文件类别代码：</th>
			        <td style="width:50%">
			            <input id="FILE_CODE" name="map/FILE_CODE" class="nui-textbox" style="width:100%;" required="true"/>
			        </td>
			    </tr>
			    <tr>
			        <th style="text-align:right;">业务条线：</th>
			        <td >
			            <input id="BUSINESS_LINE" class="nui-dictcombobox" required="true" valueField="dictID" textField="dictName" style="width:100%;"
		          			dictTypeId="BUSINESS_LINE" name="map/BUSINESS_LINE" emptyText="请选择.." />
			        </td>
			        <th style="text-align:right;">文件类型：</th>
			        <td >
			            <input id="FILE_TYPE" class="nui-dictcombobox" required="true" valueField="dictID" textField="dictName" style="width:100%;"
		          			dictTypeId="FILE_TYPE" name="map/FILE_TYPE" emptyText="请选择.."/>
			        </td>
			    </tr>
			    
			    <!--考评导入专有字段↓-->
			    <tr id="check_tr_1" style="display:none">
			        <th style="text-align:right;">导入类型：</th>
			        <td >
	                    <input id="CHECKIMP_TYPE" name="map/CHECKIMP_TYPE" class="nui-combobox" emptyText="请选择.."
	                       data="[{id: 1, text: '人员导入'}, {id: 2, text: '机构导入'}, {id: 3, text: '指标名称导入'}, {id: 4, text: '考评信息导入'}, {id: 5, text: '考核指标导入'}]" 
			            	style="width:100%;" required="true" onValueChanged="checkimpTypeChange"/>
			        </td>
			        <th style="text-align:right;">考核方案名称：</th>
			        <td >
			            <input class="nui-combobox"  valueField="ID" textField="CHECK_NAME" style="width:100%;"
	          			name="map/CHECK_ID" required="true" emptyText="请选择.." id="CHECK_ID" dataField="checkNamesList" onValueChanged="checkNameChange"/>
			        </td>
			    </tr>
			    
			    <tr id="check_tr_2" style="display:none">
			        <th id="ASSESS_TYPE_TH" style="text-align:right;">考评指标类型：</th>
			        <td id="ASSESS_TYPE_TD">
			            <input class="nui-combobox"  valueField="ID" textField="TYPE_NAME" style="width:100%;"
	          			name="map/ASSESS_TYPE" required="true" emptyText="请选择.." id="ASSESS_TYPE" dataField="assessTypesList" onValueChanged="checkAssessTypeChange"/>
			        </td>
			        <th id="EXAMINE_NAME_TH" style="text-align:right;display:none;" >指标名称：</th>
			        <td id="EXAMINE_NAME_TD" style="display:none;">
			            <input class="nui-combobox"  valueField="ID" textField="EXAMINE_NAME" style="width:100%;"
	          			name="map/EXAMINE_NAME" required="true" emptyText="请选择.." id="EXAMINE_NAME" dataField="examineNamesList"/>
			        </td>
			    </tr>
			    <!--考评导入专有字段↑-->
			    
			    <tr>
			        <th style="text-align:right;">操作类型：</th>
			        <td >
			            <input id="OPERATION_TYPE" class="nui-dictcombobox" required="true" valueField="dictID" textField="dictName" style="width:100%;"
		          			dictTypeId="OPERATION_TYPE" name="map/OPERATION_TYPE" emptyText="请选择.." onValueChanged="operationTypeChange"/>
			        </td>
			        <th style="text-align:right;">操作周期：</th>
			        <td >
			            <input id="OPERATION_CYCLE" class="nui-dictcombobox" required="true" valueField="dictID" textField="dictName" style="width:100%;"
		          			dictTypeId="OPERATION_CYCLE" name="map/OPERATION_CYCLE" emptyText="请选择.."/>
			        </td>
			    </tr>
			    <tr>
			        <th style="text-align:right;">上传权限设置：</th>
			        <td>
			            <input id="import_security" name="map/IMPORT_SECURITY" class="nui-buttonedit" style="width:100%;" onbuttonclick="selectImportSecurity"/>
			        </td>
			        <th style="text-align:right;">文件路径：</th>
			        <td >
			            <input id="IMPORT_FILE_PATH" name="map/IMPORT_FILE_PATH" class="nui-textbox" style="width:100%;" required="true"/>
			        </td>
			    </tr>
			    <tr>
			        <th style="text-align:right;">下载权限设置：</th>
			        <td>
			            <input id="export_security" name="map/EXPORT_SECURITY" class="nui-buttonedit" style="width:100%;" onbuttonclick="selectExportSecurity"/>
			        </td>
			        <th style="text-align:right;">模版文件路径：</th>
			        <td>
			            <input id="TEMPLATE_FILE_PATH" name="map/TEMPLATE_FILE_PATH" class="nui-textbox" style="width:100%;"/>
			        </td>
			    </tr>
			    <tr>
			        <th style="text-align:right;">文件上传处理方式：</th>
			        <td>
			            <input id="IMPORT_TYPE" class="nui-dictcombobox" valueField="dictID" textField="dictName" style="width:100%;"
		          			dictTypeId="IMPORT_DEAL_TYPE" name="map/IMPORT_TYPE" emptyText="请选择.." onValueChanged="importDealTypeChange"/>
			        </td>
			        <th style="text-align:right;">临时文件路径：</th>
			        <td>
			            <input id="TEMP_FILE_PATH" name="map/TEMP_FILE_PATH" class="nui-textbox" style="width:100%;"/>
			        </td>
			    </tr>
			    <tr id="oldTemplate" style="display:none;">
			    	<th style="text-align:right;">旧模版文件：</th>
			        <td >
			            <input id="TEMPLATE_PATH" name="map/TEMPLATE_PATH" class="nui-hidden" style="width:100%;" readOnly="true"/>
			            <a id="template_path" style="color:blue;cursor:pointer;" onClick="downloadTeamplate()" style="width:100%;"></a>
			        </td>
			    </tr>
			    <tr>
			        <th style="text-align:right;">模版文件：</th>
			        <td >
			            <input id="IMPORT_FILE" name="IMPORT_FILE" type="file" size="60" style="width:100%;" required="true" onchange="templateChange()"/>
			        </td>
			        <th style="text-align:right;">是否拼接机构号：</th>
			        <td>
			            <input id="IS_LINK_ORGCODE" name="map/IS_LINK_ORGCODE" class="nui-combobox"  textField="text" valueField="id" data="[{'id': '1', 'text': '是'}, {'id': '2', 'text': '否'}]"
			            	style="width:100%;"/>
			        </td>
			    </tr>
			    <tr>
			    	<th style="text-align:right;">导入数据库表名：</th>
			        <td >
			            <input id="TABLE_NAME" name="map/TABLE_NAME" class="nui-textbox" style="width:100%;" />
			        </td>
			        <th style="text-align:right;">起始行：</th>
			        <td>
			            <input id="START_ROW" name="map/START_ROW" value="1" class="nui-spinner" style="width:100%;"/>
			        </td>
			    </tr>
			    <tr>
			    	<th style="text-align:right;">是否自动导入日期：</th>
			        <td >
			            <input id="IS_IMPORT_DATE" name="map/IS_IMPORT_DATE" class="nui-combobox" style="width:100%;" 
			            	textField="text" valueField="value" data="[{value:'y',text:'是'},{value:'n',text:'否'}]"
			            	emptyText="请选择.." onValueChanged="isImportDateChanged"/>
			        </td>
			        <th style="text-align:right;">日期字段名：</th>
			        <td>
			            <input id="DATE_COLUMN_NAME" name="map/DATE_COLUMN_NAME" class="nui-textbox" style="width:100%;"/>
			        </td>
			    </tr>
			    <tr>
			    	<th style="text-align:right;">是否保存导入序号：</th>
			        <td >
			            <input id="IS_SAVE_NO" name="map/IS_SAVE_NO" class="nui-combobox" style="width:100%;" 
			            	textField="text" valueField="value" data="[{value:'y',text:'是'},{value:'n',text:'否'}]"
			            	emptyText="请选择.." onValueChanged="isSaveNoChanged"/>
			        </td>
			        <th style="text-align:right;">序号字段名：</th>
			        <td>
			            <input id="NO_COLUMN_NAME" name="map/NO_COLUMN_NAME" class="nui-textbox" style="width:100%;"/>
			        </td>
			    </tr>
			    <tr>
			    	<th style="text-align:right;">是否保存操作员工号：</th>
			        <td >
			            <input id="IS_SAVE_CODE" name="map/IS_SAVE_CODE" class="nui-combobox" style="width:100%;" 
			            	textField="text" valueField="value" data="[{value:'y',text:'是'},{value:'n',text:'否'}]"
			            	emptyText="请选择.." onValueChanged="isSaveCodeChanged"/>
			        </td>
			        <th style="text-align:right;">操作员工号字段名：</th>
			        <td>
			            <input id="CODE_COLUMN_NAME" name="map/CODE_COLUMN_NAME" class="nui-textbox" style="width:100%;"/>
			        </td>
			    </tr>
			    <tr>
			        <th style="text-align:right;">是否触发前置存储过程：</th>
			        <td>
			            <input id="IS_PRE_PROCEDURE" name="map/IS_PRE_PROCEDURE" class="nui-combobox" data="[{id: 1, text: '是'}, {id: 2, text: '否'}]" value="2" 
			            	style="width:100%;" required="true" onValueChanged="isPreProcedureChange"/>
			        </td>
			    </tr>
			    <tr>
			        <th style="text-align:right;">前置存储过程名称：</th>
			        <td >
			            <input id="PRE_PROCEDURE_NAME" name="map/PRE_PROCEDURE_NAME" class="nui-textbox" style="width:100%;"/>
			        </td>
			        <th style="text-align:right;">前置存储过程参数：</th>
			        <td >
			            <input id="PRE_PROCEDURE_PARAM" name="map/PRE_PROCEDURE_PARAM" class="nui-textbox" style="width:100%;" />
			        </td>
			    </tr>
			    <tr>
			        <th style="text-align:right;">是否触发后置存储过程：</th>
			        <td>
			            <input id="IS_SUF_PROCEDURE" name="map/IS_SUF_PROCEDURE" class="nui-combobox" data="[{id: 3, text: '是'}, {id: 4, text: '否'}]" value="4" 
			            	style="width:100%;" required="true" onValueChanged="isSubProcedureChange"/>
			        </td>
			    </tr>
			    <tr>
			        <th style="text-align:right;">后置存储过程名称：</th>
			        <td >
			            <input id="SUF_PROCEDURE_NAME" name="map/SUF_PROCEDURE_NAME" class="nui-textbox" style="width:100%;"/>
			        </td>
			        <th style="text-align:right;">后置存储过程参数：</th>
			        <td >
			            <input id="SUF_PROCEDURE_PARAM" name="map/SUF_PROCEDURE_PARAM" class="nui-textbox" style="width:100%;"/>
			        </td>
			    </tr>
			    <tr>
			        <th style="text-align:right;">固定值字段：</th>
			        <td >
			            <input id="COLUMN_1" name="map/COLUMN_1" class="nui-textbox" style="width:100%;"/>
			        </td>
			        <!--  <th style="text-align:right;">为空是否导入：</th>
			        <td >
			            <input id="IS_IMPORT" name="map/IS_IMPORT" class="nui-combobox" textField="text" valueField="id"  data="[{'id': '0', 'text': '正常导入'}, {'id': '1', 'text': '不导入'}]"  style="width:100%;" />
			        </td> -->
			    </tr>
			    <!-- <tr>
			    <th style="text-align:right;">文件标识：</th>
			        <td >
			            <input id="COLUMN_2" name="map/COLUMN_2" class="nui-textbox" style="width:100%;"/>
			            <textarea id="COLUMN_2" name="map/COLUMN_2" class="mini-textarea" style="width:250%;" emptyText="请输入对应txt文件标识，若有多个，请用'|'分隔"></textarea>
			        </td>
			    </tr> -->
			</table>
			<div id="add" style="text-align:center">
				<a class="nui-button" iconCls="icon-ok" onClick="save">确定</a>&nbsp
				<a class="nui-button" iconCls="icon-cancel" onClick="cancel">取消</a>
			</div>
			<div id="update" style="text-align:center">
				<a class="nui-button" iconCls="icon-ok" onClick="update">确定</a>&nbsp
				<a class="nui-button" iconCls="icon-cancel" onClick="cancel">取消</a>
			</div>
			<div id="show" style="text-align:center">
				<a class="nui-button" iconCls="icon-ok" onClick="cancel">确定</a>
			</div>
		</form>
	</div>


	<script type="text/javascript">
    	nui.parse();
    	
    	var form = new nui.Form("form");
    	
    	nui.get("IMPORT_FILE_PATH").setValue(nui.getDictText("IMPORT_FILE_PATH","01"));
    	nui.get("TEMP_FILE_PATH").setValue(nui.getDictText("IMPORT_FILE_PATH","02"));
    	nui.get("TEMPLATE_FILE_PATH").setValue(nui.getDictText("IMPORT_FILE_PATH","04"));
    	
    	$("#add").show();
    	$("#update").hide();
    	$("#show").hide();
    	
    	var oldFileName = "";
    	
    	<%
    		Object object = request.getAttribute("result");
    		String result = "";
    		if(object != null){
    			result = object.toString();
    		}
    	%>
    	
    	var result = "<%=result %>";
    	if(result != null && result != ""){
    		if(result == "ok"){
        		nui.alert("修改成功","系统提示",function(action){
					if("ok" == action || action == "close"){
						closeWindow("ok");
					}
				});
        	}else{
        		nui.alert("修改失败","系统提示",function(action){
					if("ok" == action || action == "close"){
						closeWindow("fail");
					}
				});
        	}
    	}
    	
    	function checkFileName(e){
    		var value = e.value;
    		var isOk = "false";
    		if(value != oldFileName){
    			$.ajax({
	    			url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.checkFileName.biz.ext",
	    			type:"post",
	    			data:"file_name=" + value,
	    			async:false,
	    			success:function(text){
	    				isOk = text.result;
	    			}
	    		});
    		}
    		if(isOk == "true"){
    			e.isValid = false;
    			e.errorText = "文件类别名称已存在";
    		}
    	}
    	
    	function templateChange(){
    		var template = $("#IMPORT_FILE").val();
    		if(template != "" && template != null){
    			nui.get("TEMPLATE_FILE_PATH").setRequired(true);
    		}else{
    			nui.get("TEMPLATE_FILE_PATH").setRequired(false);
    		}
    	}
    	
    	function isSaveNoChanged(e){
    		var value = String(e.value);
    		if(value == "y"){
    			nui.get("NO_COLUMN_NAME").setRequired(true);
    		}else{
    			nui.get("NO_COLUMN_NAME").setRequired(false);
    		}
    	}
    	
    	function isSaveCodeChanged(e){
    		var value = String(e.value);
    		if(value == "y"){
    			nui.get("CODE_COLUMN_NAME").setRequired(true);
    		}else{
    			nui.get("CODE_COLUMN_NAME").setRequired(false);
    		}
    	}
    	
    	function isImportDateChanged(e){
    		var value = String(e.value);
    		if(value == "y"){
    			nui.get("DATE_COLUMN_NAME").setRequired(true);
    		}else{
    			nui.get("DATE_COLUMN_NAME").setRequired(false);
    		}
    	}
    	
    	function downloadTeamplate(){
    		var file_name = $("#template_path").html();
    		var file_path = nui.get("TEMPLATE_PATH").getValue();
    		window.location.href = "<%=request.getContextPath() %>/fileImportOrExport/fileExport/download.jsp?file_name=" + file_name + "&file_path=" + file_path;
    	}
    	
    	function update(){
    		if(form.validate() == true){
    			var fileName = $("#IMPORT_FILE").val();
    			nui.get("FILENAME").setValue(fileName);
    			var b= nui.loading("正在操作中,请稍等...","提示");
    			$("#form").submit();
    			/* $("#form").ajaxSubmit({
	    			url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.updateFileParameters.biz.ext",
	    			type:"post",
	    			dataType:"json",
	    			success:function(text){
	    				nui.hideMessageBox(b);
	                	if(text.result == "ok"){
	                		nui.alert("修改成功","系统提示",function(action){
    							if("ok" == action || action == "close"){
    								closeWindow("ok");
    							}
    						});
	                	}else{
	                		nui.alert("修改失败","系统提示",function(action){
    							if("ok" == action || action == "close"){
    								closeWindow("fail");
    							}
    						});
	                	}
	    			}
	    		}); */
    			//var data = form.getData(true,true);
    			//var json = nui.encode(data);
    			//$.ajax({
    			//	url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.updateFileParameters.biz.ext",
    			//	type:"post",
    			//	contentType:"text/json",
    			//	data:json,
    			//	async:false,
    			//	success:function(text){
    			//		if("ok" == text.result){
    			//			nui.alert("修改成功","系统提示",function(action){
    			//				if("ok" == action || action == "close"){
    			//					closeWindow("ok");
    			//				}
    			//			});
    			//		}else{
    			//			nui.alert("修改失败","系统提示",function(action){
    			//				if("ok" == action || action == "close"){
    			//					closeWindow("fail");
    			//				}
    			//			});
    			//		}
    			//	}
    			//});
    		}
    	}
    	
    	function setData(data){
    		data = nui.clone(data);
    	//	businessLineChange({value:data.row.BUSINESS_LINE});
    	//	checkimpTypeChange({value:data.row.CHECKIMP_TYPE});
    	//	checkNameChange({value:data.row.CHECK_ID});
    		//根据方案id、方案类型、考核指标类型、
	   //     var check_id = data.row.CHECK_ID;
	   //     var assess_type = data.row.ASSESS_TYPE;
		    //获取考评指标名称
    	//    var url = "com.gotop.xmzg.performance.checkImport.getExamineNames.biz.ext?check_id="
    //		      + check_id +"&assess_type="+assess_type;
    	//	nui.get("EXAMINE_NAME").load(url);
    		
    		if(data.type == "edit"){
    			getSecurityData(data);
    			$("#add").hide();
    			$("#update").show();
    			$("#show").hide();
    			nui.get("IMPORT_FILE_PATH").setRequired(false);
    		}else if(data.type == "show"){
    			getSecurityData(data);
    			form.setEnabled(false);
    			$("#add").hide();
    			$("#update").hide();
    			$("#show").show();
    		}
    	}
    	
    	function getSecurityData(data){
    		var map = data.row;
    		for(var key in map){
    			try{
    				if(map[key] == null){
    					nui.get(key).setValue("");
    				}else{
    					nui.get(key).setValue(map[key]);
    				}
    				if(key == "FILE_NAME"){
    					oldFileName = map[key];
    				}
    			}catch(err){
    				$("#" + key).val(map[key]);
    			}
    		}
    		if(map.TEMPLATE_PATH != null){
    			$("#oldTemplate").show();
    			var filename = String(map.TEMPLATE_PATH).substring(String(map.TEMPLATE_PATH).lastIndexOf("/") + 1);
    			$("#template_path").html(filename);
    		}
    		//var row = {map:data.row};
			//form.setData(row);
			var import_security_value = data.row.IMPORT_SECURITY;
			var export_security_value = data.row.EXPORT_SECURITY;
			if(import_security_value != null && import_security_value != ""){
				var import_security = import_security_value.split(";");
				var import_security_text = new Array();
				var import_security_value2 = new Array();
				var index = 0;
				for(var i=0;i<import_security.length;i++){
					var arr = import_security[i].split(":");
    				if(arr[0] == "org"){
    					var org_text = "机构:";
    					var org_value = "org:";
    					var temp = arr[1].split(",");
    					for(var j=0;j<temp.length;j++){
    						temp[j] = "'" + temp[j] + "'";
    					}
    					temp = temp.join(",");
    					var json = nui.encode({queryData:{orgCodes:temp}});
    					$.ajax({
    						url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.getOrgList2.biz.ext",
    						type:"post",
    						data:json,
    						contentType:"text/json",
    						async:false,
    						success:function(text){
    							var resultList = text.resultList;
    							for(var k=0;k<resultList.length;k++){
    								org_text = org_text + resultList[k].ORGNAME + ",";
    								org_value = org_value + resultList[k].ORGID + ",";
    							}
    						}
    					});
    					org_text = org_text.substring(0, org_text.lastIndexOf(","));
    					org_value = org_value.substring(0, org_value.lastIndexOf(","));
    					import_security_text[index] = org_text;
    					import_security_value2[index] = org_value;
    					index++;
    				}else if(arr[0] == "role"){
    					var role_text = "角色:";
    					var role_value = "role:";
    					var temp = arr[1].split(",");
    					for(var j=0;j<temp.length;j++){
    						temp[j] = "'" + temp[j] + "'";
    					}
    					temp = temp.join(",");
    					var json = nui.encode({queryData:{roleCodes:temp}});
    					$.ajax({
    						url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.getRoleList2.biz.ext",
    						type:"post",
    						data:json,
    						contentType:"text/json",
    						async:false,
    						success:function(text){
    							var resultList = text.resultList;
    							for(var k=0;k<resultList.length;k++){
    								role_text = role_text + resultList[k].ROLE_NAME + ",";
    								role_value = role_value + resultList[k].ROLE_ID + ",";
    							}
    						}
    					});
    					role_text = role_text.substring(0, role_text.lastIndexOf(","));
    					role_value = role_value.substring(0, role_value.lastIndexOf(","));
    					import_security_text[index] = role_text;
    					import_security_value2[index] = role_value;
    					index++;
    				}else if(arr[0] == "emp"){
    					var emp_text = "人员:";
    					var emp_value = "emp:";
    					var temp = arr[1].split(",");
    					for(var j=0;j<temp.length;j++){
    						temp[j] = "'" + temp[j] + "'";
    					}
    					temp = temp.join(",");
    					var json = nui.encode({queryData:{empCodes:temp}});
    					$.ajax({
    						url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.getEmpList2.biz.ext",
    						type:"post",
    						data:json,
    						contentType:"text/json",
    						async:false,
    						success:function(text){
    							var resultList = text.resultList;
    							for(var k=0;k<resultList.length;k++){
    								emp_text = emp_text + resultList[k].EMPNAME + ",";
    								emp_value = emp_value + resultList[k].EMPID + ",";
    							}
    						}
    					});
    					emp_text = emp_text.substring(0, emp_text.lastIndexOf(","));
    					emp_value = emp_value.substring(0, emp_value.lastIndexOf(","));
    					import_security_text[index] = emp_text;
    					import_security_value2[index] = emp_value;
    					index++;
    				}else if(arr[0] == "group"){
    					var group_text = "群组:";
    					var group_value = "group:";
    					var temp = arr[1];
    					var json = nui.encode({queryData:{ids:temp}});
    					$.ajax({
    						url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.getGroupList2.biz.ext",
    						type:"post",
    						data:json,
    						contentType:"text/json",
    						async:false,
    						success:function(text){
    							var resultList = text.resultList;
    							for(var k=0;k<resultList.length;k++){
    								group_text = group_text + resultList[k].GROUP_NAME + ",";
    								group_value = group_value + resultList[k].REC_ID + ",";
    							}
    						}
    					});
    					group_text = group_text.substring(0, group_text.lastIndexOf(","));
    					group_value = group_value.substring(0, group_value.lastIndexOf(","));
    					import_security_text[index] = group_text;
    					import_security_value2[index] = group_value;
    					index++;
    				}
    			}
    			import_security_text = import_security_text.join(";");
    			import_security_value = import_security_value2.join(";");
    			nui.get("import_security").setText(import_security_text);
    			nui.get("import_security").setValue(import_security_value);
			}
			if(export_security_value != null && export_security_value != ""){
				var export_security = export_security_value.split(";");
				var export_security_text = new Array();
				var export_security_value2 = new Array();
				var index = 0;
				for(var i=0;i<export_security.length;i++){
					var arr = export_security[i].split(":");
    				if(arr[0] == "org"){
    					var org_text = "机构:";
    					var org_value = "org:";
    					var temp = arr[1].split(",");
    					for(var j=0;j<temp.length;j++){
    						temp[j] = "'" + temp[j] + "'";
    					}
    					temp = temp.join(",");
    					var json = nui.encode({queryData:{orgCodes:temp}});
    					$.ajax({
    						url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.getOrgList2.biz.ext",
    						type:"post",
    						data:json,
    						contentType:"text/json",
    						async:false,
    						success:function(text){
    							var resultList = text.resultList;
    							for(var k=0;k<resultList.length;k++){
    								org_text = org_text + resultList[k].ORGNAME + ",";
    								org_value = org_value + resultList[k].ORGID + ",";
    							}
    						}
    					});
    					org_text = org_text.substring(0, org_text.lastIndexOf(","));
    					org_value = org_value.substring(0, org_value.lastIndexOf(","));
    					export_security_text[index] = org_text;
    					export_security_value2[index] = org_value;
    					index++;
    				}else if(arr[0] == "role"){
    					var role_text = "角色:";
    					var role_value = "role:";
    					var temp = arr[1].split(",");
    					for(var j=0;j<temp.length;j++){
    						temp[j] = "'" + temp[j] + "'";
    					}
    					temp = temp.join(",");
    					var json = nui.encode({queryData:{roleCodes:temp}});
    					$.ajax({
    						url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.getRoleList2.biz.ext",
    						type:"post",
    						data:json,
    						contentType:"text/json",
    						async:false,
    						success:function(text){
    							var resultList = text.resultList;
    							for(var k=0;k<resultList.length;k++){
    								role_text = role_text + resultList[k].ROLE_NAME + ",";
    								role_value = role_value + resultList[k].ROLE_ID + ",";
    							}
    						}
    					});
    					role_text = role_text.substring(0, role_text.lastIndexOf(","));
    					role_value = role_value.substring(0, role_value.lastIndexOf(","));
    					export_security_text[index] = role_text;
    					export_security_value2[index] = role_value;
    					index++;
    				}else if(arr[0] == "emp"){
    					var emp_text = "人员:";
    					var emp_value = "emp:";
    					var temp = arr[1].split(",");
    					for(var j=0;j<temp.length;j++){
    						temp[j] = "'" + temp[j] + "'";
    					}
    					temp = temp.join(",");
    					var json = nui.encode({queryData:{empCodes:temp}});
    					$.ajax({
    						url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.getEmpList2.biz.ext",
    						type:"post",
    						data:json,
    						contentType:"text/json",
    						async:false,
    						success:function(text){
    							var resultList = text.resultList;
    							for(var k=0;k<resultList.length;k++){
    								emp_text = emp_text + resultList[k].EMPNAME + ",";
    								emp_value = emp_value + resultList[k].EMPID + ",";
    							}
    						}
    					});
    					emp_text = emp_text.substring(0, emp_text.lastIndexOf(","));
    					emp_value = emp_value.substring(0, emp_value.lastIndexOf(","));
    					export_security_text[index] = emp_text;
    					export_security_value2[index] = emp_value;
    					index++;
    				}else if(arr[0] == "group"){
    					var group_text = "群组:";
    					var group_value = "group:";
    					var temp = arr[1];
    					var json = nui.encode({queryData:{ids:temp}});
    					$.ajax({
    						url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.getGroupList2.biz.ext",
    						type:"post",
    						data:json,
    						contentType:"text/json",
    						async:false,
    						success:function(text){
    							var resultList = text.resultList;
    							for(var k=0;k<resultList.length;k++){
    								group_text = group_text + resultList[k].GROUP_NAME + ",";
    								group_value = group_value + resultList[k].REC_ID + ",";
    							}
    						}
    					});
    					group_text = group_text.substring(0, group_text.lastIndexOf(","));
    					group_value = group_value.substring(0, group_value.lastIndexOf(","));
    					export_security_text[index] = group_text;
    					export_security_value2[index] = group_value;
    					index++;
    				}
    			}
    			export_security_text = export_security_text.join(";");
    			export_security_value = export_security_value2.join(";");
    			nui.get("export_security").setText(export_security_text);
    			nui.get("export_security").setValue(export_security_value);
			}
    	}
    	
    	function save(){
    		if(form.validate() == true){
    			var fileName = $("#IMPORT_FILE").val();
    			nui.get("FILENAME").setValue(fileName);
    			var b= nui.loading("正在操作中,请稍等...","提示");
    			$("#form").submit();
    			/* $("#form").ajaxSubmit({
	    			url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.addFileParameters.biz.ext",
	    			type:"post",
	    			dataType:"json",
	    			success:function(text){
	    				nui.hideMessageBox(b);
	                	if(text.result == "ok"){
	                		nui.alert("添加成功","系统提示",function(action){
    							if("ok" == action || action == "close"){
    								closeWindow("ok");
    							}
    						});
	                	}else{
	                		nui.alert("添加失败","系统提示",function(action){
    							if("ok" == action || action == "close"){
    								closeWindow("fail");
    							}
    						});
	                	}
	    			}
	    		}); */
    			//var data = form.getData(true,true);
    			//var json = nui.encode(data);
    			//$.ajax({
    			//	url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.addFileParameters.biz.ext",
    			//	type:"post",
    			//	contentType:"text/json",
    			//	data:json,
    			//	async:false,
    			//	success:function(text){
    			//		if("ok" == text.result){
    			//			nui.alert("添加成功","系统提示",function(action){
    			//				if("ok" == action || action == "close"){
    			//					closeWindow("ok");
    			//				}
    			//			});
    			//		}else{
    			//			nui.alert("添加失败","系统提示",function(action){
    			//				if("ok" == action || action == "close"){
    			//					closeWindow("fail");
    			//				}
    			//			});
    			//		}
    			//	}
    			//});
    		}
    	}
    	
    	function selectImportSecurity(e){
    		var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath()%>/fileImportOrExport/fileParametersMaintain/selectSecurity.jsp",
                title: "选择权限",
                width: 800,
                height: 380,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	var text = nui.get("import_security").getText();
                	var value = nui.get("import_security").getValue();
                	data.text = text;
                	data.value = value;
                	frame.contentWindow.setData(data);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);    //必须
                        if (data) {
                            btnEdit.setValue(data.value);
                            btnEdit.setText(data.text);
                        }
                    }

                }
            });
    	}
    	
    	function selectExportSecurity(e){
    		var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath()%>/fileImportOrExport/fileParametersMaintain/selectSecurity.jsp",
                title: "选择权限",
                width: 800,
                height: 380,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	var text = nui.get("export_security").getText();
                	var value = nui.get("export_security").getValue();
                	data.text = text;
                	data.value = value;
                	frame.contentWindow.setData(data);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);    //必须
                        if (data) {
                            btnEdit.setValue(data.value);
                            btnEdit.setText(data.text);
                        }
                    }

                }
            });
    	}
    	
    	function isSubProcedureChange(e){
    		if(e.value == 3){
    			nui.get("SUB_PROCEDURE_NAME").setRequired(true);
    		}else{
    			nui.get("SUB_PROCEDURE_NAME").setRequired(false);
    		}
    	}
    	
    	function isPreProcedureChange(e){
    		if(e.value == 1){
    			nui.get("PRE_PROCEDURE_NAME").setRequired(true);
    		}else{
    			nui.get("PRE_PROCEDURE_NAME").setRequired(false);
    		}
    	}
    	
    	function importDealTypeChange(e){
    		if(e.value == 1){
    			nui.get("IMPORT_FILE_PATH").setEnabled(true);
    			nui.get("IMPORT_FILE_PATH").setRequired(true);
    			nui.get("TABLE_NAME").setEnabled(false);
    			nui.get("START_ROW").setEnabled(false);
    			nui.get("TABLE_NAME").setValue("");
    			nui.get("START_ROW").setValue("");
    			nui.get("TEMP_FILE_PATH").setRequired(false);
    		}else if(e.value == 2){
    			nui.get("IMPORT_FILE_PATH").setEnabled(false);
    			nui.get("TABLE_NAME").setEnabled(true);
    			nui.get("START_ROW").setEnabled(true);
    			nui.get("TABLE_NAME").setRequired(true);
    			nui.get("START_ROW").setRequired(true);
    			nui.get("IMPORT_FILE_PATH").setValue("");
    			nui.get("TEMP_FILE_PATH").setRequired(true);
    		}else if(e.value == 3){
    			nui.get("IMPORT_FILE_PATH").setEnabled(true);
    			nui.get("TABLE_NAME").setEnabled(true);
    			nui.get("START_ROW").setEnabled(true);
    			nui.get("TABLE_NAME").setRequired(true);
    			nui.get("START_ROW").setRequired(true);
    			nui.get("IMPORT_FILE_PATH").setRequired(true);
    			nui.get("TEMP_FILE_PATH").setRequired(false);
    		}
    	}
    	
    	function operationTypeChange(e){
    		if(e.value == 1){
    			nui.get("import_security").setEnabled(true);
    			nui.get("import_security").setRequired(true);
    			nui.get("IMPORT_TYPE").setEnabled(true);
    			nui.get("IMPORT_TYPE").setRequired(true);
    			nui.get("export_security").setValue("");
    			nui.get("export_security").setText("");
    			nui.get("export_security").setEnabled(false);
    		}else{
    			nui.get("IMPORT_TYPE").setEnabled(false);
    			nui.get("IMPORT_TYPE").setValue("");
    			nui.get("export_security").setEnabled(true);
    			nui.get("export_security").setRequired(true);
    			nui.get("export_security").setRequired(true);
    			nui.get("import_security").setValue("");
    			nui.get("import_security").setText("");
    			nui.get("import_security").setEnabled(false);
    		}
    	}
    	
    	function cancel(){
    		closeWindow("cancel");
    	}
    	
    	function closeWindow(action){
    		return window.CloseOwnerWindow(action);
    	}
    	
    	//业务条线
    	function businessLineChange(e){
    	    //清空方案id
    		 nui.get("CHECK_ID").setValue("");
    		 nui.get("CHECK_ID").load({});
    		 
    		 //清空方案类型
    		 nui.get("CHECK_TYPE").setValue("");
    		 
    	     //清空考核指标类型
    	     nui.get("ASSESS_TYPE").setValue("");
    	     nui.get("ASSESS_TYPE").load({});
    	      
    	     //清空考核指标名称
    	     nui.get("EXAMINE_NAME").setValue("");
    	     nui.get("EXAMINE_NAME").load({});
    		if(e.value == 90){
    		    $("#check_tr_1").show();
    		    $("#check_tr_2").show();
    		    //方案名称
	    		var url = "com.gotop.xmzg.performance.checkImport.getCheckNames.biz.ext";
	    		nui.get("CHECK_ID").load(url);
    		}else{
    			$("#check_tr_1").hide();
    		    $("#check_tr_2").hide();
    		}
    	}
    	
    	
    	//导入类型
    	function checkimpTypeChange(e){
    	     //清空方案id
    		 nui.get("CHECK_ID").setValue("");
    		 
    		 //清空方案类型
    		 nui.get("CHECK_TYPE").setValue("");
    		
    	     //清空考核指标类型
    	     nui.get("ASSESS_TYPE").setValue("");
    	     nui.get("ASSESS_TYPE").load({});
    	      
    	     //清空考核指标名称
    	     nui.get("EXAMINE_NAME").setValue("");
    	     nui.get("EXAMINE_NAME").load({});
    	    //导入类型  4-5要显示指标名称
    		if(e.value == 4 || e.value == 5){
    		    $("#check_tr_2").show();
    		    $("#ASSESS_TYPE_TH").show();
    		    $("#ASSESS_TYPE_TD").show();
    		    $("#EXAMINE_NAME_TH").show();
    		    $("#EXAMINE_NAME_TD").show();
    		   
    		}else if(e.value == 3){//指标名称导入
    		    $("#check_tr_2").show();
    		    $("#ASSESS_TYPE_TH").show();
    		    $("#ASSESS_TYPE_TD").show();
    		    $("#EXAMINE_NAME_TH").hide();
    		    $("#EXAMINE_NAME_TD").hide();
    		}else{
    		    $("#check_tr_2").hide();
    		    $("#ASSESS_TYPE_TH").hide();
    		    $("#ASSESS_TYPE_TD").hide();
    			$("#EXAMINE_NAME_TH").hide();
    		    $("#EXAMINE_NAME_TD").hide();
    		}
    	}
    	
    	//考评方案id(名称)
    	function checkNameChange(e){
    	      //清空方案类型
    		  nui.get("CHECK_TYPE").setValue("");
    		  
    	      //清空考核指标类型
    	      nui.get("ASSESS_TYPE").setValue("");
    	      nui.get("ASSESS_TYPE").load({});
    	      
    	      //清空考核指标名称
    	      nui.get("EXAMINE_NAME").setValue("");
    	      nui.get("EXAMINE_NAME").load({});
    	      
    	      //方案id
    		  var check_id = e.value;
    		  if(e.sender){
	    		  var data = e.sender.data;
	    		  for(var i=0;i<data.length;i++){
	    			if(data[i].ID == check_id){
	    				nui.get("CHECK_TYPE").setValue(data[i].CHECK_TYPE);
	    			}
	    		   }
    		  }
    		  //获取考评指标类型
	    	  var url = "com.gotop.xmzg.performance.checkImport.getAssessTypes.biz.ext?check_id="+check_id;
	    	  nui.get("ASSESS_TYPE").load(url);
    	}
    	
    	//考评指标类型
    	function checkAssessTypeChange(e){
    	      //清空考核指标名称
    	      nui.get("EXAMINE_NAME").setValue("");
    	      
    	      //根据方案id、方案类型、考核指标类型、
		       var check_id = nui.get("CHECK_ID").getValue();
		       //var check_type = nui.get("CHECK_TYPE").getValue();
		       var assess_type = e.value;
    		  //获取考评指标名称
	    	  var url = "com.gotop.xmzg.performance.checkImport.getExamineNames.biz.ext?check_id="
	    		      + check_id +"&assess_type="+assess_type;
	    		nui.get("EXAMINE_NAME").load(url);
    	}
    </script>
</body>
</html>
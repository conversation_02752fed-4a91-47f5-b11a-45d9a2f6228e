<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
<%@ page import="com.eos.data.datacontext.UserObject"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): jw
  - Date: 2018-08-02 09:53:00
  - Description:
-->
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
 %>
<!-- $Header: svn://**************:7029/svn/xm_project/xm_zgxt/com.gotop.fileManger.jw/src/webcontent/filegl_update2.jsp 1935 2018-09-12 03:44:13Z jw $-->
<title>文件修改</title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<%@include file="/coframe/tools/skins/common.jsp"%>
<script src="<%=request.getContextPath()%>/common/nui/nui.js" type="text/javascript"></script>
<style type="text/css">
html,body {
	padding: 0;
	margin: 0;
	border: 0;
	height: 100%;
	overflow-y:auto;
}

.nui-textarea {
	width: 100%;
}

.nui-popup .nui-shadow {
	height: 150px;
}

.nui-textboxlist {
	width: 100%;
	height: 40px;
}
.errorText{
	color:red;
	font-size: 10px;
}
</style>
</head>
<body>
	<fieldset style="border: solid 1px #aaa; padding: 3px;margin-top: 2%">
		<legend>文件基本信息</legend>
		<div style="padding: 5px;">
			<form id="filefrom" method="post">
				<table border="0" cellpadding="1" cellspacing="2" align="center" style="width: 100%">
					<tr>
						<td style="width: 15%; text-align: right;">文件名：</td>
						<td style="width: 70%">
							<input id="fileName" name="file.fileName" class="nui-textbox nui-form-input" required="true" style="width: 100%;" onblur="checkFileName"/>							
						</td>
						<td style="width: 15%;" id="filename_error" class="errorText">
							<input id="errorname" class="nui-hidden"/>
						</td>
					</tr>
					<tr>
						<td style="width: 15%; text-align: right;">文号：</td>
						<td style="width: 70%">
							<input id="fileNo" name="file.fileNo" class="nui-textbox nui-form-input" style="width: 100%;" onblur="checkFileNo"/>							
						</td>
						<td style="width: 15%;" id="fileno_error" class="errorText">
							<input id="errorno" class="nui-hidden"/>
						</td>
					</tr>
					<tr>
						<td style="width: 15%; text-align: right;">关键字：</td>
						<td style="width: 70%">
							<input id="fileKey" name="file.fileKey" class="nui-textbox nui-form-input"  style="width: 100%;" />							
						</td>
						<td style="width: 15%;"></td>
					</tr>
				</table>
				<input id="fileId" name="file.fileId" class="nui-hidden" />
				<input id="uploadName" name="file.uploadName" class="nui-hidden"/>
				<input id="realFileName" name="file.realFileName" class="nui-hidden"/>
				<input id="listIds" name="file.listId" class="nui-hidden"/>
				<input id="filePath" name="file.filePath" class="nui-hidden"/>
			</form> 
			<input id="testFileName" class="nui-hidden"/>
			<input id="testFileNo" class="nui-hidden"/>
			<input id="testFileKey" class="nui-hidden"/>
		</div>
	</fieldset>
	<div class="nui-toolbar" style="text-align:center;padding-top:5px;padding-bottom:5px;" borderStyle="border:0;">
	    <a class="nui-button" style="width:60px;" iconCls="icon-save" onclick="submitUpdate()">保存</a>
	    <span style="display:inline-block;width:25px;"></span>
	    <a class="nui-button" style="width:60px;" iconCls="icon-cancel" onclick="onCancel()">取消</a>
	</div>
	
	<script src="<%= request.getContextPath() %>/js/jquery.form.js" type="text/javascript"></script>
	<script type="text/javascript">
			nui.parse();
			
			var username="<%=userObject.getUserName()%>";
			//路径
			var path="<%= request.getContextPath() %>";
			var userid="<%=userObject.getUserId()%>";
			//文件表单	
			var form = new nui.Form("#filefrom");
			var filename;
			//标准方法接口定义
	        function SetData(file){
	        	//跨页面传递的数据对象，克隆后才可以安全使用	        	
	        	file = nui.clone(file);
	        	form.setData(file);
	        	form.setChanged(false);
	        	nui.get("testFileName").setValue(nui.get("fileName").getValue());
	        	nui.get("testFileNo").setValue(nui.get("fileNo").getValue());
	        	nui.get("testFileKey").setValue(nui.get("fileKey").getValue());
			}
			
	      	//文件添加事件
	        function submitUpdate() {	    
		        nui.get("uploadName").setValue(username);
				var error=nui.get("errorname").getValue(); 
				var filepath=nui.get("filePath").getValue(); 
				filepath=pathdecode(filepath);
				var errorno=nui.get("errorno").getValue();
				var fileName=""||nui.get("fileName").getValue();//新文件名			
				var testFileName=""||nui.get("testFileName").getValue();//旧文件名	
				var fileNo=""||nui.get("fileNo").getValue();//新文号	
				var testFileNo=""||nui.get("testFileNo").getValue();//旧文号	
				var fileKey=""||nui.get("fileKey").getValue();//新关键字
				var testFileKey=""||nui.get("testFileKey").getValue();//旧关键字
				var newtest=fileName+","+fileNo+","+fileKey;
				var oidtest=testFileName+","+testFileNo+","+testFileKey;
				if(error){
					nui.alert("文件名重复，请修改文件名");
					return;
				}else if(fileName==null||fileName==""||fileName==undefined){
					nui.alert("请填写文件名");
					return;
				}else if(errorno){
					nui.confirm("文号重复，是否要保存数据？","保存确认",function(action){
						if(action!="ok") return;
						FileUpdate(oidtest,newtest,filepath+"["+fileName+"]");
					})
				}else{
					nui.confirm("是否要保存数据？","保存确认",function(action){
						if(action!="ok") return;
						FileUpdate(oidtest,newtest,filepath+"["+fileName+"]");
					})
				}
	        }
	        
			
	</script>
	<script src="<%= request.getContextPath() %>/js/filecs2.js" type="text/javascript"></script>
</body>
</html>
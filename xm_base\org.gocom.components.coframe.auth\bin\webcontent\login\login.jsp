<%@page import="com.eos.system.utility.StringUtil"%>
<%@page import="com.eos.access.http.security.config.HttpSecurityConfig"%>
<%@page pageEncoding="UTF-8"%>
<%@page import="com.primeton.cap.AppUserManager"%>
<%@include file="/common/common.jsp"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): shitf
  - Date: 2013-03-07 15:24:13
  - Description:
-->
<head>
<title>用户登录</title>
<%
   String contextPath=request.getContextPath();
   String url = null;
   HttpSecurityConfig securityConfig = new HttpSecurityConfig();
   boolean isOpenSecurity = securityConfig.isOpenSecurity();
   if(isOpenSecurity){
   		boolean isAllInHttps = securityConfig.isAllInHttps();
   		if(!isAllInHttps){
   			String ip = securityConfig.getHost();
   			String https_port = securityConfig.getHttps_port();
   			url = "https://" + ip + ":" + https_port + contextPath + "/coframe/auth/login/org.gocom.components.coframe.auth.login.login.flow";
   		}else{
   			url = "org.gocom.components.coframe.auth.login.login.flow"; 
   		}
   }else{
   		url = "org.gocom.components.coframe.auth.login.login.flow";
   }
 %>
<script type="text/javascript" src="<%=contextPath%>/common/nui/nui.js"></script>
<link rel="stylesheet" type="text/css" href="<%=contextPath %>/coframe/auth/login/css/style.css" />
</head>
<%
	String original_url=null;
	Object objAttr=request.getAttribute("original_url");
	
	String ipct = "["+request.getRemoteAddr()+"]";
	
	if(objAttr != null){
		original_url=StringUtil.htmlFilter((String)objAttr);
	}

 %>
<body class="login">

<div id="warpper" class="wrap">
		
		<div class="main">
			<h3 id="titleWord" style="color:red;font-size:20px;position: absolute;top:30%; left:40%;">用户不存在或token已失效</h3>	
			<div id="form1" class="login-box" >
				
			<div id="loadWait"  style="color:red;font-size:20px;position: absolute;top:50%; left:40%;"	>
			
			<div class="loadingOne">
    			<span>跳</span>
    			<span>转</span>
    			<span>中</span>
    			<span>.</span>
    			<span>.</span>
    			<span>.</span>
			</div>	
		</div>
				
				<h3 id="formBox2">新一代综合管理微服务</h3>
				<h3 id="titleWord2" style="margin-top:50px;color:red;font-size:20px">请前往邮连登录！</h3>
				<div id="formBox">
				
				<form method="post"	name="loginForm" onsubmit="return login();" action="<%=url%>" >
					<input id="original_url" class="nui-hidden" name="original_url" value="<%=original_url+"-["+request.getRemoteAddr()+"]" %>"/>
					<p class="login-item">
					  <em>员工号：</em>
					  <input class="nui-textbox" id="userId" name="userId" style="width:247px;height:26px;"
					   onenter="keyboardLogin" onvalidation="onCheckUserId"/>
					</p>
					<p class="login-item">
					  <em>密　码：</em>
					  <input name="password" id="password"  class="nui-password" vtype="minLength:6" minLengthErrorText="密码不能少于6个字符"
			                onenter="keyboardLogin" style="width:247px;height:26px;" onvalidation="onCheckPassword" 
			                autocomplete="off" />
					</p>
					<p class="login-item">
					  <em>验证码：</em>
					  <input name="piccode" id="piccode" onenter="keyboardLogin"  class="nui-textbox" style="width:130px;height:26px;" onvalidation="onCheckPicCode" />
					  <img style="border: none;outline: none;height:26px;display:inline-block;vertical-align: middle;padding-left: 30px;" src="" id="pitureCode"  onclick="changePic();"/>
					</p>
					<p id="error" class="login-error" style="display:inline-block;height:0px;color:red;"></p>
					<p class="login-btn center" style="padding-top: 5px;">
						<input class="log" type="submit" value="登 录" />
					</p>
					<input id="jumpFlag" class="nui-hidden" name="jumpFlag" value="1" />
				</form>
				</div>
				
			</div>
		</div>
		
		<div class="foot">
			<p>中国邮政储蓄银行厦门分行</p>
		</div>
	</div>

<%
		session.invalidate();//清空session
		if(request.getCookies()!=null && request.getCookies().length>0){
			Cookie cookie = request.getCookies()[0];//获取cookie
			cookie.setMaxAge(0);//让cookie过期 
		}
 %>
 
 	<form id="form2" action="org.gocom.components.coframe.urlverify.zhbgLogin.flow" method="post" enctype="multipart/form-data">
 			<input id="userToken" class="nui-hidden" name="userToken" />
 			<input id="lgt" class="nui-hidden" name="lgt" />
 			<input id="empCheck" class="nui-hidden" name="empCheck" />
    </form>
    
<script type="text/javascript">

     if(window.top!=window){
		window.top.location = window.location;
	 }
     nui.parse();
  
     var form = new nui.Form("#form1");
     
     var isLoginForm =  nui.getDictText("IS_LOGIN_FORM", "ctrlShow");
     
     var t1 = document.getElementById('loadWait');//登录中
     t1.style.display = "none"; // 设置隐藏元素
     var t2 = document.getElementById('formBox');//账号密码表单
     t2.style.display = "none"; // 设置隐藏元素
     //var t3 = document.getElementById('formBox2');
     var t4 = document.getElementById('titleWord'); //用户不存在token已失效
     t4.style.display = "none"; // 设置隐藏元素
     var t5 = document.getElementById('titleWord2'); //请前往邮连登录
     t5.style.display = "none"; // 设置隐藏元素
	
		
     
     var showFlag = false;
     var isYlError = false;
     var str = "异常！";
     if(isLoginForm!=null&& isLoginForm!=""&& isLoginForm == "1"){
     	//显示表单
     	showFlag = true ;
   	
     }else{
     	//不显示表单
     	showFlag = true ;

     }
     
   
     nui.get("userId").focus();
     
     function onCheckUserId(e){
       if (e.isValid) {
         if(e.value==""){
           e.errorText = "用户名不能为空";
           e.isValid = false;
         }
       }
     }
     
     function onCheckPassword(e){
       if (e.isValid) {
         if(e.value==""){
           e.errorText = "密码不能为空";
           e.isValid = false;
         }
       }
     }
     
     function onCheckPicCode(e){
       if (e.isValid) {
         if(e.value==""){
           e.errorText = "验证码不能为空";
           e.isValid = false;
         }
       }
     }
     
     <% 
     	Object result = request.getAttribute("result");
     	//Object val = request.getAttribute("val");
     	String userName = (String)request.getAttribute("userId");
     	if (userName==null)userName="";
     	String piccode = (String)request.getAttribute("piccode");
     	if (piccode==null)piccode="";
     	String password = (String)request.getAttribute("password");
     	if (password==null)password="";
     	//if(val!=null){
     	  //   String valCode = (String)val;
     		// if("1".equals(valCode)){
		  //   	out.println("checkisExist()");
		  //   }else{
		       if(result != null){
		     		Integer resultCode = (Integer)result;
		     		 if(resultCode == 0){
		     		    Object ope_content = request.getAttribute("ope_content");
				     	out.println("showError('"+(String)ope_content+"')");
				     }else if(resultCode == -1){
				     	out.println("showError('用户不存在！')");
				     }else if(resultCode == -2){
				     	out.println("showError('用户无权限登录，请联系系统管理员！')");
				     }else if(resultCode == -10){
		      			out.println("showError('验证码输入错误！')");
		     		 }else if(resultCode == 3){
				     	out.println("showError('用户已过期！')");
				     }else if(resultCode == 4){
				     	out.println("showError('用户未到开始使用时间！')");
				     }else if(resultCode == 5){
				     	out.println("showError('密码已过期！')");
				     }else if(resultCode == -3){
		      			out.println("showError('查询用户信息失败，请联系系统管理员检查数据库连接！')");
		     		 }else if(resultCode == 6){
		      			out.println("showError('密码被冻结，请联系系统管理员！')");
		     		 }else if(resultCode == 7){
		     		    Object ope_content = request.getAttribute("ope_content");
				     	out.println("showError('"+(String)ope_content+"')");
		     		 }else if(resultCode == 8){
		     		    Object ope_content = request.getAttribute("ope_content");
				     	out.println("showError('"+(String)ope_content+"')");
		     		 }else if(resultCode == -1000){
		      			out.println("showError2('跳转异常，token已失效！')");
		     		 }else if(resultCode == -1001){
		      			out.println("showError2('跳转异常，用户不存在！')");
		     		 }else if(resultCode == -1003){
		      			out.println("showError2('跳转异常，数据库异常！')");
		     		 }else if(resultCode == 1006){
		      			out.println("showError2('跳转异常，密码被冻结！')");
		     		 }else{
		      			out.println("showError('未知的异常，请联系系统管理员！')");
		     		 }
		     //	}
		   //  }
     	
     	}
     	
	  %>
	  
      function showError(msg){
      	 $("#error").html(msg);
      }
      
      //弹窗的方式提示
      function showError2(msg){
       alert(msg);
       str = msg ;
       isYlError = true;
       window.close();
       
      }
      
      //获取键盘 Enter 键事件并响应登录
     function keyboardLogin(e){
       login();
     }
     function login(){
     	var form = new nui.Form("#form1");
        form.validate();
        if (form.isValid() == false) 
        	return false;
        document.loginForm.submit();
     }
 	 
 	//判断密码是否超过设定天数
    function checkisExist(){
       var mcode = nui.get("userId").getValue();	
				  nui.open({
			          url:"<%=contextPath%>/coframe/auth/expire/update_password_qz.jsp",
			          title:'密码修改',
			          width:600,
			          height:400,
			          onload:function(){
			             var iframe = this.getIFrameEl();
			             
			             //方法1：后台查询一次，获取信息回显
			             /* var data = row;
			             iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
			             
			             //方法2：直接从页面获取，不用去后台获取
			               var data = {pageType:"edit",record:{mcode:mcode}};
			              iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
			          },
			          ondestroy:function(action){
			             if(action=="saveSuccess"){
			                grid.reload();
			             }
			          }
			       });
				
		    
		 }
		 
    //图形验证码
	$(function(){
	    changePic();
	});
	function changePic(){
	    var pitureCode = document.getElementById("pitureCode");  
	    <%-- verify.setAttribute('src','<%=request.getContextPath() %>/coframe/auth/login/picCode.jsp?it='+Math.random());  --%>
	    $.ajax({
	        type : "POST",
	        dataType : "json",
	        contentType : "application/json",
	        url : "org.gocom.components.coframe.piccode.picCode.createPicCode.biz.ext",
	        success : function(text) {
			    var bytes = new Uint8Array(text.piccodebyte);
                var blob = new Blob([bytes], { type: "image/png"});
                var url = URL.createObjectURL(blob);	
                pitureCode.src = url;
		    }
	    });
	}
	

	
	
	 //检验是否从综合办公系统跳转过来
     $(function verifyFromUrl(){
     	var params = getUrlParams2(window.location.href);
     	var userToken = params["userToken"];
     	var lgt = params["X-ER-LGT"];
     	var empCheck = params["empCheck"];
		var json = nui.encode({userToken:userToken,lgt:lgt,empCheck:empCheck});
		

     	if(userToken!=null && lgt != null ){
     	 	t1.style.display = ""; // 设置显示元素
     		var form1 = $("#form2");
     		nui.get("userToken").setValue(userToken);
     		nui.get("lgt").setValue(lgt);
     		nui.get("empCheck").setValue(empCheck);
     		form1.submit();
     	}else{
     	   //是否显示表单
     	   if(!showFlag){
     	   	//显示请前往邮连跳转
     	    t5.style.display = ""; // 设置显示元素
     	   }else{
     	    t2.style.display = ""; // 设置显示元素
     	   }
     	   if(isYlError){
     	   	document.getElementById("titleWord").innerHTML = str;
     	   	t5.style.display = "none"; // 设置隐藏元素
     	   	t4.style.display = ""; // 设置显示元素
     	   }
     	   
     	}
     	
     })																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																									
     
		function getUrlParams2(url) {
			let urlStr = url.split('?')[1]
			const urlSearchParams = new URLSearchParams(urlStr)
			const result = Object.fromEntries(urlSearchParams.entries())
			return result
		}
 	 
</script>
 </body>
  <%
 	request.getSession().invalidate();
 	Cookie[] cookies = request.getCookies();
 	if(cookies != null){
 		for(int i=0;i<cookies.length;i++){
 			if(StringUtil.equals("jsessioinid", cookies[i].getName())){
 				cookies[i].setMaxAge(0);
 			}
 		}
 	
 	}
  %>
</html>

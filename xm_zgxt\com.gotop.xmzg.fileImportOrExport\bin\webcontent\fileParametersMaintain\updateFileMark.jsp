<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): zhanghongkai
  - Date: 2019-05-09 11:41:06
  - Description:
-->
<head>
	<%@include file="/coframe/tools/skins/common.jsp" %>
	<title>增加或者修改文件标识</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
</head>
<body>
	<div id="form" class="nui-fit">
		<input name="map.ID" class="nui-hidden">
		<table border="0" cellpadding="1" cellspacing="2" style="width:100%;table-layout:fixed;">
		    <tr>
		        <th style="text-align:right;">文件类别名称：</th>
		        <td>
		            <input class="nui-combobox" required="true" valueField="ID" textField="FILE_NAME" style="width:100%;"
	          			name="map.FILE_ID" emptyText="请选择.." id="fileName" dataField="resultList" url="com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.getFileType.biz.ext"/>
		        </td>
		        <th style="text-align:right;">文件标识：</th>
		        <td >
		            <input name="map.FILE_MARK" class="nui-textbox" required="true" style="width:100%;"/>
		        </td>
		    </tr>
		    <tr>
		    	<th style="text-align:right;">为空文件是否导入：</th>
		    	<td>
	         		<input name="map.IS_IMPORT" id="IS_IMPORT" required="true" class="nui-combobox" valueField="id" textField="text"
	         		 data="[{'id':'0','text':'正常导入'},{'id':'1','text':'不导入'}]" showNullItem="true" nullItemText="请选择" emptyText="请选择" style="width:100%;"/>
	        	</td>
		    </tr>
		</table>
		<div id="add" style="text-align:center">
			<a class="nui-button" iconCls="icon-ok" onClick="save">确定</a>&nbsp
			<a class="nui-button" iconCls="icon-cancel" onClick="cancel">取消</a>
		</div>
		<div id="update" style="text-align:center">
			<a class="nui-button" iconCls="icon-ok" onClick="update">确定</a>&nbsp
			<a class="nui-button" iconCls="icon-cancel" onClick="cancel">取消</a>
		</div>
		<div id="show" style="text-align:center">
			<a class="nui-button" iconCls="icon-ok" onClick="cancel">确定</a>
		</div>
	</div>


	<script type="text/javascript">
    	nui.parse();
    	
    	var form = new nui.Form("form");
    	
    	$("#add").show();
    	$("#update").hide();
    	$("#show").hide();
    	
    	function update(){
    		if(form.validate() == true){
    			var data = form.getData(true,true);
    			var json = nui.encode(data);
    			$.ajax({
    				url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.updateFIleMark.biz.ext",
    				type:"post",
    				contentType:"text/json",
    				data:json,
    				async:false,
    				success:function(text){
    					if("ok" == text.result){
    						nui.alert("修改成功","系统提示",function(action){
    							if("ok" == action || action == "close"){
    								closeWindow("ok");
    							}
    						});
    					}else{
    						nui.alert("修改失败","系统提示",function(action){
    							if("ok" == action || action == "close"){
    								closeWindow("fail");
    							}
    						});
    					}
    				}
    			});
    		}
    	}
    	
    	function setData(data){
    		data = nui.clone(data);
    		form.setData({map:data.row});
    		if(data.type == "edit"){
    			$("#add").hide();
    			$("#update").show();
    			$("#show").hide();
    		}else if(data.type == "show"){
    			$("#add").hide();
    			$("#update").hide();
    			$("#show").show();
    			form.setEnabled(false);
    		}
    	}
    	
    	function save(){
    		if(form.validate() == true){
    			var data = form.getData(true,true);
    			var json = nui.encode(data);
    			$.ajax({
    				url:"com.gotop.xmzg.fileImportOrExport.fileParametersMaintain.addFileMark.biz.ext",
    				type:"post",
    				contentType:"text/json",
    				data:json,
    				async:false,
    				success:function(text){
    					if("ok" == text.result){
    						nui.alert("添加成功","系统提示",function(action){
    							if("ok" == action || action == "close"){
    								closeWindow("ok");
    							}
    						});
    					}else{
    						nui.alert("添加失败","系统提示",function(action){
    							if("ok" == action || action == "close"){
    								closeWindow("fail");
    							}
    						});
    					}
    				}
    			});
    		}
    	}
    	
    	function columnTypeChanged(e){
    		var columnType = e.value;
    		if(columnType == 4){
    			nui.get("dateFormat").setRequired(true);
    		}else{
    			nui.get("dateFormat").setRequired(false);
    		}
    	}
    	
    	function cancel(){
    		closeWindow("cancel");
    	}
    	
    	function closeWindow(action){
    		return window.CloseOwnerWindow(action);
    	}
    </script>
</body>
</html>
<%@page pageEncoding="UTF-8"%>	
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2019-06-04 14:51:16
  - Description:
-->
<head>
	<title>指标新增</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%@include file="/achieve/init.jsp"%>
	<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
</head>
<body>
  <div id="prameter" style="padding-top:5px;"> 
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table" align="center"> 
    <tbody>
     <tr> 
      <th class="nui-form-label" style="width:35%"><font class="b_red">*</font>业务条线代码：</th> 
      <td style="width:65%">
      	<div id="tip_code" name="tip_code" class="nui-combobox" allowInput="false" required="true"
	      	dataField="list" textField="TIP_NAME" valueField="TIP_CODE" style="width:200px;"
	      	emptyText="全部" onvaluechanged="onTipCodeChanged"
	      	url="com.gotop.xmzg.achieve.indicators.indicators_plate_choice.biz.ext">
	      	<div property="columns">
		        <div header="条线代码" field="TIP_CODE" width="60"></div>
		        <div header="条线名称" field="TIP_NAME" width="120"></div>
		    </div>
		</div>
      </td> 
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>指标代码：</th> 
      <td>
      <input class="nui-hidden" id="ti_start" name="ti_start" value="1"/>
      <input class="nui-textbox" id="ti_code" name="ti_code" style="width:200px;" required="true"/></td> 
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>指标名称：</th> 
      <td><input class="nui-textbox" id="ti_name" name="ti_name" style="width:200px;" required="true"/></td> 
     </tr>
     <tr> 
      <th class="nui-form-label">指标说明：</th> 
      <td><input class="nui-textbox" id="ti_remark" name="ti_remark" style="width:200px;"/></td> 
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>积分系数：</th> 
      <td><input class="nui-spinner" id="ti_integral" name="ti_integral" value="1.00" style="width:200px;" minValue="0" maxValue="99999999" format="#,0.00" required="true"/></td> 
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>排序：</th> 
      <td><input class="nui-textbox" id="ti_sorting" name="ti_sorting" style="width:200px;" value="0" required="true"/></td> 
     </tr>
    </tbody>
   </table> 
   <div class="nui-toolbar" style="padding:0px;" borderstyle="border:0;"> 
    <table width="100%"> 
     <tbody>
      <tr> 
       	<td style="text-align:center;"> 
       		<input class="nui-hidden" id="orgs"/>
	       	<a class="nui-button" iconcls="icon-save" onclick="seve">提交</a> 
	       	<span style="display:inline-block;width:10px;"></span> 
	       	<a class="nui-button" iconcls="icon-cancel" onclick="onCancel">取消</a>
       	</td> 
      </tr> 
     </tbody>
    </table> 
   </div> 
  </div>
  <script type="text/javascript">
  	nui.parse();
  	//获取机构集
  	function onTipCodeChanged(e){
  		var orgs = e.selected.TIP_ORGCODES;
  		nui.get("orgs").setValue(orgs);
  	}
  	
  	function seve(){
  		var form = new nui.Form("#prameter");
		form.validate();
		if (form.isValid() == false) return;
		var num = nui.get("ti_integral").getValue();
		if(num == "0"){
			nui.alert("积分系数不能为0");
			return;
		}
  		contain(function(){
  			var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
  			//提交数据
  	        nui.ajax({
  	       		url: "com.gotop.xmzg.achieve.indicators.indicators_judge_add.biz.ext",
  	           	type: "post",
  	           	data: nui.encode({type:1,t_code:nui.get("ti_code").getValue()}),
  	           	contentType:'text/json',
  	           	success: function (text) {
  	        	   nui.hideMessageBox(load);  //隐藏遮罩层
  	        	   var code = text.code;
  	        	   var msg = text.msg;
  	        	   if(code != "0000"){
  	        		  	nui.alert(msg, "系统提示");
  		           }else{
	  		        	//提交数据
	  		   	        nui.ajax({
	  		   	       		url: "com.gotop.xmzg.achieve.indicators.indicators_add.biz.ext",
	  		   	           	type: "post",
	  		   	           	data: nui.encode({"obj":form.getData()}),
	  		   	           	contentType:'text/json',
	  		   	           	success: function (text) {
	  		   	        	   nui.hideMessageBox(load);  //隐藏遮罩层
	  		   	        	   var code = text.code;
	  		   	        	   var msg = text.msg;
	  		   	        	   if(code != "1"){
	  		   	        		  	nui.alert(msg, "系统提示");
	  		   		           }else{
	  		   	          			nui.alert(msg, "系统提示", function(action){
	  		   							if(action == "ok"){
	  		   								CloseWindow("saveSuccess");
	  		   							}
	  		   						});
	  		   		          }
	  		   	           }
	  		   	       });
  		          }
  	           }
  	       });
  		});
  	}
  </script>
</body>
</html>
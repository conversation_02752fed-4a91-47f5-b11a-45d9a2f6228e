<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="com.eos.data.datacontext.UserObject"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- $Header: svn://1*************:7029/svn/xm_project/xm_zgxt/com.gotop.fileManger.jw/src/webcontent/update_menu.jsp 1935 2018-09-12 03:44:13Z jw $ -->
<!-- 
  - Author(s): JW
  - Date: 2018-07-25 11:08:29
  -   
-->
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
 %>
<head>
<title>修改所属目录</title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=EDGE">
<%@include file="/coframe/tools/skins/common.jsp"%>
<script src="<%=request.getContextPath()%>/common/nui/nui.js" type="text/javascript"></script>
<style type="text/css">
html,body {
	padding: 0;
	margin: 0;
	border: 0;
	height: 100%;
	overflow-y: auto;
}

.errorText{
	color:red;
	font-size: 12px;
}
</style>
</head>
<body>
	<fieldset style="border: solid 1px #aaa; padding: 3px;">
		<legend>修改所属目录</legend>
		<div style="padding: 5px;">
			<form id="filefrom" method="post">	              
				<table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">	
					<tr>
						<th class="nui-form-label" style="width: 15%;"><label>修改目录：</label></th>
						<td style="width: 70%">
							<input id="uplist" class="nui-textbox nui-form-input" style="width: 100%;" allowInput="false"/>
							<input id="uplistId" class="nui-hidden"/>	            	
						</td>
						<td style="width: 15%;">
							<a class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">修改</a>
						</td>
					</tr>
				</table>
			</form>
		</div>
	</fieldset>
	<div class="nui-toolbar" style="text-align:center;padding-top:5px;padding-bottom:5px;" borderStyle="border:0;">
	    <a class="nui-button" style="width:60px;" iconCls="icon-save" onclick="submitForm()">保存</a>
	    <span style="display:inline-block;width:25px;"></span>
	    <a class="nui-button" style="width:60px;" iconCls="icon-cancel" onclick="onCancel()">取消</a>
	</div>
	<script type="text/javascript">
	var userid="<%=userObject.getUserId()%>";
	var orgid="<%=userObject.getUserOrgId()%>";
	var permId="1";
	</script>
	
	<script src="<%= request.getContextPath() %>/js/auth.js" type="text/javascript"></script>
	
	<script type="text/javascript">
		nui.parse();
			
		/* 0（预览） 1（修改） 2（删除） 3（下载）  */
		var username="<%=userObject.getUserName()%>";
		
		//路径
		var path="<%=request.getContextPath()%>";
		var fileid="";
		var fileName="";//真实文件名
		var onlyName="";//唯一文件名
		var addr=[];//要移动的目录文件的集合
		var oidpath="";//要移动的目录
		var newaddr="";//新的目录路径
		//标准方法接口定义
		function SetData(data,oidpaths) {
			//跨页面传递的数据对象，克隆后才可以安全使用
			data = nui.clone(data);
			oidpaths = nui.clone(oidpaths);
			addr = data;
			oidpath = pathdecode(oidpaths);
			
			
		}
		//跳转界面
		function onClick() {            
            nui.open({
            	url: "<%= request.getContextPath() %>/jw/menu.jsp",
            	title: "选择目录",
	            iconCls: "icon-edit", 
	            width: 350, 
	            height: 350,
	            ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data); 
                        newaddr = data;
                        nui.get("uplist").setValue(data.aurl);                   
                        nui.get("uplistId").setValue(data.aid);                   
                    }
                }
	        });
             
        }
		
		
		function submitForm(){
			var oid=nui.get("uplist").getValue();
			var news=nui.get("uplist").getValue();
			if(news==null||news==""||news==undefined){
				CloseWindow("saveSuccess");
			}else if(news=="文件系统"){
				nui.alert("不能移动到根目录下，请重新选择目录！");
				return;
			}else{
				nui.confirm("确定要把这些文件移动到["+nui.get("uplist").getValue()+"]这个文件夹吗？", "系统提示", function(action) {
					if (action == "ok" || action == "close") {
						updateMenu();
					}else{
						CloseWindow("saveSuccess");
					}
				});
			}
		}
		
		
		
		
		
		
		//移动文件目录
		function updateMenu(){
			nui.mask({
				el : document.body,
				cls : 'mini-mask-loading',
				html : '移动数据中...'
			});
			
			var json=nui.encode({
							"fileid":addr,
							"newpath":nui.get("uplist").getValue(),
							"listid":nui.get("uplistId").getValue()});
			var names="";
			var name="";//文件名
			var name1="";//真实文件名
			var newpath=nui.get("uplist").getValue()+"/";
			$.ajax({
				type : 'POST',
				url : "com.gotop.fileManger.jw.action.filegl.updateFilePath.biz.ext",
				dataType : "json",//返回的格式为json
				data : json,
				contentType:'application/json',
				success : function(data) {
					
					nui.unmask(document.body);
					var obj = data.msg;
					if (obj.resCode == "2") {
						names=obj.filename;
						for(var i=0;i< names.length;i++){
							if(i==names.length-1){
								name+=names[i].FILENAME;
							}else{
								name+=names[i].FILENAME+",";
							}
						}
						var content="["+username+"]"+"由于目录["+newpath+"]下存在相同的["+name+"]文件名，移动失败";
						sys_log(content,oidpath+"["+name+"]");
						nui.alert("移动失败。目录下存在["+name+"]这些文件重名,请修改后重新移动", "系统提示", function(action) {
							if (action == "ok" || action == "close") {
								CloseWindow("saveSuccess");
							}
						});
					}else if (obj.resCode == "1") {
						names=obj.filename;
						for(var i=0;i< names.length;i++){
							if(i==names.length-1){
								name+=names[i].FILENAME;
								name1+=names[i].REALFILENAME;
							}else{
								name+=names[i].FILENAME+",";
								name1+=names[i].REALFILENAME+",";
							}
						}
						var content="["+username+"]"+"将文件["+name1+"]从目录["+oidpath+"]移动到["+newpath+"]成功";
						sys_log(content,newpath+"["+name+"]");
						nui.alert("移动成功", "系统提示", function(action) {
							if (action == "ok" || action == "close") {
								CloseWindow("saveSuccess");
							}
						});
					} else {
						names=obj.filename;
						for(var i=0;i< names.length;i++){
							if(i==names.length-1){
								name+=names[i].FILENAME;
								name1+=names[i].REALFILENAME;
							}else{
								name+=names[i].FILENAME+",";
								name1+=names[i].REALFILENAME+",";
							}
						}
						var content="["+username+"]"+"将文件["+name+"]从目录["+oidpath+"]移动到["+newpath+"]失败";
						//sys_log(content,oidpath+"["+name+"]");
						sys_log(content,"");
						nui.alert("移动失败", "系统提示", function(action) {
							if (action == "ok" || action == "close") {
								CloseWindow("saveFailed");
							}
						});
					}
				}
			}); 
		}
		
		
		function onOk() {
			CloseWindow("ok");
		}
		function onCancel() {
			CloseWindow("cancel");
		}
		function CloseWindow(action) {
			if (window.CloseOwnerWindow)
				return window.CloseOwnerWindow(action);
			else
				window.close();
		}
		
		/**
		 * 日志操作记录
		 * @param mod 模块
		 * @param content 内容
		 */
		function sys_log(content, name) {
			var json = nui.encode({
				"mod" : "文件模块",
				"content" : content,
				"name" : name
			}); //序列化成JSON
			$.ajax({
				url : "com.gotop.xmzg.util.Excel.syslog.biz.ext",
				type : "post",
				data : json,
				cache : false,
				contentType : 'text/json',
				success : function(data) {
					
				}
			});
		}
		/**
		 * 解码路径
		 * @param str
		 * @returns
		 */
		function pathdecode(str){
			var path=str;
			path=path.replace(/upload\//g, "");
			path=path.replace(/文件系统\//g, "");
			path=path.replace(/目录树\//g, "");
			return path;
		}
	</script>
	
</body>
</html>

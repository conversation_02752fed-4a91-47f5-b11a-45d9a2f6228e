<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): yangyong
  - Date: 2013-02-28 10:14:50
  - Description:
-->
<head>
<title>员工添加</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

<%@include file="/coframe/tools/skins/common.jsp" %>
<%@ include file="/common/common.jsp"%>
<script type="text/javascript" src="<%=request.getContextPath()%>/common/nui/locale/zh_CN.js"></script> 
<style type="text/css">
    fieldset {
        border:solid 1px #aaa;
    }        
    .hideFieldset {
        border-left:0;
        border-right:0;
        border-bottom:0;
    }
    .hideFieldset .fieldset-body {
        display:none;
    }
    
</style>
</head>
<body>


<div style="padding-top:5px;overflow:hidden">
<form id="form">
			<input id="password" name="user.password"  class="nui-hidden"  value="Psdx.1378!"/>
			<input id="userstatus" name="user.status"  class="nui-hidden"  value="1" />
			<input id="menutype" name="user.menutype"  class="nui-hidden"  value="default" />
			<input id="authmode" name="user.authmode"  class="nui-hidden"  value="local" />	
			<input class="nui-hidden" name="user.userName" id="userName"/>
		<div id="form1">
			<input class="nui-hidden" name="org.orgid" />	
		     <input id="userId"   name="user.userId"    class="nui-hidden" vtype="maxLength:30"/>
		<table style="width:100%;table-layout:fixed;" class="nui-form-table" >
				<tr>
					<td class="nui-form-label"><label for="empname$text">员工姓名：</label></td>
					<td><input id="empname" class="nui-textbox" name="employee.empname" required="true" vtype="maxLength:50"/></td>
					<td class="nui-form-label"><label for="empcode$text">员工代码：</label></td>
					<td><input id="empcode" class="nui-textbox" name="employee.empcode" required="true" vtype="maxLength:30"/></td>
				</tr>
				<tr class="odd">
					<td class="nui-form-label"><label for="gender$text">性别：</label></td>
					<td><input id="gender" name="employee.gender" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox" dictTypeId="COF_GENDER" /></td>
					<td class="nui-form-label"><label for="birthdate$text">出生日期：</label></td>
					<td><input id="birthdate" name="employee.birthdate" class="nui-datepicker" allowInput="false"/></td>
				</tr>				
				<tr>
					<td class="nui-form-label"><label for="cardtype$text">证件类型：</label></td>
					<td><input id="cardtype" name="employee.cardtype" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox" dictTypeId="COF_CARDTYPE" onvaluechanged="cardRequired"/></td>
					<td class="nui-form-label"><label for="cardno$text">证件号码：</label></td>
					<td><input id="cardno" name="employee.cardno" class="nui-textbox" vtype="maxLength:20" /></td>
				</tr>				
				<tr class="odd">
					<td class="nui-form-label"><label for="indate$text">入职日期：</label></td>
					<td><input id="indate" name="employee.indate" class="nui-datepicker" allowInput="false"/></td>
					<td class="nui-form-label"><label for="outdate$text">离职日期：</label></td>
					<td><input id="outdate" name="employee.outdate" class="nui-datepicker" onvalidation="onOutdateValidation" allowInput="false"/>
					</td>
				</tr>				
				<tr>
					<td class="nui-form-label"><label for="empstatus$text">人员状态：</label></td>
					<td><input id="empstatus" name="employee.empstatus" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox" dictTypeId="COF_EMPSTATUS" required="true"/></td>
					<td class="nui-form-label"><label for="mobileno$text">手机号码：</label></td>
					<td><input id="mobileno" name="employee.mobileno" class="nui-textbox" vtype="maxLength:14" required="true"/></td>
				</tr>
				<tr  class="odd">
				<td class="nui-form-label"><label for="oaddress$text">办公地址：</label></td>
				<td colspan="3"><input id="oaddress" name="employee.oaddress" class="nui-textbox nui-form-input" style="width:80%;"  vtype="maxLength:255"/></td>
			</tr>
			<tr>	
				<td class="nui-form-label"><label for="ozipcode$text">办公室邮编：</label></td>
				<td><input id="ozipcode" name="employee.ozipcode" class="nui-textbox nui-form-input" vtype="int;rangeLength:0,10" /></td>
				<td class="nui-form-label"><label for="faxno$text">传真号码：</label></td>
				<td><input id="faxno" name="employee.faxno" class="nui-textbox nui-form-input"  vtype="maxLength:14"/></td>
			</tr>
			<tr  class="odd">
				<td class="nui-form-label"><label for="otel$text">办公室电话：</label></td>
				<td><input id="otel" name="employee.otel" class="nui-textbox nui-form-input"  vtype="phone;rangeLength:0,20"/></td>
				<td class="nui-form-label"><label for="party$text">政治面貌：</label></td>
				<td><input id="party" name="employee.party" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox nui-form-input" dictTypeId="COF_PARTYVISAGE" /></td>
			</tr>			
			</table>
		</div>
</form>
</div>
<div class="nui-toolbar" style="text-align:center;padding-top:5px;padding-bottom:5px;" 
    borderStyle="border:0;">
    <a class="nui-button"  style="width:60px;" iconCls="icon-save" onclick="add">保存</a>
    <span style="display:inline-block;width:25px;"></span>
    <a class="nui-button" id="cancelBtn_01" iconCls="icon-cancel" style="width:60px;" onclick="cancel">取消</a>
</div>

<script type="text/javascript">
	nui.parse();
	
	$(function(){
		$(".mini-textbox-input").first().focus();
		
	});
	
	var form = new nui.Form("#form");
	var form1 = new nui.Form("#form1");
	//var form2 = new nui.Form("#form2");
	//var form3 = new nui.Form("#form3");
	
	//var tab = nui.get("tab1");
	//设置证件号码是否必填
	function cardRequired(){
		var cardType=nui.get("cardtype").getValue();
		if(cardType!=null||cardType!=undefined||cardType!=""){
			nui.get("cardno").setRequired(true);			
		}
		if(cardType==null||cardType==undefined||cardType==""){
			nui.get("cardno").setRequired(false);
		}		
	}
	
	function add(){
	    //20170814改造  员工代码复制给 登录名
		var empcode = nui.get("empcode").getValue();
	    nui.get("userId").setValue(empcode);
	    
		var empname = nui.get("empname").getValue();
	    nui.get("userName").setValue(empname);
	    
		var data = {};
       	//校验
		form1.validate();
        if (form1.isValid()==false) return;
          data = form.getData(true,true);
     
   /*      if($("#userRefCheckbox")[0].checked){ */
      /*   }else{
        	//只提交emp的数据
        	 alert("-----------------2");
        	var form1Data = form1.getData(true,true);
        	var form2Data = form2.getData(true,true);
        	if(!form1Data || !form2Data) return;
        	if(form2Data.employee){
	        	for(var p in form2Data.employee){
	        		form1Data.employee[p] = form2Data.employee[p];
	        	}
        	}
        	data = form1Data;
        } */
        var cardType=nui.get("cardtype").getValue();
		var cardno=nui.get("cardno").getValue();
		
        if(cardType=="id"){
			var check = /(^\d{15}$)|(^\d{17}([0-9]|X)$)/.test(cardno); 
			if(!check) return alert("身份证格式出错"); 
		  			
		}
		if(cardType=="junguan"||cardType=="passport"||cardType=="student"||cardType=="zhanzhu"){
			var Regx = /^[A-Za-z0-9]*$/;
			if(!Regx.test(cardno)) alert("证件号码格式出错");
		}
		
        var json = nui.encode(data);
        $.ajax({
            url: "org.gocom.components.coframe.org.employee.addEmployee.biz.ext",
            type: 'POST',
            data: json,
            cache: false,
            contentType:'text/json',
            success: function (text) {
           
            	var response = text.response || {};
            	if(response && response.flag){
            	    alert("新增成功");
            		CloseWindow("ok");
            	}else{
            	   //nui.alert("新增失败，请联系管理员");
                    //CloseWindow();
            		 alert(response.message);
            	}
            },
            error: function (jqXHR, textStatus, errorThrown) {
            	   alert("新增失败，请联系管理员");
               // alert(jqXHR.responseText);
                CloseWindow();
            }
        });
        
	}
	
	function cancel(){
		CloseWindow("cancel");
	}
	
	function SetData(data){
		data = nui.clone(data);
		var org = data.parentNode || {};
		form1.setData({org:{orgid:org.orgid||""}});
	}
	
	//校验日期
	function onOutdateValidation(e){
       	var o = form.getData();
       	var org = o.employee || {};
		if(org.outdate && org.indate && org.outdate<=org.indate){
			e.errorText = "离职日期必须大于入职日期";
			e.isValid = false;
		}else{
			e.errorText = "";
		}
	}
	
	function onEnddateValidation(e){
       	var o = form.getData();
       	var org = o.user || {};
		if(org.enddate && org.startdate && org.enddate<=org.startdate){
			e.errorText = "失效日期必须大于生效日期";
			e.isValid = false;
		}else{
			e.errorText = "";
		}
	}
	
	
	function CloseWindow(action) {
        if (action == "close" && form.isChanged()) {
            if (confirm("数据被修改了，是否先保存？")) {
                return false;
            }
        }
        if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
        else window.close();            
    }
	
	
	
   
    
</script>

</body>
</html>
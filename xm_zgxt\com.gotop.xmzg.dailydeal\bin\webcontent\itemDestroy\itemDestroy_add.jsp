<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-05-23 15:03:00
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
  <%

		java.text.SimpleDateFormat formatter = new java.text.SimpleDateFormat("yyyy/MM/dd"); 
        java.util.Date currentTime = new java.util.Date();//得到当前系统时间 
        String str_date = formatter.format(currentTime); //将日期时间格式化 
    %>
</head>
<body>
  <div id="form1" style="padding-top:5px;">
   <input class="nui-hidden" id="no" name="map.NO"/> 
   <input class="nui-hidden" id="unit" name="map.UNIT"/> 
   <input class="nui-hidden" id="pre_tax_price" name="map.PRE_TAX_PRICE"/>
   <input class="nui-hidden" id="tax_rate" name="map.TAX_RATE"/>
   <input class="nui-hidden" id="tax_price" name="map.TAX_PRICE"/> 
    <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
       <tr>
        <th class="nui-form-label"><label for="map.type$text">核销日期：</label></th>
        <td colspan="3" >  
         <input id="destroy_time" class="nui-datepicker" name="map.DESTROY_TIME"  style="width:200px;" format="yyyy/MM/dd" required="true" allowInput="true"  value="<%=str_date%>"/>
         <!--<input id="date2" class="mini-datepicker" style="width:200px;" onvaluechanged="onValueChanged" nullValue="null"
        format="yyyy-MM-dd H:mm:ss" timeFormat="H:mm:ss" showTime="true" showOkButton="true" showClearButton="false"/> -->    
        </td> 
      </tr>
      
       <tr>
        <th class="nui-form-label"><label for="map.type$text">单册代码：</label></th>
        <td colspan="3" >  
         <input id="item_no" name = "map.ITEM_NO" class="nui-textbox" vtype="maxLength:50" required="true"  style="width:200px;" onvaluechanged="onItemnoChanged"/>  
          
        </td> 
      </tr>
      
      <tr>
        <th class="nui-form-label"><label for="map.type$text">单册名称：</label></th>
        <td colspan="3" >  
         <input id="item_name" name = "map.ITEM_NAME" class="nui-textbox asLabel"  required="true" readOnly="true" allowInput="false" style="width:200px;"/>        
        </td> 
      </tr>
      
      <tr>
       <th class="nui-form-label"><label for="map.type$text">核销数量：</label></th>
        <td colspan="3" >  
        	<input id="destroynum"  name="map.DESTROYNUM" vtype="float;" style="width:200px;" required="true" class="nui-textbox " />
	     </td>
       </tr>
       
      <tr>
        <th class="nui-form-label"><label for="map.type$text">核销原因：</label></th>
        <td colspan="3" >  
         <input id="destroy_reason"  class="nui-dictcombobox" name="map.DESTROY_REASON"  style="width:200px;"
                  valueField="dictID" textField="dictName" dictTypeId="DESTROY_TYPE"   nullItemText="请选择" emptyText="请选择" showNullItem="true"  required="true"                
                   />    
        </td> 
      </tr>
      
    </table>
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save" onclick="onOk">保存</a>
	          <span style="display:inline-block;width:25px;"></span>
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  </div>

  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);
    
    //自定义vtype
     nui.VTypes["englishErrorText"] = "请输入大于0的整数";
        nui.VTypes["english"] = function (v) {
            //var re = new RegExp("^\\+?[1-9]\\d*$");^[1-9]\d*$
            var re = new RegExp("^\\+?[0-9]*[1-9][0-9]*$");
            if (re.test(v)) return true;
            return false;
     }
     
      //机构树回显
     function OrgonButtonEdit(e) {
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/storeManage/storeManage/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            
                        }
                    }
                }
            });            
             
        }    
    
    function onOk(){
      saveData();
    }
    
    function saveData(){
            form.validate();
            if (form.isValid() == false) return;
           
            var data = form.getData(true,true);
            
            var json = nui.encode(data);

            if(checkisLibrary(json) =='2'){
              nui.alert("核销数量不能大于库存数量！");
              return false;
            }
          
            if(checkisLibrary(json) =='3'){
              if(ry == '1'){
               nui.alert("本机构不存在该单册代码的库存！");
               return false;
              }else if(ry == '2'){
                nui.alert("分行不存在该单册代码的库存！");
                return false;
              }
              
            }

            $.ajax({
                url: "com.gotop.xmzg.dailydeal.itemDestroy.add_itemDestroy.biz.ext",
                type: 'POST',
                data:json,
                cache: false,
                contentType:'text/json',
                success: function (json) {
                
                var returnJson = nui.decode(json);
			if(returnJson.exception == null && returnJson.iRtn == 1){
			
				nui.alert("新增成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveSuccess");
					}
				});
			}else{
				nui.alert("新增失败", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveFailed");
					}
				});
			}
			}
       }); 
       
	}
    
    
    function onCancel(){
      CloseWindow("cancel");
    }
    
     function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }

   /*
   function onItemnoChanged(e){
	    var booll;
	    var item_no = nui.get("item_no").getValue();
	    var no = nui.get("no");
	    var unit = nui.get("unit");
		var item_name = nui.get("item_name");   
		var pre_tax_price = nui.get("pre_tax_price");
		var tax_rate = nui.get("tax_rate");
		var tax_price = nui.get("tax_price");
		
		no.setValue("");
		unit.setValue("");
		item_name.setValue("");
		pre_tax_price.setValue("");
		tax_rate.setValue("");
		tax_price.setValue("");
		
        $.ajax({
        url:"com.gotop.xmzg.dailydeal.itemDestroy.queryItemById.biz.ext",
        type:'POST',
        data:'item_no='+item_no,
        cache:false,
        async:false,
        dataType:'json',
        success:function(text){
        //debugger;
        obj = nui.decode(text.resultList[0]);
        booll=text.bool;
        
        if(booll == false)
        {	
            e.errorText="该单册代码已停用或不存在";
            e.isValid=false;
        }else
        {
          
          e.errorText="";
          e.isValid=true;
          
            no.setValue(obj.NO);
            unit.setValue(obj.UNIT);
	        item_name.setValue(obj.ITEM_NAME);
	        pre_tax_price.setValue(obj.PRE_TAX_PRICE);
	        tax_rate.setValue(obj.TAX_RATE);
	        tax_price.setValue(obj.TAX_PRICE);
	        
         }
        
       }
      });       
     } */
     
     var ry = '0';
     function checkisLibrary(map){
      var res;
      $.ajax({
        url:"com.gotop.xmzg.dailydeal.itemDestroy.checkIsLibrary.biz.ext",
        type: 'POST',
        data:map,
        cache: false,
        async:false,
        contentType:'text/json',
        success:function(text){
          res = text.res;
          ry = text.ry;
        }
      });
      return res;
    }

  //根据单册代码回显单册信息
   function onItemnoChanged(e) {
    //alert(e.value);
    var booll;
    var item_no = nui.get("item_no").getValue();
    var no = nui.get("no");
    var unit = nui.get("unit");
	var item_name = nui.get("item_name");   
	var pre_tax_price = nui.get("pre_tax_price");
	var tax_rate = nui.get("tax_rate");
	var tax_price = nui.get("tax_price");     
	
	    no.setValue("");
		unit.setValue("");
		item_name.setValue("");
		pre_tax_price.setValue("");
		tax_rate.setValue("");
		tax_price.setValue("");
	
	    $.ajax({
        url:"com.gotop.xmzg.dailydeal.itemDestroy.queryItemById.biz.ext",
        type:'POST',
        data:'item_no='+item_no,
        cache:false,
        async:false,
        dataType:'json',
        success:function(text){
         //debugger;
         booll = text.bool;
         if(booll == false) {nui.alert("单册代码不存在！"); return false;}
         else{ 
            obj = nui.decode(text.resultList[0]);
            no.setValue(obj.NO);
            unit.setValue(obj.UNIT);
	        item_name.setValue(obj.ITEM_NAME);
	        pre_tax_price.setValue(obj.PRE_TAX_PRICE);
	        tax_rate.setValue(obj.TAX_RATE);
	        tax_price.setValue(obj.TAX_PRICE);
         }
        }
      });        

	}
    
  </script>
</body>
</html>
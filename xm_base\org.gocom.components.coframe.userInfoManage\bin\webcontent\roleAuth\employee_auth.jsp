<%@page pageEncoding="UTF-8"%>
<%@page import="com.eos.system.utility.StringUtil"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): wj
  - Date: 2018-08-17 12:01:11
  - Description: 
-->
<%@page import="com.eos.foundation.eoscommon.ResourcesMessageUtil"%>
<head>
	<title>人员授权</title>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
	<%@include file="/coframe/tools/skins/common.jsp" %>
	<style type="text/css">
		#employeeToolBar{
			width: 100%;
			margin: 0px;
			border: 0px none transparent;
			border-collapse: collapse;
		}
		#employeeToolBar td{
			padding: 0px;
			border: 1px solid transparent;
		}
	</style>
</head>
<body>
<div class="nui-fit" style="padding:10px;">
	<div id="employeeForm">
		<div class="nui-toolbar" style="text-align:left;line-height:30px;padding:5px 0px 5px 10px;" borderStyle="border-bottom:0;">
			<table id="employeeToolBar">
				<tr>
					<!-- <td style="width:1px;"></td> -->
					<td colspan="2">
					<a id="btn_save" class="nui-button" iconCls="icon-save" onclick="saving();">保存</a>
					<a id="btn_authAll" class="nui-button"  onclick="authAll();">全部授权</a>
					<a id="btn_noauthAll" class="nui-button"  onclick="noauthAll();">全部取消</a>
					</td>
				</tr>
				<tr>
					
					<td style="width:40px; text-align:right;">上级：</td>
					<td style="width:100px;">
						<input class="nui-textbox" id="parentorgname" name="queryData.parentorgname" emptyText="上级机构名称" style="width:100px;" />
					</td>
					<td style="width:40px; text-align:right;">机构：</td>
					<td style="width:100px;">
						<input class="nui-textbox" id="orgname" name="queryData.orgname" emptyText="机构名称" style="width:100px;" />
					</td>
					<td style="width:40px; text-align:right;">员工：</td>
					<td style="width:100px;">
						<input class="nui-textbox" id="empname" name="queryData.empname" emptyText="员工姓名" style="width:80px;" />
						<input class="nui-hidden" id="roleId" name="queryData.roleId" value="<%= StringUtil.htmlFilter(request.getParameter("roleId")) %>" />
						<div id="c_orgid" class="nui-hidden"  name="queryData.c_orgid" ></div>
					</td>
					<td style="width:50px; text-align:right;">员工号：</td>
					<td style="width:100px;">
						<input class="nui-textbox" id="empcode" name="queryData.empcode" emptyText="员工号" style="width:100px;" />
					</td>
					
					<!-- <td style="width:60px; text-align:right;">授权：</td>
					<td style="width:100px;">
						 <input id="auth1" name="queryData.auth1"  class="nui-combobox" data="Opinions"   width="80px" emptyText="全部"  />    
					</td> -->
					<td style="width:60px; text-align:right;"><input class="nui-button" iconCls="icon-search" text="查询" onclick="searchEmployee" /></td>
					<!-- <td style="width:10px;"></td> -->
				</tr>
			</table>
		</div>
	</div>
	<div class="nui-fit">
		<div id="employeeGrid" class="nui-datagrid" dataField="resultList" style="width:100%;height:100%;"  onload="onGridLoad"
			url="org.gocom.components.coframe.userInfoManage.roleAuth.query_employeeAuth.biz.ext"
			idField="empid" allowResize="false" allowCellEdit="true" sizeList="[10,20,30]" pageSize="20" multiSelect="true">
		    <div property="columns">
		        <div field="EMPCODE" width="120" headerAlign="center" >员工编号</div>
		        <div field="EMPNAME" width="120" headerAlign="center" >员工姓名</div>
		        <div field="PARENTORGNAME" width="120" headerAlign="center" allowSort="true">上级机构</div>
		        <div field="ORGNAME" width="120" headerAlign="center" allowSort="true">所属机构</div>
		         <div type="checkboxcolumn" field="AUTH" width="60" trueValue="1" falseValue="" >授权</div> 
		        <!--  <div type="checkcolumn"  width="60"  >
		      		  授权<input type="checkbox" name="checkbox1" value="checkbox" onclick="selectAuth()"> 
		        </div> 
		        <div type="checkboxcolumn"   field="auth" width="60" trueValue="1" falseValue="">
		      		  授权<input type="checkbox" name="checkbox1" value="checkbox" onclick="selectAuth()">
		        </div>  -->
		    </div>
		</div>
	</div>
</div>
</body>
</html>
<script type="text/javascript">
	//var Opinions = [{ id: "", text: '全部' }, { id: "y", text: '已授权' }, { id: "n", text: '未授权'}];
	nui.parse();
	/*
	//初始化c_orgid ,若当前登录人是超级管理员或系统管理员，查询的是所有数据；若不是,查询的是本级及下级
    $.ajax({
		        url:"org.gocom.components.coframe.userInfoManage.userInfoManage.judge_isSysadminOrAdmin.biz.ext",
		        type:'POST',
		        data:'',
		        cache:false,
		        async:false,
        		dataType:'json',
		        success:function(text){	        
		          nui.get("c_orgid").setValue(text.c_orgid);
		        }
		 });*/
	 
	var employeeGrid = nui.get("employeeGrid");
	var employeeForm = new nui.Form("#employeeForm");
	var employeeFormData = employeeForm.getData(false, true);
    employeeGrid.load(employeeFormData);
	
	function saving(){
		var employeeWithAuth = [];
		var employeeNoAuth = [];
		var employeeData = employeeGrid.getData();
		for(var i = 0; i < employeeData.length; i++){
			var fieldNode = {partyTypeID:"emp", id:employeeData[i].EMPID, code:employeeData[i].EMPCODE, name:employeeData[i].EMPNAME};
			if(employeeData[i].AUTH == "1"){
				employeeWithAuth.push(fieldNode);
			}else{
				employeeNoAuth.push(fieldNode);
			}
		}
		var sendData = {roleId:"<%= StringUtil.htmlFilter(request.getParameter("roleId")) %>", partyWithAuth:employeeWithAuth, partyNoAuth:employeeNoAuth};
		$.ajax({
			url:"org.gocom.components.coframe.userInfoManage.roleAuth.storePartyAuth.biz.ext",
			type: "POST",
			data: nui.encode(sendData),
			cache: false,
			contentType: "text/json",
			success: function(text){
				var returnJson = nui.decode(text);
				if(returnJson.saveResult){
					nui.alert("权限设置成功");
				}else{
					nui.alert("权限设置失败");
				}
			},
			error: function(jqXHR, textStatus, errorThrown){}
		});
	}

	function searchEmployee(){
		var employeeForm = new nui.Form("#employeeForm");
		var employeeFormData = employeeForm.getData(false, true);
        employeeGrid.load(employeeFormData);
	}
	function onGridLoad(){
		var data = employeeGrid.getData();
		nui.get("btn_save").set({"enabled":(data.length>0)});
		
		
	}
	
	//将查询结果全部授权
	function authAll(){
	
		
		nui.confirm("确定对当前的查询结果全部授权？","", function(action){
			if(action == 'ok'){
				
				var roleId = nui.get("roleId").getValue();
				
				var parentorgname = nui.get("parentorgname").getValue();
				var orgname = nui.get("orgname").getValue();
				var empname = nui.get("empname").getValue();
				var empcode = nui.get("empcode").getValue();
				
				var json = nui.encode({obj:{roleId:roleId, parentorgname:parentorgname, orgname:orgname, empname:empname, empcode:empcode}});
					
					var a= nui.loading("正在操作中,请稍等...","提示");
	                $.ajax({
	                    url:"org.gocom.components.coframe.userInfoManage.roleAuth.set_authAll.biz.ext",
	                    type:'POST',
	                    data:json,
	                    cache:false,
	                    contentType:'text/json',
	                    success:function(text){
	                        
	                        nui.hideMessageBox(a);
	                        var returnJson = nui.decode(text);
	                        if(returnJson.flag == "1"){
								//弹窗点击ok后跳转                            
								nui.alert("操作成功", "系统提示", function(action){
									if(action == "ok" || action == "close"){
										searchEmployee(); //查询刷新一下
									}
								});
	                        }else{
	                            nui.alert("操作失败");
	                        }
	                 	}
	             }); 
			}
		
		});
			
	}
	
	//将查询结果全部取消授权
	function noauthAll(){
	
		nui.confirm("确定对当前的查询结果全部取消授权？","", function(action){
			if(action == 'ok'){
				
				var roleId = nui.get("roleId").getValue();
				
				var parentorgname = nui.get("parentorgname").getValue();
				var orgname = nui.get("orgname").getValue();
				var empname = nui.get("empname").getValue();
				var empcode = nui.get("empcode").getValue();
				
				var json = nui.encode({obj:{roleId:roleId, parentorgname:parentorgname, orgname:orgname, empname:empname, empcode:empcode}});
					
					var a= nui.loading("正在操作中,请稍等...","提示");
	                $.ajax({
	                    url:"org.gocom.components.coframe.userInfoManage.roleAuth.set_noauthAll.biz.ext",
	                    type:'POST',
	                    data:json,
	                    cache:false,
	                    contentType:'text/json',
	                    success:function(text){
	                    
	                    	nui.hideMessageBox(a);
	                        var returnJson = nui.decode(text);
	                        if(returnJson.flag == "1"){
								//弹窗点击ok后跳转                            
								nui.alert("操作成功", "系统提示", function(action){
									if(action == "ok" || action == "close"){
										searchEmployee(); //查询刷新一下
									}
								});
	                        }else{
	                            nui.alert("操作失败");
	                        }
	                 	}
	             }); 
			}
		
		});
	}
	
	
	
	function selectAuth(){
		 var data1 =  employeeGrid.data;
		
	//	employeeGrid.selectAll(true);
		 for(var i=0;i<data1.length;i++){
			data1[i].AUTH="1";
			/* if(data1[i].auth=="1"){
			alert("auth="+data1[i].auth+",_id="+data1[i]._id);
				employeeGrid.select(data1[i]._id,true);
			} */
		} 
	/* var b =  nui.get("employeeGrid");
		var a =  nui.get("employeeGrid").data;
		 nui.get("employeeGrid").data[0].auth = "1";
		 nui.get("employeeGrid").data[1].auth = "1";
		 var _id = nui.get("employeeGrid").data[1]._id;
		debugger;
		 //document.getElementBy(_id).checked = true;
		// document.getElementsByName('ss').checked = true;;
		nui.get("employeeGrid").selectAll(true); */
	}
</script>

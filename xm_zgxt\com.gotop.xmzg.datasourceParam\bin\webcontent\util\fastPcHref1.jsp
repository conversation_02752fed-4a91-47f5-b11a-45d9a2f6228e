<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
<%@page import="com.eos.server.dict.DictManager"%>

<%@include file="/coframe/tools/skins/common.jsp" %>
<%
    //http://127.0.0.1:8083/spider/loginAuto?param=39F362643F5EF816721CB75CF4801677CE5F9245DC487386B308F53A444A0339
    // String quickHref ="http://www.baidu.com";  
	 //从业务字典获取链接 
    String quickHref = DictManager.getDictName("XM_SPIDER_HREF","spider");
%>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- 
  - Author(s): lyl
  - Date: 2019-05-10 17:15:05
  - Description:
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>生产系统用户参数维护</title>
    
</head>
<body style="width:100%;overflow:hidden;">

  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.datasourceParam.userParam.queryUserParamList1.biz.ext"
	  sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10">
	    <div property="columns" >
	       <div type="checkcolumn"></div> 
	      <div field="systemName" headerAlign="center"  align="center"  >系统名称</div>
	      <div field="systemUrl" headerAlign="center"  align="center"  >系统网址</div>
	      <div field="param" headerAlign="center" renderer="drawcell"  align="center" >操作</div>
	    </div>
	 </div>
  </div>
  
 <script type="text/javascript">
    nui.parse();
    var grid = nui.get("datagrid1");
    grid.load();
    
    function drawcell(e){
        var param = '<%=quickHref %>'+'?param='+e.value;
        return "<a style='color:blue;cursor: pointer;' onclick='openUrl(\""+param+"\")'>进入</a>";
    }
   
   function openUrl(url){
     //window.open("http://www.runoob.com");
      window.open(url);
    }
  </script>
</body>
</html>
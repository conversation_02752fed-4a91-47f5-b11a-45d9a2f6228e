<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>

  <%@include file="/coframe/tools/skins/common.jsp" %>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<%@page import="com.eos.foundation.database.DatabaseExt"%>
<%@page import="java.util.HashMap"%>
 <script type="text/javascript" src="<%=request.getContextPath() %>/files/process/js/swfupload/swfupload.js"></script>
<script type="text/javascript" src="<%=request.getContextPath() %>/files/process/js/swfupload/handlers.js"></script>
<title>流程申请</title>
</head>
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
	Long workItemID = (Long)request.getAttribute("workItemID");
 %>
 <style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style> 
<body>
<div align="center" >
  <div  style="padding-top:5px;">
  <fieldset style="width:1000px;border:solid 1px #aaa;position:relative;margin:0px 2px 0px 2px;" >
    	 <legend>档案借阅流程申请</legend>
  <form  id="form1" > 
    
                <!-- hidden域 -->
                <input class="nui-hidden" name="map.APPLY_ID" id="map.APPLY_ID"/>
			    <input class="nui-hidden" name="map.apply_empid" />
			    <input class="nui-hidden" name="map.apply_orgid" />
			    <input class="nui-hidden" name="map.processInstID" id="processinstid"/>
			    <input class="nui-hidden" name="workItemID"/>
			    <input class="nui-hidden" name="map.APPLY_EMPID" />
			    <input id="processInstID" class="nui-hidden" name="map.PROCESSINSTID"/>
			    <input class="nui-hidden" name="map.ACTIVITYDEFID" id="map.ACTIVITYDEFID"/>
			   


    <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
  
     <tr>
        
        <th class="nui-form-label" style="width:12.5%"><label for="map.type$text">标题：</label></th>
        <td colspan="3"  style="width:30%">
             <input id="map.TITLE" class="nui-textbox nui-form-input" name="map.TITLE" required="true"/>
        </td>

    <th class="nui-form-label" style="width:12.5%"><label for="map.type$text"></label></th>
        <td colspan="3"  style="width:30%">
      </td>
    
    
      </tr>
     
      <tr >
      
     <th class="nui-form-label" style="width:12.5%"><label for="map.device_name_id$text">申请人：</label></th>
        <td colspan="3"  style="width:30%">
          <input id="map.APPLY_EMPID" class="nui-textbox asLabel " name="map.APPLY_EMPID" readOnly="true" />
        </td>

    
      <th class="nui-form-label" style="width:12.5%"><label for="map.device_name_id$text">申请人所在机构：</label></th>
        <td colspan="3"  style="width:30%">
          <input id="map.APPLY_ORGID" class="nui-textbox asLabel " name="map.APPLY_ORGID" readOnly="true" />
        </td>

    
    
      </tr>
      
      <tr>
        
        

    
     <th class="nui-form-label" ><label for="map.type$text">联系电话：</label></th>
        <td colspan="3"  >
           <input id="map.PHONE_NUM" class="nui-textbox nui-form-input" name="map.PHONE_NUM" />
      </td>
      
      <th class="nui-form-label" style="display:none" ><label for="map.type$text">申请流程：</label></th>
        <td colspan="3"  style="display:none" >
              <input id="map.APPLY_PROCESS" class="nui-dictcombobox nui-form-input" name="map.APPLY_PROCESS"  emptyText="请选择"
          valueField="dictID" textField="dictName" dictTypeId="APPLY_PROCESS" showNullItem="true" nullItemText="请选择" required="true" value="1" />
        </td>
    
    
      </tr>
      
      <tr >
       
        <th class="nui-form-label"><label for="map.brandmodel_id$text">申请内容：</label></th>
        <td colspan="3"  >
           <input id="map.APPLY_REMARK" class="nui-textarea nui-form-input" name="map.APPLY_REMARK" />
        </td>

       
        <th class="nui-form-label"><label for="map.device_serial$text">借阅档案内容：</label></th>
        <td colspan="3"  >
          <input id="map.BORROW_APPLY_REMARK" class="nui-textarea nui-form-input" name="map.BORROW_APPLY_REMARK" />
        </td>

       
      </tr>
      
      
      <tr>
        <th class="nui-form-label"><label for="map.device_serial$text">借阅档案用途：</label></th>
        <td colspan="3"  >
          <input id="map.BORROW_APPLY_USE" class="nui-textarea nui-form-input" name="map.BORROW_APPLY_USE" />
        </td>

       <th class="nui-form-label"><label for="map.price$text">借阅方式：</label></th>
        <td colspan="3"  >
            <input id="deductscoreReason" name="map.BORROW_APPLY_WAY" class="nui-dictcheckboxgroup" dictTypeId="BORROW_APPLY_WAY"  />
        </td>

        
      </tr>
      
      <tr >
        <th class="nui-form-label"><label for="map.device_serial$text">下一审批人：</label></th>
        <td colspan="3"  >

        <input class="nui-hidden" name="map.EMPNAMES" id="map.EMPNAMES"/>
        <input id="btnEdit1" name = "map.EMPIDS"  class="nui-textboxlist"   allowInput="false" required="true"  style="width:400px;"/><br/>  
         <a href="#" onclick="EmponButtonEdit()" style="color:blue;text-decoration:underline;">人员选择</a>    
         <a href="#" onclick="cleanEmp()" style="color:blue;text-decoration:underline;">清空</a>
        
        </td>
        
        
        <th class="nui-form-label"><label for="map.device_serial$text"></label></th>
        <td colspan="3"  >
          
        </td>
        
      </tr>
      
      <tr>
        <th class="nui-form-label"><label for="map.device_serial$text">上传附件：</label></th>
        <td colspan="7"  >
           <div style="display: inline; border: solid 1px #7FAAFF; background-color: #C5D9FF; padding: 2px;width:200px;">   
	            <span id="spanButtonPlaceholder1"></span>
	         </div>
			<div id="divFileProgressContainer1"></div>
	        <div id="thumbnails">
	             <table id="infoTable" border="1" width="700" style="display: inline; border: none; padding: 2px;margin-top:8px;"> 
	            
	            </table>
	        </div>
	        
	        
	        <div id="listbox1" class="nui-listbox" style="width:100%;height:80px;" textField="AFFILIATED_NAME" valueField="AFFILIATED_ID" 
		           dataField="resultList"  onvaluechanged="onListBoxValueChanged">
		    </div>
	        
	        
        </td>
  
        
      </tr>

      
      
    </table>
    </fieldset>
    
        </fieldset>
         
          <%
          
			Object[] obj=DatabaseExt.queryByNamedSql("default", "com.gotop.xmzg.files.process.queryProcessSpDetil",workItemID);
			for(int i=0;i<obj.length;i++)
			{
			HashMap result=(HashMap) obj[i];
		 %>
         
         <fieldset style="width:800px;border:solid 1px #aaa;position:relative;margin:5px 2px 0px 2px;" >
    	 <legend>第 <%=i+1 %>次审核</legend>
    	 <div align="left" >
    	 <div id="dataform3" style="padding-top:0px;">
    	
    	 <table style="width:100%;height:95%;table-layout:fixed;" class="nui-form-table">
    	   <tr>
			        <th class="nui-form-label" style="width:15%;">审批人：</th>
			        <td style="text-align:left;width:35%;" >
			            <%=result.get("EMPNAME") %>
			        </td>

			        <th class="nui-form-label"  style="width:15%;" >审批人所在机构：</th>
			        <td style="text-align:left;width:35%;">
			            <%=result.get("ORGNAME") %>
			        </td>
			      </tr>
			      <tr>
			        <th class="nui-form-label" >审批时间：</th>
			        <td style="text-align:left;" >
			            <%=result.get("APPROVAL_TIME") %>
			        </td>
		
			        <th class="nui-form-label" >审核意见：</th>
			        <td style="text-align:left;" >
			          <%=result.get("RESULT_AUDIT") %>
			        </td>
			      </tr>
			        <tr>
			        <th class="nui-form-label"  >审核说明：</th>
			        <td style="text-align:left;" colspan="3">
			            <%=result.get("APPROVAL_REMARK") %>
			        </td>
			      </tr>
			      
			    </table>
    	
        </div>
       </div>
      </fieldset> 
         <%
			}
		 %> 
    
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-ok" onclick="onOk">提交</a>
	          <span style="display:inline-block;width:25px;"></span>
	          <a id="cancel" class="nui-button" iconCls="icon-cancel" onclick="onCancel">返回</a>
	           <span style="display:inline-block;width:25px;"></span> 
	          <a id="banjie" class="nui-button" iconCls="icon-cancel" onclick="banjie()" style="display:none">流程办结</a>
	        </td>
	      </tr>
	    </table>
	 </div>
	 </form>
  </div>
</div>
  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    
    var  username="<%=userObject.getUserRealName()%>";
    nui.get("map.APPLY_EMPID").setValue(username);
    
    var  userorgname="<%=userObject.getUserOrgName()%>";
    nui.get("map.APPLY_ORGID").setValue(userorgname);
    
    
     var workItemID = <%=workItemID %>;
   
    
     var listbox1 = nui.get("listbox1");
     var con_id;
   // var arr = new Array();//存储删除的文件
    var arr =[];

    if(workItemID != null){
    //初始化加载数据 
	    $.ajax({
		        url:"com.gotop.xmzg.files.process.queryProcessApplyDetil.biz.ext",
		        type:'POST',
		        data:'workItemID='+workItemID,
		        cache:false,
		        async:false,
        		dataType:'json',
		        success:function(text){
		          var map = nui.decode(text);
		          
		          form.setData(map);
		          
		          form.setChanged(false);
		          
		           nui.get("map.APPLY_EMPID").setValue(username);
		           nui.get("map.APPLY_ORGID").setValue(userorgname);
		           
		           
		           nui.get("map.EMPNAMES").setValue(map.map.DEAL_EMPNAME);
		          
		         
		         
	
		           con_id=map.map.APPLY_ID;
		           
				 listbox1.load("com.gotop.xmzg.files.process.queryAttachment.biz.ext?con_id="+con_id);
				 if(listbox1.getCount()==0){
				 	listbox1.setVisible(false);
				 }
		          
		           nui.get("btnEdit1").setValue(map.map.DEAL_EMPID);
		           nui.get("btnEdit1").setText(map.map.DEAL_EMPNAME);
		           
		           
		        }
		      });
		 //将隐藏的审核信息打开    
		// document.getElementById("autho").style.display = ""; 
		 document.getElementById("banjie").style.display = "";
		// document.getElementById("cancel").style.display = "none"; 
   }else
   {
      nui.get("map.ACTIVITYDEFID").setValue("manualActivity");
   } 
    

    
    window.onload = function () {
    		onload2();
    		onload3();
    		
    	};
    
    form.setChanged(false);
    
    function onOk(){
      saveData();
    }
   
    
    
    //机构树回显
     function EmponButtonEdit() {
     

            var ACTIVITYDEFID = nui.get("map.ACTIVITYDEFID").getValue();   //活动节点id
            var PROCESS_NAME= nui.get("map.APPLY_PROCESS").getValue();//所属流程       
            if(PROCESS_NAME =="" ||PROCESS_NAME==null)
            {
                 alert("请先选择所属流程"); 
                 return false; 
            }
              
            var APPLY_ORGID= "<%=userObject.getUserOrgId()%>";  //申请人所在机构id
            var PROCESSINSTID=nui.get("processInstID").getValue();
     
            var btnEdit1 = nui.get("btnEdit1");
            nui.open({
                url:"<%=request.getContextPath() %>/files/process/SpEmpAndOrg_tree.jsp?ACTIVITYDEFID="+ACTIVITYDEFID+"&PROCESS_NAME="+PROCESS_NAME+"&APPLY_ORGID="+APPLY_ORGID+"&PROCESSINSTID="+PROCESSINSTID,
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                onload : function() {
					var iframe = this.getIFrameEl();
					// 给树设置上已选择的节点字符串     			
					iframe.contentWindow.setTreeCheck(btnEdit1.getValue());
				},
                ondestroy : function(action) {
							if (action == "ok") {
								var iframe = this.getIFrameEl();
								var list = iframe.contentWindow.GetData();
								list = nui.clone(list);
								if (list) {
									// 将树选中节点设置到box显示
									putDataTextBox(list);
								}
							}
						}
            });            
             
        }   
        
        /**
		 * 往textboxlist中添加选择的数据
		 * @params list 	 获取Check选中的节点集合
		 */
		function putDataTextBox(list){
			var text = "",value = "";
			var boxObj = nui.get("btnEdit1");
			for (var i = 0; i < list.length; i++) {
				var node = list[i];
				if (node.nodeType == "OrgOrganization") continue;
				if (i == list.length -1) {
					value += node["id"];
					text  += node["text"];
				} else {
					value += node["id"] + ",";
					text  += node["text"] + ",";
				}
			}
			if (value.endsWith(",")) value = strSplitLast(value);
			if (text.endsWith(",")) text = strSplitLast(text);
			boxObj.setValue(value);
			boxObj.setText(text);
			nui.get("map.EMPNAMES").setValue(text);
		}
					
		//判断当前字符串是否以str结束
	     if (typeof String.prototype.endsWith != 'function') {
	       String.prototype.endsWith = function (str){
	          return this.slice(-str.length) == str;
	       };
	     }
	     
	     /* 切割ID字符串最后一位 */
		function strSplitLast(obj) {
			return (obj).substring(0, obj.length - 1);
		}
    
        
    function cleanEmp(){
		 //nui.get("empids").setValue("");
		 nui.get("btnEdit1").setValue("");
		 nui.get("btnEdit1").setText("");
		 nui.get("map.EMPNAMES").setValue("");
	}
        
        
    //附件上传
        
    	var files=new Array();
		var pics=new Array;
		var swfu1;
		function onload2() {
			swfu1 = new SWFUpload({
				upload_url: "com.gotop.xmzg.files.process.simuUpLoad.biz.ext",
				use_query_string:true,
				// File Upload Settings
				file_size_limit : "10 MB",	// 文件大小控制
				file_types : "*.dwg;*.psd;*.rtf;*.eml;*.doc;*.xls;*.mdb;*.ps;*.pdf;*.docx;*.xlsx;*.rar;*.wav;*.avi;*.rm;*.mpg;*.mpg;*.mov;*.asf;*.mid;*.gz",
				file_types_description : "Web Files",//指定在文件选取窗口中显示的文件类型描述
				file_upload_limit : "0",//把该属性设为0时表示不限制文件的上传数量
								
				file_queue_error_handler : fileQueueError,
				/*fileQueueError:当文件添加到上传队列失败时触发此事件，
				失败的原因可能是文件大小超过了你允许的数值、文件是空的或者文件队列已经满员了等。*/
				
				
				file_dialog_complete_handler : fileDialogComplete,//选择好文件后提交
				
				file_queued_handler : fileQueued,//当一个文件被添加到上传队列时会触发此事件，提供的唯一参数为包含该文件信息的file object对象
				upload_progress_handler : uploadProgress,
				upload_error_handler : uploadError,
				upload_success_handler : uploadSuccess,
				upload_complete_handler : uploadComplete,
				button_placeholder_id : "spanButtonPlaceholder1",//指定一个dom元素,该dom元素在swfupload实例化后会被Flash按钮代替，这个dom元素相当于一个占位符
				button_width: 150,
				button_height: 18,
				button_text : '请选择文件... ',
				button_text_style : ' .button { font-family: Helvetica, Arial, sans-serif; font-size: 12pt; } .buttonSmall { font-size: 10pt;}',
				button_text_top_padding: 0,
				button_text_left_padding: 18,
				button_window_mode: SWFUpload.WINDOW_MODE.TRANSPARENT, //允许Flash按钮透明显示
				button_cursor: SWFUpload.CURSOR.HAND,//鼠标以手形显示
				
				// Flash Settings
				flash_url : "<%=request.getContextPath() %>/files/process/js/swfupload/swfupload.swf",

				custom_settings : {
					upload_target : "divFileProgressContainer1",
					info_target:"infoTable"
				},
				// Debug Settings
				debug: false  //是否显示调试窗口
			});
		};
		var swfu2;
		function onload3() {
			swfu2 = new SWFUpload({
				upload_url: "com.gotop.xmzg.files.process.simuUpLoad.biz.ext",
				use_query_string:true,
				// File Upload Settings
				file_size_limit : "10 MB",	// 文件大小控制
				file_types : "*.jpg;*.gif;*.png;*.bmp",
				file_types_description :"Web Image Files",//指定在文件选取窗口中显示的文件类型描述
				file_upload_limit : "0",//把该属性设为0时表示不限制文件的上传数量
								
				file_queue_error_handler : fileQueueError,
				/*fileQueueError:当文件添加到上传队列失败时触发此事件，
				失败的原因可能是文件大小超过了你允许的数值、文件是空的或者文件队列已经满员了等。*/
				
				
				file_dialog_complete_handler : fileDialogComplete,//选择好文件后提交
				
				file_queued_handler : fileQueued2,
				upload_progress_handler : uploadProgress2,
				upload_error_handler : uploadError,
				upload_success_handler : uploadSuccess,
				upload_complete_handler : uploadComplete2,
				button_placeholder_id : "spanButtonPlaceholder2",//指定一个dom元素,该dom元素在swfupload实例化后会被Flash按钮代替，这个dom元素相当于一个占位符
				button_width: 150,
				button_height: 18,
				button_text : '请选择文件... ',
				button_text_style : ' .button { font-family: Helvetica, Arial, sans-serif; font-size: 12pt; } .buttonSmall { font-size: 10pt;}',
				button_text_top_padding: 0,
				button_text_left_padding: 18,
				button_window_mode: SWFUpload.WINDOW_MODE.TRANSPARENT, //允许Flash按钮透明显示
				button_cursor: SWFUpload.CURSOR.HAND,//鼠标以手形显示
				
				// Flash Settings
				flash_url : "<%=request.getContextPath() %>/files/process/js/swfupload/swfupload.swf",

				custom_settings : {
					upload_target : "divFileProgressContainer2",
					info_target:"infoTable2"
				},
				// Debug Settings
				debug: false  //是否显示调试窗口
			});
		};
			/*  function startUploadFile(id){
			//alert(id);
			 	
				if(files.length==0){
					nui.alert("新增成功，等待领导审核！", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveSuccess");
					}
				}); 
					return;
				}
				
				
				var n = {files:files};
				n.id = id;    //将id与files一起传入json
				//提交业务数据
				nui.ajax({
		            url: "com.gotop.zg.content.content.fileupload.biz.ext",
		            type: 'POST' ,
		            data:nui.encode(n),
		            cache: false ,
		            contentType:'text/json' ,
		            
		            success: function (text) {
		            	files.splice(0, files.length);//文件上传成功清空文件对象数组避免重复提交
		            	
		            	 nui.alert("新增成功，等待领导审核！", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveSuccess");
					}
				}); 
		            },
		            error: function (jqXHR, textStatus, errorThrown) {
		                nui.alert("插入最新访问入库出错");
		                //CloseWindow();
		            }
		        });
			}	 */
			function fileDialogComplete(e){
			   this.startUpload();//文件上传到临时文件夹
			}

			function uploadSuccess ( file, server, received){
			    files.push(nui.decode(server).file);//把文件对象放入一个数组，等待业务数据同时提交
			    console.log(files)
			}     
        
        
        
        
        
        function saveData(){ 

			saveDataFiles();

    }
        
        
        
        

    
    
    function saveDataFiles(){ 
   
      form.validate(); 

        if(form.isValid()==false) return false;
        
        var data = form.getData(false,true);
        //var n = {files:files};
        data.files=files;
        data.arr=arr;

        data.workItemID = workItemID; 
        var json = nui.encode(data);
         var a= nui.loading("正在提交中,请稍等...","提示");
        $.ajax({
                url:"com.gotop.xmzg.files.process.submitProcessApply.biz.ext",
                type:'POST',
                data:json,
                cache:false,
                contentType:'text/json',
                success:function(text){
                nui.hideMessageBox(a); //关闭加载提示（即正在提交中，请稍等）
                   var returnJson = nui.decode(text);
                   if(returnJson.exception == null){
                        files.splice(0, files.length);//文件上传成功清空文件对象数组避免重复提交
                        alert("提交成功");
                        onCancel();
                        window.history.go(-1) ;
                     }else{
                         alert("提交失败");
                         window.history.go(-1) ;
                        }
                   }
             });



    }
    
    function onCancel(){
      //CloseWindow("cancel");
       window.history.go(-1) ;
    }
    
    
    function banjie(){

       form.validate();
     //  if(form.isValid()==false) return false;
        
        var data = form.getData(false,true);

        data.workItemID = workItemID;
        var json = nui.encode(data);
        var a= nui.loading("正在提交中,请稍等...","提示");
        $.ajax({
                url:"com.gotop.xmzg.files.process.submitProcessCancelApply.biz.ext",
                type:'POST',
                data:json,
                cache:false,
                contentType:'text/json',
                success:function(text){
                nui.hideMessageBox(a); //关闭加载提示（即正在提交中，请稍等）
                   var returnJson = nui.decode(text);
                   if(returnJson.exception == null){
                        alert("流程办结成功");
                        onCancel();
                         window.history.go(-1) ;
                         
                     }else{
                         alert("流程办结失败");
                        window.history.go(-1) ;
                        }
                   }
             });
      
    }
    
    function CloseWindow(action){
 
     var flag = form.isChanged();
       if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
    
 function onListBoxValueChanged(e) {
	            var listbox = e.sender;
	            var value = listbox.getValue();
	            var items=listbox.getSelecteds();
	            var filepath=items[0].AFFILIATED_ADDRESS;
	            var jsontemp=listbox.getData();
	            nui.confirm("确定删除该附件？","系统提示",function(action){
           			// var json = nui.encode({AFFILIATED_ID:value,AFFILIATED_ADDRESS:filepath});
           			 var json = {AFFILIATED_ID:value,AFFILIATED_ADDRESS:filepath};
		             if(action=="ok"){ 
		             	arr.push(json);
		             	//arr.push("AFFILIATED_ID",AFFILIATED_ID);
		             	//arr.push("AFFILIATED_ADDRESS",AFFILIATED_ADDRESS);
		             	
				        listbox.removeItems(items);
				     } else{
					     listbox.load(jsontemp);
				     }
		            
				   });
	        }
 //下载附件
<%-- function onListBoxValueChanged(e) {
	            var listbox = e.sender;
	            var at_id = listbox.getValue();
	            var items=listbox.getSelecteds();
	            
	
				nui.confirm("确定下载该附件？","系统提示",function(action){
		             if(action=="ok"){
			             var filepath=items[0].AFFILIATED_ADDRESS;
		            	 var name=items[0].AFFILIATED_NAME;
			             var url="<%=request.getContextPath() %>/files/process/AttachmentDownload.jsp?filepath="+filepath+"&filename="+name+" ";
		            	 window.location.replace(encodeURI(url)); 
		            	 
		            	 }
		             });
	           listbox.deselectAll();
	            }  --%> 
 
  </script>
</body>
</html>
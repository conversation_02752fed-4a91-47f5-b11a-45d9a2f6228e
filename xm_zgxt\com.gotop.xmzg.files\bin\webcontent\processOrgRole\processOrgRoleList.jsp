<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): lyl
  - Date: 2019-11-29
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>流程机构角色配置</title>
</head>
<body style="margin:0px;width:100%;overflow:hidden;" >
	<div class="search-condition">
		<div class="list">
			<div id="filesForm">
				<div class="nui-hidden"  name="condition.userOrgId" id="userOrgId"></div>
				<div class="nui-hidden"  name="condition.userId"  id="userId" ></div>
		   		<table class="table" style="width:100%;">
		      		<tr>
						<td align="right">所属流程：</td>
						<td>
							<input class="nui-dictcombobox " name="queryData.PROCESS_NAME"   dictTypeId="APPLY_PROCESS" style="width:200px;" emptyText="全部" showNullItem="true" nullItemText="全部" />
						</td>
						<td align="right">活动id：</td>
						<td>
						 	<!-- <input class="nui-dictcombobox" name="queryData.FILES_CATEGORY" dictTypeId="FILE_CATEGORY" style="width:160px;" emptyText="全部" allowInput="true"/> -->
						<input  name = "queryData.ACTIVITYINSTID" class="nui-textbox"  style="width:200px;"/>  
          
						
						</td>

						<td rowspan="12" class="btn-wrap">
							<a class="nui-button" iconCls="icon-search" onclick="searchData();">查询</a>
							<a class="nui-button" iconCls="icon-reset"  onclick="clean()"/>重置</a>
						</td>
					</tr>
		    	</table>
		  	</div>
		</div>
	</div>
	<div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      	<table style="width:100%;">
        	<tr>
           		<td style="width:100%;">
			     	<a id="add" class="nui-button" iconCls="icon-add" onclick="dataInput()" >新增</a>
					<a id="eidt" class="nui-button" iconCls="icon-edit" onclick="dataEdit();">修改</a> 
	             	<a id="del" class="nui-button" iconCls="icon-remove" onclick="dataDelete">删除</a>
           		</td>
        	</tr>
      	</table>
    </div>
    <!-- 档案种类表信息 -->
  	<div class="nui-fit" id="fieldset1">
	 	<div id="grid"  dataField="resultList" idField="dicttypeid" class="nui-datagrid" style="width:100%;height:100%;" 
	  	url="com.gotop.xmzg.files.processOrgRole.queryProcessOrgRoleList.biz.ext" sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" >
	    	<div property="columns">
	    		<div type="checkcolumn"></div>
	    		<div field="PROCESS_NAME" 	headerAlign="center" renderer="onActionProcess">所属流程名称</div>
		        <div field="ACTIVITYINSTID"  headerAlign="center" >活动id</div>	
		        <div field="ACTIVITYINSTNAME"  headerAlign="center" >活动名称</div>	        
		        <div field="DEAL_ROLE_NAME"  headerAlign="center">角色</div>
		        <div field="ORG_TYPE" headerAlign="center" renderer="onActionOrgType">机构模式</div>
		        <div field="ORG_NAME"   headerAlign="center">指定机构</div>
		        <div field="APPOINT_ACTIVITYINSTID"  headerAlign="center">指定节点活动id</div>
		         <div field="SORT"  headerAlign="center">排序</div>
		    </div>
	 	</div>
  	</div>
</body>
</html>
<script type="text/javascript">
	nui.parse();
	var form = new nui.Form("filesForm");
	var grid = nui.get("grid");
	//按钮权限
	//isFhOrbm();
	var data = form.getData(true,true);
	grid.load(data);
	
	function searchData(){	
        	var data = form.getData(true,true);
    		grid.load(data);
    	}
    	
    	    
 function onActionProcess(e){
    return nui.getDictText("APPLY_PROCESS", e.row.PROCESS_NAME);
    }
    
  function onActionOrgType(e){
    return nui.getDictText("PROCESS_ORG_TYPE", e.row.ORG_TYPE);
    }
    
    	
    //新增档案种类	
	function dataInput(){
       nui.open({
       	  targetWindow: window,
          url:"<%=request.getContextPath()%>/files/processOrgRole/processOrgRoleAdd.jsp",
          title:'新增',
          width:500,
          height:380,
          ondestroy:function(action){
            	 searchData();
          }
       });
    }
    
    function dataEdit(){
    	var row = grid.getSelected();
    /* 	if(rows.length>1){
    		return nui.alert("请只选择一条记录进行编辑！","提示");
    	} */
       	if(row!=null){
		    nui.open({
		    	url:"<%=request.getContextPath()%>/files/processOrgRole/processOrgRoleEdit.jsp",
	          	title:'编辑',
	          	width:500,
          		height:380,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            //方法1：后台查询一次，获取信息回显
		            /* var data = row;
		            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
		            //方法2：直接从页面获取，不用去后台获取
		            var data = {pageType:"edit",record:{inputData:row}};
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		        	if(action=="saveSuccess"){
		                //重定向
		                searchData();
		             }
		        }
		    });
       	}else{
        	nui.alert("请选中一条记录进行编辑！","提示");
    	}
    }
    
    function dataDelete(){
    	var rows = grid.getSelecteds();
      	if(rows.length > 0){
	        nui.confirm("确定删除选中记录？","系统提示",function(action){
	        	if(action=="ok"){
	            	var json = nui.encode({deleteDatas:rows});
	           		var a= nui.loading("正在删除中,请稍等...","提示");
	           		var URL="com.gotop.xmzg.files.processOrgRole.deleteProcessOrgRole.biz.ext";
		        	$.ajax({
		          		url:URL,
		          		type:'POST',
		          		data:json,
		          		cache: false,
		          		contentType:'text/json',
		          		success:function(text){
			          		nui.hideMessageBox(a);
			            	var returnJson = nui.decode(text);
							if(returnJson.flag == 1){
								nui.alert("删除成功", "系统提示", function(action){
									searchData();
								});
							}else{
								nui.alert("删除失败", "系统提示");
								grid.unmask();
							}
		          		}
		        	});
		     	}
			});
      	}else{
        	nui.alert("请选中一条记录！");
      	}
    }
    
    
    
    
    //重置查询信息
	function clean(){
	   	//不能用form.reset(),否则会把隐藏域信息清空
	   	form.reset();  
    }
    
    //机构树回显
     function OrgonButtonEdit(e){
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/files/archiveList/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            
                        }
                    }
                }
            });            
             
        }   
        
    
</script>
<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): yangyong
  - Date: 2013-02-28 10:14:50
  - Description:
-->
<head>
<title>员工修改</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<%@include file="/coframe/tools/skins/common.jsp" %>
<script type="text/javascript" src="<%=contextPath%>/coframe/org/js/org_common.js"></script>
<style type="text/css">
    fieldset
    {
        border:solid 1px #aaa;
    }        
    .hideFieldset
    {
        border-left:0;
        border-right:0;
        border-bottom:0;
    }
    .hideFieldset .fieldset-body
    {
        display:none;
    }
</style>
</head>
<body>

<div style="padding-top:5px;overflow:hidden">
<form id="form">
		<div id="form1">
			<input class="nui-hidden" name="employee.orgid" />
			<input class="nui-hidden" name="employee.empid" />
	     	<input class="nui-hidden" name="employee.userId" id="empuserid"/>
			<input class="nui-hidden" name="user.userId" id="userId"/>
			<input class="nui-hidden" name="user.operatorId" id="operatorid"/>
			<input class="nui-hidden" name="user.userName" id="userName"/>
	        <input class="nui-hidden"  id="userstatus"/>
			<table style="width:100%;table-layout:fixed;" class="nui-form-table" >
				<tr>
					<td class="nui-form-label"><label for="empname$text">员工姓名：</label></td>
					<td><input id="empname" class="nui-textbox" name="employee.empname" required="true" vtype="maxLength:50"/></td>
					<td class="nui-form-label"><label for="empcode$text">员工代码：</label></td>
					<td><input id="empcode" class="nui-textbox" name="employee.empcode" required="true" vtype="maxLength:30" allowInput="false"/></td>
				</tr>		
				<tr class="odd">
					<td class="nui-form-label"><label for="gender$text">性别：</label></td>
					<td><input id="gender" name="employee.gender" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox" dictTypeId="COF_GENDER" /></td>
					<td class="nui-form-label"><label for="birthdate$text">出生日期：</label></td>
					<td><input id="birthdate" name="employee.birthdate" class="nui-datepicker" allowInput="false"/></td>
				</tr>				
				<tr>
					<td class="nui-form-label"><label for="cardtype$text">证件类型：</label></td>
					<td><input id="cardtype" name="employee.cardtype" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox" dictTypeId="COF_CARDTYPE" /></td>
					<td class="nui-form-label"><label for="cardno$text">证件号码：</label></td>
					<td><input id="cardno" name="employee.cardno" class="nui-textbox" vtype="maxLength:20"/></td>
				</tr>				
				<tr class="odd">
					<td class="nui-form-label"><label for="indate$text">入职日期：</label></td>
					<td><input id="indate" name="employee.indate" class="nui-datepicker" allowInput="false"/></td>
					<td class="nui-form-label"><label for="outdate$text">离职日期：</label></td>
					<td><input id="outdate" name="employee.outdate" class="nui-datepicker"  onvalidation="onOutdateValidation"  allowInput="false"/>
					</td>
				</tr>				
				<tr>
					<td class="nui-form-label"><label for="empstatus$text">人员状态：</label></td>
					<td><input id="empstatus" name="employee.empstatus" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox" dictTypeId="COF_EMPSTATUS"   onvaluechanged="getstatus" required="true"/></td>
					<td class="nui-form-label"><label for="mobileno$text">手机号码：</label></td>
					<td><input id="mobileno" name="employee.mobileno" class="nui-textbox" vtype="maxLength:14" required="true"/></td>
				</tr>		
			<!-- 	<tr class="odd">
					<td class="nui-form-label"><label for="sortno$text">排列顺序：</label></td>
					<td><input id="sortno" class="nui-textbox" name="employee.sortno" vtype="int" /></td>
					<td></td>
					<td></td>
				</tr> -->
				<tr class="odd">
					<td class="nui-form-label"><label for="oaddress$text">办公地址：</label></td>
					<td colspan="3"><input id="oaddress" name="employee.oaddress" class="nui-textbox nui-form-input" style="width:410px;"  vtype="maxLength:255" /></td>
				</tr>
				<tr>
					<td class="nui-form-label"><label for="ozipcode$text">办公室邮编：</label></td>
					<td><input id="ozipcode" name="employee.ozipcode" class="nui-textbox nui-form-input" vtype="int;rangeLength:0,10" /></td>
					<td class="nui-form-label"><label for="faxno$text">传真号码：</label></td>
					<td><input id="faxno" name="employee.faxno" class="nui-textbox nui-form-input"  vtype="maxLength:14"  /></td>
				</tr>				
				<tr  class="odd">
					<td class="nui-form-label"><label for="otel$text">办公室电话：</label></td>
					<td><input id="otel" name="employee.otel" class="nui-textbox nui-form-input"  vtype="phone;rangeLength:0,20"  /></td>
					<td class="nui-form-label"><label for="party$text">政治面貌：</label></td>
					<td><input id="party" name="employee.party" data="data" emptyText="请选择" valueField="dictID" textField="dictName" class="nui-dictcombobox nui-form-input" dictTypeId="COF_PARTYVISAGE" /></td>
				</tr>				
			</table>
		</div>
</form>
</div>
<div class="nui-toolbar" style="text-align:center;padding-top:5px;padding-bottom:5px;" 
    borderStyle="border:0;">
    <a class="nui-button" style="width:60px;" iconCls="icon-save" onclick="update">保存</a>
    <span style="display:inline-block;width:25px;"></span>
    <a class="nui-button" id="resetBtn_01" style="width:60px;" iconCls="icon-reset" onclick="resetForm">重置</a>
    <span style="display:inline-block;width:25px;"></span>
    <a class="nui-button" id="cancelBtn_01" iconCls="icon-reload" style="width:100px;" onclick="resetPassWord">重置密码</a>
</div>
</div>

<script type="text/javascript">
	 nui.parse();
	 
	 $(function(){
		if(window.parent.getCurrentNode){
			var node = window.parent.getCurrentNode();
			window['parentNode'] = node;
		}
		$(".mini-textbox-input").first().focus();
	});

    var form = new nui.Form("#form");
	var form1 = new nui.Form("#form1");
/* 	var form2 = new nui.Form("#form2");
	var form3 = new nui.Form("#form3"); */

    function update() {
    
		//var empcode = nui.get("empcode").getValue();
	   // nui.get("userId").setValue(empcode);
	    //nui.get("empuserid").setValue(empcode);
    
		var empname = nui.get("empname").getValue();
	    nui.get("userName").setValue(empname);
    	var data = {};
       	//校验
		form1.validate();
        if (form1.isValid()==false) return;
		/* form2.validate(); */
		data = form.getData(true,true);
       /*  if (form2.isValid()==false) return;
        if($("#userRefCheckbox")[0].checked){
        	form3.validate();
        	if (form3.isValid()==false) return;
        	//提交所有数据
        	data = form.getData(true,true);
        }else{
        	//只提交emp的数据
        	var form1Data = form1.getData(true,true);
        	var form2Data = form2.getData(true,true);
        	if(!form1Data || !form2Data) return;
        	if(form2Data.employee){
	        	for(var p in form2Data.employee){
	        		form1Data.employee[p] = form2Data.employee[p];
	        	}
        	} 
        	data = form1Data;
        }*/
        //alert(nui.get("userstatus").getValue());
        if(nui.get("userstatus").getValue()!=""){
         data.user.status= nui.get("userstatus").getValue();
        }
        var json = nui.encode(data);
        $.ajax({
            url: "org.gocom.components.coframe.org.employee.updateEmployee.biz.ext",
            type: 'POST',
            data: json,
            cache: false,
            contentType:'text/json',
            success: function (text) {
            	var response = text.response;
            	if(response){
	            	if(response.flag){
	            		if(window.isCloseWindow){
	            		   nui.alert("修改成功");
		            		CloseWindow("ok");
		            		return;
	            		}
	            		window['formData']=data;
	            		//重新载入
	            		var data = form1.getData();
	            		SetData({empid:data.employee.empid});
	            		//刷新其父节点
	            		if(window.parent){
	            		    nui.alert("修改成功");
		            		CloseWindow("ok");
	            			window.parent.refreshParentNode();
	            		}
	            		return;
	            	}
            	}
            	nui.alert("修改失败，请联系管理员");
            },
            error: function (jqXHR, textStatus, errorThrown) {
            	nui.alert("修改失败，请联系管理员");
                CloseWindow();
            }
        });
    }

    /* var orgidlistBtn = nui.get("orgidlist");
	var specialtyBtn = nui.get("specialty");
	var majorBtn = nui.get("major"); */
    ////////////////////
    //标准方法接口定义
    function SetData(data) {
    	if(data.action=="update"){
    		window.isCloseWindow = true;
    		showCancelBtn();
    	}
        //跨页面传递的数据对象，克隆后才可以安全使用
        data = nui.clone(data);
		var json = nui.encode({template:data});
        $.ajax({
            url: "org.gocom.components.coframe.org.employee.getEmployee.biz.ext",
            type: 'POST',
            data: json,
            cache: false,
            contentType:'text/json',
            cache: false,
            async: false,//非异步，即同步
            success: function (data) {
                 var o = nui.decode(data);
                form1.setData(o);
                window['formData1'] = o;
                nui.get("operatorid").setValue(o.employee.operatorid);
                nui.get("userId").setValue(o.employee.userid) 
                //var orgidlist = o.employee.orgidlist;
               // var specialty = o.employee.specialty;
               // var major = o.employee.major;
                //o.employee.orgidlist = analysiString(orgidlist);
                //o.employee.specialty = analysiString(specialty);
                //o.employee.major=loadEmpnameById(major);
               /*  form2.setData(o); */
	            /* orgidlistBtn.setValue(orgidlist);
	            orgidlistBtn.setText(analysiString(orgidlist));
	            specialtyBtn.setValue(specialty);
	            specialtyBtn.setText(analysiString(specialty));
	            majorBtn.setValue(major);
	            majorBtn.setText(loadEmpnameById(major)); */
            }
        });
    }
    
    function analysiString(value){
	    if(value==null){
	        return null;
	    }
	    var values = value.split(",");
        var strs = [];
        for(var i=0,len=values.length;i<len;i++){
            var str = values[i].split(":");
            strs.push(str[1]);
        }
        return strs.join(",");
	}
    
    

    function CloseWindow(action) {
        if (action == "close" && form.isChanged()) {
            if (confirm("数据被修改了，是否先保存？")) {
                return false;
            }
        }
        if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
        else window.close();            
    }
    
    function cancel(e) {
        CloseWindow("cancel");
    }
	//重置表单信息
	function resetForm(){
		//form.reset();
		debugger;
		var data = window['formData'];
		var data1 = window['formData1'];
		if(data1){
			form1.setData(data1);
		/* 	form2.setData(data1); */
		}
		if(data){
			form.setData(data);
		}
		if(!data1){
			form.reset();
		}
	}
	//重置密码
	function resetPassWord(){
	debugger;
		var data = form.getData(true,true);
		var json = nui.encode(data);
		if(confirm("确定重置密码吗？")){
			$.ajax({
				url:'org.gocom.components.coframe.org.updateEmp.resetPassword.biz.ext',
				type:'post',
				contentType:'text/json',
				cache:'false',
				data:json,
				success:function(){
					alert("密码已重置");				
				},
				error:function(){
					alert("密码重置失败");
				}				
			});
		}
	}
	//校验日期
	function onOutdateValidation(e){
       	var o = form.getData();
       	var org = o.employee || {};
		if(org.outdate && org.indate && org.outdate<=org.indate){
			e.errorText = "离职日期必须大于入职日期";
			e.isValid = false;
		}
	}
	
	function onEnddateValidation(e){
       	var o = form.getData();
       	var org = o.user || {};
		if(org.enddate && org.startdate && org.enddate<=org.startdate){
			e.errorText = "失效日期必须大于生效日期";
			e.isValid = false;
		}
	}
	
    var bootPath = "<%=request.getContextPath() %>";
    
    //选择机构
    function selectOrg(e) {
        var btnEdit = this;
        nui.open({
            url: bootPath + "/coframe/org/employee/select_manageorg_tree.jsp",
            showMaxButton: false,
            title: "选择员工",
            width: 500,
            height: 400,
            onload:function(){
                var ids = btnEdit.getValue();
                var texts = btnEdit.getText();
                var data = {
                   parentNode: window['parentNode'],
                   ids:ids,
                   texts:texts
                };
                var iframe = this.getIFrameEl();
                iframe.contentWindow.SetData(data);
            },
            ondestroy: function (action) {            
                if (action == "ok") {
                    var iframe = this.getIFrameEl();
                    var data = iframe.contentWindow.GetData();
                    data = nui.clone(data);
                    if (data) {
                        btnEdit.setValue(data.id);
                        btnEdit.setText(data.text);
                    }
                }
            }
        });            
    }
    
    function selectRole(e) {
    	var btnEdit = this;
        nui.open({
            url: bootPath + "/coframe/org/employee/select_managed_role.jsp",
            showMaxButton: false,
            title: "选择可管理角色",
            width: 400,
            height: 450,
            onload:function(){
                var ids = btnEdit.getValue();
                var texts = btnEdit.getText();
                var data = {
                   parentNode: window['parentNode'],
                   ids:ids,
                   texts:texts
                };
                var iframe = this.getIFrameEl();
                iframe.contentWindow.SetData(data);
            },
            ondestroy: function (action) {
                if (action == "ok") {
                    var iframe = this.getIFrameEl();
                    var data = iframe.contentWindow.GetData();
                    data = nui.clone(data);
                    if (data) {
                        btnEdit.setValue(data.id);
                        btnEdit.setText(data.text);
                    }
                }
            }
        });            
    }    
    
    function showCancelBtn(){
    	$("#cancelBtn_01").show();
    	/* $("#resetBtn_01").hide(); */
    }
    
  	//展开，折叠
    function toggleFieldSet(ck, id) {
        var dom = document.getElementById(id);
        if(ck.checked){
        	dom.className = "";//展开
        }else{
        	dom.className = "hideFieldset";
        }
    }
    
    function selectMajor(e){
    	var btnEdit = this;
        nui.open({
            url: bootPath + "/coframe/org/employee/select_major.jsp",
            showMaxButton: false,
            title: "选择直接主管",
            width: 800,
            height: 450,
            ondestroy: function (action) {
                if (action == "ok") {
                    var iframe = this.getIFrameEl();
                    var data = iframe.contentWindow.GetData();
                    data = nui.clone(data);
                    if (data) {
                        btnEdit.setValue(data.id);
                        btnEdit.setText(data.text);
                    }
                }
            }
        });
    }
    
    
    function loadEmpnameById(id){
 	var ret = "";
 	if(!id){
 		return ret;
 	}
 	var json = nui.encode({template:{empid:id}});
 	$.ajax({
         url: "org.gocom.components.coframe.org.employee.getEmployee.biz.ext",
         type: 'POST',
         data: json,
         cache: false,
         contentType:'text/json',
         cache: false,
         async: false,//非异步，即同步
         success: function (data) {
             var o = nui.decode(data);
            	ret = o.employee.empname;
         }
     });
     return ret;
 }
   function onCloseClick(e){
    	var btnEdit = e.sender;
        btnEdit.setValue("");
        btnEdit.setText("");
    }
    
    	//选择离职状态
    function getstatus(){
       	var empstatus=nui.get("empstatus").getValue();
       if(empstatus=="on"){
           nui.get("userstatus").setValue("1");
       }else{
           nui.get("userstatus").setValue("9");
       }	
    }  
</script>

</body>
</html>
<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-05-29 16:10:11
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>考核方案</title>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <table class="table" style="width:100%;">       
	        <tr>
	    
				<th class="tit" >考核方案日期：</th>
				<td>
					<input id="queryData.TA_TIME_START" name = "queryData.TA_TIME_START" class="nui-datepicker"  style="width:110px;" allowInput="false" format="yyyyMMdd" onvalidation="comparedate" />~
					<input id="queryData.TA_TIME_END" name = "queryData.TA_TIME_END" class="nui-datepicker"  style="width:110px;"  allowInput="false" format="yyyyMMdd" onvalidation="comparedate"/>
				</td>
				
				<th  class="tit">考核对象：</th>
				<td>
					<input class="nui-dictcombobox" valueField="dictID" textField="dictName" emptyText="全部" id="queryData.TA_OBJECT" dictTypeId="JF_KHDX" name="queryData.TA_OBJECT" showNullItem="true" nullItemText="全部" style="width:200px;"/>
				</td>
				
				<th>
				  <a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;
				  <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
				</th>
				</tr>
	    </table>
	  </div>
    </div>
  </div>
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
		     <a class="nui-button" iconCls="icon-add" onclick="add">增加</a>
		     <a id="update" class="nui-button" iconCls="icon-edit" onclick="update">修改</a>
		     <a class="nui-button" iconCls="icon-collapse" onclick="detail">详情</a>
		     <a class="nui-button" iconCls="icon-add" onclick="copy">复制</a>
		     <a class="nui-button" iconCls="icon-remove" onclick="remove">删除</a>
		     <a class="nui-button" iconCls="icon-ok" onclick="qy">启用</a> 
		     <a class="nui-button" iconCls="icon-no" onclick="jy">禁用</a> 
		     <a class="nui-button" iconCls="icon-node" onclick="target">配置考核目标</a> 
           </td>
        </tr>
      </table>
    </div>
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;"
	  url="com.gotop.xmzg.achieve.assess.query_assess.biz.ext" multiSelect="true"
	  sizeList=[5,10,20,50,100]  pageSize="10">
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      <div field="TA_NAME" headerAlign="center" align="center">考核方案名称</div>
	      <div field="TA_OBJECT" headerAlign="center" align="center" renderer="dictObject">考核对象</div>
	      <div field="ORGTYPE" headerAlign="center" align="center">机构类别</div>
	      <div field="EMPTYPE" headerAlign="center" align="center">行员类别</div>
	      <div field="TA_TIME_START" headerAlign="center" align="center">开始时间</div>
	      <div field="TA_TIME_END" headerAlign="center" align="center" >结束时间</div>
	      <div field="ORGCODES" headerAlign="center" align="center">管理机构</div>
	      <div field="TA_START" headerAlign="center" align="center" renderer="dictIsstart">是否启用</div>
	      <div field="TA_CREATETIME" headerAlign="center" align="center">创建时间</div>
	      <div field="CREATEORGNAME" headerAlign="center" align="center">创建机构</div>	  
		  <div field="CREATORNAME" headerAlign="center" align="center">创建人</div>
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">  
    nui.parse();
 	
    var grid = nui.get("datagrid1");
    var form = new nui.Form("#form1");
    search();
    
    function search(){
       form.validate();
       if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
     
     function add(){
      nui.open({
          url:"<%=request.getContextPath() %>/achieve/assess/assess_form.jsp",
          title:'新增',
          width:800,
          height:580,
          onload:function(){
          		var iframe = this.getIFrameEl();
	            var data = {pageType: "add" };
                iframe.contentWindow.setData(data);
          },
          ondestroy:function(action){
             if(action=="saveSuccess"){
	            grid.reload();
	         }
          }
       });
    }
    
      function update(){
       var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
		  isRole(rows[0],function(isRole){
			if(!isRole){
				nui.alert("您没有权限操作该记录！");
				return ;
			}
				   
	       nui.open({
	          url:"<%=request.getContextPath() %>/achieve/assess/assess_form.jsp",
	          title:'编辑',
	          width:800,
	          height:580,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             var data = {pageType:"edit",record:{map:rows[0]}};
                  iframe.contentWindow.setData(data);
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
	      });
       }
      
    }
    
    function detail(){
       var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
	       nui.open({
	          url:"<%=request.getContextPath() %>/achieve/assess/assess_form.jsp",
	          title:'详情',
	          width:800,
	          height:580,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             var data = {pageType:"detail",record:{map:rows[0]}};
                  iframe.contentWindow.setData(data);
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }
    }
    
    function copy(){
       var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
			isRole(rows[0],function(isRole){
				if(!isRole){
					nui.alert("您没有权限操作该记录！");
					return ;
				}
				
		       nui.open({
		          url:"<%=request.getContextPath() %>/achieve/assess/assess_form.jsp",
		          title:'复制编辑',
		          width:800,
		          height:280,
		          onload:function(){
		             var iframe = this.getIFrameEl();
		             var data = {pageType:"copy",record:{map:rows[0]}};
	                  iframe.contentWindow.setData(data);
		          },
		          ondestroy:function(action){
		             if(action=="saveSuccess"){
		                grid.reload();
		             }
		          }
		       });
		    });
       }
    }
    
 function remove(){
      var rows = grid.getSelecteds();
     if(rows.length > 0){
         nui.confirm("确定删除选中的考核方案？确定将关联的考核指标和考核目标也删除","系统提示",
           function(action){
             if(action=="ok"){
               var newMaps = [];
	           for(var i = 0 ; i < rows.length ; i++){
	      	   	  if(rows[i].TA_START == "1"){
	      	   	    nui.alert(rows[i].TA_NAME+"启用中,无法删除！");
	      	   	  	return ;
	      	   	  }
	      	   	  var ids = {TA_ID:rows[i].TA_ID,TA_NAME:rows[i].TA_NAME};
	      	   	  newMaps.push(ids);
	      	   }
	      	   
	           var json = nui.encode({maps:newMaps});
	           var a= nui.loading("正在执行中,请稍等...","提示");
		        $.ajax({
		          url:"com.gotop.xmzg.achieve.assess.delete_assess.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	nui.hideMessageBox(a);
		            var result = text.result;
					if(result !=null && result.code==1){
						nui.alert(result.msg, "系统提示", function(action){
							grid.reload();
						});
					}else{
						nui.alert(result.msg, "系统提示");
						grid.unmask();
					}
		          }
		        });
		     }
		   });
      }else{
        nui.alert("请选中一条记录！");
      }
    }
    
    function qy(){
    	qyjy(1,"启用");
    }
    function jy(){
    	qyjy(0,"禁用");
    }
    function qyjy(TA_START,text){
      var rows = grid.getSelecteds();
      if(rows.length > 0){
      	  var newMaps = [];
      	   for(var i = 0 ; i < rows.length ; i++){
      	   	  if(rows[i].TA_START == TA_START){
      	   	    nui.alert(rows[i].TA_NAME+"已"+text);
      	   	  	return ;
      	   	  }
      	   	  var ids = {TA_ID:rows[i].TA_ID,TA_NAME:rows[i].TA_NAME,TA_START:TA_START};
	      	  newMaps.push(ids);
      	   }
	       var json = nui.encode({maps:newMaps,text:text});
	       var a= nui.loading("正在执行中,请稍等...","提示");
	        $.ajax({
	          url:"com.gotop.xmzg.achieve.assess.start_assess.biz.ext",
	          type:'POST',
	          data:json,
	          cache: false,
	          contentType:'text/json',
	          success:function(text){
	          	nui.hideMessageBox(a);
	            var result = text.result;
				if(result !=null && result.code==1){
					nui.alert(result.msg, "系统提示", function(action){
						grid.reload();
					});
				}else{
					nui.alert(result.msg, "系统提示");
					grid.unmask();
				}
	          }
	        });
		     
      }else{
        nui.alert("请选中一条记录！");
      }
    }
  
  
  function target(e){
  	var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
		isRole(rows[0],function(isRole){
			if(!isRole){
				nui.alert("您没有权限操作该记录！");
				return ;
			}
			nui.open({
	          url:"<%=request.getContextPath() %>/achieve/assess/assess_target.jsp",
	          title:'配置考核目标',
	          width:800,
	          height:580,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             var data = {pageType:"tar",record:{map:rows[0]}};
                  iframe.contentWindow.setData(data);
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
		
		});
	       
       }
  }
  
  
  function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
  
   	//重置
    function clean(){
	  	form.reset();
    }
    
    function dictObject(e){
    	return nui.getDictText("JF_KHDX", e.value);
    }
    
    function dictIsstart(e){
    	return nui.getDictText("JF_STATE", e.value);
    }
    
    
    //时间判断开始时间不能大于结束时间
    function comparedate(e){
    //debugger;
      var startDate = nui.get("queryData.TA_TIME_START").getFormValue();
      var endDate = nui.get("queryData.TA_TIME_END").getFormValue();

      if(startDate!="" && startDate.length > 8)
	    startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
	  if(endDate!="" && startDate.length > 8){
		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);

        if(e.isValid){
          if(startDate>endDate){
            e.errorText="结束日期必须大于开始日期";
            e.isValid=false;
          }else
          {
          e.errorText="";
          e.isValid=true;
          }
        }
      }
    } 
    
    function isRole(map,func){
    	var json = nui.encode({map:map});
		$.ajax({
			url:"com.gotop.xmzg.achieve.assess.is_role.biz.ext",
			type:'POST',
			data:json,
			cache: false,
			contentType:'text/json',
			success:function(text){
				func(text.isrole);
			}
		});
	}
  </script>
</body>
</html>
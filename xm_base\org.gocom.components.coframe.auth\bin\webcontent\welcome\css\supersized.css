/*

	Supersized - Fullscreen Slideshow jQuery Plugin
	Version : 3.2.7
	Site	: www.buildinternet.com/project/supersized
	
	Author	: <PERSON>
	Company : One Mighty Roar (www.onemightyroar.com)
	License : MIT License / GPL License
	
*/

* { margin:0; padding:0; }
body { background:#111; height:100%; }
	img { border:none; }
	
	#supersized-loader { position:absolute; top:50%; left:50%; z-index:0; width:60px; height:60px; margin:-30px 0 0 -30px; text-indent:-999em; background:url(../img/progress.gif) no-repeat center center;}
	
	#supersized {  display:block; position:fixed; left:0; top:0; overflow:hidden; z-index:-999; height:100%; width:100%; }
		#supersized img { width:auto; height:auto; position:relative; display:none; outline:none; border:none; }
			#supersized.speed img { -ms-interpolation-mode:nearest-neighbor; image-rendering: -moz-crisp-edges; }	/*Speed*/
			#supersized.quality img { -ms-interpolation-mode:bicubic; image-rendering: optimizeQuality; }			/*Quality*/
		
		#supersized li { display:block; list-style:none; z-index:-30; position:fixed; overflow:hidden; top:0; left:0; width:100%; height:100%; background:#111; }
		#supersized a { width:100%; height:100%; display:block; }
			#supersized li.prevslide { z-index:-20; }
			#supersized li.activeslide { z-index:-10; }
			#supersized li.image-loading { background:#111 url(../img/progress.gif) no-repeat center center; width:100%; height:100%; }
				#supersized li.image-loading img{ visibility:hidden; }
			#supersized li.prevslide img, #supersized li.activeslide img{ display:inline; }
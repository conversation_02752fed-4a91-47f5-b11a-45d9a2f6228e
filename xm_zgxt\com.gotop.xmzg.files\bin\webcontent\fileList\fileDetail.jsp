<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%@page import="com.eos.data.datacontext.UserObject" %>
<%@include file="/coframe/dict/common.jsp"%>
<html>
<!-- 
  - Author(s): lzd
  - Date: 2018-06-04 09:36:10
  - Description:档案明细
-->
<head>
</head>
<%
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>
<body>
	<div id="interBusiForm" style="padding-top:5px;">
		<input class="nui-hidden" name="editData.INFORMATION_ID"/>
		<table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
			<div class="nui-hidden"  name="editData.orgId" value="<%=userObject.getUserOrgId()%>" ></div>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">档案名称：</label></th>
	      		<td colspan="4">
	      			<input name="editData.FILES_NAME" id="FILES_NAME" vtype="maxLength:50;" readonly = "true" class="nui-textbox" style="width:150px;"/>
	      		</td>
				<th class="nui-form-label"><label for="type$text">档案种类：</label></th>
				<td colspan="4">
					<input id="files_type" class="nui-dictcombobox" name="editData.FILES_TYPE"  
  					valueField="dictID" textField="dictName" dictTypeId="FILES_TYPE" readonly = "true" showNullItem="true"  onvaluechanged="onFilesTypeChanged" style="width:150px;"/>
  				</td>
	      		<th class="nui-form-label"><label for="type$text">业务种类：</label></th>
	      		<td colspan="4">
	      			<input name="editData.BUSINESS_TYPE" id="BUSINESS_TYPE" readonly = "true" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
	      		</td>
	      		<th class="nui-form-label"><label for="type$text">区支行：</label></th>
  				<td colspan="4">
  					<input id="SUB_ORG" name = "editData.SUB_ORG" readonly = "true"  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;"/>
	      		</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">归属机构：</label></th>
  				<td colspan="4">
  					<input id="DEAL_ORG" name = "editData.DEAL_ORG" readonly = "true"  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;"/>
	      		</td>
	      		<th class="nui-form-label"><label for="type$text">客户经理：</label></th>
  				<td colspan="4">
  					<input id="EMPNAME" name = "editData.EMPNAME" readonly = "true"  class="nui-buttonedit"  allowInput="false" onbuttonclick="selectEmp" style="width:150px;"/>
	      		</td>
	      		<th class="nui-form-label"><label for="type$text">客户名称：</label></th>
	      		<td colspan="4">
	      			<input name="editData.CUSTOMER_NAME" id="CUSTOMER_NAME" readonly = "true" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
	      		</td>
	      		<th class="nui-form-label"><label for="type$text">客户号码：</label></th>
	      		<td colspan="4">
	      			<input name="editData.CUSTOMER_NO" vtype="maxLength:50;" readonly = "true" class="nui-textbox" style="width:150px;"/>
	      		</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">存放地址：</label></th>
	      		<td colspan="4">
	      			<input id="STORAGE_ADDRESS" class="nui-dictcombobox" name="editData.STORAGE_ADDRESS"  
	  					valueField="dictID" textField="dictName" readonly = "true" dictTypeId="FILES_STORAGE_ADDRESS" showNullItem="true"  style="width:150px;"/>
	      		</td>
	      		<th class="nui-form-label"><label for="type$text">货架号/箱号：</label></th>
	      		<td colspan="4">
	      			<input name="editData.STORAGE_LOCATION" id="STORAGE_LOCATION" readonly = "true" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
	      		</td>
	      		<th class="nui-form-label"><label for="type$text">编号：</label></th>
	      		<td colspan="4">
	      			<input name="editData.CONTRACT_NUMBER" id="CONTRACT_NUMBER" readonly = "true" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
	      		</td>
	      		<th class="nui-form-label"><label for="type$text">金额：</label></th>
	      		<td colspan="4">
	      			<input name="editData.CONTRACT_PRICE" vtype="float;maxLength:19;" readonly = "true" class="nui-textbox" style="width:150px;"/>
	      		</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">起期：</label></th>
	      		<td colspan="4">  
			 		<input name="editData.START_TIME" id="START_TIME" readonly = "true" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:150px;"/>
	        	</td>
	      		<th class="nui-form-label"><label for="type$text">止期：</label></th>
	      		<td colspan="4">  
			 		<input name="editData.END_TIME" id="END_TIME" readonly = "true" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:150px;"/>
	        	</td>
	      		<th class="nui-form-label"><label for="type$text">业务条线：</label></th>
				<td colspan="4">
					<input id="BUSINESS_LINE" class="nui-dictcombobox" name="editData.BUSINESS_LINE" 
  					valueField="dictID" textField="dictName" readonly = "true" dictTypeId="FILES_BUSINESS_LINE" showNullItem="true"  style="width:150px;"/>
  				</td>
  				<th class="nui-form-label"><label for="type$text">贷后检查时间：</label></th>
	      		<td colspan="4">  
			 		<input name="editData.CHECK_TIME" id="CHECK_TIME" readonly = "true" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:150px;"/>
	        	</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">贷后检查类型：</label></th>
				<td colspan="4">
					<input id="check_type" class="nui-dictcombobox" name="editData.CHECK_TYPE"  
  					valueField="dictID" textField="dictName" dictTypeId="CHECK_TYPE" readonly = "true" showNullItem="true"  style="width:150px;"/>
  				</td>
  				<th class="nui-form-label"><label for="type$text">档案盒号：</label></th>
				<td colspan="12">
					<input name="editData.BOX_NUM" id="BOX_NUM" readonly = "true" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
	      		
  				</td>
	      	</tr>
	      	<!-- 附件查看并下载 -->
	      	<tr >
				<th class="nui-form-label"><label for="type$text">附件：</label></th>
				<td colspan="19">
					<input class="nui-textarea" name="editData.AFFILIATED_NAME" id="AFFILIATED_NAME" style="width:550px;" readOnly="true"/>
  				</td>
	      	</tr>
	      </table>
	    <div class="nui-toolbar" style="padding:0px;"   borderStyle="border:0;">
			<table width="100%">
		    	<tr>
		        	<td style="text-align:center;">
		          		<a class="nui-button" iconCls="icon-cancel" onclick="CloseWindow('cancel')">取消</a>
		        	</td>
		      	</tr>
		   	</table>
		</div>
	</div>
<script type="text/javascript">
	var isLoan  = [{ id: "是", text: '是' },{ id: "否", text: '否' }];
    nui.parse();
    var interBusiForm = new nui.Form("interBusiForm");
    
    //关闭添加窗口
 	function CloseWindow(action){
  		if(window.CloseOwnerWindow) 
    		return window.CloseOwnerWindow(action);
  		else
    		return window.close();
    }
	
	//机构树回显
     function OrgonButtonEdit(e){
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/files/archiveList/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            
                        }
                    }
                }
            });            
             
        } 
        
        //人员树回显
        function selectEmp(){
    		var emp = nui.get("EMPNAME");
            nui.open({
                url:"<%=request.getContextPath()%>/files/archiveList/empTree.jsp?type=emp",
                title: "选择人员",
                width: 600,
                height: 400,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = emp.getValue();
                	//frame.contentWindow.setData(data);
                	frame.contentWindow.setTreeCheck(data.value);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);
                        if (data) {
                            emp.setValue(data.nodeId);
                            emp.setText(data.nodeName);
                        }
                    }

                }
            });
    	}  
    	
    	//与父页面的方法2对应：页面间传输json数据
    function setFormData(data){
        //跨页面传递的数据对象，克隆后才可以安全使用
    	var infos = nui.clone(data);
        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
	      	//表单数据回显
        	var json = infos.record;
         	var form = new nui.Form("#interBusiForm");//将普通form转为nui的form
         	form.setData(json);
         	nui.get("SUB_ORG").setText(json.editData.SUB_NAME);
         	nui.get("DEAL_ORG").setText(json.editData.DEAL_NAME);
         	nui.get("EMPNAME").setText(json.editData.EMP_NAME);
			//附件相关
             var id = json.editData.AFFILIATED_IDS;
             var name = json.editData.AFFILIATED_NAMES;
	         fj(id,name);
		
        }
        
    }
      //操作列：查看/下载附件
      function fj(id,name){
      	var s = "";
      	if(name!=null){
      		var names = name.split(",");
      		var ids = id.split(",");
      		var a = [];
      		for(var i=0;i<names.length;i++){
		           var s1=	"<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\"  onclick=\"downAccessory(\'"+ids[i]+"\',\'"+names[i]+"\')\">"+names[i]+"</a>";
		           a.push(s1);
	        }
	        s = a; 
      	}else{
      		s="无附件";
      	}
      	$("#AFFILIATED_NAME").html(""+s+"");
      }
      
      //下载附件
	function downAccessory(id,name){
       	var fileId = id;
       	var fileName = name;
       	var url="<%=request.getContextPath()%>/files/fileList/downloadFile.jsp?fileId="+fileId+"&fileName="+fileName;
		window.location.replace(url);
	  }
</script>
</body>
</html>
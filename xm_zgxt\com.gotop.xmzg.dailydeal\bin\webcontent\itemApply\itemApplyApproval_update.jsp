<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-01-05 11:41:00
  - Description:
-->
<%@page import="com.eos.system.utility.StringUtil"%>
<head>
<%-- <%@include file="/coframe/tools/skins/common.jsp" %> --%>
<%@include file="/coframe/dict/common.jsp"%>
<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
</head>
<body>
  <div id="form1" style="padding-top:5px;">
	  <input class="nui-hidden" name="map.T_AID"/>
	  <input class="nui-hidden" id="rad2" name="map.APPLY_APPROVAL_RESULT"/>
	  <input class="nui-hidden" name="map.ITEM_NO"/>
	  <input class="nui-hidden" name="map.APPLY_NUM"/>
	  <input class="nui-hidden" name="map.ORGID"/>
	  <input class="nui-hidden" name="map.ITEM_NAME"/>
	  <input class="nui-hidden" name="map.TAX_PRICE"/>
	  <input class="nui-hidden" name="map.PRE_TAX_PRICE"/>
	  <input class="nui-hidden" name="map.TAX_RATE"/>
       <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
       
       <tr>
        <th class="nui-form-label"><label for="map.type$text">审批：</label></th>
        <td colspan="3" >  
           <input name="map.APPROVAL_RESULT2" type="radio" value="1" checked="checked"/>同意&nbsp;
	       <input name="map.APPROVAL_RESULT2" type="radio" value="0"/>不同意&nbsp;
        </td> 
      </tr>
     
      
     <tr>
        <th class="nui-form-label"><label for="map.type$text">审批理由：</label></th>
        <td colspan="3" >  
         <input id="map.APPLY_APPROVAL_REASON" name = "map.APPLY_APPROVAL_REASON"  class="nui-textarea nui-form-input" vtype="maxLength:100" />
          
        </td> 
      </tr>
         
    </table>
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save" onclick="onOk">提交</a>
	          <span style="display:inline-block;width:25px;"></span>
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  </div>

  <script type="text/javascript">
     var datas = [{id:1, text:"同意"},{id:0, text:"不同意"}];
     var datas2 = [{id:1, text:"是"},{id:0, text:"否"}];
  
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);
    
    //与父页面的方法2对应：页面间传输json数据
    function setFormData(data){
                   
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);

        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
        
	      //表单数据回显
             var json = infos.record;
             var form = new nui.Form("#form1");//将普通form转为nui的form
             form.setData(json);
         }
    }
    
    
    function onOk(){
      saveData();
    }
    
    //提交
    function saveData(){   
      form.validate();
      if(form.isValid()==false) return;
       //debugger; 
       
      var approval_result2 = $("input[name='map.APPROVAL_RESULT2']:checked").val();
      
      var approval_desc = nui.get("map.APPLY_APPROVAL_REASON").getValue();
      
      if(approval_result2 == null || approval_result2 =="")
	   {
		  return nui.alert("必须选择审批意见(同意或不同意)！");
	   }
	  /* if(approval_result2 == "0")
	   {
		  if(approval_desc ==null || approval_desc =="")
		  {
			  return nui.alert("必须输入审批理由！");
	      }
		    
	   }*/
      
      nui.get("rad2").setValue(approval_result2);
     
      
      var data = form.getData(false,true);
      var json = nui.encode(data);
      
      //判断审批状态(已审批)
        //if(checkisExist(json)== true)
        //{
         //nui.alert("已经审批的记录不能重复审批！");
         //return false;
        //}
        //debugger;
      
      $.ajax({
        url:"com.gotop.xmzg.dailydeal.itemApply.submitApproval.biz.ext",
        type:'POST',
        data:json,
        cache:false,
        contentType:'text/json',
        success:function(text){
            var returnJson = nui.decode(text);
			if(returnJson.exception == null && returnJson.iRtn == 1){
			
				nui.alert("审批成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
					 
						CloseWindow("saveSuccess");
					}
				});
			}else{
				nui.alert("审批失败", "系统提示", function(action){
					if(action == "ok" || action == "close"){
					 //refreshDictCache();
						CloseWindow("saveFailed");
					}
				});
			}
        }
      });
   
    }
    
    function onReset(){
      form.setData(obj);
      form.setChanged(false);
    } 
    
    function onCancel(){
      CloseWindow("cancel");
    }
    
     function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
    
    
    
    function checkisExist(map){
      var bool;
      $.ajax({
        url:"com.gotop.xmzg.errorAdjust.errorAdjust.checkIsExitApproval.biz.ext",
        type: 'POST',
        data:map,
        cache: false,
        async:false,
        contentType:'text/json',
        success:function(text){
          bool = text.bool;
        }
      });
      return bool;
    } 
    
  </script>
</body>
</html>
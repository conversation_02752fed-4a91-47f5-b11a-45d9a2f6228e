html, body 
{
    width:100%;
    height:100%;
    overflow:hidden;
    margin:0;
    padding:0;
    border:0;
    
}

.mini-desktop
{
    width:100%;
    height:100%;
    overflow:hidden;
    position:relative;
    z-index:1;
}
.mini-desktop-viewport
{
	background-color:#3CC8FA;
	position:relative;
    width:100%;
    overflow:hidden;
    height:100%;
}
.mini-desktop-startbutton
{
    position:absolute;left:2px;top:2px;width:38px;height:38px;
    background:url(images/start.png) no-repeat 50% 50%;
    cursor:hand;
    cursor:pointer;display:block;
}
a.mini-desktop-startbutton:hover
{
    background:url(images/starth.png) no-repeat 50% 50%;
}
.mini-desktop-taskbar-inner
{
    position:relative;overflow:hidden;   height:100%;
    margin-left:55px;
}
.mini-desktop-bars
{
    width:2000px;position:relative;
}
.mini-desktop-taskbar-showarrow .mini-desktop-taskbar-inner
{
    margin-right:80px;
}
.mini-desktop-bars-leftarrow, .mini-desktop-bars-rightarrow
{
    position:absolute;right:0;top:0;width:30px;height:40px;display:none;
    cursor:pointer;
    background:url(images/btn_right.png) no-repeat 50% 50%;
}
.mini-desktop-bars-leftarrow
{
    right:35px;
    background:url(images/btn_left.png) no-repeat 50% 50%;
}
.mini-desktop-taskbar-showarrow .mini-desktop-bars-leftarrow,
.mini-desktop-taskbar-showarrow .mini-desktop-bars-rightarrow
{
    display:block;
}

.mini-desktop-modules
{
    margin:20px;
}
.mini-desktop-modules-list
{
    float:left;
    padding-right:20px;
}
.mini-desktop-module
{
    width:75px;
    height:80px;
    margin-bottom:15px;
    cursor:pointer;
    overflow:hidden;
    background:none;
}
.mini-desktop-module-icon
{
    width:50px;
    height:45px;
    margin:auto;
    background:url(images/deskicon.png) no-repeat 50% 50%;
    margin-bottom:3px;
}
.mini-desktop-module-text
{
    text-align:center;
    font-family:Tahoma,Verdana,宋体;
    font-size:12px;    
    color:White;
    line-height:16px;
}


.mini-desktop-proxy
{
    position:absolute;
    overflow:hidden;
    z-index:100000000;
    background:gray;
    opacity: .2;-moz-opacity: .2;filter: alpha(opacity=20);    
}

.user {
	padding-left:20px;
	height:25px;
    background:url(images/member.png) no-repeat !important;
}


.search-container{
	background:transparent url(images/startmenu/search-bg.gif) repeat-x;
}


.person-picture{
	width: 75px;
	height: 75px;
	padding: 1px;
	border:0;
}
.searchbox{
	width:200px;
}
.searchbox .mini-buttonedit-icon
{
  background:url(images/startmenu/search.gif) no-repeat 50% 50%;
}

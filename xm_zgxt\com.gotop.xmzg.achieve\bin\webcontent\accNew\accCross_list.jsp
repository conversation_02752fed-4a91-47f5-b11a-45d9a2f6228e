<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - 业绩分润查询 -个金
-->
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>当前生效中的分润信息查询</title>
<style type="text/css">

</style>
</head>
<body style="margin:0px;width:100%;overflow:hidden;" >
	<div class="search-condition">
		<div class="list">
			<div id="filesForm">
		   		<table class="table" style="width:100%;">
		   			<tr>
						<th class="tit">业务条线：</th>
						<td>
							<div id="TIP_CODE" name="queryData.tip_code" class="nui-combobox" style="width:180px;" dataField="datas" textField="TIP_NAME" valueField="TIP_CODE" popupWidth="400"
						    	url="com.gotop.xmzg.achieve.configClaPro.tip_get.biz.ext" value="10" readOnly="true" multiSelect="false" allowInput="false" showNullItem="false" nullItemText="请选择" emptyText="请选择"
						    	onvaluechanged="onTipChanged">     
							    <div property="columns">
							        <div header="业务条线代码" field="TIP_CODE" width="60"></div>
							        <div header="业务条线名称" field="TIP_NAME" width="120"></div>
							    </div>
						    </div>
						</td>
						<th class="tit">指标：</th>
						<td>
							<div id="TI_CODE" name="queryData.ti_code" class="nui-combobox" style="width:180px;" dataField="datas" textField="TI_NAME" valueField="TI_CODE" popupWidth="400"
						    	url="com.gotop.xmzg.achieve.configClaPro.ti_get.biz.ext?code=10" value="10001" readOnly="true" multiSelect="false" allowInput="true" showNullItem="false" nullItemText="请选择" emptyText="请选择"
						    	onvaluechanged="onTiChanged">     
							    <div property="columns">
							        <div header="指标代码" field="TI_CODE" width="60"></div>
							        <div header="指标名称" field="TI_NAME" width="120"></div>
							    </div>
						    </div>
						</td>
			        	
						
						<th class="tit">指标细项：</th>
						<td>
							<div id="TID_CODE" name="queryData.tid_code" class="nui-combobox" style="width:180px;" dataField="datas" textField="TID_NAME" valueField="TID_CODE" popupWidth="400"
						    	url="com.gotop.xmzg.achieve.configClaPro.tid_get.biz.ext?code=10001" multiSelect="false" allowInput="true" showNullItem="false" nullItemText="全部" emptyText="全部">     
							    <div property="columns">
							        <div header="指标细项代码" field="TID_CODE" width="60"></div>
							        <div header="指标细项名称" field="TID_NAME" width="120"></div>
							    </div>
						    </div>
						</td>
			        </tr>
		      		<tr>
		      		    <th class="tit">产品名称：</th>
						<td>
							<input id="tac_busi_name" name="queryData.tac_busi_name" class="nui-textbox" style="width:180px;"/>
						</td>
						<th class="tit">类型：</th>
						<td>
							<input id="tac_rl_type" class="nui-dictcombobox" name="queryData.tac_rl_type"  emptyText="全部"
          					valueField="dictID" textField="dictName" dictTypeId="JF_FPLX" showNullItem="true" nullItemText="全部" style="width:180px;"/>
						</td>
						<th class="tit">指标/指标细项名称：</th>
						<td>
							<input id="tac_zb_name" name="queryData.tac_zb_name" class="nui-textbox" style="width:180px;"/>
						</td>
						
					</tr>
					<tr>
						<th class="tit">产品编号：</th>
						<td>
							<input id="tac_busi_no" name="queryData.tac_busi_no" class="nui-textbox" style="width:180px;"/>
						</td>	
			        	<th class="tit">分润客户经理名称：</th>
						<td>
							<input id="tac_cr_empname" name="queryData.tac_cr_empname" class="nui-textbox" style="width:180px;"/>
						</td>
						
						
						<td align="right">
							<a class="nui-button" iconCls="icon-search" onclick="searchData();">查询</a>
						</td>
						<td align="left">
							<a class="nui-button" iconCls="icon-reset"  onclick="clean()"/>重置</a>
							<a class="nui-button" iconCls="icon-add" onclick="excel">导出Excel</a>	
						</td>
						
			        </tr>
		    	</table>
		  	</div>
		</div>
	</div>
	<div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      	<table style="width:100%;">
        	<tr>
           		<td style="width:100%;">
           			<!-- <a class="nui-button" iconCls="icon-add" onclick="add">增加</a> -->
           			<a class="nui-button" iconCls="icon-collapse" onclick="gotoProfits('detail')">详情</a>
					<!-- <a class="nui-button" iconCls="icon-save" onclick="gotoProfits();">修改</a>  -->
           		</td>
        	</tr>
      	</table>
    </div>
  	<div class="nui-fit" id="fieldset1">
	 	<div id="grid"  dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" 
	  	url="com.gotop.xmzg.achieve.accNew.queryAccCrossList.biz.ext" sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" >
	    	<div property="columns">
	    		<div type="checkcolumn"></div>
	    		<div field="tad_id" class="nui-hidden" visible="false">TADID</div>
	    		<div field="tac_id" class="nui-hidden" visible="false">ID</div>
	    		<div field="tac_rl_type" headerAlign="center" renderer="filesType">类型</div>
	    		<div field="tac_zb_code" headerAlign="center">指标/指标细项代码</div>
		        <div field="tac_zb_name" headerAlign="center">指标/指标细项名称</div>
		        <div field="tac_busi_name" headerAlign="center">产品名称</div>
		        <div field="tac_busi_no" headerAlign="center">产品编号</div>
		        <!-- <div field="tac_busi_org" headerAlign="center" visible="false">产品所属机构代码</div> -->
		        <div field="tab_busi_orgname" headerAlign="center">产品所属机构名称</div>
		        <div field="tab_emp" headerAlign="center">归属客户经理编号</div>
		        <div field="tab_empname" headerAlign="center">归属客户经理</div>
		        <div field="tab_orgname" headerAlign="center">归属客户经理机构</div>
		        <div field="taa_status" headerAlign="center" visible="false">taa_status</div>
		        <div field="tac_status" headerAlign="center">是否有正在审核的分润信息</div>
		        <div field="tac_cr_emp" headerAlign="center">分润客户经理编号</div>
		        <div field="tac_cr_empname" headerAlign="center">分润客户经理</div>
		        <div field="tac_orgname" headerAlign="center">分润客户经理机构</div>
		        <div field="tac_rate" headerAlign="center">分润比例(%)</div>
		        <div field="tac_begin" headerAlign="center">开始时间</div>
		        <div field="tac_end" headerAlign="center">结束时间</div>
		    </div>
	 	</div>
  	</div>
</body>
</html>
<script type="text/javascript">
	nui.parse();
	var path = '<%=request.getContextPath() %>';
	var form = new nui.Form("filesForm");
	var grid = nui.get("grid");
	var data = form.getData(true,true);
	grid.load(data);
	
	function filesType(e){
	    return nui.getDictText("JF_FPLX", e.row.tac_rl_type);
	}
	
	function onyesno(e){
	    return nui.getDictText("JF_YES_NO", e.value);
	}
	
	function onsp(e){
	    return nui.getDictText("JF_SHZG", e.value);
	}
	
	function gslx(e){
	    return nui.getDictText("JF_GSLX", e.value);
	}
	
	function searchData(){	
        	var data = form.getData(true,true);
    		grid.load(data);
    }
    
    //重置查询信息
	function clean(){
	   	form.reset();  
    }
    
    //业绩账户分润
    function gotoProfits(type){
    	var rows = grid.getSelecteds();
        	if(rows.length>1 || rows.length==0){
    			nui.alert("请选择一条记录！");
    		}else{
	    		var row = grid.getSelected();
				checkForProfits(row,type,function(isCode){
				
			    nui.ajax({
					url: "com.gotop.xmzg.achieve.accNew.checkAccIsClaimOrBelong.biz.ext",
					type: "post",
					data: nui.encode({"tab_rl_type":row.tac_rl_type,"tab_zb_code":row.tac_zb_code,"tab_busi_no":row.tac_busi_no,"type":type}),
					contentType:'text/json',
					success: function (res) {
						if(res.exception == null && res.iRtn == 0 && res.tp_profits != null){
						var tp_profits = res.tp_profits;
						var tp_auth = res.tp_auth;
						if(tp_profits == "1"){
							//固定比例分配
							//先判断是否有配置固定比例的值
							var tp_pro = res.tp_pro;
							if(tp_pro == null){
								nui.alert("请先维护分润比例配置信息！");
							}else{
								nui.open({
			                		url: path+"/achieve/accNew/accProfitsFormGd.jsp",
					                title: "业绩账户固定比例分润",
					                iconCls: "icon-edit", 
					                width: 800, 
					                height: 620,
					                onload: function () { 
					                    var iframe = this.getIFrameEl();
						                var data = {
						                    tac_busi_no : row.tac_busi_no,
						                    tac_busi_name : row.tac_busi_name,
						                    tac_rl_type : row.tac_rl_type,
						                    tac_zb_code : row.tac_zb_code,
						                    tac_emp : row.tab_emp,
						                    tac_emp_name : row.tab_empname,
						                    tab_busi_org: row.tab_busi_org,
						                    tp_auth : tp_auth,
						                    tp_acrossorg:row.tp_acrossorg,
						                    /* tp_pro : tp_pro,
						                     */
						                    type:type,
						                    tab_begin : row.tac_begin,
				                    		tab_end : row.tac_end,
						                    TP_DATE : row.TP_DATE,
						                    TP_CYCLE : row.TP_CYCLE
						                 };
					                  	iframe.contentWindow.setFormData(data);
					                },
					                ondestroy: function (action) {
					                	if (action == "ok") {
					                		grid.reload();
					                	}else if (action == "cancel") {
					                		grid.reload();
					                	}
					                }
            					});
							}
						}else if(tp_profits == "2"){
							//动态比例分配
							nui.open({
	                		url: path+"/achieve/accNew/accProfitsFormDt.jsp",
			                title: "业绩账户动态比例分润",
			                iconCls: "icon-edit", 
			                width: 800, 
			                height: 620,
			                onload: function () {
			                    var iframe = this.getIFrameEl();
				                var data = {
				                    tac_busi_no : row.tac_busi_no,
				                    tac_busi_name : row.tac_busi_name,
				                    tac_rl_type : row.tac_rl_type,
				                    tac_zb_code : row.tac_zb_code,
				                    tac_emp : row.tab_emp,
				                    tac_emp_name : row.tab_empname,
				                    tab_busi_org: row.tab_busi_org,
				                    tp_pro:res.tp_pro,
				                    tp_auth : tp_auth,
				                    tp_acrossorg:row.tp_acrossorg,
				                    type:type,
				                    tab_begin : row.tac_begin,
				                    tab_end : row.tac_end,
				                    TP_DATE : row.TP_DATE,
				                    TP_CYCLE : row.TP_CYCLE
				                 };
			                  	iframe.contentWindow.setFormData(data);
			                },
			                ondestroy: function (action) {
			                	if (action == "ok") {
			                		grid.reload();
			                	}else if (action == "cancel") {
			                		grid.reload();
			                	}
			                }
            				});
						}
						}else if(res.iRtn == 1){
							nui.alert("请先分配账户才能进行分润！");
						}else if(res.iRtn == 2){
							nui.alert("未查询到分润配置表的数据！");
						}
                    }
				});
				});
    		}
    }
    
    
    function add(){
		nui.open({
    		url: path+"/achieve/accNew/accProfitsFormGdAdd.jsp",
            title: "分润新增",
            iconCls: "icon-edit", 
            width: 800, 
            height: 550,
            onload: function () {
                var iframe = this.getIFrameEl();
                var data = {type:"add"};
              	iframe.contentWindow.setFormData(data);
            },
            ondestroy: function (action) {
            	if (action == "ok") {
            		grid.reload();
            	}else if (action == "cancel") {
            		grid.reload();
            	}
            }
		});
    }
    
    function isCheck(obj,func){
    	var json = nui.encode({obj:obj});
		$.ajax({
			url:"com.gotop.xmzg.achieve.acc.checkIs.biz.ext",
			type:'POST',
			data:json,
			cache: false,
			contentType:'text/json',
			success:function(text){
				func(text.code);
			}
		});
	}
	
	//判断当前选中的账户数据是否存在审核中的分润记录
	function checkForProfits(obj,type,func){
		if(type == "detail") {
			func("1");
			return;
		}
		var jsonObj = {
			"TAB_RL_TYPE":obj.tac_rl_type,
			"TAB_ZB_CODE":obj.tac_zb_code,
			"TAB_BUSI_NO":obj.tac_busi_no,
			"tab_emp":obj.tab_emp,
			"type":type
		};
		var json = nui.encode({obj:jsonObj});
		$.ajax({
			url:"com.gotop.xmzg.achieve.accNew.check4Profits.biz.ext",
			type:'POST',
			data:json,
			cache: false,
			contentType:'text/json',
			success:function(text){
				if(text.code=="1"){
					func(text.code);
				}else{
					if(text.msg){
						nui.alert(text.msg);
					}else{
						nui.alert("系统异常");
					}
					return;
				}
			}
		});
	}
	
	function beforenodeselect(e) {

    	//组织机构不能选 
	    if (e.node.NODETYPE=="OrgOrganization") {
	        e.cancel = true;
	    }
	}
	var ti = nui.get("TI_CODE");
    var tid = nui.get("TID_CODE");
    function onTipChanged(e){
        ti.setValue("");
        tid.setValue("");
        var url = "com.gotop.xmzg.achieve.configClaPro.ti_get.biz.ext?code=" + e.value;
        ti.setUrl(url);
    }
    
    function onTiChanged(e){

        tid.setValue("");
        var url = "com.gotop.xmzg.achieve.configClaPro.tid_get.biz.ext?code=" + e.value;
        tid.setUrl(url);
    }
    
    
    
    function excel(){
		var form=new nui.Form("filesForm");
		form.validate();
	    if (form.isValid() == false) return;
		var data=form.getData(true, true);
		console.log(data);
		$.ajax({
			url: "com.gotop.xmzg.achieve.acc.checkIsSys.biz.ext",
            type: 'POST',
            async:false,
            cache: false,
            contentType:'text/json',
            success: function (text){
            	console.log(text.queryData.userId);
           	 	data.queryData.userId = text.queryData.userId;
           	 	data.queryData.orgid = text.queryData.orgid;
           	 	data.queryData.empcode = text.queryData.empcode;
           	 	data.queryData.roles = text.queryData.roles;
           	 	data.queryData.isSys = text.queryData.isSys;
			}
		});
		console.log(data);
		
		var fileName="业绩分润报表";
		var queryPath="com.gotop.xmzg.achieve.accProfits_new.queryCrossListGJ";
		var columns=grid.getBottomColumns();
		columns=columns.clone();
		for(var i=0;i<columns.length;i++){
			var column=columns[i];
			if(!column.field){
				columns.removeAt(i);
			}else{
				var c={header:column.header,field:column.field };
				columns[i]=c;
			}
		}
		var sheet = '业绩分润';
		
		columns=nui.encode(columns);
		data=nui.encode(data);
	    var url="<%=request.getContextPath()%>/achieve/report/exportExcelAc.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data+"&sheet="+sheet;
		window.location.replace(encodeURI(url));
		 
		 //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
	} 
</script>
*
{
font:9pt "宋体"; 
color:#00368E;
}
body{
scrollbar-highlight-color: white;
scrollbar-3dlight-color: #049ED0;						
scrollbar-face-color:#DDEAF5;
scrollbar-arrow-color:#394452;
scrollbar-shadow-color:#90D2E7;
scrollbar-darkshadow-color:#049ED0;
scrollbar-base-color:#049ED0;
scrollbar-track-color:#BAD2E8;
}
a:link{
text-decoration:underline;
color:#00414E;
}
a:hover
{
text-decoration:underline;
color:FE6C00;
}
a:visited
{
color:#588993;
text-decoration:underline;
}
.button
{
height:20px;
background:#F6FBFF;
border-top:1px #31C3F2 solid;
border-right:1px #006282 solid;
border-bottom:1px #006282 solid;
border-left:1px #31C3F2 solid;
padding:2 5;
}
.textbox
{
border:1px solid #016F87;
background:#fff;
}
hr  /* compart line's style */
{
color:#01B7DE;
background:#fff;
height:2px;
border-bottom:1px solid #fff;
width:98%;
}
/* work area's style*/
.workarea
{
background:#F3F9FA;
border:1px solid #75B5C3;
}
.workarea .workarea_title
{
background:#E3F0F2;
height:27px;
border-bottom:1px solid #75B5C3;
padding-left:18px;
}
h3 /*the title's font style of current page*/
{
display:inline;
font-weight:bold;
color:#FE6C00;
}
.EOS_panel_head  /* the block title */
{
background:#6AAFCC;
height:22px;
font-weight:bold;
border:none;
color:#000;
padding-left:20px;
}

/*the different table's style*/
.EOS_panel_body       /* Query table and Result table share this table style */
{
background:#F5F5F5;
}
.EOS_table
{
background:#F7FDFD;
border-collapse: collapse;
border:1px solid #AEC2CD;
}
.EOS_table tr td,.EOS_table tr th    /*  the common style of Result table's td and th */
{
border:1px solid #AEC2CD;
height:20px;
padding:0px 3px;
}
.EOS_table tr th
{
height:25px;
vertical-align:middle;
padding-top:3px;
background:#F0F9FE;
filter:progid:DXImageTransform.Microsoft.gradient(enabled='enabled',startColorstr=#D6F1FA, endColorstr=#F0F9FE);
text-align:center;
}
.EOS_table_row  /* the different background color between rows in result table */
{
background:#EAF3F8;
}
.EOS_table_selectRow /* the selected row's background color in result table */
{
background:#E6FFD1;
}

.command_sort_area /* the bottom of result table */
{
padding:2px 0 5px 8px;
background:#F5F5F5;
}
.command_sort_area h4 /*the pagination's style*/
{
float:right;
padding-right:8px;
margin-top:-18px;
}
.form_table
{
border-collapse: collapse;
border:1px solid #85D1E9;
background:#F7FDFD;
padding-left:5px;
}
.form_table td
{
height:25px;
border:1px solid #85D1E9;
}
.form_bottom /* the bottom of form table */
{
padding:10px 0 5px 8px;
background:#F5F5F5;
text-align:center;
}
.form_label
{
background:#EAF3F8;
}

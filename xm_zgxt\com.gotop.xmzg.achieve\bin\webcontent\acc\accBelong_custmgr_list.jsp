<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): wj
  - Date: 2022-02-16
  - Description: 客户经理  业绩账户查询
-->
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>业绩账户查询</title>
<style type="text/css">
.search-condition .table td {
    height: 0; 
    line-height: 0; 
}
</style>
</head>
<input id="JF_CHANGEORG_ZB" class="nui-dictcombobox" style="display: none;"
	valueField="dictID" textField="dictName" dictTypeId="JF_CHANGEORG_ZB"/>
<body style="margin:0px;width:100%;overflow:hidden;" >
	<div class="search-condition">
		<div class="list">
			<div id="filesForm">
		   		<table class="table" style="width:100%;">
		   			<tr>
						<th class="tit">业务条线：</th>
						<td>
							<div required="true"  id="TIP_CODE" name="queryData.tip_code" class="nui-combobox" style="width:150px;" dataField="datas" textField="TIP_NAME" valueField="TIP_CODE" popupWidth="400"
						    	url="com.gotop.xmzg.achieve.configClaPro.tip_get.biz.ext" multiSelect="false" allowInput="false" showNullItem="false" nullItemText="请选择" emptyText="请选择"
						    	onvaluechanged="onTipChanged">     
							    <div property="columns">
							        <div header="业务条线代码" field="TIP_CODE" width="60"></div>
							        <div header="业务条线名称" field="TIP_NAME" width="120"></div>
							    </div>
						    </div>
						</td>
						<th class="tit">指标：</th>
						<td>
							<div required="true" id="TI_CODE" name="queryData.ti_code" class="nui-combobox" style="width:150px;" dataField="datas" textField="TI_NAME" valueField="TI_CODE" popupWidth="400"
						    	multiSelect="false" allowInput="true" showNullItem="false" nullItemText="请选择" emptyText="请选择"
						    	onvaluechanged="onTiChanged">     
							    <div property="columns">
							        <div header="指标代码" field="TI_CODE" width="60"></div>
							        <div header="指标名称" field="TI_NAME" width="120"></div>
							    </div>
						    </div>
						</td>
			        	
						
						<th class="tit">指标细项：</th>
						<td>
							<div id="TID_CODE" name="queryData.tid_code" class="nui-combobox" style="width:150px;" dataField="datas" textField="TID_NAME" valueField="TID_CODE" popupWidth="400"
						    	multiSelect="false" allowInput="true" showNullItem="false" nullItemText="全部" emptyText="全部">     
							    <div property="columns">
							        <div header="指标细项代码" field="TID_CODE" width="60"></div>
							        <div header="指标细项名称" field="TID_NAME" width="120"></div>
							    </div>
						    </div>
						</td>
			        </tr>
		      		<tr>
		      			<th class="tit">产品所属机构：</th>
						<td >
							<input required="true"  id="tab_busi_org" name="queryData.tab_busi_org"  class="nui-buttonedit" style="width:150px;" allowInput="false" onbuttonclick="OrgonButtonEdit" />
						</td>
		      			<th class="tit">认领编号：</th>
						<td>
							<input id="tab_busi_no" name="queryData.tab_busi_no" class="nui-textbox" style="width:150px;"/>
						</td>
		      		    <th class="tit">认领名称：</th>
						<td>
							<input id="tab_busi_name" name="queryData.tab_busi_name" class="nui-textbox" style="width:150px;"/>
						</td>
						
						
					</tr>
					<tr>
						<th class="tit">认领类型：</th>
						<td>
							<input id="tab_rl_type" class="nui-dictcombobox" name="queryData.tab_rl_type"  emptyText="全部"
          					valueField="dictID" textField="dictName" dictTypeId="JF_FPLX" showNullItem="true" nullItemText="全部" style="width:150px;"/>
						</td>
						<th class="tit">指标名称：</th>
						<td>
							<input id="tab_zb_name" name="queryData.tab_zb_name" class="nui-textbox" style="width:150px;"/>
						</td>
			        	
						
						<th class="tit">是否已认领：</th>
						<td>
							
							<input id="tab_yesno" class="nui-dictcombobox" name="queryData.tab_bl_flag"  emptyText="全部"
          					valueField="dictID" textField="dictName" dictTypeId="JF_YES_NO" showNullItem="true" nullItemText="全部" style="width:150px;"/>
						</td>
			        </tr>
			        <tr>
			        	<th class="tit">客户经理名称：</th>
						<td>
							<input id="tab_empname" name="queryData.tab_empname" class="nui-textbox" style="width:150px;"/>
						</td>
						
						<th class="tit">是否可分润：</th>
						<td >
							<input id="is_pro" class="nui-dictcombobox" name="queryData.is_pro"  emptyText="全部"
          					valueField="dictID" textField="dictName" dictTypeId="JF_YES_NO" showNullItem="true" nullItemText="全部" style="width:150px;"/>
						</td>
						<th class="tit">认领方式：</th>
						<td >
							<input id="tc_type" class="nui-dictcombobox" name="queryData.tc_type"  emptyText="全部"
          					valueField="dictID" textField="dictName" dictTypeId="JF_RLFS" showNullItem="true" nullItemText="全部" style="width:150px;"/>
						</td>
						
			        </tr>
			        <tr>
			        	<th class="tit">客户关系类型：</th>
						<td >
							<input id="tab_cust_rela" class="nui-dictcombobox" name="queryData.tab_cust_rela"  emptyText="全部"
          					valueField="dictID" textField="dictName" dictTypeId="JF_GRCKKHGXLX" showNullItem="true" nullItemText="全部" style="width:150px;"/>
						</td>
			        	<td colspan="4" style="text-align: left;">
							<a class="nui-button" iconCls="icon-search" onclick="searchData();">查询</a>
							<a class="nui-button" iconCls="icon-reset"  onclick="clean()"/>重置</a>
							<a class="nui-button" iconCls="icon-download"  onclick="exportData();"/>导出认领关系</a>	
							<a class="nui-button" iconCls="icon-upload"  onclick="importData();"/>导入认领关系</a>
							<a class="nui-button" iconCls="icon-download"  onclick="exportFrData();"/>导出认领/分润信息</a> 
						</td>
			        </tr>
		    	</table>
		  	</div>
		</div>
	</div>
	<div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      	<table style="width:100%;">
        	<tr>
           		<td style="width:100%;">
           			<a class="nui-button" iconCls="icon-collapse" onclick="detail()">详情</a>
           			<a class="nui-button" iconCls="icon-add" onclick="gotoClaim();">认领</a> 
           			<a class="nui-button" iconCls="icon-add" onclick="gotoClaims();">批量认领</a>
           			<a class="nui-button" iconCls="icon-add" onclick="gotoClaimsYlj();">批量认定</a>
					<a class="nui-button" iconCls="icon-edit" onclick="gotoBelong();">移交</a> 
					<!-- <a class="nui-button" iconCls="icon-edit" onclick="gotoBelongs();">批量移交</a>  -->
					<a class="nui-button" iconCls="icon-no" onclick="gotoClaimTerNot();">认领终止</a>
					<a class="nui-button" iconCls="icon-no" onclick="gotoClaimTerNots();">批量认领终止</a>
					<a class="nui-button" iconCls="icon-save" onclick="gotoProfits();">分润</a> 
					<a class="nui-button" iconCls="icon-save" onclick="gotoProfitsTwo();">二次分润</a> 
					<a class="nui-button" iconCls="icon-edit" onclick="updateCustReal();">修改客户关系类型</a>
					<a class="nui-button" iconCls="icon-edit" onclick="updateCustSort();">修改客户类型</a>
					<a class="nui-button" iconCls="icon-edit" onclick="updateBusiOrg();">修改产品机构</a>
					<a class="nui-button" iconCls="icon-cancel" onclick="gotoClaimTer();">撤销</a>
					<a class="nui-button" iconCls="icon-cancel" onclick="gotoClaimTers();">批量撤销</a>
           		</td>
        	</tr>
      	</table>
    </div>
  	<div class="nui-fit" id="fieldset1">
	 	<div id="grid"  dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" 
	  	url="com.gotop.xmzg.achieve.acc.queryAccBelogToCustMgr.biz.ext" sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" >
	    	<div property="columns">
	    		<div type="checkcolumn"></div>
	    		<div field="TAB_ID" class="nui-hidden" visible="false">ID</div>
	    		<div field="TAB_RL_TYPE" headerAlign="center" renderer="filesType">认领类型</div>
	    		<div field="TAB_ZB_CODE" headerAlign="center">指标代码</div>
		        <div field="TAB_ZB_NAME" headerAlign="center">指标名称</div>
		        <div field="TC_PRODUCTNAME" headerAlign="center">产品类型</div>
		        <div field="TAB_BUSI_NO" headerAlign="center">认领编号</div>
		        <div field="TAB_BUSI_NAME" headerAlign="center">认领名称</div>
		        <div field="TAB_BUSI_ORGNAME" headerAlign="center">产品及所属机构</div>
		        <div field="TAB_BUSI_ORG" headerAlign="center" visible="false">产品及所属机构代码</div>
		        <div field="TAB_BL_FLAG" headerAlign="center" renderer="onyesno">是否已认领</div>
		        <div field="TAB_EMP" headerAlign="center">客户经理编号</div>
		        <div field="TAB_EMPNAME" headerAlign="center">客户经理</div>
		        <div field="TAB_ORGNAME" headerAlign="center">客户经理机构</div>
		        <div field="TAB_BEGIN" headerAlign="center">开始时间</div>
		        <div field="TAB_END" headerAlign="center">结束时间</div>
		        <div field="TAA_STATUS" headerAlign="center"renderer="onsp">认领审核状态</div>
		        <div field="TAA_STATUS_PRO" headerAlign="center"renderer="onsp">分润审核状态</div>
		        <div field="IS_PRO" headerAlign="center"renderer="onyesno">是否可分润</div>
		        
		        <div field="TAC_SY" visible="false">剩余分润比率（%）</div>
		        <div field="TAC_INFOS" visible="false">分润信息</div>
		        
		        <div field="TAB_CUST_RELA" headerAlign="center" renderer="JF_GRCKKHGXLX">客户关系类型</div>
		        <div field="DD_CUST_TYPE" headerAlign="center" renderer="JF_GRCKKHLX2">客户类型</div>
		        <div field="TC_TYPE" headerAlign="center" renderer="JF_RLFS">认领方式</div>
		        <!-- <div field="TAB_BUSI_DATE" headerAlign="center">产品日期</div>
		        <div field="TAB_BUSI_TYPE" headerAlign="center">产品类型</div>
		        <div field="TAB_BUSI_STATUS" headerAlign="center"renderer="JF_CPZT">产品状态</div>
		        <div field="TAB_BUSI_REMARK" headerAlign="center">产品说明</div>
		       
		        <div field="TAB_CUST_TYPE" headerAlign="center"renderer="JF_GRCKKHLX">客户类型</div> -->
		    </div>
	 	</div>
  	</div>
</body>
</html>
<script type="text/javascript">
	nui.parse();
	var path = '<%=request.getContextPath() %>';
	
	var form = new nui.Form("filesForm");
	var grid = nui.get("grid");
	var data = form.getData(true,true);
	//grid.load(data);
	nui.ajax({
        url: "com.gotop.xmzg.achieve.acc.getEmp.biz.ext",
        type: 'POST',
        data:nui.encode({map:{type:"org"}}),
        cache: false,
        contentType:'text/json',
        success: function (text) {
        	nui.get("tab_busi_org").setValue(text.orgcode);
	        nui.get("tab_busi_org").setText(text.orgname);
	    }
   });
	       
	function filesType(e){
	    return nui.getDictText("JF_FPLX", e.row.TAB_RL_TYPE);
	}
	
	function onyesno(e){
	    return nui.getDictText("JF_YES_NO", e.value);
	}
	
	function onsp(e){
	    return nui.getDictText("JF_SHZG", e.value);
	}
	
	function gslx(e){
	    return nui.getDictText("JF_GSLX", e.value);
	}
	
	function JF_GRCKKHGXLX(e){
	    return nui.getDictText("JF_GRCKKHGXLX", e.value);
	}
	
	/* function JF_GRCKKHLX(e){
	    return nui.getDictText("JF_GRCKKHLX", e.value);
	} */
	
	function JF_GRCKKHLX2(e){
	    return nui.getDictText("JF_GRCKKHLX2", e.value);
	}
	
	function JF_CPZT(e){
	    return nui.getDictText("JF_CPZT", e.value);
	}
	function JF_RLFS(e){
	    return nui.getDictText("JF_RLFS", e.value);
	}
	function searchData(){	
		form.validate();
        if (form.isValid() == false) return;
    	var data = form.getData(true,true);
		grid.load(data);
    }
    //认领
    function gotoClaim(){
       var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选择一条记录进行认领！");
		}else{
			if(rows[0].TC_INDIC_CODE == null){
       	   		nui.alert("该记录未配置认领信息！", "系统提示");
       	   		return ;
       	   }
       	   
       	   if(rows[0].TC_TYPE != "1"){
       	   		nui.alert("该记录认领方式不为手工认领，无法操作！", "系统提示");
       	   		return ;
       	   }
			if(rows[0].TAB_EMP != null && rows[0].TAB_EMP != ''){
       	   		nui.alert("该记录已认领！", "系统提示");
       	   		return ;
       	   }
       	   
			if(rows[0].TAA_STATUS == 0){
				nui.alert("该记录正在审核中！");
				return ;
			}
			
	       nui.open({
	          url:"<%=request.getContextPath() %>/achieve/acc/accBelong_distr.jsp",
	          title:'认领',
	          width:800,
          	  height:680,
	          onload:function(){
	          	var iframe = this.getIFrameEl();
				var data = {pageType:"claim",record:{map:rows[0]}};
				iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }else if(action == "cancel"){
	             	grid.reload();
	             }
	          }
	       });
       }
       
    }
    
    //批量认领
    function gotoClaims(){
    	var rows = grid.getSelecteds();
		if(rows.length == 0){
			nui.alert("请至少选择一条记录进行认领！");
			return;
		}
		var isZbCode1003 = "0";
		var isCode = null;
		for(var i = 0 ; i < rows.length ; i++){
			
			if(isCode != null && isCode != rows[i].TAB_BUSI_ORG){
				nui.alert("请您选择产品及所属机构相同的指标进行认领", "系统提示");
				return ;
			}
			isCode = rows[i].TAB_BUSI_ORG;
			
			var t1003=false;
			var zbCode = rows[i].TAB_ZB_CODE;
		   if(zbCode == '10003' || zbCode == '10003001'|| zbCode == '10003002'||zbCode == '10003003'){
		   		isZbCode1003 = "1";
		   		t1003 = true;
		   }
		   
		   if(isZbCode1003 == "1" && !t1003){
		   		nui.alert("储蓄存款需单独认领,批量认领指标需全为储蓄存款！", "系统提示");
       	   		return ;
		   }
		   
		   
           if(rows[i].TC_INDIC_CODE == null){
       	   		nui.alert("该记录未配置认领信息！", "系统提示");
       	   		return ;
       	   }
       	   if(rows[i].TC_TYPE != "1"){
       	   		nui.alert("该记录认领方式不为手工认领，无法操作！", "系统提示");
       	   		return ;
       	   }
       	   
  	   	   if(rows[i].TAB_EMP != null && rows[i].TAB_EMP != ''){
       	   		nui.alert("该记录已认领！", "系统提示");
       	   		return ;
       	   }
   	   		if(rows[i].TAA_STATUS == 0){
				nui.alert("该记录正在审核中！");
				return ;
			}
		}
		//rows[0].TC_ACROSSORG
    	nui.ajax({
			url: "com.gotop.xmzg.achieve.acc.positionIs.biz.ext",
			type: "post",
			data: nui.encode({}),
			contentType:'text/json',
			success: function (text) {
				if(text.code==1){
					nui.open({
		                url:"<%=request.getContextPath()%>/achieve/acc/accEmptree.jsp?type=1&isrole=1&orgcode="+rows[0].TAB_BUSI_ORG,
		                title: "选择人员",
		                width: 600,
		                height: 400,
		                onload:function(){
		                	var frame = this.getIFrameEl();
		                	frame.contentWindow.setTreeCheck();
		                },
		                ondestroy: function (action) {
		                    if (action == "ok") {
		                        var iframe = this.getIFrameEl();
		                        var data = iframe.contentWindow.GetData();
		                        data = nui.clone(data);
		                        if (data) {
		                            for(var i = 0 ; i < rows.length ; i++){
										rows[i].NEW_TAB_EMP = data.ORGID;
							        }
							        gotoClaims2(rows,isZbCode1003);
		                        }
		                    }
		                }
		            });
				}else{
					gotoClaims2(rows,isZbCode1003);
				}
			}	
		});					
    }
    function  updateCustReal(){
    	var rows = grid.getSelecteds();
		if(rows.length == 0){
			nui.alert("请至少选择一条记录进行修改客户关系类型！");
			return;
		}
		
		for(var i = 0 ; i < rows.length ; i++){
		   var zbCode = rows[i].TAB_ZB_CODE;
		   if(zbCode != '10003' && zbCode != '10003001' && zbCode != '10003002' && zbCode != '10003003'){
		   		nui.alert("只能修改指标为储蓄存款的记录！", "系统提示");
       	   		return ;
		   }
		   if(rows[i].TAA_STATUS == 0){
				nui.alert("该记录正在审核中！");
				return ;
		   }
		   rows[i].NEW_TAB_EMP = rows[i].TAB_EMP;
		}
		nui.open({
            url:"<%=request.getContextPath()%>/achieve/acc/accBelog_detail_add.jsp",
            title: "选择客户关系类型",
            width: 400,
            height: 150,
            onload:function(){
            	var frame = this.getIFrameEl();
            	var data = {pageType:"updates",objs:rows};
            	frame.contentWindow.setFormData(data);
            },
            ondestroy: function (action) {
                grid.reload();
            }
        });
		
    }
    //修改客户分类
    function  updateCustSort(){
    	var rows = grid.getSelecteds();
    	if(rows.length>1 || rows.length==0){
			nui.alert("请选择一条记录进行客户类型修改！");
		}else{
    		var row = grid.getSelected();
			isCheckForProfits(row,function(isCode){
			if(isCode == 1){
				nui.alert("该记录正在审核中！");
				return ;
			}
			
			var zbCode = row.TAB_ZB_CODE;
	   		//公司贷款利差收入 11003002，贸易融资表外12006001 票据承兑12007001需要先维护客户类型
		    /* if(zbCode != '11003002' && zbCode != '12006001' && zbCode != '12007001' ){
			   	nui.alert("只能修改指标为公司贷款利差收入、贸易融资表外、票据承兑的记录！", "系统提示");
	       	   	return ;
		    } */
		    //公司贷款利差收入11003002、贸易融资表外12006001、票据承兑12007001、供应链12002、贸易融资表内12005、二级福费廷12001
		    if( zbCode != '12006001' && zbCode != '12007001'  && zbCode.substring(0,5) != '12002' &&  zbCode.substring(0,5) != '12005' && zbCode.substring(0,5) != '12001'){
			   	nui.alert("只能修改指标为贸易融资表外、票据承兑、供应链、贸易融资表内、二级福费廷的记录！", "系统提示");
	       	   	return ;
		    }
			
		    nui.ajax({
				url: "com.gotop.xmzg.achieve.acc.checkAccIsClaimOrBelong.biz.ext",
				type: "post",
				data: nui.encode({"tab_rl_type":row.TAB_RL_TYPE,"tab_zb_code":row.TAB_ZB_CODE,"tab_busi_no":row.TAB_BUSI_NO}),
				contentType:'text/json',
				success: function (res) {
					if(res.exception == null && res.iRtn == 0 && res.tp_profits != null){
					var tp_profits = res.tp_profits;
					var tp_auth = res.tp_auth;
					if(tp_profits == "1"){
						//固定比例分配
						//先判断是否有配置固定比例的值
						var tp_pro = res.tp_pro;
						if(tp_pro == null){
							nui.alert("请先维护分润比例配置信息！");
						}else{
							nui.open({
		                		url: path+"/achieve/acc/accSortGd.jsp",
				                title: "业绩账户固定比例分润",
				                iconCls: "icon-edit", 
				                width: 800, 
				                height: 620,
				                onload: function () {
				                    var iframe = this.getIFrameEl();
					                var data = {
					                    tac_busi_no : row.TAB_BUSI_NO,
					                    tac_busi_name : row.TAB_BUSI_NAME,
					                    tac_rl_type : row.TAB_RL_TYPE,
					                    tac_zb_code : row.TAB_ZB_CODE,
					                    tac_emp : row.TAB_EMP,
					                    tac_emp_name : row.TAB_EMPNAME,
					                    tp_pro : tp_pro,
					                    tp_auth : tp_auth,
					                    tp_acrossorg : res.tp_acrossorg,
					                    tab_busi_org : row.TAB_BUSI_ORG,
					                    tab_begin : row.TAB_BEGIN,
			                    		tab_end : row.TAB_END,
					                    TP_DATE : row.TP_DATE,
					                    TP_CYCLE : row.TP_CYCLE,
					                   	DD_CUST_TYPE : row.DD_CUST_TYPE,
					                    CUST_AMT : row.CUST_AMT,
					                    tab_id : row.TAB_ID,
					            		taa_status_pro:row.TAA_STATUS_PRO
					                 };
				                  	iframe.contentWindow.setFormData(data);
				                },
				                ondestroy: function (action) {
				                	if (action == "ok") {
				                		grid.reload();
				                	}else if (action == "cancel") {
				                		grid.reload();
				                	}
				                }
        					});
						}
					}else if(tp_profits == "2"){
						//动态比例分配
						nui.open({
                		url: path+"/achieve/acc/accSortDt.jsp",
		                title: "业绩账户动态比例分润",
		                iconCls: "icon-edit", 
		                width: 800, 
		                height: 620,
		                onload: function () {
		                    var iframe = this.getIFrameEl();
			                var data = {
			                    tac_busi_no : row.TAB_BUSI_NO,
			                    tac_busi_name : row.TAB_BUSI_NAME,
			                    tac_rl_type : row.TAB_RL_TYPE,
			                    tac_zb_code : row.TAB_ZB_CODE,
			                    tac_emp : row.TAB_EMP,
			                    tac_emp_name : row.TAB_EMPNAME,
			                    tp_auth : tp_auth,
			                    tp_acrossorg : res.tp_acrossorg,
			                    tp_pro:res.tp_pro,
			                    tab_busi_org : row.TAB_BUSI_ORG,
			                    tab_begin : row.TAB_BEGIN,
			                    tab_end : row.TAB_END,
			                    TP_DATE : row.TP_DATE,
			                    TP_CYCLE : row.TP_CYCLE,
			                    DD_CUST_TYPE : row.DD_CUST_TYPE,
					            CUST_AMT : row.CUST_AMT,
					            tab_id : row.TAB_ID,
					            taa_status_pro:row.TAA_STATUS_PRO
			                 };
		                  	iframe.contentWindow.setFormData(data);
		                },
		                ondestroy: function (action) {
		                	if (action == "ok") {
		                		grid.reload();
		                	}else if (action == "cancel") {
		                		grid.reload();
		                	}
		                }
        			});
					}
					}else if(res.iRtn == 1){
						nui.alert("请先分配账户才能进行客户类型修改！");
					}else if(res.iRtn == 2){
						nui.alert("未查询到分润配置表的数据！");
					}
                }
			});
			});
		}
    }
    function gotoClaims2(rows,isZbCode1003){
    	if(isZbCode1003 == "1"){
    		nui.open({
                url:"<%=request.getContextPath()%>/achieve/acc/accBelog_detail_add.jsp",
                title: "选择客户关系类型",
                width: 400,
                height: 200,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {pageType:"batch",objs:rows};
                	frame.contentWindow.setFormData(data);
                },
                ondestroy: function (action) {
                    grid.reload();
                }
            });
    		return;
    	}
    
    	var load= nui.loading("正在保存请稍侯...","温馨提示 =^_^=");
       	nui.ajax({
			url: "com.gotop.xmzg.achieve.acc.accBelong_add_claims.biz.ext",
			type: "post",
			data: nui.encode({objs:rows}),
			contentType:'text/json',
			success: function (text) {
				nui.hideMessageBox(load);  //隐藏遮罩层
	        	   var code = text.code;
	        	   var msg = text.msg;
	        	   if(code != "1"){
	        		  	nui.alert(msg, "系统提示", function(action){
							if(action == "ok" || action == "close"){
								//CloseWindow("saveFailed");
							}
						});
		           }else{
	          			nui.alert(msg, "系统提示", function(action){
	          				if(action == "ok" || action == "close"){
								grid.reload();
							}
						});
		          }
			}
		});
    }
    //认领撤销
    function gotoClaimTer(){
       var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选择一条记录进行撤销！");
		}else{
			nui.ajax({
			url: "com.gotop.xmzg.achieve.acc.positionIsAdmin.biz.ext",
			type: "post",
			data: nui.encode({}),
			contentType:'text/json',
			success: function (text) {
				if(text.code!=1){
					nui.alert("您没有权限进行撤销！");
					return;
				}
				
				if(rows[0].TC_TYPE != "1"){
	       	   		nui.alert("该记录认领方式不为手工认领，无法操作！", "系统提示");
	       	   		return ;
	       	   }
	       	   
			   /* if(rows[0].TAB_EMP == null || rows[0].TAB_EMP == ''){
	       	   		nui.alert("选择记录没有认领记录！无法撤销", "系统提示");
	       	   		return ;
	       	   } */
	       	   if(rows[0].TAA_STATUS == 0){
					nui.alert("该记录正在审核中！");
					return ;
				}
		       nui.open({
		          url:"<%=request.getContextPath() %>/achieve/acc/accBelong_distr.jsp",
		          title:'撤销',
		          width:800,
	          	  height:680,
		          onload:function(){
		          	var iframe = this.getIFrameEl();
					var data = {pageType:"claim_ter",record:{map:rows[0]}};
					iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		          },
		          ondestroy:function(action){
		             if(action=="saveSuccess"){
		                grid.reload();
		             }else if(action == "cancel"){
		             	grid.reload();
		             }
		          }
		       });
	       }
	       });
       }
    }
    
    //批量撤销
    function gotoClaimTers(){
       var rows = grid.getSelecteds();
		if(rows.length > 0){
			nui.ajax({
			url: "com.gotop.xmzg.achieve.acc.positionIsAdmin.biz.ext",
			type: "post",
			data: nui.encode({}),
			contentType:'text/json',
			success: function (text) {
				if(text.code!=1){
					nui.alert("您没有权限进行撤销！");
					return;
				}
	           for(var i = 0 ; i < rows.length ; i++){
	           
	           		if(rows[i].TC_TYPE != "1"){
       	   				nui.alert("该记录认领方式不为手工认领，无法操作！", "系统提示");
		       	   		return ;
		       	   }
	      	   	  /* if(rows[i].TAB_EMP == null || rows[i].TAB_EMP == ''){
       	   				nui.alert("选择记录没有认领记录！无法撤销", "系统提示");
       	   				return ;
	       	   		} */
	       	   		if(rows[i].TAA_STATUS == 0){
						nui.alert("该记录正在审核中！");
						return ;
					}
					rows[i].NEW_TAB_CUST_RELA = rows[i].TAB_CUST_RELA;
	      	   }
       	   var load= nui.loading("正在保存请稍侯...","温馨提示 =^_^=");
	       nui.ajax({
				url: "com.gotop.xmzg.achieve.acc.accBelong_add_claim_ters.biz.ext",
				type: "post",
				data: nui.encode({objs:rows}),
				contentType:'text/json',
				success: function (text) {
					nui.hideMessageBox(load);  //隐藏遮罩层
		        	   var code = text.code;
		        	   var msg = text.msg;
		        	   if(code != "1"){
		        		  	nui.alert(msg, "系统提示", function(action){
								if(action == "ok" || action == "close"){
									//CloseWindow("saveFailed");
								}
							});
			           }else{
		          			nui.alert(msg, "系统提示", function(action){
		          				if(action == "ok" || action == "close"){
									grid.reload();
								}
							});
			          }
				}
			});
			}
			});
       }else{
           nui.alert("请至少选择一条记录进行撤销！");
       }  
    }
    
    //认领终止
    function gotoClaimTerNot(){
       var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选择一条记录进行认领终止！");
		}else{
				
				if(rows[0].TC_TYPE != "1"){
	       	   		nui.alert("该记录认领方式不为手工认领，无法操作！", "系统提示");
	       	   		return ;
	       	   }
	       	   
				if(rows[0].TAB_EMP == null || rows[0].TAB_EMP == ''){
	       	   		nui.alert("选择记录没有认领记录！无法认领终止", "系统提示");
	       	   		return ;
	       	   }
	       	   if(rows[0].TAA_STATUS == 0){
					nui.alert("该记录正在审核中！");
					return ;
				}
		       nui.open({
		          url:"<%=request.getContextPath() %>/achieve/acc/accBelong_distr.jsp",
		          title:'终止',
		          width:800,
	          	  height:680,
		          onload:function(){
		          	var iframe = this.getIFrameEl();
					var data = {pageType:"claim_terNot",record:{map:rows[0]}};
					iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		          },
		          ondestroy:function(action){
		             if(action=="saveSuccess"){
		                grid.reload();
		             }else if(action == "cancel"){
		             	grid.reload();
		             }
		          }
		       });
       }
    }
    
    //批量终止
    function gotoClaimTerNots(){
       var rows = grid.getSelecteds();
		if(rows.length > 0){
			   
			   var isCode = rows[0].TAB_ZB_CODE;
			   nui.open({
		          url:"<%=request.getContextPath() %>/achieve/acc/accBelog_detail_add.jsp",
		          title:'选择终止时间',
		          width: 500,
            	  height: 260,
		          onload:function(){
		          	var iframe = this.getIFrameEl();
					var data = {pageType:"claim_terNot",objs:rows};
					iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		          },
		          ondestroy: function (action) {
		                if (action == "ok") {
		                    var iframe = this.getIFrameEl();
		                    var data = iframe.contentWindow.GetData();
		                    data = nui.clone(data);
		                    console.log(data);
		                    if (data) {
		                    
		             for(var i = 0 ; i < rows.length ; i++){
		           		if(rows[i].TC_TYPE != "1"){
	       	   				nui.alert("该记录认领方式不为手工认领，无法操作！", "系统提示");
			       	   		return ;
			       	    }
		      	   	    if(rows[i].TAB_EMP == null || rows[i].TAB_EMP == ''){
	       	   				nui.alert("选择记录没有认领记录！无法认领终止", "系统提示");
	       	   				return ;
		       	   		}
		       	   		if(rows[i].TAA_STATUS == 0){
							nui.alert("该记录正在审核中！");
							return ;
						}
						
						if(isCode != rows[i].TAB_ZB_CODE){
							nui.alert("请选择同一指标的终止记录");
							return ;
						}
						rows[i].NEW_TAB_BEGIN = data;
						rows[i].NEW_TAB_CUST_RELA = rows[i].TAB_CUST_RELA;
		      	   }
		       	   var load= nui.loading("正在保存请稍侯...","温馨提示 =^_^=");
			       nui.ajax({
						url: "com.gotop.xmzg.achieve.acc.accBelong_add_claim_terNots.biz.ext",
						type: "post",
						data: nui.encode({objs:rows}),
						contentType:'text/json',
						success: function (text) {
							nui.hideMessageBox(load);  //隐藏遮罩层
				        	   var code = text.code;
				        	   var msg = text.msg;
				        	   if(code != "1"){
				        		  	nui.alert(msg, "系统提示", function(action){
										if(action == "ok" || action == "close"){
											//CloseWindow("saveFailed");
										}
									});
					           }else{
				          			nui.alert(msg, "系统提示", function(action){
				          				if(action == "ok" || action == "close"){
											grid.reload();
										}
									});
					          }
						}
					});
				}}
	          }
	       });
			   
       }else{
           nui.alert("请至少选择一条记录进行认领终止！");
       }  
    }
    
    //移交
    function gotoBelong(){
      	var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选择一条记录进行移交！");
		}else{
			if(rows[0].TC_TYPE != "1"){
       	   		nui.alert("该记录认领方式不为手工认领，无法操作！", "系统提示");
       	   		return ;
       	   }
       	   
			if(rows[0].TAB_EMP == null || rows[0].TAB_EMP == ''){
       	   		nui.alert("选择记录没有认领记录！无法移交", "系统提示");
       	   		return ;
       	   }
       	   
       		if(rows[0].TAA_STATUS == 0){
				nui.alert("该记录正在审核中！");
				return ;
			}
			
	       nui.open({
	          url:"<%=request.getContextPath() %>/achieve/acc/accBelong_distr.jsp",
	          title:'移交',
	          width:800,
          	  height:680,
	          onload:function(){
	          	var iframe = this.getIFrameEl();
				var data = {pageType:"over",record:{map:rows[0]}};
				iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }else if(action == "cancel"){
	             	grid.reload();
	             }
	          }
	       });
       }
    }
	
    function detail(){
       var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选择一条记录！");
		}else{
	       nui.open({
	          url:"<%=request.getContextPath() %>/achieve/acc/accBelong_distr.jsp",
	          title:'详情',
	          width:800,
          	  height:680,
	          onload:function(){
	          	var iframe = this.getIFrameEl();
				var data = {pageType:"detail",record:{map:rows[0]}};
				iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          }
	       });
       }
       
    }
    //重置查询信息
	function clean(){
	   	form.reset();  
    }
    
    //业绩账户分润
    function gotoProfits(){
    	var rows = grid.getSelecteds();
        	if(rows.length>1 || rows.length==0){
    			nui.alert("请选择一条记录进行分润！");
    		}else{
	    		var row = grid.getSelected();
				isCheckForProfits(row,function(isCode){
				if(isCode == 1){
					nui.alert("该记录正在审核中！");
					return ;
				}
				
				var zbCode = row.TAB_ZB_CODE;
		   		//公司贷款利差收入 11003002，贸易融资表外12006001 票据承兑12007001需要先维护客户类型
			    if( zbCode == '12006001' || zbCode == '12007001' ){
			    	nui.alert("指标为贸易融资表外、票据承兑的记录在修改客户类型进行分润！", "系统提示");
		       	   	return ;
			    } 
				
			    nui.ajax({
					url: "com.gotop.xmzg.achieve.acc.checkAccIsClaimOrBelong.biz.ext",
					type: "post",
					data: nui.encode({"tab_rl_type":row.TAB_RL_TYPE,"tab_zb_code":row.TAB_ZB_CODE,"tab_busi_no":row.TAB_BUSI_NO}),
					contentType:'text/json',
					success: function (res) {
						if(res.exception == null && res.iRtn == 0 && res.tp_profits != null){
						var tp_profits = res.tp_profits;
						var tp_auth = res.tp_auth;
						//console.log(res);
						if(tp_profits == "1"){
							//固定比例分配
							//先判断是否有配置固定比例的值
								var tp_pro = res.tp_pro;
								if(tp_pro == null){
									nui.alert("请先维护分润比例配置信息！");
								}else{
									nui.open({
				                		url: path+"/achieve/acc/accProfitsFormGd.jsp",
						                title: "业绩账户固定比例分润",
						                iconCls: "icon-edit", 
						                width: 800, 
						                height: 620,
						                onload: function () {
						                    var iframe = this.getIFrameEl();
							                var data = {
							                    tac_busi_no : row.TAB_BUSI_NO,
							                    tac_busi_name : row.TAB_BUSI_NAME,
							                    tac_rl_type : row.TAB_RL_TYPE,
							                    tac_zb_code : row.TAB_ZB_CODE,
							                    tac_emp : row.TAB_EMP,
							                    tac_emp_name : row.TAB_EMPNAME,
							                    tp_pro : tp_pro,
							                    tp_auth : tp_auth,
							                    tp_acrossorg : res.tp_acrossorg,
							                    tab_busi_org : row.TAB_BUSI_ORG,
							                    tab_begin : row.TAB_BEGIN,
					                    		tab_end : row.TAB_END,
							                    TP_DATE : row.TP_DATE,
							                    TP_CYCLE : row.TP_CYCLE,
							                    TAB_CUST_TYPE : row.TAB_CUST_TYPE
							                 };
						                  	iframe.contentWindow.setFormData(data);
						                },
						                ondestroy: function (action) {
						                	if (action == "ok") {
						                		grid.reload();
						                	}else if (action == "cancel") {
						                		grid.reload();
						                	}
						                }
	            					});
								}
							}else if(tp_profits == "2"){
								//动态比例分配
								nui.open({
		                		url: path+"/achieve/acc/accProfitsFormDt.jsp",
				                title: "业绩账户动态比例分润",
				                iconCls: "icon-edit", 
				                width: 800, 
				                height: 620,
				                onload: function () {
				                    var iframe = this.getIFrameEl();
					                var data = {
					                    tac_busi_no : row.TAB_BUSI_NO,
					                    tac_busi_name : row.TAB_BUSI_NAME,
					                    tac_rl_type : row.TAB_RL_TYPE,
					                    tac_zb_code : row.TAB_ZB_CODE,
					                    tac_emp : row.TAB_EMP,
					                    tac_emp_name : row.TAB_EMPNAME,
					                    tp_auth : tp_auth,
					                    tp_acrossorg : res.tp_acrossorg,
					                    tp_pro:res.tp_pro,
					                    tab_busi_org : row.TAB_BUSI_ORG,
					                    tab_begin : row.TAB_BEGIN,
					                    tab_end : row.TAB_END,
					                    TP_DATE : row.TP_DATE,
					                    TP_CYCLE : row.TP_CYCLE,
					                    TAB_CUST_TYPE : row.TAB_CUST_TYPE
					                 };
				                  	iframe.contentWindow.setFormData(data);
				                },
				                ondestroy: function (action) {
				                	if (action == "ok") {
				                		grid.reload();
				                	}else if (action == "cancel") {
				                		grid.reload();
				                	}
				                }
	            				});
							}
						}else if(res.iRtn == 1){
							nui.alert("请先分配账户才能进行分润！");
						}else if(res.iRtn == 2){
							nui.alert("未查询到分润配置表的数据！");
						}
                    }
				});
				});
    		}
    }
    
    //业绩账户二次分润
    function gotoProfitsTwo(){
    		var rows = grid.getSelecteds();
        	if(rows.length>1 || rows.length==0){
    			nui.alert("请选择一条记录进行二次分润！");
    		}else{
	    		var row = grid.getSelected();	    		
				isFistCheck(row,function(isCode){
					if(isCode == 0){
						nui.alert("该记录未完成一次分润不能进行二次分润！");
						return ;
					}

				
					var tabZbCode = row.TAB_ZB_CODE;//指标编号
					if(tabZbCode=="12006001"||tabZbCode=="12007001"){
					
					}else{
						nui.alert("只有贸易融资（表外）和票据承兑指标才能继续二次分润！");
						return ;
					}
					
					var kh_type = row.DD_CUST_TYPE;//客户类型
					
					if(kh_type=="11"){
						
					}else{
						nui.alert("只有客户类型为【经办维护客户】的才能继续二次分润！");
						return;
					}
					

					nui.ajax({
						url: "com.gotop.xmzg.achieve.acc.checkAccIsClaimOrBelong.biz.ext",
						type: "post",
						data: nui.encode({"tab_rl_type":row.TAB_RL_TYPE,"tab_zb_code":row.TAB_ZB_CODE,"tab_busi_no":row.TAB_BUSI_NO}),
						contentType:'text/json',
						success: function (res) {
							if(res.exception == null && res.iRtn == 0 && res.tp_profits != null){
							var tp_two_profits = res.tp_two_profits;
							var tp_auth = res.tp_two_auth;
							if(tp_two_profits == "1"){
								//固定比例分配
								//先判断是否有配置固定比例的值
								var tp_pro = res.tp_two_pro;
								if(tp_pro == null){
									nui.alert("请先维护分润比例配置信息！");
								}else{
									nui.open({
				                		url: path+"/achieve/acc/accProfitsTwo.jsp",
						                title: "业绩账户二次分润",
						                iconCls: "icon-edit", 
						                width: 800, 
						                height: 620,
						                onload: function () {
						                    var iframe = this.getIFrameEl();
							                var data = {
							                    tac_busi_no : row.TAB_BUSI_NO,
							                    tac_busi_name : row.TAB_BUSI_NAME,
							                    tac_rl_type : row.TAB_RL_TYPE,
							                    tac_zb_code : row.TAB_ZB_CODE,
							                    tac_emp : row.TAB_EMP,
							                    tac_emp_name : row.TAB_EMPNAME,
							                    tp_pro : tp_pro,
							                    tp_auth : tp_auth,
							                    tp_acrossorg : res.tp_acrossorg,
							                    tab_busi_org : row.TAB_BUSI_ORG,
							                    tab_begin : row.TAB_BEGIN,
					                    		tab_end : row.TAB_END,
							                    TP_DATE : row.TP_DATE,
							                    TP_CYCLE : row.TP_CYCLE,
							                    DD_CUST_TYPE : row.DD_CUST_TYPE,
						            			CUST_AMT : row.CUST_AMT,
						            			tab_id : row.TAB_ID
							                 };
						                  	iframe.contentWindow.setFormData(data);
						                },
						                ondestroy: function (action) {
						                	if (action == "ok") {
						                		grid.reload();
						                	}else if (action == "cancel") {
						                		grid.reload();
						                	}
						                }
	            					});
								}
								}
							}else if(res.iRtn == 1){
								nui.alert("请先分配账户才能进行分润！");
							}else if(res.iRtn == 2){
								nui.alert("未查询到分润配置表的数据！");
							}
	                    }
					});
				});
				
				
				
			    
    		}
    }
    
    
    function isCheck(obj,func){
    	var json = nui.encode({obj:obj});
		$.ajax({
			url:"com.gotop.xmzg.achieve.acc.checkIs.biz.ext",
			type:'POST',
			data:json,
			cache: false,
			contentType:'text/json',
			success:function(text){
				func(text.code);
			}
		});
	}
	//查询是否一次分润
	function isFistCheck(obj,func){
    	var json = nui.encode({obj:obj});
		$.ajax({
			url:"com.gotop.xmzg.achieve.acc.checkIsFirstCross.biz.ext",
			type:'POST',
			data:json,
			cache: false,
			contentType:'text/json',
			success:function(text){
				func(text.code);
			}
		});
	}
	
	//判断当前选中的账户数据是否存在审核中的分润记录
	function isCheckForProfits(obj,func){
		var json = nui.encode({obj:obj});
		$.ajax({
			url:"com.gotop.xmzg.achieve.acc.checkIsForProfits.biz.ext",
			type:'POST',
			data:json,
			cache: false,
			contentType:'text/json',
			success:function(text){
				func(text.code);
			}
		});
	}
	
	function beforenodeselect(e) {

    	//组织机构不能选 
	    if (e.node.NODETYPE=="OrgOrganization") {
	        e.cancel = true;
	    }
	}
	
	function exportData(){
		form.validate();
        if (form.isValid() == false) return;
		nui.ajax({
			url: "com.gotop.xmzg.achieve.acc.positionIs.biz.ext",
			type: "post",
			data: nui.encode({}),
			contentType:'text/json',
			success: function (text) {
				if(text.code==1){
					var newColumns = [];
					var columns=grid.getBottomColumns();
					columns=columns.clone();
					for(var i=0;i<columns.length;i++){
						var column=columns[i];
						if(!column.field || column.field == "TAB_RL_TYPE" 
								||column.field == "TAB_ORGNAME"
								||column.field == "TAB_BEGIN"
								||column.field == "TAB_END" 
								||column.field =="TAA_STATUS"
								||column.field =="TAA_STATUS_PRO"
								||column.field =="IS_PRO" 
								||column.field =="TC_PRODUCTNAME"
								||column.field =="TC_TYPE"
								||column.field =="TAC_INFOS"
								||column.field =="TAC_SY"){
							//console.log(column);
						}else{
							if(column.field =="TAB_CUST_RELA"){
								newColumns.push({header:column.header+"（个人存款：1管户客户 2自营客户 3托管客户）",field:column.field });
							}else{
								newColumns.push({header:column.header,field:column.field });
							}
						}
					}
					columns=nui.encode(newColumns);
					var data = form.getData(true, true);
					data.queryData.empcode = text.empcodeLd;
					var tab_yesno = nui.get("tab_yesno").getValue();
					var TI_CODE = nui.get("TI_CODE").getValue();
					console.log(TI_CODE);
					if(TI_CODE){
						$.ajax({
						url: "com.gotop.xmzg.achieve.acc.checkTiInDict.biz.ext",
			            type: 'POST',
			            data:nui.encode({"TI_CODE":TI_CODE}),
			            async:false,
			            cache: false,
			            contentType:'text/json',
			            success: function (text){
			            	console.log(text.isIn);
			            	if(text.isIn){
			            		data.queryData.isIn = text.isIn;
			            		data.queryData.TAB_BL_FLAG = tab_yesno;
			            		
			            	}
						}
					})
					}
					
					data = nui.encode(data);
					console.log(data);
					var sheet = "业绩账户";
					var fileName = "业绩账户认领情况信息";
					var queryPath = "com.gotop.xmzg.achieve.acc.queryAccBelogForCustExp";
					var url="<%=request.getContextPath()%>/achieve/excel/exportBigExcel.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data+"&sheet="+sheet;
					window.location.replace(encodeURI(url));
				}else{
					nui.alert("您没有权限导出数据！");
				}
			}
		});
	} 
	
	function exportFrData(){
		form.validate();
        if (form.isValid() == false) return;
		nui.ajax({
			url: "com.gotop.xmzg.achieve.acc.positionIs.biz.ext",
			type: "post",
			data: nui.encode({}),
			contentType:'text/json',
			success: function (text) {
				if(text.code==1){
					var newColumns = [];
					var columns=grid.getBottomColumns();
					columns=columns.clone();
					for(var i=0;i<columns.length;i++){
						var column=columns[i];
						if(!column.field || column.field == "TAB_RL_TYPE" 
								||column.field == "TAB_ORGNAME"
								||column.field == "TAB_BEGIN"
								||column.field == "TAB_END" 
								||column.field =="TAA_STATUS"
								||column.field =="TAA_STATUS_PRO"
								||column.field =="IS_PRO" 
								||column.field =="TC_PRODUCTNAME"
								||column.field =="TC_TYPE"
								||column.field =="TAB_CUST_RELA"){
							//console.log(column);
						}else{
							newColumns.push({header:column.header,field:column.field });
						}
					}
					
					columns=nui.encode(newColumns);
					var data = form.getData(true, true);
					data.queryData.empcode = text.empcodeLd;
					var tab_yesno = nui.get("tab_yesno").getValue();
					var TI_CODE = nui.get("TI_CODE").getValue();
					console.log(TI_CODE);
					if(TI_CODE){
						$.ajax({
						url: "com.gotop.xmzg.achieve.acc.checkTiInDict.biz.ext",
			            type: 'POST',
			            data:nui.encode({"TI_CODE":TI_CODE}),
			            async:false,
			            cache: false,
			            contentType:'text/json',
			            success: function (text){
			            	console.log(text.isIn);
			            	if(text.isIn){
			            		data.queryData.isIn = text.isIn;
			            		data.queryData.TAB_BL_FLAG = tab_yesno;
			            		
			            	}
						}
					});
					}
					
					data = nui.encode(data);
					console.log(data);
					var sheet = "业绩账户";
					var fileName = "业绩账户认领/分润情况信息";
					var queryPath = "com.gotop.xmzg.achieve.acc.queryAccBelogForCustFrExp";
					
					nui.open({
			            url:"<%=request.getContextPath()%>/achieve/acc/accBelog_emp.jsp",
			            title: "选择分润日期",
			            width: 400,
			            height: 350,
			            onload:function(){
			            	var frame = this.getIFrameEl();
			            	var dataPage = {pageType:"exp"};
			            	frame.contentWindow.setFormData(dataPage);
			            },
			            ondestroy: function (action) {
		                	if (action == "ok") {
		                		var iframe = this.getIFrameEl();
		                        var rdata = iframe.contentWindow.GetData();
		                        data=nui.decode(data);
						        data.queryData.TAC_DATE = rdata;
						        data = nui.encode(data);
        						console.log(data);
		                		var url="<%=request.getContextPath()%>/achieve/excel/exportBigExcel.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data+"&sheet="+sheet;
								window.location.replace(encodeURI(url));
		                	}
			            }
			        });
        			
					
				}else{
					nui.alert("您没有权限导出数据！");
				}
			}
		});
	} 
	
	function importData(){
		nui.ajax({
			url: "com.gotop.xmzg.achieve.acc.positionIs.biz.ext",
			type: "post",
			data: nui.encode({}),
			contentType:'text/json',
			success: function (text) {
				if(text.code==1){
					nui.open({
			            url:"<%=request.getContextPath()%>/achieve/acc/accBelog_imp.jsp",
			            title: "导入认领数据",
			            width: 550,
			            height: 230,
			            ondestroy: function (action) {
	                		grid.reload();
			            }
			        });
        		}else{
					nui.alert("您没有权限导出数据！");
				}
			}
		});
	}
	
	function  updateBusiOrg(){
    	var rows = grid.getSelecteds();
		if(rows.length == 0){
			nui.alert("请至少选择一条记录进行修改！");
			return;
		}
		
		nui.ajax({
			url: "com.gotop.xmzg.achieve.acc.positionIs.biz.ext",
			type: "post",
			data: nui.encode({}),
			contentType:'text/json',
			success: function (text) {
				if(text.code!=1){
					nui.alert("您没有权限进行修改！");
					return;
				}
				var dicts = nui.get("JF_CHANGEORG_ZB").getData();
				for(var i = 0 ; i < rows.length ; i++){
				   var zbCode = rows[i].TAB_ZB_CODE;
				   var isTrue = false;
				   for(var x = 0 ; x < dicts.length ; x++){
					   if(zbCode.indexOf(dicts[x].dictID) == 0){
					   		isTrue = true;
					   }
				   }
				   
				   if(!isTrue){
				   		var msg = "";
				   		for(var y = 0 ; y < dicts.length ; y++){
							msg +=dicts[y].dictName+";";
					    }
				   		nui.alert("只能修改指标或指标细项："+msg, "系统提示");
				   		return ;
				   }
				   
				   if(rows[i].TAA_STATUS == 0){
						nui.alert("该记录正在审核中！");
						return ;
				   }
				}
				nui.open({
		            url:"<%=request.getContextPath()%>/achieve/common/org_tree.jsp?type=2",
		            title: "选择机构",
		            width: 500,
		            height: 400,
		            onload:function(){
		            	
		            },
		            ondestroy: function (action) {
		                if (action == "ok") {
		                    var iframe = this.getIFrameEl();
		                    var data = iframe.contentWindow.GetData();
		                    data = nui.clone(data);
		                    if (data) {
		                    	
		                        for(var i = 0 ; i < rows.length ; i++){
									rows[i].TAB_BUSI_ORG = data.ORGCODE;
						        }
						        //调用修改方法
						        var load= nui.loading("正在保存请稍侯...","温馨提示 =^_^=");
						        $.ajax({
									url:"com.gotop.xmzg.achieve.acc.accBusiOrg_updates.biz.ext",
									type:'POST',
									data: nui.encode({objs:rows}),
									cache: false,
									contentType:'text/json',
									success:function(text){
										nui.hideMessageBox(load);  //隐藏遮罩层
							        	   var code = text.code;
							        	   var msg = text.msg;
							        	   if(code != "1"){
							        		  	nui.alert(msg, "系统提示", function(action){
													if(action == "ok" || action == "close"){
														//CloseWindow("saveFailed");
													}
												});
								           }else{
							          			nui.alert(msg, "系统提示", function(action){
							          				if(action == "ok" || action == "close"){
														grid.reload();
													}
												});
								          }
									}
								});
		                    }
		                }
		            }
		        });
			}
		});	
    }
    
    var ti = nui.get("TI_CODE");
    var tid = nui.get("TID_CODE");
    function onTipChanged(e){
        ti.setValue("");
        tid.setValue("");
        var url = "com.gotop.xmzg.achieve.configClaPro.ti_get.biz.ext?code=" + e.value;
        ti.setUrl(url);
    }
    
    function onTiChanged(e){

        tid.setValue("");
        var url = "com.gotop.xmzg.achieve.configClaPro.tid_get.biz.ext?code=" + e.value;
        tid.setUrl(url);
    }
    
    
    function OrgonButtonEdit(e) {	
        var btnEdit = this;
	    nui.open({
	        url:"<%=request.getContextPath()%>/achieve/common/org_tree.jsp?type=2",
	        title: "选择机构",
	        width: 500,
	        height: 400,
	        onload:function(){},
	        ondestroy: function (action) {
	            if (action == "ok") {
	                var iframe = this.getIFrameEl();
	                var data = iframe.contentWindow.GetData();
	                data = nui.clone(data);
	                if (data) {
	                	btnEdit.setValue(data.ORGCODE);
	                    btnEdit.setText(data.TEXT);
	                }
	            }
	        }
	    });
    }
    
    //个人养老金条线批量认定 养老金账户 指标编号10027001
    function gotoClaimsYlj(){
    	var btnEdit = this;
	    nui.open({
	        url:"<%=request.getContextPath()%>/achieve/acc/impGrylj.jsp",
	        title: "批量认定导入",
	        width: 900,
	        height: 200,
	        onload:function(){},
	        ondestroy: function (action) {
	            if (action == "ok") {
	                var iframe = this.getIFrameEl();
	                var data = iframe.contentWindow.GetData();
	                data = nui.clone(data);
	                if (data) {
	                	btnEdit.setValue(data.ORGCODE);
	                    btnEdit.setText(data.TEXT);
	                }
	            }
	        }
	    });
    
    }
</script>
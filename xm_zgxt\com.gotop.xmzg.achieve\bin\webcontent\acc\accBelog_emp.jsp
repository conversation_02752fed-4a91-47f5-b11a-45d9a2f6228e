<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): wj
  - Date: 2022-02-27 14:02:29
  - Description:
-->
<head>
<title>业绩账户分配</title>
  <%@include file="/coframe/dict/common.jsp"%>
<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
</head>
<body>
  <div id="form1" style="padding-top:5px;">
    <table style="width:100%;height:90%;table-layout:fixed;" class="nui-form-table">
      <tr>
        <th class="nui-form-label"><label for="map.type$text">分润时间：</label></th>
        <td colspan="3" >  
        	<input id="TAC_DATE" name = "map.TAC_DATE" class="nui-datepicker"  style="width:100%;" required="true" allowInput="false" format="yyyyMMdd" />
        </td> 
      </tr>
    </table>
    </div>
   
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	        	<a id="sub" class="nui-button" iconCls="icon-save" onclick="onOk">提交</a>
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">关闭</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  </div>

  <script type="text/javascript">
    nui.parse();   
    var form = new nui.Form("#form1");

	var param = null;
    //与父页面的方法2对应：页面间传输json数据
    function setFormData(data){                   
        //跨页面传递的数据对象，克隆后才可以安全使用
       var infos = nui.clone(data);
	   param = infos;
    }
    
    function GetData(){
    	return nui.get("TAC_DATE").getFormValue();
    }
    function onOk() {

        CloseWindow("ok");        
    }

	function onCancel(){
        CloseWindow("close");
    }
    
    function CloseWindow(action){
     	var flag = form.isChanged();
		if(window.CloseOwnerWindow) 
		    return window.CloseOwnerWindow(action);
		else
		    return window.close();
    }
  </script>
</body>
</html>
<%@page pageEncoding="UTF-8"%>	
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2019-06-04 14:51:16
  - Description:
-->
<head>
	<title>指标业务条线修改</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%@include file="/achieve/init.jsp"%>
	<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
</head>
<body>
	<input  id="tree1" class="nui-treeselect" multiSelect="true" style="display: none;" dataField="treeNodes"
	url="com.gotop.xmzg.achieve.common.get_OrgTree.biz.ext" showTreeIcon="true" textField="TEXT" idField="ID" parentField="PID" />
	
  <div id="prameter" style="padding-top:5px;"> 
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table" align="center"> 
    <tbody>
     <tr> 
      <th class="nui-form-label" style="width:35%"><font class="b_red">*</font>业务条线代码：</th> 
      <td style="width:65%"> <input class="nui-textbox" id="tip_code" name="tip_code" style="width:200px;" required="true" /> </td> 
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>业务条线名称：</th> 
      <td><input class="nui-textbox" id="tip_name" name="tip_name" style="width:200px;" required="true"/></td> 
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>排序：</th> 
      <td><input class="nui-textbox" id="tip_sorting" name="tip_sorting" style="width:200px;" value="0" required="true"/></td> 
     </tr>
    <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>管理机构：</th> 
      <td>
      	<input id="btnEdit"  name="tip_orgcodes" class="nui-textboxlist" style="width: 200px;" allowInput="false" required="true"/>  
        <a class="nui-button " plain="true" iconCls="icon-edit" onclick="OrgonButtonEdit('btnEdit')">配置</a>
      	<!-- <input class="nui-hidden" id="tip_orgcodes" name="tip_orgcodes" /> -->
      </td> 
     </tr>
    </tbody>
   </table> 
   <div class="nui-toolbar" style="padding:0px;" borderstyle="border:0;"> 
    <table width="100%"> 
     <tbody>
      <tr> 
       	<td style="text-align:center;"> 
	       	<a class="nui-button" iconcls="icon-save" onclick="seve">提交</a> 
	       	<span style="display:inline-block;width:10px;"></span> 
	       	<a class="nui-button" iconcls="icon-cancel" onclick="onCancel">取消</a>
       	</td> 
      </tr> 
     </tbody>
    </table> 
   </div> 
  </div>


  <script type="text/javascript">
  	nui.parse();
  	
  	function setTreeCheck(id,codeStr){	
    	var datas = nui.get("tree1").getList();
    	var nameStr = "";
    	var codes = codeStr.split(",");
    	 for (var i = 0, l = codes.length; i < l; i++) {
    	 	for (var x = 0, y = datas.length; x < y; x++) {
    	 		if(codes[i] == datas[x].ORGCODE) nameStr+=datas[x].TEXT+",";
    	 	}
    	 }
    	nui.get(id).setText(nameStr);
    }
    
  	function setData(data){
  		//跨页面传递的数据对象，克隆后才可以安全使用
        data = nui.clone(data);
        data = lowerJson(data);
        var form = new nui.Form("#prameter");
        form.setData(data);
        var btnEdit = nui.get("btnEdit");
        btnEdit.setValue(data.tip_orgcodes);
        //btnEdit.setText(data.orgcodes);
        setTreeCheck("btnEdit",data.tip_orgcodes);
        
        //nui.get("tip_orgcodes").setValue(data.tip_orgcodes);
        nui.get("tip_code").disable();
  	}
  	
  	function seve(){
  		var form = new nui.Form("#prameter");
		form.validate();
		if (form.isValid() == false) return;
		var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
		//提交数据
        nui.ajax({
       		url: "com.gotop.xmzg.achieve.indicators.indicators_plate_update.biz.ext",
           	type: "post",
           	data: nui.encode({"obj":form.getData()}),
           	contentType:'text/json',
           	success: function (text) {
        	   nui.hideMessageBox(load);  //隐藏遮罩层
        	   var code = text.code;
        	   var msg = text.msg;
        	   if(code != "1"){
        		  	nui.alert(msg, "系统提示");
	           }else{
          			nui.alert(msg, "系统提示", function(action){
						if(action == "ok" || action == "close"){
							CloseWindow("saveSuccess");
						}
					});
	          }
           }
       });
  	}
  	
  	
  	
  	
  	//机构树回显
    function OrgonButtonEdit(id) {
    	var btnEdit = nui.get(id);
       nui.open({
           url:bactpath+"/achieve/common/org_multSelectTree.jsp",
           showMaxButton: false,
           title: "选择树",
           width: 350,
           height: 350,
           onload:function(){
				var iframe = this.getIFrameEl();
      	    	iframe.contentWindow.setTreeCheck(btnEdit.getValue());
		   },
           ondestroy: function (action) {                    
               if (action == "ok") {
                   var iframe = this.getIFrameEl();
                   var data = iframe.contentWindow.GetData();
                   data = nui.clone(data);
                   if (data) {
                       btnEdit.setValue(data.ORGCODE);
                       btnEdit.setText(data.TEXT);
                       //nui.get("tip_orgcodes").setValue(data.ORGCODE);
                   }
               }
           }
       });
     }    
  </script>
</body>
</html>
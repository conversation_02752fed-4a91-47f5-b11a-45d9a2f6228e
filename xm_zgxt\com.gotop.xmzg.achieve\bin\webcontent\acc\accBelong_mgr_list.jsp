<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): wj
  - Date: 2022-02-16
  - Description: 业绩账户历史查询
-->
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>业绩账户历史查询</title>
<style type="text/css">
.search-condition .table td {
    height: 0; 
    line-height: 0; 
}
</style>
</head>
<body style="margin:0px;width:100%;overflow:hidden;" >
	<div class="search-condition">
		<div class="list">
			<div id="filesForm">
				<input name = "queryData.datetype" value="0" class="nui-hidden"/>
		   		<table class="table" style="width:100%;">
		   			<tr>
						<th class="tit">业务条线：</th>
						<td>
							<div required="true"  id="TIP_CODE" name="queryData.tip_code" class="nui-combobox" style="width:150px;" dataField="datas" textField="TIP_NAME" valueField="TIP_CODE" popupWidth="400"
						    	url="com.gotop.xmzg.achieve.configClaPro.tip_get.biz.ext" multiSelect="false" allowInput="false" showNullItem="false" nullItemText="请选择" emptyText="请选择"
						    	onvaluechanged="onTipChanged">     
							    <div property="columns">
							        <div header="业务条线代码" field="TIP_CODE" width="60"></div>
							        <div header="业务条线名称" field="TIP_NAME" width="120"></div>
							    </div>
						    </div>
						</td>
						<th class="tit">指标：</th>
						<td>
							<div required="true" id="TI_CODE" name="queryData.ti_code" class="nui-combobox" style="width:150px;" dataField="datas" textField="TI_NAME" valueField="TI_CODE" popupWidth="400"
						    	multiSelect="false" allowInput="true" showNullItem="false" nullItemText="请选择" emptyText="请选择"
						    	onvaluechanged="onTiChanged">     
							    <div property="columns">
							        <div header="指标代码" field="TI_CODE" width="60"></div>
							        <div header="指标名称" field="TI_NAME" width="120"></div>
							    </div>
						    </div>
						</td>
			        	
						
						<th class="tit">指标细项：</th>
						<td>
							<div id="TID_CODE" name="queryData.tid_code" class="nui-combobox" style="width:150px;" dataField="datas" textField="TID_NAME" valueField="TID_CODE" popupWidth="400"
						    	multiSelect="false" allowInput="true" showNullItem="false" nullItemText="全部" emptyText="全部">     
							    <div property="columns">
							        <div header="指标细项代码" field="TID_CODE" width="60"></div>
							        <div header="指标细项名称" field="TID_NAME" width="120"></div>
							    </div>
						    </div>
						</td>
			        </tr>
		      		<tr>
		      			<th class="tit">产品所属机构：</th>
						<td >
							<input required="true"  id="tab_busi_org" name="queryData.tab_busi_org"  class="nui-buttonedit" style="width:150px;" allowInput="false" onbuttonclick="OrgonButtonEdit" />
						</td>
						
		      			<th class="tit">产品编号：</th>
						<td>
							<input id="tab_busi_no" name="queryData.tab_busi_no" class="nui-textbox" style="width:150px;"/>
						</td>
		      		    <th class="tit">产品名称：</th>
						<td>
							<input id="tab_busi_name" name="queryData.tab_busi_name" class="nui-textbox" style="width:150px;"/>
						</td>
						
						
					</tr>
					<tr>
						<th class="tit">认领类型：</th>
						<td>
							<input id="tab_rl_type" class="nui-dictcombobox" name="queryData.tab_rl_type"  emptyText="全部"
          					valueField="dictID" textField="dictName" dictTypeId="JF_FPLX" showNullItem="true" nullItemText="全部" style="width:150px;"/>
						</td>
						<th class="tit">指标名称：</th>
						<td>
							<input id="tab_zb_name" name="queryData.tab_zb_name" class="nui-textbox" style="width:150px;"/>
						</td>
			        	
						
						<th class="tit">是否已认领：</th>
						<td>
							
							<input id="tab_yesno" class="nui-dictcombobox" name="queryData.tab_bl_flag"  emptyText="全部"
          					valueField="dictID" textField="dictName" dictTypeId="JF_YES_NO" showNullItem="true" nullItemText="全部" style="width:150px;"/>
						</td>
						<td>
							
						</td>
			        </tr>
			        <tr>
			        	<th class="tit">客户经理名称：</th>
						<td>
							<input id="tab_empname" name="queryData.tab_empname" class="nui-textbox" style="width:150px;"/>
						</td>
						<th class="tit">是否可分润：</th>
						<td >
							<input id="is_pro" class="nui-dictcombobox" name="queryData.is_pro"  emptyText="全部"
          					valueField="dictID" textField="dictName" dictTypeId="JF_YES_NO" showNullItem="true" nullItemText="全部" style="width:150px;"/>
						</td>
						<th class="tit">认领方式：</th>
						<td >
							<input id="tc_type" class="nui-dictcombobox" name="queryData.tc_type"  emptyText="全部"
          					valueField="dictID" textField="dictName" dictTypeId="JF_RLFS" showNullItem="true" nullItemText="全部" style="width:150px;"/>
						</td>
						
			        </tr>
			        <tr>
			        	<th class="tit">客户关系类型：</th>
						<td >
							<input id="tab_cust_rela" class="nui-dictcombobox" name="queryData.tab_cust_rela"  emptyText="全部"
          					valueField="dictID" textField="dictName" dictTypeId="JF_GRCKKHGXLX" showNullItem="true" nullItemText="全部" style="width:150px;"/>
						</td>
						<td colspan="4" style="text-align: left;">
							<a class="nui-button" iconCls="icon-search" onclick="searchData();">查询</a>
							<a class="nui-button" iconCls="icon-reset"  onclick="clean()"/>重置</a>
						</td>
			        </tr>
		    	</table>
		  	</div>
		</div>
	</div>
	<div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      	<table style="width:100%;">
        	<tr>
           		<td style="width:100%;">
           			<a class="nui-button" iconCls="icon-collapse" onclick="detail()">详情</a>
           		</td>
        	</tr>
      	</table>
    </div>
  	<div class="nui-fit" id="fieldset1">
	 	<div id="grid"  dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" 
	  	url="com.gotop.xmzg.achieve.acc.queryAccBelogToCustMgr.biz.ext" sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" >
	    	<div property="columns">
	    		<div type="checkcolumn"></div>
	    		<div field="TAB_ID" class="nui-hidden" visible="false">ID</div>
	    		<div field="TAB_RL_TYPE" headerAlign="center" renderer="filesType">认领类型</div>
	    		<div field="TAB_ZB_CODE" headerAlign="center">指标代码</div>
		        <div field="TAB_ZB_NAME" headerAlign="center">指标名称</div>
		        <div field="TC_PRODUCTNAME" headerAlign="center">产品类型</div>
		        <div field="TAB_BUSI_NO" headerAlign="center">产品编号</div>
		        <div field="TAB_BUSI_NAME" headerAlign="center">产品名称</div>
		        <div field="TAB_BUSI_ORGNAME" headerAlign="center">产品及所属机构</div>
		        <div field="TAB_BUSI_ORG" headerAlign="center" visible="false">产品及所属机构代码</div>
		        <div field="TAB_BL_FLAG" headerAlign="center" renderer="onyesno">是否已认领</div>
		        <div field="TAB_EMP" headerAlign="center">客户经理编号</div>
		        <div field="TAB_EMPNAME" headerAlign="center">客户经理</div>
		        <div field="TAB_ORGNAME" headerAlign="center">客户经理机构</div>
		        <div field="TAB_BEGIN" headerAlign="center">开始时间</div>
		        <div field="TAB_END" headerAlign="center">结束时间</div>
		        <div field="TAA_STATUS" headerAlign="center"renderer="onsp">认领审核状态</div>
		        <div field="TAA_STATUS_PRO" headerAlign="center"renderer="onsp">分润审核状态</div>
		        <div field="IS_PRO" headerAlign="center"renderer="onyesno">是否可分润</div>
		         <div field="TAB_CUST_RELA" headerAlign="center"renderer="JF_GRCKKHGXLX">客户关系类型</div>
		         <div field="TC_TYPE" headerAlign="center" renderer="JF_RLFS">认领方式</div>
		        <!-- <div field="TAB_BUSI_DATE" headerAlign="center">产品日期</div>
		        <div field="TAB_BUSI_TYPE" headerAlign="center">产品类型</div>
		        <div field="TAB_BUSI_STATUS" headerAlign="center"renderer="JF_CPZT">产品状态</div>
		        <div field="TAB_BUSI_REMARK" headerAlign="center">产品说明</div>
		       
		        <div field="TAB_CUST_TYPE" headerAlign="center"renderer="JF_GRCKKHLX">客户类型</div> -->
		    </div>
	 	</div>
  	</div>
</body>
</html>
<script type="text/javascript">
	nui.parse();
	var path = '<%=request.getContextPath() %>';
	
	var form = new nui.Form("filesForm");
	var grid = nui.get("grid");
	var data = form.getData(true,true);
	//grid.load(data);
	
	nui.ajax({
        url: "com.gotop.xmzg.achieve.acc.getEmp.biz.ext",
        type: 'POST',
        data:nui.encode({map:{type:"org"}}),
        cache: false,
        contentType:'text/json',
        success: function (text) {
        	nui.get("tab_busi_org").setValue(text.orgcode);
	        nui.get("tab_busi_org").setText(text.orgname);
	    }
   });
	
	
	function filesType(e){
	    return nui.getDictText("JF_FPLX", e.row.TAB_RL_TYPE);
	}
	
	function onyesno(e){
	    return nui.getDictText("JF_YES_NO", e.value);
	}
	
	function onsp(e){
	    return nui.getDictText("JF_SHZG", e.value);
	}
	
	function gslx(e){
	    return nui.getDictText("JF_GSLX", e.value);
	}
	
	function JF_GRCKKHGXLX(e){
	    return nui.getDictText("JF_GRCKKHGXLX", e.value);
	}
	
	function JF_GRCKKHLX(e){
	    return nui.getDictText("JF_GRCKKHLX", e.value);
	}
	function JF_CPZT(e){
	    return nui.getDictText("JF_CPZT", e.value);
	}
	
	function searchData(){	
		form.validate();
        if (form.isValid() == false) return;
        var data = form.getData(true,true);
    	grid.load(data);
    }
    function JF_RLFS(e){
	    return nui.getDictText("JF_RLFS", e.value);
	}
    function detail(){
       var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选择一条记录！");
		}else{
	       nui.open({
	          url:"<%=request.getContextPath() %>/achieve/acc/accBelong_distr.jsp",
	          title:'详情',
	          width:800,
          	  height:680,
	          onload:function(){
	          	var iframe = this.getIFrameEl();
				var data = {pageType:"detail",record:{map:rows[0]}};
				iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          }
	       });
       }
       
    }
    //重置查询信息
	function clean(){
	   	form.reset();  
    }
    
    var ti = nui.get("TI_CODE");
    var tid = nui.get("TID_CODE");
    function onTipChanged(e){
        ti.setValue("");
        tid.setValue("");
        var url = "com.gotop.xmzg.achieve.configClaPro.ti_get.biz.ext?code=" + e.value;
        ti.setUrl(url);
    }
    
    function onTiChanged(e){

        tid.setValue("");
        var url = "com.gotop.xmzg.achieve.configClaPro.tid_get.biz.ext?code=" + e.value;
        tid.setUrl(url);
    }
    
    function OrgonButtonEdit(e) {	
        var btnEdit = this;
	    nui.open({
	        url:"<%=request.getContextPath()%>/achieve/common/org_tree.jsp?type=2",
	        title: "选择机构",
	        width: 500,
	        height: 400,
	        onload:function(){
	        	
	        },
	        ondestroy: function (action) {
	            if (action == "ok") {
	                var iframe = this.getIFrameEl();
	                var data = iframe.contentWindow.GetData();
	                data = nui.clone(data);
	                if (data) {
	                	btnEdit.setValue(data.ORGCODE);
	                    btnEdit.setText(data.TEXT);
	                }
	            }
	        }
	    });
    }
</script>
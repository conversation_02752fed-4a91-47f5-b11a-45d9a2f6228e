<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
  <!-- 
  - Author(s): wj
  - Date: 2022-02-27 14:02:29
  - Description:
-->
  <head>
    <title>业绩账户分配</title>
    <%@include file="/coframe/dict/common.jsp"%>
    <style type="text/css">
		.asLabel .mini-textbox-border,
		.asLabel .mini-textbox-input,
		.asLabel .mini-buttonedit-border,
		.asLabel .mini-buttonedit-input,
		.asLabel .mini-textboxlist-border {
		  border: 0;
		  background: none;
		  cursor: default;
		}
		
		.asLabel .mini-buttonedit-button,
		.asLabel .mini-textboxlist-close {
		  display: none;
		}
		
		.asLabel .mini-textboxlist-item {
		  padding-right: 8px;
		}
    </style>
  </head>
  <body>
    <div id="form1" style="padding-top:5px;">
      <input class="nui-hidden" id="tab_num_zy" name="map.num" />
      <input class="nui-hidden" id="tab_num_tg" name="map.tgnum" />
      <input class="nui-hidden" id="tab_id" name="map.TAB_ID" />
      <input class="nui-hidden" id="tab_busi_org" name="map.TAB_BUSI_ORG" />
      <input class="nui-hidden" id="tab_pre_id" name="map.TAB_PRE_ID" />
      <input class="nui-hidden" id="cust_num" name="map.cust_num" />
      <input class="nui-hidden" id="inst_no" name="map.inst_no" />

      <table style="width:100%;height:90%;table-layout:fixed;" class="nui-form-table">
        <tr>
          <th class="nui-form-label" style="width:100px;"><label for="map.status$text">产品类型：</label></th>
          <td colspan="3">
            <input id="map.TC_PRODUCTNAME" name="map.TC_PRODUCTNAME" readOnly="true" style="width:100%;"
              class="nui-textbox asLabel" />
          </td>
          <th class="nui-form-label" style="width:130px;"><label for="map.type$text">产品编号：</label></th>
          <td colspan="3">
            <input id="map.TAB_BUSI_NO" name="map.TAB_BUSI_NO" readOnly="true" class="nui-textbox asLabel"
              style="width:100%;" />
          </td>
        </tr>
        <tr>
          <th class="nui-form-label" style="width:100px;"><label for="map.status$text">产品名称：</label></th>
          <td colspan="3">
            <input id="map.TAB_BUSI_NAME" name="map.TAB_BUSI_NAME" readOnly="true" style="width:100%;"
              class="nui-textbox asLabel" />
          </td>
          <th class="nui-form-label"><label for="map.type$text">产品所属机构：</label></th>
          <td colspan="3">
            <input id="map.TAB_BUSI_ORGNAME" name="map.TAB_BUSI_ORGNAME" readOnly="true" style="width:100%;"
              class="nui-textbox asLabel" />
          </td>
        </tr>
        <tr>
          <th class="nui-form-label"><label for="map.type$text">认领类型：</label></th>
          <td colspan="3">
            <input id="map.TAB_RL_TYPE" class="nui-dictcombobox asLabel" name="map.TAB_RL_TYPE" style="width:150px;"
              valueField="dictID" textField="dictName" dictTypeId="JF_FPLX" readOnly="true" />
          </td>
          <th class="nui-form-label"><label for="map.type$text">指标代码：</label></th>
          <td colspan="3">
            <input id="map.TAB_ZB_CODE" name="map.TAB_ZB_CODE" class="nui-textbox asLabel" />
          </td>
        </tr>
        <tr>
          <th class="nui-form-label"><label for="map.type$text">指标名称：</label></th>
          <td colspan="3">
            <input id="map.TAB_ZB_NAME" name="map.TAB_ZB_NAME" style="width:100%;" class="nui-textbox asLabel" />
          </td>
          <th class="nui-form-label"><label for="map.type$text">是否认领：</label></th>
          <td colspan="3">
            <input id="tab_bl_flag" name="map.TAB_BL_FLAG" class="nui-dictcombobox asLabel" readOnly="true"
              style="width:150px;" valueField="dictID" textField="dictName" dictTypeId="JF_YES_NO" />
          </td>
        </tr>
        <tr>
          <th class="nui-form-label"><label for="map.type$text">分配开始时间：</label></th>
          <td colspan="3">
            <input id="map.TAB_BEGIN" name="map.TAB_BEGIN"  style="width:150px;"  class="nui-datepicker" required="true"  allowInput="false" format="yyyyMMdd" />
          </td>
          <th class="nui-form-label"><label for="map.type$text">分配结束时间：</label></th>
          <td colspan="3">
            <input id="map.TAB_END" name="map.TAB_END"  style="width:150px;"  class="nui-datepicker"     allowInput="false" format="yyyyMMdd"
            required="true"  />
          </td>
        </tr>
        <tr>
          <th class="nui-form-label"><label for="map.type$text">审核状态：</label></th>
          <td colspan="3">
            <input id="taa_status" name="map.TAA_STATUS" class="nui-dictcombobox asLabel" readOnly="true"
              style="width:150px;" valueField="dictID" textField="dictName" dictTypeId="JF_SHZG" />
          </td>
          <th class="nui-form-label"><label for="map.type$text">是否分润配置：</label></th>
          <td colspan="3">
            <input id="is_pro" name="map.IS_PRO" class="nui-dictcombobox asLabel" readOnly="true" style="width:150px;"
              valueField="dictID" textField="dictName" dictTypeId="JF_YES_NO" />
          </td>
        </tr>
        <tr>
          <th class="nui-form-label"><label for="map.type$text">产品日期：</label></th>
          <td colspan="3">
            <input id="map.TAB_BUSI_DATE" name="map.TAB_BUSI_DATE" readOnly="true" style="width:100%;"
              class="nui-textbox asLabel" />
          </td>
          <th class="nui-form-label"><label for="map.type$text">产品属性：</label></th>
          <td colspan="3">
            <input id="map.TAB_BUSI_TYPE" name="map.TAB_BUSI_TYPE" readOnly="true" style="width:100%;"
              class="nui-textbox asLabel" />
          </td>
        </tr>
        <tr>
          <th class="nui-form-label"><label for="map.type$text">产品状态：</label></th>
          <td colspan="3">
            <input id="TAB_BUSI_STATUS" name="map.TAB_BUSI_STATUS" class="nui-dictcombobox asLabel" readOnly="true"
              style="width:150px;" valueField="dictID" textField="dictName" dictTypeId="JF_CPZT" />
          </td>
          <th class="nui-form-label"><label for="map.type$text">产品说明：</label></th>
          <td colspan="3">
            <input id="map.TAB_BUSI_REMARK" name="map.TAB_BUSI_REMARK" readOnly="true" style="width:100%;"
              class="nui-textbox asLabel" />
          </td>
        </tr>
        <tr>
          <th class="nui-form-label"><label for="map.type$text">客户关系类型：</label></th>
          <td colspan="3">
            <input class="nui-hidden" id="TAB_CUST_RELA" name="map.TAB_CUST_RELA" />
            <input id="NEW_TAB_CUST_RELA" name="map.NEW_TAB_CUST_RELA" class="nui-dictcombobox asLabel" readOnly="true"
              style="width:150px;" valueField="dictID" textField="dictName" dictTypeId="JF_GRCKKHGXLX" />
          </td>
          <th class="nui-form-label"><label for="map.type$text">客户类型：</label></th>
          <td colspan="3">
            <input id="TAB_CUST_TYPE" name="map.TAB_CUST_TYPE" class="nui-dictcombobox asLabel" readOnly="true"
              style="width:150px;" valueField="dictID" textField="dictName" dictTypeId="JF_GRCKKHLX" />
          </td>
        </tr>
        <tr id="gszlTr">
          <th class="nui-form-label"><label for="map.type$text">归属客户经理：</label></th>
          <td colspan="7">
            <input class="nui-hidden" id="map.TAB_EMP" name="map.TAB_EMP" />
            <input id="NEW_TAB_EMP" name="map.NEW_TAB_EMP" class="nui-buttonedit" allowInput="false"
              onbuttonclick="selectEmp" style="width:150px;" required="true" />
          </td>
        </tr>
        <tr id="xsjTr">
          <th class="nui-form-label"><label for="map.type$text">新开始时间：</label></th>
          <td colspan="7">
            <input id="NEW_TAB_BEGIN" name="map.NEW_TAB_BEGIN" class="nui-datepicker" style="width:150px;"
              required="true" allowInput="false" format="yyyyMMdd" />
          </td>
        </tr>
      </table>
    </div>
    <div class="nui-fit">

      <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:55%;"
        allowCellEdit="true" allowCellSelect="true" multiSelect="true"
        url="com.gotop.xmzg.achieve.accNew.queryAccBelogList.biz.ext" sizeList=[5,10,20,50,100] pageSize="10">
        <div property="columns">
          <!-- <div type="checkcolumn"></div>  -->
          <div field="TAB_CHANGE_TYPE" headerAlign="center" align="center" renderer="gslx">归属类型</div>
          <div field="TAB_EMP" headerAlign="center" align="center">归属客户经理号</div>
          <div field="TAB_EMPNAME" headerAlign="center" align="center">归属客户经理名称</div>
          <div field="TAB_ORGNAME" headerAlign="center" align="center">归属客户经理机构</div>
          <div field="TAB_BEGIN" headerAlign="center" align="center">分配开始时间</div>
          <div field="TAB_END" headerAlign="center" align="center">分配结束时间</div>
        </div>
      </div>
      <br>
      <div id="datagrid2" dataField="datas" class="nui-datagrid" style="width:100%;height:35%;" allowCellEdit="true"
        allowCellSelect="true" multiSelect="true" showPager="false"
        url="com.gotop.xmzg.achieve.accNew.accBelong_detail_yj.biz.ext">
        <div property="columns">
          <!-- <div type="checkcolumn"></div>  -->
          <div field="TZAD_DATE" headerAlign="center" width="40" align="center">统计日期</div>
          <div field="TZAD_NAME" headerAlign="center" align="center">指标细项名称</div>
          <div field="TZAD_ACHIEVE" headerAlign="center" width="60" align="center">业绩值</div>
          <div field="TID_UNIT" headerAlign="center" width="40" align="center" renderer="tid_unit">单位</div>
        </div>
      </div>

    </div>

    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
      <table width="100%">
        <tr>
          <td style="text-align:center;">
            <a id="sub" class="nui-button" iconCls="icon-save" onclick="onOk">提交</a>
            <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">关闭</a>
          </td>
        </tr>
      </table>
    </div>
    </div>

    <script type="text/javascript">
	nui.parse();
	var form = new nui.Form("#form1");
	form.setChanged(false);
	//认领biz
	var okUrl = "com.gotop.xmzg.achieve.accNew.accBelong_add_claim_ter.biz.ext";
	
	function tid_unit (e) {
	  return nui.getDictText("JF_UINT", e.value);
	}
	
	function gslx (e) {
	  return nui.getDictText("JF_GSLX", e.value);
	}
	
	var type = "0";
	//与父页面的方法2对应：页面间传输json数据
	function setFormData (data) {
	  //跨页面传递的数据对象，克隆后才可以安全使用
	  var infos = nui.clone(data);
	  okUrl = "com.gotop.xmzg.achieve.accNew.accBelong_add_" + infos.pageType + ".biz.ext";
	  var json = infos.record;
	  if (infos.pageType == "claim_terNot") {
	    $("#gszlTr").hide();
	  }
	  if (infos.pageType == "claim_ter") {
	    $("#gszlTr").hide();
	    $("#xsjTr").hide();
	  }
	  if (infos.pageType == "detail") {
	    $("#sub").hide();
	    $("#gszlTr").hide();
	    $("#xsjTr").hide();
	  }
	
	  if (infos.pageType == "claim" || infos.pageType == "over") {
	    var zbCode = json.map.TAB_ZB_CODE;
	    if (zbCode == '10003' || zbCode == '10003001' || zbCode == '10003002' || zbCode == '10003003') {
	      nui.get("NEW_TAB_CUST_RELA").setRequired(true);
	      nui.get("NEW_TAB_CUST_RELA").setReadOnly(false);
	      $("#NEW_TAB_CUST_RELA").removeClass("asLabel");
	    }
	  }
	
	  var form = new nui.Form("#form1"); //将普通form转为nui的form
	  form.setData(json);
	  nui.get("NEW_TAB_CUST_RELA").setValue(json.map.TAB_CUST_RELA);
	  nui.get("tab_num_zy").setValue("1");
	  nui.get("tab_num_tg").setValue("1");
	  type = json.map.TC_ACROSSORG;
	
	  var grid = nui.get("datagrid1");
	  var data = form.getData(true, true);
	  grid.load(data);
	
	  var grid2 = nui.get("datagrid2");
	  grid2.load(data);
	
	  if (infos.pageType == "claim") {
	    $("#xsjTr").hide();
	    //初始化 当前登录人
	    nui.ajax({
	      url: "com.gotop.xmzg.achieve.acc.getEmp.biz.ext",
	      type: 'POST',
	      data: data,
	      cache: false,
	      contentType: 'text/json',
	      success: function (text) {
	        if (text.empcode != null) {
	
	          nui.get("NEW_TAB_EMP").setValue(text.empcode);
	          nui.get("NEW_TAB_EMP").setText(text.empname);
	          nui.get("NEW_TAB_EMP").setReadOnly(true);
	        }
	      }
	    });
	
	  }
	
	  if (infos.pageType == "over" || infos.pageType == "claim_terNot") {
	    nui.get("NEW_TAB_BEGIN").setMinDate(new Date());
	    nui.get("NEW_TAB_BEGIN").setMaxDate(new Date());
	    var info = json.map;
	    info.type = infos.pageType == "over" ? "ZY" : "RLZZ";
	    nui.ajax({
	      url: "com.gotop.xmzg.achieve.accNew.accBelong_add_zydate_get.biz.ext",
	      type: 'POST',
	      data: nui.encode({ "info": info }),
	      cache: false,
	      contentType: 'text/json',
	      success: function (text) {
	        console.log(text.res);
	        if (text.res != null) {
	          nui.get("NEW_TAB_BEGIN").setMinDate(text.res.min);
	          nui.get("NEW_TAB_BEGIN").setMaxDate(text.res.max);
	        }
	      }
	    });
	  }
	}
	
	function update () {
	  nui.open({
	    url: "<%=request.getContextPath() %>/achieve/accNew/accBelog_detail_add.jsp",
	    title: '修改',
	    width: 350,
	    height: 150,
	    onload: function () {
	      var iframe = this.getIFrameEl();
	      var form = new nui.Form("#form1");
	      //方法2：直接从页面获取，不用去后台获取
	      var data = { pageType: "edit", record: form.getData() };
	      iframe.contentWindow.setFormData(data); //调用弹出窗口的 setFormData方法
	    },
	    ondestroy: function (action) {
	      if (action != null && action != "close") {
	        initData();
	
	        var grid = nui.get("datagrid1");
	        var data = form.getData(true, true);
	        grid.load(data);
	
	      }
	    }
	  });
	}
	
	function onReset () {
	  form.setData(obj);
	  form.setChanged(false);
	}
	
	function onCancel () {
	  CloseWindow("cancel");
	}
	
	function CloseWindow (action) {
	  if (window.CloseOwnerWindow)
	    return window.CloseOwnerWindow(action);
	  else
	    return window.close();
	}
	
	function initData () {
	  var form = new nui.Form("#form1"); //将普通form转为nui的form
	  var data = form.getData(true, true);
	  nui.ajax({
	    url: "com.gotop.xmzg.achieve.accNew.getAccBelong.biz.ext",
	    type: 'POST',
	    data: data,
	    cache: false,
	    contentType: 'text/json',
	    success: function (json) {
	      var returnJson = nui.decode(json);
	      var tab_empname = returnJson.res.TAB_EMPNAME;
	      nui.get("map.TAB_EMPNAME").setValue(tab_empname);
	    }
	  });
	}
	
	
	function selectEmp (e) {
	  var ele = e.sender.id;
	  var emp = nui.get(ele);
	  nui.open({
	    url: "<%=request.getContextPath()%>/achieve/acc/accEmptree.jsp?type=" + type + "&isrole=1&orgcode=" + nui.get(
	      "tab_busi_org").getValue(),
	    title: "选择人员",
	    width: 600,
	    height: 400,
	    onload: function () {
	      var frame = this.getIFrameEl();
	      var data = {};
	      data.value = emp.getValue();
	      //frame.contentWindow.setData(data);
	      frame.contentWindow.setTreeCheck(data.value);
	    },
	    ondestroy: function (action) {
	      //if (action == "close") return false;
	      if (action == "ok") {
	        var iframe = this.getIFrameEl();
	        var data = iframe.contentWindow.GetData();
	        data = nui.clone(data);
	        if (data) {
	          emp.setValue(data.ORGID);
	          emp.setText(data.ORGNAME);
	        }
	      }
	
	    }
	  });
	}
	
	function yyyymmdd(e){
	    var timeStr = e;
	    if(timeStr==""){
	    } else{
	    // 将字符串转换为 Date 对象
			var date = new Date(timeStr);
			// 获取年、月、日
			var year = date.getFullYear(); // 2023
			var month = String(date.getMonth() + 1).padStart(2, '0'); // 10 (注意月份从 0 开始，需要 +1)
			var day = String(date.getDate()).padStart(2, '0'); // 05
			
			// 拼接成 YYYYMMDD 格式
			var formattedDate = year+month+day;
			timeStr = formattedDate;
			}
		return timeStr;
	}
	
	function onOk () {
	  form.validate();
	  if (form.isValid() == false) return;
	  var a = nui.get("map.TAB_BEGIN").getValue();//中国标准时间
	  var b =  nui.get("map.TAB_END").getValue();//中国标准时间
	  var begin = yyyymmdd(a);
	  var end = yyyymmdd(b); 
	   nui.get("map.TAB_BEGIN").setValue(begin);
	   nui.get("map.TAB_END").setValue(end);
	  var load = nui.loading("正在提交请稍侯...", "温馨提示 =^_^=");
	  //提交数据
	  nui.ajax({
	    url: okUrl,
	    type: "post",
	    data: nui.encode({ "obj": form.getData().map }),
	    contentType: 'text/json',
	    success: function (text) {
	      nui.hideMessageBox(load);
	      var code = text.code;
	      var msg = text.msg;
	      if (code != "1") {
	        nui.alert(msg, "系统提示", function (action) {
	          if (action == "ok" || action == "close") {
	            //CloseWindow("saveFailed");
	            //认领成功
	  			//更新客户明细表认领信息
	            var custNum = nui.get("cust_num").getValue();
	            var instNo = nui.get("inst_no").getValue();
	            var msgCustNum = nui.get("NEW_TAB_EMP").getValue();
	            var msgCustName = nui.get("NEW_TAB_EMP").getText();
	            if(msgCustNum==""||msgCustNum==null){
	              console.log("归属客户经理为空，需要确认是否选择！");
	            }else{
	               nui.ajax({
	                  url: "com.gotop.newReport.queryCustDetail.update_custDetail_claim.biz.ext",
	                  type: "post",
	                  data: nui.encode({ obj: { "custNum": custNum, "instNo": instNo,"msgCustNum":msgCustNum,"msgCustName":msgCustName} }),
	                  contentType: 'text/json',
	                  success: function (text) {
						console.log("更新认领信息返回结果："+text.code);
	                  }
	                });  
	            }
	                    
	          }
	        });
	      } else {
	        nui.alert(msg, "系统提示", function (action) {
	          if (action == "ok") {
	            CloseWindow("saveSuccess");
	          }
	        });
	      }
	    }
	  });
	}
    </script>
  </body>
</html>
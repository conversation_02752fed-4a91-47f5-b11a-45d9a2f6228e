<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="com.eos.data.datacontext.UserObject"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- $Header: svn://**************:7029/svn/xm_project/xm_zgxt/com.gotop.fileManger.jw/src/webcontent/menu.jsp 1713 2018-08-27 07:34:09Z tq $ -->
<!-- 
  - Author(s): JW
  - Date: 2018-07-25 11:08:29
  -   
-->
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
 %>
<head>
<title>文件添加</title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=EDGE">
<%@include file="/coframe/tools/skins/common.jsp"%>
<script src="<%=request.getContextPath()%>/common/nui/nui.js" type="text/javascript"></script>
<style type="text/css">
html,body {
	padding: 0;
	margin: 0;
	border: 0;
	height: 100%;
	overflow-y: auto;
}

.errorText {
	color: red;
	font-size: 12px;
}
</style>
</head>
<body>
	<div class="nui-fit">
		<!--
					showTreeIcon:显示节点图标
					textField:节点文本字段
					idField:值字段
					parentField:父节点字段
				-->
		<ul id="menu_tree" class="nui-tree" style="width: 98%; height: 98%; padding: 5px; background: #fafafa; margin-top: 5px;" showTreeIcon="true" expandOnLoad="0"
			resultAsTree="true" idField="LISTID" textField="LISTNAME" dataField="nodes">
		</ul>
	</div>
	<div style="text-align: center; padding: 10px;">
		<a class="nui-button" onclick="onOk()" style="width: 60px; margin-right: 20px;">确定</a> <a class="nui-button" onclick="onCancel()" style="width: 60px;">取消</a>
	</div>
	<script type="text/javascript">
	var tree = nui.get("menu_tree");
	var userid="<%=userObject.getUserId()%>";
	var orgid="<%=userObject.getUserOrgId()%>";
	var permId=1;
	</script>
	<script src="<%= request.getContextPath() %>/js/meun.js" type="text/javascript"></script>
	<script type="text/javascript">
		nui.parse();
		menulist();	
		/* 0（预览） 1（修改） 2（删除） 3（下载）  */
		var username="<%=userObject.getUserName()%>";
		
		//路径
		var path="<%=request.getContextPath()%>";

		//标准方法接口定义

		function GetData() {
			var newpath="";
			var newid="";
			var node = tree.getSelectedNode();
			newid=node.LISTID;
			var arrnode=tree.getAncestors(node);
			for(var i=0;i<arrnode.length;i++){
				if(isNull(arrnode[i].LISTPID)){
					newpath+=arrnode[i].LISTNAME+"/";
				}
			}
			var arr={"aurl":newpath+node.LISTNAME,"aid":newid};
			return nui.decode(arr);
		}
		
		function onOk() {			
			CloseWindow("ok");
		}
		function onCancel() {
			CloseWindow("cancel");
		}
		function CloseWindow(action) {
			if (window.CloseOwnerWindow)
				return window.CloseOwnerWindow(action);
			else
				window.close();
		}
	</script>
</body>
</html>

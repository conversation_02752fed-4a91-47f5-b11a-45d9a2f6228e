<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): 54305
  - Date: 2022-02-26 12:27:15
  - Description:
-->
<head>
<style>
#table1 .tit{
	height: 10px;
    line-height: 10px;
    	text-align: right;
}
#table1 td{
	height: 10px;
    line-height: 10px;
}
#table1 .roleLabel{
	text-align: right;
}
#table1 .roleText{
	padding-left: 5px;
}
</style>
<%@include file="/coframe/tools/skins/common.jsp" %>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<title>机构业绩查询</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
</head>
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");

 %>
<body>
	<div class="search-condition">
		<div class="list">
			<div id="form1">
				<table class="table" style="width:100%;">							
					<tr>			
						<td class="tit" STYLE="width:100px;">统计日期：</td>
						<td>
							<input id="queryData.DATE_BEGIN" class="nui-datepicker" name="queryData.DATE_BEGIN"  style="width:100px;" allowInput="false" required="true"/>
			                 ~
			                <input id="queryData.DATE_END" class="nui-datepicker" name="queryData.DATE_END"  style="width:100px;" allowInput="false" required="true"/>
						</td>
						
						<td class="tit" STYLE="width:100px;">机构：</td>
						<td>
							<input id="queryData.ORGCODE" name = "queryData.ORGCODE"  class="nui-buttonedit" allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;" required="true"/>
						</td>
						
						<td><td>
						<td><td>
						  <th rowspan="2"><a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp; 
				            <input class="nui-button" text="重置" iconCls="icon-reset" onclick="clean"/>
				       </th>			      
					</tr>
					
					<tr>			
						
						
						<td class="tit" STYLE="width:100px;">业绩类型：</td>
						<td>
						<input  id="queryData.TYPE" name="queryData.TYPE" class="nui-combobox"  data="TYPE_DICT" textField="text" valueField="id"  style="width:220px;"
							required="true" onvaluechanged="typeChange"
						 />
						</td>
						<td class="tit TIP_ID" STYLE="width:100px;">业务线条：</td>
						<td class="TIP_ID">
						<input  id="queryData.TIP_ID" name="queryData.TIP_ID" class="nui-combobox"   textField="TEXT" valueField="ID" dataField="list" style="width:150px;"
						required="true" 
                  			  />
						</td>
						<td class="tit TA_ID" STYLE="width:100px;">考核方案：</td>
						<td class="TA_ID">
						<input  id="queryData.TA_ID" name="queryData.TA_ID" class="nui-combobox"  textField="TEXT" valueField="ID"  dataField="list"style="width:150px;"
                  			  required="true" 
                  			  />
						</td>
					</tr>
					
					
				</table>
			</div>
		</div>
	</div>
		 <div class="nui-toolbar" style="border-bottom:0;">
			<table style="width:100%">
				<tr>
					<td>
						<a class="nui-button" iconCls="icon-add" onclick="excel">导出Excel</a>								
					</td>
				</tr>
			</table>
	</div>
    <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.achieve.report.query_org_integral_report.biz.ext"   
	  sizeList=[5,10,20,50,100] multiSelect="false" pageSize="10"
	  >
	    <div property="columns" >
	      	  	       
	            <div field="TAIS_DATE" >统计日期</div>
	            <div field="YJZH" >一级支行</div>
	            <div field="TAIS_ORGNAME" >二级支行</div>
	            <div field="TAIS_EMPNAME" >客户经理名称</div>
	            <div field="TAIS_EMPCODE" >客户经理编号</div>
	            <div field="TA_NAME" >考核方案</div>
	            <div field="TIP_NAME" >业务条线</div>
	            <div field="TI_NAME" >指标</div>
	            <div field="TID_NAME" >指标细项</div>
				<div field="TAIS_ACHIEVE_PRA" >业绩</div>
				<div field="TAIS_INTEGRAL" >积分</div>
				<div field="TAIS_SORT">排名</div>			
				<div field="TAIS_BJG_SORT">区支行排名</div>			
				<div field="TAIS_QZH_SORT">本机构排名</div>			
				<div field="TAIS_QH_SORT">业务条线排名</div>			
	    </div>
	 </div>
  </div>


	
</body>
<script type="text/javascript">
	var TYPE_DICT =[{ id: "0", text: '考核指标' },{ id: "1", text: '基础指标'}];
	nui.parse();
	var grid = nui.get("datagrid1");
   
	
	//初始化加载
	$(function(){
		nui.get("queryData.TYPE").setValue("1");
		typeChange();
	});
	
	//隐藏下拉
	function hidenCombox(){
		$(".TIP_ID").hide();
		$(".TA_ID").hide();
	}
	//
	function typeChange(){
		hidenCombox();
		var typeVal =  nui.get("queryData.TYPE").getValue();
		if(typeVal==0){
			$(".TA_ID").show();
			nui.get("queryData.TA_ID").load("com.gotop.xmzg.achieve.report.loadTACombox.biz.ext");
		}else{
			$(".TIP_ID").show();
			nui.get("queryData.TIP_ID").load("com.gotop.xmzg.achieve.report.loadTIPCombox.biz.ext");
		}
	}
	
	function search(){
        var form = new nui.Form("#form1");
        form.validate();
        if (form.isValid() == false) return;
        var data = form.getData(true,true);   
        
        grid.load(data);
    }
    	
	//机构树点击事件
	function OrgonButtonEdit(e) {	
        var btnEdit = this;
        nui.open({
            url:"<%=request.getContextPath() %>/achieve/report/org_tree.jsp",
        showMaxButton: false,
        title: "选择树",
        width: 350,
        height: 350,
        ondestroy: function (action) {                    
            if (action == "ok") {
                var iframe = this.getIFrameEl();
                var data = iframe.contentWindow.GetData();
                data = nui.clone(data);
                if (data) {
                    btnEdit.setValue(data.CODE);
                    btnEdit.setText(data.TEXT);
                    
                }
            }
        }
    	});            
	}
	
	//导出Excel
	function excel(){
		      
		var form=new nui.Form("form1");
		form.validate();
	    if (form.isValid() == false) return;
		var data=form.getData(true, true);
		var fileName="机构业绩查询报表";
		var queryPath="com.gotop.xmzg.achieve.report.query_org_integral_report";
		var columns=grid.getBottomColumns();
		columns=columns.clone();
		for(var i=0;i<columns.length;i++){
			var column=columns[i];
			if(!column.field){
				columns.removeAt(i);
			}else{
				var c={header:column.header,field:column.field };
				columns[i]=c;
			}
		}
		columns=nui.encode(columns);
		data=nui.encode(data);
	     var url="<%=request.getContextPath()%>/util/exportExcel/exportExcel.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data;
		 window.location.replace(encodeURI(url));
		 
		 //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
	     setMask();
	} 
    </script>
</html>
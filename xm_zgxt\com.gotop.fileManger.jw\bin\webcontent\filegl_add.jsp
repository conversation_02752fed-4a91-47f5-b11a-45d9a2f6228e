<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- $Header: svn://**************:7029/svn/xm_project/xm_zgxt/com.gotop.fileManger.jw/src/webcontent/filegl_add.jsp 1801 2018-08-30 03:37:13Z hyq $ -->
<!-- 
  - Author(s): JW
  - Date: 2018-07-25 11:08:29
  -   
-->
<head>
<title>文件添加</title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<%@include file="/coframe/tools/skins/common.jsp"%>
<script src="<%=request.getContextPath()%>/common/nui/nui.js" type="text/javascript"></script>
<style type="text/css">
html,body {
	padding: 0;
	margin: 0;
	border: 0;
	height: 100%;
	overflow-y:auto;
}

.mini-textarea {
	width: 100%;
}

#ss {
	border: 1px solid black;
}

.mini-popup .mini-shadow {
	height: 150px;
}

.mini-textboxlist {
	width: 100%;
	height: 40px;
}
.mini-radiobuttonlist-table .mini-radiobuttonlist-td{
	width: 50px;
	padding: 5px;
	border: 1px solid #ccc;
	border-radius:5px;
	background-color: #ccc;
}
/* .mini-radiobuttonlist-table .mini-radiobuttonlist-td:nth-child(1){
	background-color: #ccc;
} 
.mini-radiobuttonlist-table .mini-radiobuttonlist-td:nth-child(2){
	background-color: blue;
}
.mini-radiobuttonlist-table .mini-radiobuttonlist-td:nth-child(3){
	background-color: red;
}
.mini-radiobuttonlist-table .mini-radiobuttonlist-td:nth-child(4){
	background-color: aqua;
}*/
.mini-radiobuttonlist-item{
	text-align: center;
}
.mini-radiobuttonlist-item label {
	font-size: 15px;
}
</style>
</head>
<body>
	<fieldset style="border: solid 1px #aaa; padding: 3px;">
		<legend>文件基本信息</legend>
		<div style="padding: 5px;">
			<form id="filefrom" method="post">
				<table border="0" cellpadding="1" cellspacing="2" align="center" style="width: 100%">
					<tr>
						<td style="width: 15%; text-align: right;">文号：</td>
						<td style="width: 70%"><input name="file.fileNo" class="mini-textbox" required="true" style="width: 100%;" /></td>
						<td style="width: 15%;"></td>
					</tr>
					<tr>
						<td style="width: 15%; text-align: right;">关键字：</td>
						<td style="width: 70%"><input name="file.fileKey" class="mini-textbox" required="true" style="width: 100%;" /></td>
						<td style="width: 15%;"></td>
					</tr>
					<tr>
						<td style="width: 15%; text-align: right;">所属目录：</td>
						<td style="width: 70%"><input id="vest_meun" class="mini-treeselect" multiSelect="false" valueFromSelect="false" textField="LISTNAME"
							name="file.listId" valueField="LISTID" parentField="LISTPID" allowInput="true" showRadioButton="true" showFolderCheckBox="true" style="width: 100%;" /></td>
						<td style="width: 15%;"></td>
					</tr>
					<tr>
						<td style="width: 15%; text-align: right;">文件名：</td>
						<td style="width: 70%"><input id="fileName" name="file.fileName" class="mini-textbox" required="true" style="width: 100%;" allowInput="false" /></td>
						<td style="width: 15%;"></td>
					</tr>
				</table>
				<input id="uploadname" name="file.uploadName" class="mini-hidden" /> <input name="file.fileStatus" class="mini-hidden" value="1" />
			</form>
			<form id="upload" method="post" enctype="multipart/form-data">
				<table border="0" cellpadding="1" cellspacing="2" align="center" style="width: 100%">
					<tr>
						<td style="width: 15%; text-align: right;">文件：</td>
						<td style="width: 70%"><input class="mini-htmlfile" name="Fdata" id="uploadfile" style="width: 100%;" onfileselect="onFileSelect" /></td>
						<td style="width: 15%;"></td>
					</tr>
				</table>
			</form>
		</div>
	</fieldset>
	<fieldset style="border: solid 1px #aaa; padding: 3px;">
		<legend>权限信息</legend>
		<div style="padding: 5px;">
			<table border="0" cellpadding="1" cellspacing="2" align="center" style="width: 100%">
				<tr>
					<td colspan="2" align="center">
						<div id="radio_type" class="mini-radiobuttonlist" repeatItems="1" repeatLayout="table" repeatDirection="vertical" textField="DICTNAME" valueField="DICTID"
							value="check"></div>
					</td>
				</tr>
				<tr>
					<td style="width: 15%; text-align: right;">已分配人员：</td>
					<td style="width: 70%" id="check">
						<input id="check_empBox" class="mini-textboxlist hide_check" name="check" textName="tblName" required="true" valueField="id" textField="text" /> 
						<input id="update_empBox" class="mini-textboxlist hide_update" name="tbl" textName="tblName" required="true" valueField="id" textField="text" /> 
						<input id="down_empBox" class="mini-textboxlist hide_down" name="tbl" textName="tblName" required="true" valueField="id" textField="text" />
						<input id="delete_empBox" class="mini-textboxlist hide_delete" name="tbl" textName="tblName" required="true" valueField="id" textField="text" />
					</td>
					<td style="width: 15%">
		            	<a value="emp" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
		            </td>
				</tr>
				<tr>
					<td style="width: 15%; text-align: right;">已分配机构：</td>
					<td style="width: 70%" id="update">
						<input id="check_orgBox" class="mini-textboxlist hide_check" name="emp" textName="tblName" required="true" valueField="id" textField="text" /> 
						<input id="update_orgBox" class="mini-textboxlist hide_update" name="tbl" textName="tblName" required="true" valueField="id" textField="text" /> 
						<input id="down_orgBox" class="mini-textboxlist hide_down" name="tbl" textName="tblName" required="true" valueField="id" textField="text" />
						<input id="delete_orgBox" class="mini-textboxlist hide_delete" name="tbl" textName="tblName" required="true" valueField="id" textField="text" />
					</td>
					<td style="width: 70%">
		            	<a value="org" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
		            </td>
				</tr>
				<tr>
					<td style="width: 15%; text-align: right;">已分配群主：</td>
					<td style="width: 70%" id="delete">
						<input id="check_groupBox" class="mini-textboxlist hide_check" name="emp" textName="tblName" required="true" valueField="id" textField="text" /> 
						<input id="update_groupBox" class="mini-textboxlist hide_update" name="tbl" textName="tblName" required="true" valueField="id" textField="text" /> 
						<input id="down_groupBox" class="mini-textboxlist hide_down" name="tbl" textName="tblName" required="true" valueField="id" textField="text" />
						<input id="delete_groupBox" class="mini-textboxlist hide_delete" name="tbl" textName="tblName" required="true" valueField="id" textField="text" />
					</td >
					<td style="width: 15%">
		            	<a value="group" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
		            </td>
				</tr>
				<tr>
					<td style="width: 15%; text-align: right;">已分配岗位：</td>
					<td style="width: 70%" id="down">
						<input id="check_roleBox" class="mini-textboxlist hide_check" name="role" textName="tblName" required="true" valueField="id" textField="text" /> 
						<input id="update_roleBox" class="mini-textboxlist hide_update" name="role" textName="tblName" required="true" valueField="id" textField="text" /> 
						<input id="down_roleBox" class="mini-textboxlist hide_down" name="role" textName="tblName" required="true" valueField="id" textField="text" />
						<input id="delete_roleBox" class="mini-textboxlist hide_delete" name="role" textName="tblName" required="true" valueField="id" textField="text" />
					</td>
					<td style="width: 15%">
		            	<a value="role" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
		            </td>
				</tr>
			</table>
		</div>
	</fieldset>
	<div style="text-align: center; padding: 10px;">
		<a class="mini-button" onclick="submitForm()" style="width: 60px; margin-right: 20px;">确定</a> <a class="mini-button" onclick="" style="width: 60px;">取消</a>
	</div>
	<script type="text/javascript">
			nui.parse();
			
			/* 0（预览） 1（修改） 2（删除） 3（下载）  */
			
			//路径
			var path="<%= request.getContextPath() %>";
			var powerBox = nui.get("radio_type");
			var type=['check','update','delete','down'];
			var map =["emp", "role", "org", "group"];
			//所属目录id
			var vestmeun=nui.get("vest_meun");
			
			var form = new nui.Form("#filefrom");
			var fileinfo=nui.get("#uploadfile");
			//标准方法接口定义
	        function SetData(data,datalist,username){
	        	//跨页面传递的数据对象，克隆后才可以安全使用
	        	data = nui.clone(data);	        	
	        	vestmeun.loadList(datalist, "LISTID", "LISTPID");
	        	vestmeun.setValue(data.id);
	        	nui.get("uploadname").setValue(username);
	        	DictType();   
			}
	        //文件浏览按钮触发事件
			function onFileSelect(e){				
				var path=fileinfo.value;								
				filename=path.substring(path.lastIndexOf("\\")+1,path.length);
				nui.get("fileName").setValue(filename);
			}
			//文件添加事件
			function submitForm() {
				
 				nui.mask({
		            el: document.body,
		            cls: 'mini-mask-loading',
		            html: '数据上传保存中...'
	        	}); 
				form.validate();
				if (form.isValid() == false) return;
				//提交表单数据	                        
				var data = form.getData(); //获取表单多个控件的数据
				var json = nui.encode(data); //序列化成JSON
		        var state ="";
				$.ajax({
					url : "com.gotop.fileManger.jw.action.filegl.addfile.biz.ext",
					type : "post",
					data : json,
					cache : false,
					contentType : 'text/json',
					success : function(data) {					
						nui.unmask(document.body);					
						var obj=data.msg;
						//console.log(data.msg.fileid);
						if (obj.resCode == "1") {
							sendFilePowerMsg(data.msg.fileid);
							FileUpload();
							CloseWindow("saveSuccess");
						}else{
							nui.alert("保存失败", "系统提示", function(action){
		                         if(action == "ok" || action == "close"){
		                             CloseWindow("saveFailed");
		                   		}
		                    });
						} 					
					}
				}); 
			}
			
			function sendFilePowerMsg(fileId){
				var data = getSaveData(fileId);
				$.ajax({
		        	url:"com.gotop.fileManger.jw.action.filegl.addFilePowerInfo.biz.ext",
		        	type:'POST',
		            data:data,
		            traditional: true,
		            success:function(data){
		 	        	if(data.msg=="success"){
		 	        		alert(type+"成功");
		 	        		CloseWindow("close");
		            	}else{
		             		nui.alert("保存失败", "系统提示", function(action){
		                         if(action == "ok" || action == "close"){
		                             CloseWindow("saveFailed");
		                   		}
		                    });
		             	} 
		             }
		         }); 
			}
			
			/* 弹出权限设置界面  */
			function onClick(e) {
				var powerType = powerBox.getValue();
				var boxType = e.sender.defaultValue;

				//console.log("boxType:"+boxType+"   powerType:"+powerType);
				nui.open({
		        	url: path + "/hyq/list_powertree.jsp?type="+boxType,
		        	title: "权限配置",
		            iconCls: "icon-edit", 
		            width: 350, 
		            height: 350,
		            onload: function () {
		                var iframe = this.getIFrameEl(); 
		                // 给树设置上已选择的节点字符串     
		                var boxObj = createBoxObj(powerType, boxType); 
		              	iframe.contentWindow.setTreeCheck(boxObj.getValue());
		            },
		            ondestroy: function (action) {
						if (action == "ok") {
		                    var iframe = this.getIFrameEl();
		                    var list = iframe.contentWindow.GetData();
		                    list = mini.clone(list);    
		                    if (list) {
		                    	// 将树选中节点设置到box显示
		                    	putDataTextBox(powerType, boxType, list, "nodeId", "nodeName");
		                    }
		                }
		            }
		        });
			}
			
			/**
			 * 往textboxlist中添加选择的数据
			 * @params powerType判断是否是check（查看）？update（修改）
			 * @params boxType	根据点击按钮的类型 添加到不同box里面
			 * @params list 	 获取Check选中的节点集合
			 */
			function putDataTextBox(powerType, boxType, list){
				var text = "",value = "";
				var boxObj = createBoxObj(powerType, boxType);
				for (var i = 0; i < list.length; i++) {
					var node = list[i];
					if (i == list.length -1) {
						value += node["nodeId"];
						text  += node["nodeName"];
					} else {
						value += node["nodeId"] + ",";
						text  += node["nodeName"] + ",";
					}
				}
				boxObj.setValue(value);
				boxObj.setText(text);
			}
			
			 /* 获取添加/修改的数据  */
		    function getSaveData(fileId) {
		    	var checkList = [],updateList = [],
		    		downList  = [],deleteList = [];
		        for (var i=0; i < 4; i++) {
		        	checkList[i] = nui.get("check_"+map[i]+"Box").getValue();
		        }
				for (var i=0; i < 4; i++) {
					updateList[i] = nui.get("update_"+map[i]+"Box").getValue();
				}
				for (var i=0; i < 4; i++) {
					downList[i] = nui.get("delete_"+map[i]+"Box").getValue();
				}
				for (var i=0; i < 4; i++) {
					deleteList[i] = nui.get("down_"+map[i]+"Box").getValue();
				}
				
				var json = {
							checkList:checkList,
		            		updateList:updateList,
		            		downList:downList,
		            		deleteList:deleteList,
		            		fileId:fileId};
		        return json;	
		    }   
			
			
			//关闭窗口
	        function CloseWindow(action) {
	            if (action == "close" && form.isChanged()) {
	                if (confirm("数据被修改了，是否先保存？")) {
	                    saveData();
	                }
	            }
	            if (window.CloseOwnerWindow)
	            return window.CloseOwnerWindow(action);
	            else window.close();
	        }
			//上传文件from表单
			function FileUpload(){
				var fileinfos = document.forms['upload'];
				fileinfos.action=path+"/jw/upload.jsp";
				fileinfos.submit();
			}
			
			//以下为权限js部分
			//权限控制的4个类型选项id
			var radiotype=nui.get("radio_type");
			function DictType(){
				var json = nui.encode({dicttypeid:"FILE_JURISDICTION_TYPE"}); //序列化成JSON
				$.ajax({
					url : "com.gotop.fileManger.jw.action.filegl.ditcfind.biz.ext",
					type : "post",
					data : json,
					cache : false,
					contentType : 'text/json',
					success : function(data) {
						var msg = data.msg.dict;
						var list = [msg[0],msg[3],msg[1],msg[2]];				
						radiotype.setData(list);
						hidcss();
					}
				});
			}
			//隐藏权限选择框的
			function hidcss(){
				var pitch=radiotype.getValue();
				
				for(var i=0;i<type.length;i++){
					$(".hide_"+type[i]).hide();
				}
				$(".hide_"+pitch).show();
			}
			//radio改变事件
			radiotype.on("valuechanged", function (e) {
				hidcss();
		    });
		    
		    
		     /* 切割ID字符串最后一位 */
		    function strSplitLast(obj) {
		    	return (obj).substring(0, obj.length-1);
		    }
		    
		    function createBoxObj(powerType, boxType) {
		    
		    	var boxId = powerType+"_"+boxType+"Box";
		    	
		    	return nui.get(boxId);
		    }
	</script>
</body>
</html>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): 51075
  - Date: 2023-01-11 14:19:20
  - Description:
-->
<head>
<style>
#table1 .tit{
	height: 10px;
    line-height: 10px;
    	text-align: right;
}
#table1 td{
	height: 10px;
    line-height: 10px;
}
#table1 .roleLabel{
	text-align: right;
}
#table1 .roleText{
	padding-left: 5px;
}
</style>
<%@include file="/coframe/tools/skins/common.jsp" %>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<title>人员业绩统计</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
  <script type="text/javascript" src="/default/common/nui/nui.js"></script>
	<script type="text/javascript" src="/default/common/nui/locale/zh_CN.js"></script>
	<link id="css_skin" rel="stylesheet" type="text/css" href="/default/coframe/tools/skins/skin1/css/style.css" />
    
</head>
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");

 %>
<body>
<div class="search-condition">
		<div class="list">
			<div id="form1">
			<table class="table" style="width:100%;">							
					<tr>	
					<td class="tit" STYLE="width:100px;">开户日期:</td>
						<td>
							<input id="queryData.TAB_BUSI_DATE" class="nui-datepicker" name="queryData.TAB_BUSI_DATE"  style="width:150px;" allowInput="false" required="true" />	
						</td>

                    <td class="tit" STYLE="width:100px;">开户机构:</td>
					<td>
							<input id="queryData.ORGNAME" name = "queryData.ORGNAME"  class="nui-buttonedit" allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;" />
						</td>

                        	  <th rowspan="3"><a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp; 
				            <input class="nui-button" text="重置" iconCls="icon-reset" onclick="clean"/><a class="nui-button" iconCls="icon-add" onclick="excel">导出Excel</a>	
				           <!--  <br><br><a class="nui-button"  iconCls="icon-edit" onclick="exportData">人员业绩统计</a> -->
				       </th>			      
					</tr>
				
                        				
				</table>
			</div>
		</div>
	</div>
	
	
<div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.achieve.report.org_score_statistics.biz.ext" 

	  sizeList=[5,10,20,50,100] multiSelect="false" pageSize="10">
	  
	    <div property="columns" >
	           
	            <div field="ORGNAME" >开户机构</div>
			    <div field="NOW_OPEN_NUM">当日开户数</div>
			    <div field="TOTAL_OPEN_NUM">累计开户数</div>	
			    <div field="YGS">员工数</div>
			    <div field="PEO_SUM">累计人均开户数</div>
			    <div field="RWS">任务数</div>
			    <div field="COM_SUM">完成率</div>
			    <div field="COM_SUM_RANK">完成率排名</div>
			    <div field="SUM_ACC">账户资产余额</div>   
			    <div field="SUM_ACC_RANK">户均资产余额</div>
			    <div field="SAFE_NUM">缴存户数</div>
			    <div field="SAFE_NUM_RANK">缴存占比</div>
					
	    </div>
	 </div>
  </div>
</body>
<script type="text/javascript">

    nui.parse();
	var grid = nui.get("datagrid1");
    grid.load();
        nui.get("queryData.TAB_BUSI_DATE").setValue(new Date((new Date().getTime() - 24*60*60*1000)));
        
	function search(){
		
		/* loadGrids(); */
     var form = new nui.Form("#form1");
      form.validate();
        if (form.isValid() == false) return; 
        var data = form.getData(true,true);   
        
        grid.load(data);
    }
	
		function clean(){
	   var form = new nui.Form("#form1");
	   form.clear();
	}
	
	function OrgonButtonEdit(e) {	
        var btnEdit = this;
        nui.open({
            url:"<%=request.getContextPath() %>/achieve/report/org_tree.jsp",
        showMaxButton: false,
        title: "选择树",
        width: 350,
        height: 350,
        ondestroy: function (action) {                    
            if (action == "ok") {
                var iframe = this.getIFrameEl();
                var data = iframe.contentWindow.GetData();
                data = nui.clone(data);
                if (data) {
                    btnEdit.setValue(data.CODE);
                    btnEdit.setText(data.TEXT);
                    var form = new nui.Form("#form1");
       				form.validate();
                    
                }
            }
        }
    	});            
	}
		
		//初始化加载
	$(function(){
		loadAccess();
		
	});
	function loadAccess(){
	   setOrgInfo();
		
	}
	function setOrgInfo(){
		$.ajax({
				url: "com.gotop.xmzg.achieve.report.getUserInfo.biz.ext",
	            type: 'POST',
	            
	            async:false,
	            cache: false,
	            contentType:'text/json',
	            success: function (text){
	           	 	var returnJson = nui.decode(text);
	           	 	console.log(returnJson.userInfo);
	           	 	
	       	 		var ORGCODE = returnJson.userInfo.ORGCODE;
	       	 		var ORGNAME = returnJson.userInfo.ORGNAME;
	       	 		console.log(ORGCODE);
	       	 		console.log(ORGNAME);
	       	 		nui.get("queryData.ORGNAME").setValue(ORGCODE);
	       	 		nui.get("queryData.ORGNAME").setText(ORGNAME);
				}
			});
	}
	
	//导出Excel
		  function excel(){
		                var form=new nui.Form("form1");
						form.validate();
					    if (form.isValid() == false) return;
						var data=form.getData(true, true);
						var fileName="网点业绩统计报表";
						var queryPath="com.gotop.xmzg.achieve.orgScore.org_score";
						
						var columns=grid.getBottomColumns();
						columns=columns.clone();
						for(var i=0;i<columns.length;i++){
							var column=columns[i];
							if(!column.field){
								columns.removeAt(i);
							}else{
								var c={header:column.header,field:column.field };
								columns[i]=c;
							}
						}
						var sheet = "网点业绩统计报表";
						columns=nui.encode(columns);
						//获取ORGCODE
						data.ORGNAME = nui.get("queryData.ORGNAME").getValue();
						data.TAB_BUSI_DATE = nui.get("queryData.TAB_BUSI_DATE").getValue();
					
					
						data=nui.encode(data);
					    var url="<%=request.getContextPath()%>/achieve/report/exportOrgScore.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data+"&sheet="+sheet;
						window.location.replace(encodeURI(url));
						
						 //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
					     //setMask();
	       
	    
	    
	     
	     }
    </script>
</html>
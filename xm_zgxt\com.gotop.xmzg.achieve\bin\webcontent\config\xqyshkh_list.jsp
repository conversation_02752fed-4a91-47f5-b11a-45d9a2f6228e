<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-05-29 16:10:11
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>小企业授信客户配置</title>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <table class="table" style="width:100%;">
			<tr>	
				<th  class="tit">客户号：</th>
				<td >
					<input id="CUST_NO" name = "queryData.CUST_NO" class="nui-textbox" style="width:100%;" />  
				</td>
				<th  class="tit">客户名称：</th>
				<td >
					<input id="CUST_NAME" name = "queryData.CUST_NAME" class="nui-textbox" style="width:100%;" />  
				</td>
				
				<th>
				  <a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;
				  <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
				</th>
			</tr>
	    </table>
	  </div>
    </div>
  </div>
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
		     <a class="nui-button" iconCls="icon-add" onclick="add">增加</a>
		     <a class="nui-button" iconCls="icon-edit" onclick="update">修改</a>
		     <a class="nui-button" iconCls="icon-remove" onclick="remove">删除</a>
           </td>
        </tr>
      </table>
    </div>
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;"
	  url="com.gotop.xmzg.achieve.config.xqysxkh_list.biz.ext" multiSelect="true"
	  sizeList=[5,10,20,50,100]  pageSize="10">
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      <div field="SUMM_DATE" headerAlign="center" align="center">统计时间</div>
	      <div field="CUST_NO" headerAlign="center" align="center">客户号</div>
	      <div field="CUST_NAME" headerAlign="center" align="center">客户名称</div>
	      <div field="CUST_TYPE" headerAlign="center" renderer="onGenderRenderer" align="center">客户类别</div>
	      <div field="NEW_LIMIT" headerAlign="center" align="center">新的授信金额（万元）</div>
	      <div field="EMPID" headerAlign="center" align="center">客户经理号</div>
	      <div field="EMPNAME" headerAlign="center" align="center" >客户经理名称</div>
	      <div field="PER_NUM" headerAlign="center" align="center">营销人员工号</div>
	      <div field="PER_NAME" headerAlign="center" align="center" >营销人员名称</div>
	      <div field="APPEOVED_LIMIT" headerAlign="center" align="center" visible="false">已批复额度</div>
	      <div field="NEW_LIMIT_BGN_DATE" headerAlign="center" align="center" visible="false" >新额度期限</div>
	      <div field="CR_EMPNAME" headerAlign="center" align="center" >创建人</div>
	      <div field="TX_CREATETIME" headerAlign="center" align="center" >创建时间</div>
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">  
    nui.parse();
 	
    var grid = nui.get("datagrid1");
    var form = new nui.Form("#form1");
    search();
    
    function search(){
       form.validate();
       if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
     
    function add(){
      nui.open({
          url:"<%=request.getContextPath() %>/achieve/config/xqyshkh_form.jsp",
          title:'新增',
          width:500,
          height:260,
          onload:function(){
          	var iframe = this.getIFrameEl();
	             var data = {pageType:"add"};
                  iframe.contentWindow.setData(data);
          },
          ondestroy:function(action){
             if(action=="saveSuccess"){
	            grid.reload();
	         }
          }
       });
    }
    
    function update(){
      var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
	       nui.open({
	          url:"<%=request.getContextPath() %>/achieve/config/xqyshkh_form.jsp",
	          title:'编辑',
	          width:500,
	          height:450,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             var data = {pageType:"edit",record:{map:rows[0]}};
                  iframe.contentWindow.setData(data);
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }
    }
    
     function remove(){
      var rows = grid.getSelecteds();
     if(rows.length > 0){
         nui.confirm("确定删除选中记录？","系统提示",
           function(action){
             if(action=="ok"){
	           var json = nui.encode({maps:rows});
	           var a= nui.loading("正在执行中,请稍等...","提示");
		        $.ajax({
		          url:"com.gotop.xmzg.achieve.config.xqysxkh_delete.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	nui.hideMessageBox(a);
		            var result = text.result;
					if(result !=null && result.code==1){
						nui.alert(result.msg, "系统提示", function(action){
							grid.reload();
						});
					}else{
						nui.alert(result.msg, "系统提示");
						grid.unmask();
					}
		          }
		        });
		     }
		   });
      }else{
        nui.alert("请选中一条记录！");
      }
    }
  
    var Genders = [{ id: 1, text: '新客户授信' }, { id: 2, text: '老客户增信'}];
        function onGenderRenderer(e) {
            for (var i = 0, l = Genders.length; i < l; i++) {
                var g = Genders[i];
                if (g.id == e.value) return g.text;
            }
            return "";
        }
          
  function CloseWindow(action){
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
  
   	//重置
    function clean(){
	   form.reset();
    }
 
  </script>
</body>
</html>
<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): shitf
  - Date: 2013-03-03 16:20:11
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>首页</title>
</head>
<body style="width:100%;">
  
  
 <div id="panel1" class="nui-listbox"  style="margin:-5px;"
		     dataField="tasks" url="org.gocom.components.coframe.auth.Welcom.queryProToDoList.biz.ext" >     
		    <div property="columns">
		    <div name="action1" header="待办事项"  width="25%"  renderer="onActionRenderer" ></div> 
		        <div header="申请人" field="EMPNAME" width="12.5%" ></div>
		        <div header="接收时间" field="STARTDATE" width="22.5%" ></div>
		        <div header="系统名称" field="PROCESSINSTNAME" width="40%" ></div>
		    </div>
		</div>

<div id="panel2" class="nui-listbox"  style="margin:-5px;"
		     dataField="tasks" url="org.gocom.components.coframe.auth.Welcom.queryProDoneList.biz.ext" >     
		    <div property="columns">
		    <div name="action2" header="已办事项"  width="25%"  renderer="onActionRenderer" ></div> 
		        <div header="申请人" field="empName" width="12.5%" ></div>
		        <div header="接收时间" field="startDate" width="22.5%" ></div>
		        <div header="系统名称" field="processInstName" width="40%" ></div>
		    </div>
		</div>
		
<div id="panel4" class="nui-listbox"  style="margin:-5px;"
		     dataField="messages" url="org.gocom.components.coframe.auth.Welcom.queryProUnviewedMessage.biz.ext" >     
		    <div property="columns">
		    <div name="action4" header="标题"  width="30%"  renderer="onActionRenderer" ></div> 
		        <div header="内容 " field="MESSAGE" width="45%" ></div>
		        <!-- <div header="发送者" field="SENDER" width="15%" ></div> -->
		        <div header="创建时间" field="CREATETIME2" width="25%" ></div>
		    </div>
		</div>
		
<div id="panel5" class="nui-listbox"  style="margin:-5px;"
		     dataField="messages" url="org.gocom.components.coframe.auth.Welcom.queryProViewedMessage.biz.ext" >     
		    <div property="columns">
		    <div name="action5" header="标题"  width="30%"  renderer="onActionRenderer" ></div> 
		        <div header="内容 " field="MESSAGE" width="45%" ></div>
		      <!--   <div header="发送者" field="SENDER" width="15%" ></div> -->
		        <div header="确认时间" field="CONFIRMTIME2" width="25%" ></div>
		    </div>
		</div>
		
  <div id="panel3" class="nui-listbox"  style="margin:-5px;"
		     dataField="resultlist" url="org.gocom.components.coframe.auth.Welcom.queryMessage.biz.ext" >     
		    <div property="columns">
		    	<div name="action3" header="消息内容"  width="50%"  renderer="onActionRenderer" ></div> 
		        <div header="发送人" field="PUB_EMPNAME" width="20%" ></div>
		        <div header="功能模块" field="SRC_NAME" width="30%" ></div>
		    </div> 
		</div>
  
  <div id="panel6" class="nui-listbox"  style="margin:-5px;"
		     dataField="resultlist" url="org.gocom.components.coframe.auth.Welcom.queryReleInfo.biz.ext" >     
		    <div property="columns">
		    	<div name="action6" header="标题"  width="40%"  renderer="onActionRenderer" ></div>
		    	<div header="发布人" field="PUB_EMPNAME" width="14%" ></div>
		    	<div header="信息开始时间" field="BEGIN_TIME" width="23%" ></div>
		    	<div header="信息结束时间" field="END_TIME" width="23%" ></div> 		        
		    </div> 
		</div>
  <link href="css/portal.css" rel="stylesheet" type="text/css" />
  <script src="js/Portal.js" type="text/javascript"></script>
  <script type="text/javascript">
   
    var portal = new nui.ux.Portal();
    portal.set({
        style: "width: 100%;height:400px",
        columns: [650, "100%"]
    });
    portal.render(document.body);

    //panel
    portal.setPanels([
        { column: 0, id: "p1", title: "流程待办", showCloseButton: false, showCollapseButton: true, body: "#panel1", height: 230 },
        { column: 0, id: "p2", title: "流程已办（最近一周）", showCloseButton: false, showCollapseButton: true, body: "#panel2", height: 230 },

        { column: 1, id: "p4", title: "流程未阅通知", showCloseButton: false, showCollapseButton: true, body: "#panel4", height: 150},
        { column: 1, id: "p5", title: "流程已阅通知（最近一周）", showCloseButton: false, showCollapseButton: true, body: "#panel5", height: 150},
        { column: 1, id: "p3", title: "消息提醒", showCloseButton: false, showCollapseButton: true, body: "#panel3", height: 150 },
        { column: 1, id: "p6", title: "信息发布", showCloseButton: false, showCollapseButton: true, body: "#panel6", height: 150 }
        
    ]);
    
    /* var bodyEl = portal.getPanelBodyEl("p2");
    bodyEl.appendChild(document.getElementById("Button2"));  */

    //获取配置的panels信息
    var panels = portal.getPanels();
    //alert(panels.length);
  
  
  
  //首页点击事项链接，跳转操作
  function onActionRenderer(e){
  	
  	 var grid = e.sender;
     var record = e.record;
     
     var s = "";
     
     if(e.column.name == "action1"){ //流程代办
     	var workItemName = record.WORKITEMNAME;
     	var workItemId = record.WORKITEMID;
     	//s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"/default/bps/wfclient/task/taskList.jsp?taskType=self\">"+workItemName+"</a>";
     	//alert(workItemId);
     	s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"/default/bps/wfclient/task/dispatchTaskExecute.jsp?workItemID="+workItemId+"\">"+workItemName+"</a>";
       
       
       // s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\" onclick=\"view('"+workItemId+"')\">"+workItemName+"</a>";
        //alert(s);
     }else if(e.column.name == "action2"){  //流程已办
     	var workItemName = record.workItemName;
     	    	
     //	s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"/default/bps/wfclient/task/taskList.jsp?taskType=finishedSelf\">"+workItemName+"</a>";
    s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\" onclick=\"viewyiban("+e.rowIndex+")\">"+workItemName+"</a> ";
    
     }else if(e.column.name == "action3"){  //消息提醒
     	var message = record.MESSAGE;
     	var addres_url = record.ADDRES_URL;
     	var id = record.ID;
     	s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"<%=request.getContextPath() %>"+addres_url+" \" onclick=\"myclick("+id+")\">"+message+"</a>";
     	//s = "<a class=\"dgBtn\" style=\"color:blue\" onclick=\"myclick("+record+")\" >"+message+"</a>";
     }else if(e.column.name == "action4"){  //未阅通知
     	var title = record.TITLE;
     	//s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"/default/bps/wfclient/task/notificationList.jsp?state=UNVIEWED\">"+title+"</a>";
      s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\" onclick=\"viewweiyue("+e.rowIndex+")\">"+title+"</a> ";
     }else if(e.column.name == "action5"){  //已阅通知
     	var title = record.TITLE;
     	var workitemid = record.NOTIFICATIONID;
     	//s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"/default/bps/wfclient/task/notificationList.jsp?state=VIEWED\">"+title+"</a>";
      s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\" onclick=\"viewyiyue("+workitemid+")\">"+title+"</a> ";
     }else if(e.column.name == "action6"){  //信息发布
     	var message = record.MESSAGE;
     	var addres_url = record.ADDRES_URL;
     	s = "<a class=\"dgBtn\" style=\"color:blue\" href=\"<%=request.getContextPath() %>"+addres_url+" \">"+message+"</a>";
     }else{
     }
     
     return s;
  }
  
  function myclick(id){
	  
	$.ajax({
	        url:"org.gocom.components.coframe.auth.Welcom.updateMesStatus.biz.ext",
	        type:'POST',
	        data:'id='+id,
		    cache:false,
		    async:false,
        	dataType:'json',
	        success:function(text){
	          
	        }
	  });
	  
  }
  
    function view(value)
  {
  
      nui.open({
	       url:"<%=request.getContextPath() %>/bps/wfclient/task/dispatchTaskExecute.jsp?workItemID="+value,
	       title:'查看',
	       width:1050,
	       height:500,
	       onload:function(){
	       },
	       ondestroy:function(action){
	       if(action=="ok"){
	       
	          window.location.reload();
	        }
	          }
	       });
  }
  
    function viewyiban(value)
  {
   var grid = nui.get("panel2");
   var row= grid.data[value];
  var id=row.workItemID;
    nui.open({
				url: "<%=request.getContextPath() %>/myinformation/matters/task.jsp?ItemID="+id,
				title: "查看",
				width: 1050,
				height: 550,
				onload: function () {
					var iframe = this.getIFrameEl();
					if(iframe.contentWindow.initData) {
						iframe.contentWindow.initData(row, "self", true);
					}	
				},
				ondestroy: function (action){
					if (action == "ok") {
						taskListDataGridObj.load();
					} else if (action == "execute") {  
						doOperate(rowIndex, false);					
					}
				}
			});
 
  }
  
   function viewweiyue(value)
  {
   var grid = nui.get("panel4");
   var row= grid.data[value];
  var id=row.NOTIFICATIONID;
    nui.open({
	       url:"<%=request.getContextPath() %>/myinformation/matters/ProUnviewedMessage_view.jsp?ID="+id,
	       title:'查看',
	       width:850,
	       height:250,
	       onload:function(){
	       
	       var json = nui.encode({map:row});
	       $.ajax({
		          url:"com.gotop.iimp.myinformation.matters.addProViewedMessage.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	
					
		          }
		        });
	       },
	       ondestroy:function(action){
	       if(action=="cancel"){
	          window.location.reload();
	        }
	          }
	       }); 
 
  }
    
    
      function viewyiyue(value)
  {
 
    nui.open({
	       url:"<%=request.getContextPath() %>/myinformation/matters/ProViewedMessage_view.jsp?ID="+value,
	       title:'查看',
	       width:850,
	       height:250,
	       onload:function(){
	       },
	       ondestroy:function(action){
	       if(action=="cancel"){
	          window.location.reload();
	        }
	          }
	       }); 
  
  }
  </script>
</body>
</html>
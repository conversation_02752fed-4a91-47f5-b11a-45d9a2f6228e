<%@page pageEncoding="UTF-8"%>
<%@page import="com.eos.system.utility.StringUtil"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page import="com.eos.data.datacontext.UserObject"%>
<%@page import="java.util.HashMap" %>
<%@page import="java.util.Map" %>
<%@page import="com.pfpj.foundation.database.DatabaseExt" %>
<%@page import="com.primeton.ext.engine.component.LogicComponentFactory" %>
<%@page import="com.eos.engine.component.ILogicComponent" %>

<!-- 
  - Author(s): wj
  - Date: 2018-08-31 09:58:59
  - Description: 为了记录登陆日志   以后要是不用这个的话，可以将logout_20180830bf。jsp这个的内容复制过来
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>退出系统</title>
</head>
<%
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
	String userid="";
	userid = userObject.getUserId();
	
	String ip = null;
	if (request.getHeader("x-forwarded-for") == null) {
		ip = request.getRemoteAddr();
	}
	
	//String sql = "org.gocom.components.coframe.userInfoManage.userInfoManage.insert_sysLog";
	//	Map<String, Object> map = new HashMap<String, Object>();
	//	map.put("OPE_MOD", "注销");//操作模块
	//	map.put("OPE_PER", userid);//操作人员
	//	map.put("OPE_CONTENT", "["+ip+"]"+"注销");//操作内容
	//	DatabaseExt.executeNamedSql("default", sql, map);
		
    //Java里调用biz
	HashMap<String, Object> map2 = new HashMap<String, Object>();
	String OPE_MOD="注销";
	String OPE_CONTENT="["+ip+"]"+"注销";
	try{
		 String componentName = "org.gocom.components.coframe.userInfoManage.userInfoManage";// 逻辑构件名称
	     String operationName = "addSysLog";// 逻辑流名称
	     ILogicComponent logicComponent = LogicComponentFactory.create(componentName);
	     int size = 2;  //表示1个参数
	     Object[] params = new Object[size];// 逻辑流的输入参数
	     params[0] = OPE_MOD;
	     params[1] = OPE_CONTENT;
	     logicComponent.invoke(operationName, params);
	}catch(Throwable e){
	}
	        
%>
<body>
  <%
     session.invalidate();
     String _url=StringUtil.htmlFilter(request.getParameter("_url"));
     
     if(_url==null){
     	_url=request.getContextPath()+"/coframe/auth/login/login.jsp";
     }
     response.sendRedirect(_url);
   %>
</body>
</html>
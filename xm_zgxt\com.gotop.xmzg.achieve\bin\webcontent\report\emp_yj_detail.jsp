<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Date: 2018-01-16
-->

<head>
	<title>发布公告信息</title>
	<!-- <meta http-equiv="content-type" content="text/html; charset=UTF-8" /> -->
	  <%@include file="/coframe/tools/skins/common.jsp" %>
	<script type="text/javascript" src="<%=request.getContextPath() %>/notice/commmodel_pro/js/jquery.form.js"></script> 
	<%@ page import="com.eos.data.datacontext.UserObject" %>
	<style type="text/css">
		body{
			width:100%;
			height: 150px;
			padding: 0px;
		}
		.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
	</style>
</head>
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");

 %>
<body>
<div class="search-condition">
		<div class="list">
<div id="form1">
		<input class="nui-hidden" name="queryData.TA_ID" id="queryData.TA_ID" />
		<input class="nui-hidden" name="queryData.TIP_CODE" id="queryData.TIP_CODE" />
		<input class="nui-hidden" name="queryData.TI_CODE" id="queryData.TI_CODE" />
		<input class="nui-hidden" name="queryData.TID_CODE" id="queryData.TID_CODE" />
		<input class="nui-hidden" name="queryData.TAIS_DATE" id="queryData.TAIS_DATE" />
		<input class="nui-hidden" name="queryData.TAIS_EMPCODE" id="queryData.TAIS_EMPCODE" />

</div>
	<div class="nui-toolbar" style="border-bottom:0;">
			<table style="width:100%">
				<tr>
					<td>
						<a class="nui-button" iconCls="icon-add" onclick="excel">导出Excel</a>								
					</td>
				</tr>
			</table>
	</div>
    <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:500px;" idField="id"
	  url="com.gotop.xmzg.achieve.report.emp_yj_detail.biz.ext"   
	  sizeList=[5,10,20,50,100] multiSelect="false" pageSize="10"
	  >
	    <div property="columns" >
	      	  	<div field="TA_NAME" >考核方案</div>
	            <div field="TAD_DATE" >统计日期</div>
	            <div field="TAD_PRODUCTDATE" >交易日期</div>
	            <div field="TAD_ORGNAME" >机构</div>
	            <div field="TAD_POSITION_NAME" >岗位</div>
	            <div field="TAD_EMPNAME" >客户经理名称</div>
	            <div field="TAD_EMPCODE" >客户经理编号</div>
	            <div field="TIP_NAME" >业务条线</div>
	            <div  field="TI_NAME" >指标</div>
	            <div  field="TAD_PRODUCTTYPE" >产品类型</div>
	            <div field="TAD_PRODUCTNO_TM" >产品编号</div>
	            <div field="TAD_PRODUCTNAME" >产品名称</div>
	            <div field="TAD_PRODUCTREMARK" >产品说明</div>
	            <div field="TAD_UNIT" >单位</div>
				<div field="TAD_ACHIEVE" >业绩</div>
				<div field="TAD_PROPOR" >分配比例</div>
				<div field="TAD_ACHIEVE_PRA" >实际业绩值</div>
				<div field="TAD_BASE" >基数（业绩基数）</div>
				<div field="TAD_PROPORTION" >积分标准</div>
				<div field="TAD_SCORE" >标准得分系数</div>
				<div field="TAD_IS_INTEGRAL" ">是否计算积分</div>

		
	    </div>
	 </div>
  </div>
	
</body>
</html>
<script type="text/javascript">
	nui.parse();
	var path = '<%=request.getContextPath() %>';
	var form = new nui.Form("#form1");
	var grid = nui.get("datagrid1");

    function setFormData(data){
        var infos = nui.clone(data);
		var json = infos.record;
		nui.get("queryData.TA_ID").setValue(json.map.TA_ID);
		nui.get("queryData.TIP_CODE").setValue(json.map.TIP_CODE);
		nui.get("queryData.TI_CODE").setValue(json.map.TI_CODE);
		nui.get("queryData.TID_CODE").setValue(json.map.TID_CODE);
		nui.get("queryData.TAIS_DATE").setValue(json.map.TAIS_DATE);
		nui.get("queryData.TAIS_EMPCODE").setValue(json.map.TAIS_EMPCODE);
		var data = form.getData(true,true);   
        grid.load(data);
    }   
    
    

    
    
    function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
	
    
   


	function formCancel(){
		CloseWindow("cancel");
	}
	
	<%-- 关闭窗口 --%>
	function CloseWindow(action){
		if(action=="close" && form1.isChanged()){
			if(confirm("数据已改变,是否先保存?")){
				return false;
			}
		}else if(window.CloseOwnerWindow){
			return window.CloseOwnerWindow(action);
		}else{
			return window.close();
		}
	}
	
	
	function excel(){
		      
		var form=new nui.Form("form1");
		form.validate();
	    if (form.isValid() == false) return;
		var data=form.getData(true, true);
		var fileName="业绩明细报表";
		var queryPath="com.gotop.xmzg.achieve.report.query_emp_yj_detail";
		var columns=grid.getBottomColumns();
		columns=columns.clone();
		for(var i=0;i<columns.length;i++){
			var column=columns[i];
			if(!column.field){
				columns.removeAt(i);
			}else{
				var c={header:column.header,field:column.field };
				columns[i]=c;
			}
		}
		var sheet = '业绩明细';
		
		columns=nui.encode(columns);
		data=nui.encode(data);
	    var url="<%=request.getContextPath()%>/achieve/report/exportExcelAc.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data+"&sheet="+sheet;
		window.location.replace(encodeURI(url));
		 
		 //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
	     setMask();
	} 
	
     
</script>

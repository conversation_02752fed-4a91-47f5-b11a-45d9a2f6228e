<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-07-26 12:01:11
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>公共群组管理</title>
</head>
<body style="width:100%;overflow:hidden;">
 
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	  <div id="c_orgid" class="nui-hidden"  name="queryData.c_orgid" ></div>
	  <div  class="nui-hidden"  id="c_role" ></div>
	    <table class="table" style="width:100%;">
	      <tr >
		     
			<th class="tit">群组名称：</th>
			<td>
				<input id="groupName" name="queryData.groupName" class="nui-textbox" style="width:200px;" vtype="maxLength:250"/>
			</td>	

	         <th ></th>
		     <td>		  
	            <a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;
	            <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
	         </td>
	        </tr>
	    </table>
	  </div>
    </div>
  </div>
  
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
             <a class="nui-button" iconCls="icon-add" onclick="add" id="add">新增</a> 
		     <a id="update" class="nui-button" iconCls="icon-edit" onclick="update">修改</a>
		     <a class="nui-button" iconCls="icon-remove" onclick="remove" id="rem">删除</a>
           </td>
        </tr>
      </table>
    </div>
 
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="org.gocom.components.coframe.userInfoManage.groupManageGg.query_groupManageGg.biz.ext"
	  sizeList=[5,10,20,50,100]  pageSize="10" onselectionchanged="selectionChanged">
	    <div property="columns" >
	      <div type="checkcolumn"></div>
	      <div field="GROUP_NAME" headerAlign="center" align="center">群组名称</div>
	      <div field="GROUP_DETAIL" headerAlign="center" align="center" width=150>群组描述</div>
	      <!-- <div field="IS_WHOLEBANKN" headerAlign="center" align="center">是否全行应用</div>-->    
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">
    nui.parse();
    var grid = nui.get("datagrid1");   
    
    //判断当前登录人是否是系统管理员
    $.ajax({
		        url:"org.gocom.components.coframe.userInfoManage.groupManage.isSysadmin.biz.ext",
		        type:'POST',
		        data:'',
		        cache:false,
		        async:false,
        		dataType:'json',
		        success:function(text){
		          //如果是系统管理员
		          if(text.flag == "1"){
		           nui.get("c_role").setValue("sysadmin");
		          }
		        }
		 });
     
    //grid.load();
    var a;
    
    
    function search(){

       var form = new nui.Form("#form1");
            form.validate();
            if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
    
   function add(){
       nui.open({
          url:"<%=request.getContextPath() %>/userInfoManage/groupManage/groupManageGg/groupManageGg_add.jsp",
          title:'新增',
          width:600,
          height:400,
          onload:function(){
          },
          ondestroy:function(action){
             if(action=="saveSuccess"){
	            grid.reload();
	         }
          }
       });
    }
    
    function update(){
       var row = grid.getSelected();
       if(row!=null){
          var json = nui.encode({map:row});
          var c_role = nui.get("c_role").getValue();
          
          /*
          var a= nui.loading("正在检测登录人身份，请稍等...","提示");
          if(c_role != "sysadmin"){
	           if(checkisExist(json) == '1')
			   {
			     nui.hideMessageBox(a);
			     nui.alert("该群组为全行群组，只有系统管理员才执行修改操作！");		     
			     return false;
			   }
          }
          nui.hideMessageBox(a);*/
	       nui.open({
	          url:"<%=request.getContextPath() %>/userInfoManage/groupManage/groupManageGg/groupManageGg_update.jsp",
	          title:'编辑',
	          width:600,
	          height:400,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             
	             //方法1：后台查询一次，获取信息回显
	             /* var data = row;
	             iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
	             
	             //方法2：直接从页面获取，不用去后台获取
	               var data = {pageType:"edit",record:{map:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }else{
           nui.alert("请选中一条记录！");
       }
    }
    
 
    
    function selectionChanged(){
       var rows = grid.getSelecteds();
       if(rows.length>1){
           nui.get("update").disable();
       }else{
           nui.get("update").enable();
       }
    }
    
    
    function remove(){
      var row = grid.getSelected();
      if(row!=null){
        var json = nui.encode({map:row});
       
	     /*
	     var c_role = nui.get("c_role").getValue();
	     var a= nui.loading("正在检测登录人身份，请稍等...","提示");
	     if(c_role != "sysadmin"){
	           if(checkisExist(json) == '1')
			   {
			     nui.hideMessageBox(a);
			     nui.alert("该群组为全行群组，只有系统管理员才执行删除操作！");	     
			     return false;
			   }
          }
          nui.hideMessageBox(a);*/
	     
         nui.confirm("确定删除选中记录？","系统提示",
           function(action){
             if(action=="ok"){
	           var json = nui.encode({map:row});
	           var a= nui.loading("正在删除中,请稍等...","提示");
		        $.ajax({
		          url:"org.gocom.components.coframe.userInfoManage.groupManageGg.delete_groupManagerGg.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	nui.hideMessageBox(a);
		            var returnJson = nui.decode(text);
					if(returnJson.exception == null && returnJson.iRtn == 1){
						nui.alert("删除成功", "系统提示", function(action){
							grid.reload();
						});
					}else{
						nui.alert("删除失败", "系统提示");
						grid.unmask();
					}
					
		          }
		        });
		     }
		   });
      }else{
        nui.alert("请选中一条记录！");
      }
    }
   
    
    //判断该群组为全行群组时，只有系统管理员才执行删除操作
    function checkisExist(map){
      var vala;
      $.ajax({
        url:"org.gocom.components.coframe.userInfoManage.groupManage.checkIsExit.biz.ext",
        type: 'POST',
        data:map,
        cache: false,
        async:false,
        contentType:'text/json',
        success:function(text){
          vala = text.res;
        }
      });
      return vala;
    }
    
    //重置
    function clean(){
       nui.get("groupName").setValue("");
     }

  </script>
</body>
</html>
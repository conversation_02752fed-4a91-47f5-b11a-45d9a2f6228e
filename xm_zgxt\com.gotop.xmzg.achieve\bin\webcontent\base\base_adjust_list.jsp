<%@page pageEncoding="UTF-8"%>	
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2019-06-04 14:51:16
  - Description:
-->
<head>
	<title>业绩基数查询</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%@include file="/achieve/init.jsp"%>
   	<style type="text/css">
   		.tit{
   			text-align: right;
   		}
   	</style>
</head>

<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="send_bw" class="nui-form">
	    <table class="table" style="width:100%;">
	        <tr>
	        	<th class="tit">调整类型：</th> 
		        <td>
		        	<input class="nui-dictcombobox" id="tba_type" valueField="dictID" textField="dictName" 
		        	style="width:200px;" value="0" onvaluechanged="onTbaTypeChanged"
					dictTypeId="JF_TZLX" name="obj/tba_type"/>
		        </td>
		        <th class="empcode tit">客户经理code：</th> 
		        <td class="empcode">
		        	<input class="nui-buttonedit" allowInput="false" onbuttonclick="selectEmp" id="tba_empcode" name="obj/tba_empcode" style="width:200px;"/>
		        </td>
		        <th class="orgcode tit">机构号：</th> 
		        <td class="orgcode">
		        	<input id="btnEdit1" class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:200px;"/>
      				<input class="nui-hidden" id="tba_orgcode" name="obj/tba_orgcode"/>
		        </td>
		        <th class="userno tit">产品编号：</th> 
		        <td class="userno">
		        	<input class="nui-textbox" id="tba_userno" name="obj/tba_userno" style="width:200px;"/>
		        </td>
		        <th class="tit" >考核方案日期：</th>
				<td>
					<input id="tba_time_start" name = "obj/tba_time_start" class="nui-datepicker"  style="width:130px;" allowInput="false" format="yyyyMMdd"/> ~
					<input id="tba_time_end" name = "obj/tba_time_end" class="nui-datepicker"  style="width:130px;"  allowInput="false" format="yyyyMMdd"/>
				</td>
				<td>
					<a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;
					<a class="nui-button" iconCls="icon-reset"  onclick="clean">重置</a>
				</td>
		    </tr>
	    </table>
	  </div>
    </div>
  </div>
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
             <a class="nui-button" iconCls="icon-add" onclick="add">新增</a>
             <a class="nui-button" iconCls="icon-edit" onclick="update">修改</a>
             <a class="nui-button" iconCls="icon-collapse" onclick="detail">详情</a>
             <a class="nui-button" iconCls="icon-remove" onclick="del">删除</a>
           </td>
        </tr>
      </table>
    </div>

  <div class="nui-fit">
	 <div id="bw_grid" class="nui-datagrid"
			 style="height: 100%;"
			 idField="id" 
			 totalField="page.count"  
			 showPageInfo="true"
			 allowResize="false"  
			 pageSize="20"  
			 allowUnselect="true"
			 multiSelect="true"
			 sizeList=[5,10,20,50,100] 
	         dataField="list"
			 url="com.gotop.xmzg.achieve.base.base_adjust_list.biz.ext">
	    <div property="columns" >
	      	<div type="checkcolumn"></div> 
	      	<div field="TIP_CODE" visible="false"></div>
	      	<div field="TBA_ID" visible="false"></div>
	      	<div field="TBA_EMPCODE" visible="false"></div>
	      	<div field="TBA_ORGCODE" visible="false"></div>
	      	<div field="TI_CODE" headerAlign="center" align="center" allowSort="true" >指标</div>
		  	<div field="TI_NAME" headerAlign="center" align="center" allowSort="true" >指标名称</div>
		  	<div field="TID_CODE" headerAlign="center" align="center" allowSort="true" >指标细项</div>
		  	<div field="TID_NAME" headerAlign="center" align="center" allowSort="true" >指标细项名称</div>
		  	<div field="TA_ID" headerAlign="center" align="center" allowSort="true" >考核方案</div>
		  	<div field="TA_NAME" headerAlign="center" align="center" allowSort="true" >考核方案名称</div>
			<div field="TBA_TYPE" headerAlign="center" align="center" allowSort="true" renderer="tba_type">调整类型</div>
			<div field="TBA_EMPNAME" headerAlign="center" align="center" allowSort="true" >客户经理</div>
			<div field="TBA_ORGNAME" headerAlign="center" align="center" allowSort="true" >机构名称</div>
			<div field="TBA_USERNO" headerAlign="center" align="center" allowSort="true" >产品编号</div>
			<div field="TBA_TIME_START" headerAlign="center" align="center" allowSort="true" >开始日期</div>
			<div field="TBA_TIME_END" headerAlign="center" align="center" allowSort="true" >结束日期</div>
			<div field="TBA_BASE" headerAlign="center" align="center" allowSort="true" >调整基数</div>
			<div field="TBA_REMARK" headerAlign="center" align="center" allowSort="true" >调整原因</div>
	    </div>
	 </div>
  </div>
  
 <script type="text/javascript">
    nui.parse();
    $(".empcode,.orgcode").hide();
    function onTbaTypeChanged(e){
    	var obj = e.value;//userno
    	if(obj == "0"){
    		$(".userno").show();
    		$(".empcode,.orgcode").hide();
    	}else if(obj == "1"){
    		$(".empcode").show();
    		$(".userno,.orgcode").hide();
    	}else if(obj == "2"){
    		$(".orgcode").show();
    		$(".userno,.empcode").hide();
    	}
    }
    
    
    var grid = nui.get("bw_grid");
    var form = new nui.Form("#send_bw");
	search();
	//查询
    function search() {
    	var form = new nui.Form("#send_bw");
    	form.validate();
    	if (form.isValid() == false) return;
    	var startDate = nui.get("tba_time_start").getFormValue();
    	var endDate = nui.get("tba_time_end").getFormValue();
		if(!isNullOrEmpty(startDate) && !isNullOrEmpty(endDate)){
			if(startDate>endDate){
	   			nui.alert("结束日期必须大于开始日期");
	   			return;
	       	}
		}
      	grid.load(form.getData());
      	//插入系统操作日志
        var OPE_MOD = "业绩基数管理";
    	var OPE_CONTENT = "查询,输入项:"+nui.get("tba_type").getValue()+"|"+nui.get("tba_empcode").getValue()
    	+"|"+nui.get("tba_orgcode").getValue()+"|"+nui.get("tba_userno").getValue()
    	+"|"+nui.get("tba_time_start").getValue()+"|"+nui.get("tba_time_end").getValue();
      	insert_sysope_log(OPE_MOD,OPE_CONTENT);
	}
	//转换时间
    function setdate(e){
 		var date = e.record.TIP_CREATETIME;
 		if(!isNullOrEmpty(date) && date.length == 14){
 			return changeDate(date);
 		}else{
 			return "";
 		}
 	}
	
  	//调整类型
    function tba_type(e){
		var tba_type = e.record.TBA_TYPE;
		return nui.getDictText("JF_TZLX",tba_type);
	}
	
    //打开添加页面
	function add(){
		nui.open({
			url:bactpath+"/achieve/base/base_adjust_add.jsp",
			title:"新增业绩基数",
			width:800,
			height:300,
			onload:function(){},
			ondestroy:function(action){
				if(action=="saveSuccess"){
	                grid.reload();
	            }
			}
		});
	}
	
	//打开修改页面
	function update(){
		var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
			nui.open({
				url:bactpath+"/achieve/base/base_adjust_update.jsp",
				title:"修改业绩基数",
				width:800,
				height:300,
				onload:function(){
					var iframe = this.getIFrameEl();
 	      	    	iframe.contentWindow.setData(rows[0]);
				},
				ondestroy:function(action){
					if(action=="saveSuccess"){
		                grid.reload();
		            }
				}
			});
		}
	}
	//详情
	function detail(){
       var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
	       nui.open({
	          url:bactpath+"/achieve/base/base_adjust_info.jsp",
	          title:'详情',
	          width:800,
	          height:300,
	          onload:function(){
	        	  var iframe = this.getIFrameEl();
	      	      iframe.contentWindow.setData(rows[0]);
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }
    }
	//删除
	function del(){
		var rows = grid.getSelecteds();
		if(rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
			nui.confirm("确定删除以下选择的数据？", "确定？",
	           function (action) {
	             if (action == "ok") {
	            	 var newMaps = [];
	            	 var str = "";
	            	 for(var i = 0;i<rows.length;i++){
	            		 var ids = {tba_id:rows[i].TBA_ID};
	            		 newMaps.push(ids);
	            		 if(i==rows.length-1){
	            			 str+=rows[i].TBA_ID;
	            		 }else{
	            			 str+=rows[i].TBA_ID+",";
	            		 }
	            	 }
	            	 str = "删除以下【"+str+"】业绩基数";
	            	 var json = nui.encode({maps:newMaps,str:str});
	            	 var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
	            	 //提交数据
                     nui.ajax({
                        url: "com.gotop.xmzg.achieve.base.base_adjust_del.biz.ext",
                        type: "post",
                        data: json,
                        contentType:'text/json',
                        success: function (text) {
                           nui.hideMessageBox(load);  //隐藏遮罩层
                     	   var code = text.code;
                     	   var msg = text.msg;
                     	   nui.alert(msg, "系统提示", function(action){});
                     	   grid.reload();
                        }
                    });
	             }
			});
		}
	}
	//插入系统操作日志
    function insert_sysope_log(OPE_MOD,OPE_CONTENT){
		$.ajax({
				url: "com.gotop.xmzg.achieve.indicators.insert_sysope_log.biz.ext",
				type: 'POST',
				data : nui.encode({"OPE_MOD":OPE_MOD,"OPE_CONTENT":OPE_CONTENT}),
				cache: false,
				contentType:'text/json',
				success: function (res) {  
				}
		});
	}
	//重置
	function clean(){
		form.reset();
 	}
	
	//机构树回显
    function OrgonButtonEdit(e) {
       var btnEdit = this;
       nui.open({
           url:bactpath+"/achieve/common/org_tree.jsp",
           showMaxButton: false,
           title: "选择树",
           width: 350,
           height: 350,
           ondestroy: function (action) {                    
               if (action == "ok") {
                   var iframe = this.getIFrameEl();
                   var data = iframe.contentWindow.GetData();
                   data = nui.clone(data);
                   if (data) {
                       btnEdit.setValue(data.ORGCODE);
                       btnEdit.setText(data.TEXT);
                       nui.get("tba_orgcode").setValue(data.ORGCODE);
                   }
               }
           }
       });
     }
	
  	//人员树回显
    function selectEmp(e){
    	var ele = e.sender.id;
		var emp = nui.get(ele);
        nui.open({
            url:bactpath+"/files/archiveList/empTree.jsp?type=emp",
            title: "选择人员",
            width: 600,
            height: 400,
            onload:function(){
            	var frame = this.getIFrameEl();
            	var data = {};
            	data.value = emp.getValue();
            	//frame.contentWindow.setData(data);
            	frame.contentWindow.setTreeCheck(data.value);
            },
            ondestroy: function (action) {
                //if (action == "close") return false;
                if (action == "ok") {
                    var iframe = this.getIFrameEl();
                    var data = iframe.contentWindow.getData();
                    data = nui.clone(data);
                    //debugger;    //必须
                    if (data) {
                        emp.setValue(data.nodeId);
                        emp.setText(data.nodeName);
                        //将值人员id转换成人员code
                        var data={empId:data.nodeId};
				        var json = nui.encode(data);
				        var URL="com.gotop.xmzg.files.fileLedger.getEmpcode.biz.ext";
				        $.ajax({
				        	url: URL,
				            type: 'POST',
				            data: json,
				            async:false,
				            cache: false,
				            contentType:'text/json',
				            success: function (text){
				                var returnJson = nui.decode(text);
				                var result = returnJson.resultList;
				                emp.setValue(result[0].EMPCODE);
						    }
				  		});
                    }
                }

            }
        });
	}  
	 
  </script>
</body>

</html>
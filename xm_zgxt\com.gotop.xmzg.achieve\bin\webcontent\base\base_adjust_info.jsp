<%@page pageEncoding="UTF-8"%>	
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2019-06-04 14:51:16
  - Description:
-->
<head>
	<title>业绩基数修改</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%@include file="/achieve/init.jsp"%>
	<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
</head>
<body>
  <div id="prameter" style="padding-top:5px;"> 
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table" align="center"> 
    <tbody>
     <tr> 
      <th class="nui-form-label" style="width:20%">业务条线：</th> 
      <td style="width:30%">
		<input class="nui-textbox" id="tip_name" name="tip_name" style="width:200px;"/>
      	<input class="nui-hidden" id="tip_code" name="tip_code"/>
      </td>
      <th class="nui-form-label" style="width:20%">指标代码：</th> 
      <td style="width:30%">
		<input class="nui-textbox" id="ti_name" name="ti_name" style="width:200px;"/>
      	<input class="nui-hidden" id="ti_code" name="ti_code"/>
      </td>
     </tr>
     <tr> 
      <th class="nui-form-label">指标明细代码：</th>
      <td>
		<input class="nui-textbox" id="tid_name" name="tid_name"  style="width:200px;"/>
      	<input class="nui-hidden" id="tid_code" name="tid_code"/>
      </td>
      <th class="nui-form-label">考核方案：</th> 
      <td>
      	<input class="nui-textbox" id="ta_name" name="ta_name" style="width:200px;"/>
      	<input class="nui-hidden" id="ta_id" name="ta_id"/>
      </td>
     </tr>
     <tr> 
      <th class="nui-form-label">调整类型：</th> 
      <td>
      	<input class="nui-dictcombobox" id="tba_type" valueField="dictID" textField="dictName" 
      		style="width:200px;" onvaluechanged="onTbaTypeChanged"
			dictTypeId="JF_TZLX" name="tba_type"  required="true"/>
      </td> 
      <th class="nui-form-label txt"><font class="empcode b_red">*</font>客户经理：</th> 
      <td>
      	<div class="empcode">
      		<input class="nui-textbox" id="tba_empname" name="tba_empname" style="width:200px;"/>
      		<input class="nui-hidden" id="tba_empcode" name="tba_empcode"/>
      	</div>
      	<div class="orgcode">
      		<input class="nui-textbox" id="tba_orgname" name="tba_orgname" style="width:200px;"/>
      		<input class="nui-hidden" id="tba_orgcode" name="tba_orgcode"/>
      	</div>
      	<div class="userno">
      		<input class="nui-textbox" id="tba_userno" name="tba_userno" style="width:200px;" required="true"/>
      	</div>
      </td> 
     </tr>
     <tr> 
      <th class="nui-form-label">开始日期：</th> 
      <td>
      	<input id="tba_time_start" name = "tba_time_start" class="nui-datepicker" style="width:200px;" allowInput="false"/>
      </td>
      <th class="nui-form-label">结束日期：</th> 
      <td>
      	<input id="tba_time_end" name = "tba_time_end" class="nui-datepicker"  style="width:200px;"  allowInput="false"/>
      </td> 
     </tr>
     <tr> 
     <th class="nui-form-label">调整基数：</th> 
      <td colspan="3">
      	<input class="nui-spinner" id="tba_base" name="tba_base" style="width:200px;" minValue="0" maxValue="99999999" format="#,0.00"/>
      	<input class="nui-hidden" id="tba_id" name="tba_id"/>
      </td> 
     </tr>
     <th class="nui-form-label">调整原因：</th> 
      <td colspan="3">
      	<input class="nui-textarea" id="tba_remark" name="tba_remark" style="width:600px;"/>
      </td> 
     </tr>
    </tbody>
   </table> 
   <div class="nui-toolbar" style="padding:0px;" borderstyle="border:0;"> 
    <table width="100%"> 
     <tbody>
      <tr> 
       	<td style="text-align:center;"> 
	       	<a class="nui-button" iconcls="icon-cancel" onclick="onCancel">关闭</a>
       	</td> 
      </tr> 
     </tbody>
    </table> 
   </div> 
  </div>


  <script type="text/javascript">
  	nui.parse();
  	var form = new mini.Form("prameter");
  	init();
  	function init(){
  		var fields = form.getFields();                
        for (var i = 0, l = fields.length; i < l; i++) {
            var c = fields[i];
            if (c.setReadOnly) c.setReadOnly(true);     //只读
            if (c.setIsValid) c.setIsValid(true);      //去除错误提示
            if (c.addCls) c.addCls("asLabel");          //增加asLabel外观
        }
  	}
  	function setData(data){
  		//跨页面传递的数据对象，克隆后才可以安全使用
        data = nui.clone(data);
        data = lowerJson(data);
        var form = new nui.Form("#prameter");
        form.setData(data);
        /* nui.get("tip_name").disable();
        nui.get("ti_name").disable();
        nui.get("tid_name").disable();
        nui.get("ta_name").disable();
        nui.get("tba_type").disable();
        nui.get("tba_empname").disable();
        nui.get("tba_orgname").disable();
        nui.get("tba_userno").disable(); */
        onTbaTypeChanged();
  	}
  	function onTbaTypeChanged(){
    	var obj = nui.get("tba_type").getValue();
    	if(obj == "0"){
    		$(".userno").show();
    		$(".empcode,.orgcode").hide();
    		$(".txt").html("产品编号：");
    	}else if(obj == "1"){
    		$(".empcode").show();
    		$(".userno,.orgcode").hide();
    		$(".txt").html("客户经理：");
    	}else if(obj == "2"){
    		$(".orgcode").show();
    		$(".userno,.empcode").hide();
    		$(".txt").html("机构号：");
    	}
    }
  	
  	
  	//获取指标
  	function onTipCodeChanged(){
  		var json = nui.encode({tip_code:nui.get("tip_code").getValue(),type:2});
	   	//提交数据
        nui.ajax({
           url: "com.gotop.xmzg.achieve.indicators.indicators_choice.biz.ext",
           type: "post",
           data: json,
           contentType:'text/json',
           success: function (text) {
        	  var obj = text.list;
              nui.get("ti_code").setData(obj);
           }
       });
  	}
  	//获取指标明细
  	function onTiCodeChanged(){
  		var json = nui.encode({ti_code:nui.get("ti_code").getValue()});
	   	//提交数据
        nui.ajax({
           url: "com.gotop.xmzg.achieve.indicators.indicators_detail_choice.biz.ext",
           type: "post",
           data: json,
           contentType:'text/json',
           success: function (text) {
        	  var obj = text.list;
              nui.get("tid_code").setData(obj);
           }
       });
  	}
  	
  	//获取考核方案
  	function onTidCodeChanged(){
  		var json = nui.encode({tid_code:nui.get("tid_code").getValue()});
	   	//提交数据
        nui.ajax({
           url: "com.gotop.xmzg.achieve.base.base_adjust_choice.biz.ext",
           type: "post",
           data: json,
           contentType:'text/json',
           success: function (text) {
        	  var obj = text.list;
              nui.get("ta_id").setData(obj);
           }
       });
  	}
  	
  	
  </script>
</body>
</html>
<%@page pageEncoding="UTF-8"%>	
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2019-06-04 14:51:16
  - Description:
-->
<head>
	<title>人员岗位修改</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%@include file="/achieve/init.jsp"%>
	<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
</head>
<body>
  <div id="prameter" style="padding-top:5px;"> 
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table" align="center"> 
    <tbody>
     <tr> 
      <input id="TE_POSITION_OLD" name = "TE_POSITION_OLD" class="mini-hidden"/>
      <input id="TE_ORGCODE_OLD" name = "TE_ORGCODE_OLD" class="mini-hidden"/>
      <input id="TE_EMPCODE" name = "TE_EMPCODE" class="mini-hidden"/>
      <th class="nui-form-label"><font class="b_red">*</font>员工：</th> 
      <td><input id="TE_EMPNAME" name = "TE_EMPNAME"  class="nui-textbox" allowInput="false" style="width:150px;" required="true" /></td> 
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>岗位信息：</th> 
      <td><input class="nui-dictcombobox" valueField="dictID" textField="dictName" id="TE_POSITION" name="TE_POSITION" dictTypeId="JF_POSITION" style="width:150px;" required="true"/></td> 
     </tr>
   	<tr> 
      <th class="nui-form-label"><font class="b_red">*</font>机构号：</th> 
      <td><input class="nui-buttonedit" id="TE_ORGCODE" name="TE_ORGCODE" allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;" required="true"/></td> 
     </tr>
    </tbody>
   </table> 
   <div class="nui-toolbar" style="padding:0px;" borderstyle="border:0;"> 
    <table width="100%"> 
     <tbody>
      <tr> 
       	<td style="text-align:center;"> 
	       	<a class="nui-button" iconcls="icon-search" onclick="seve">提交</a> 
	       	<span style="display:inline-block;width:25px;"></span> 
	       	<a class="nui-button" iconcls="icon-cancel" onclick="onCancel">取消</a>
       	</td> 
      </tr> 
     </tbody>
    </table> 
   </div> 
  </div>

  <script type="text/javascript">
  	nui.parse();
  	
  		function setData(data){
  		//跨页面传递的数据对象，克隆后才可以安全使用
        data = nui.clone(data);
        var form = new nui.Form("#prameter");
        form.setData(data);
        console.log(data.TE_EMPCODE);
        console.log(data.EMPNAME);
        nui.get("TE_EMPNAME").setValue(data.EMPNAME);
        nui.get("TE_POSITION").setValue(data.TE_POSITION);
        nui.get("TE_POSITION").setText(data.DICTNAME);
        nui.get("TE_ORGCODE").setValue(data.TE_ORGCODE);
        nui.get("TE_ORGCODE").setText(data.ORGNAME);
        nui.get("TE_ORGCODE_OLD").setValue(data.TE_ORGCODE);
  	}
  	
  	function seve(){
  		var form = new nui.Form("#prameter");
		form.validate();
		if (form.isValid() == false) return;
		var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
		//提交数据
        nui.ajax({
       		url: "com.gotop.xmzg.achieve.personnel.personnel_update.biz.ext",
           	type: "post",
           	data: nui.encode({"obj":form.getData()}),
           	contentType:'text/json',
           	success: function (text) {
        	   nui.hideMessageBox(load);  //隐藏遮罩层
        	   var code = text.code;
        	   var msg = text.msg;
        	   if(code != "1"){
        		  	nui.alert(msg, "系统提示");
	           }else{
          			nui.alert(msg, "系统提示", function(action){
						if(action == "ok"){
							CloseWindow("saveSuccess");
						}
					});
	          }
           }
       });
  	}
  	
  	//机构树点击事件
	function OrgonButtonEdit(e) {	
        var btnEdit = this;
        nui.open({
            url:"<%=request.getContextPath() %>/achieve/report/org_tree.jsp",
        showMaxButton: false,
        title: "选择树",
        width: 350,
        height: 350,
        ondestroy: function (action) {                    
            if (action == "ok") {
                var iframe = this.getIFrameEl();
                var data = iframe.contentWindow.GetData();
                data = nui.clone(data);
                if (data) {
                    btnEdit.setValue(data.CODE);
                    btnEdit.setText(data.TEXT);
                }
            }
        }
    	});            
	}
  
  </script>
</body>
</html>
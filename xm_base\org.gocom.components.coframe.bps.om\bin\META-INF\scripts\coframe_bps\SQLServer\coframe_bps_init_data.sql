INSERT INTO APP_FUNCGROUP (<PERSON>UN<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>NA<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON>UN<PERSON><PERSON><PERSON><PERSON>SEQ,ISLEAF,SU<PERSON>OUNT,APP_ID,TENANT_ID,PARENTGROUP,APPID) VALUES (7,'工作流程',1,'.7.','n',0,NULL,'default',NULL,1);

INSERT INTO APP_FUNCTION (FUNCCODE,FUNCNAME,FUNCDESC,FUNCACTION,PARAINFO,ISCHECK,FUNCTYPE,ISMENU,APP_ID,TENANT_ID,FUNCGROUPID) VALUES ('my_task','我的任务',NULL,'/bps/wfclient/common/mytasks.jsp',NULL,'1','page','1',NULL,'default',7);
INSERT INTO APP_FUNCTION (FUNCCODE,FUNCNAME,FUNCDESC,FUNCACTION,PARAINFO,ISCH<PERSON>K,FUN<PERSON>YP<PERSON>,ISMENU,<PERSON>P_ID,TENANT_ID,<PERSON>UN<PERSON><PERSON>OUP<PERSON>) VALUES ('my_process','我的流程',NULL,'/bps/wfclient/common/myprocesses.jsp',NULL,'1','page','1',NULL,'default',7);
INSERT INTO APP_FUNCTION (FUNCCODE,FUNCNAME,FUNCDESC,FUNCACTION,PARAINFO,ISCHECK,FUNCTYPE,ISMENU,APP_ID,TENANT_ID,FUNCGROUPID) VALUES ('start_process','启动流程',NULL,'/bps/wfclient/process/processManager.jsp',NULL,'1','page','1',NULL,'default',7);
INSERT INTO APP_FUNCTION (FUNCCODE,FUNCNAME,FUNCDESC,FUNCACTION,PARAINFO,ISCHECK,FUNCTYPE,ISMENU,APP_ID,TENANT_ID,FUNCGROUPID) VALUES ('set_agent','代理设置',NULL,'/bps/wfclient/agent/agentList.jsp',NULL,'1','page','1',NULL,'default',7);
INSERT INTO APP_FUNCTION (FUNCCODE,FUNCNAME,FUNCDESC,FUNCACTION,PARAINFO,ISCHECK,FUNCTYPE,ISMENU,APP_ID,TENANT_ID,FUNCGROUPID) VALUES ('query_agent','查看代理',NULL,'/bps/wfclient/agent/myAgent.jsp',NULL,'1','page','1',NULL,'default',7);

INSERT INTO APP_MENU (MENUID,MENUNAME,MENULABEL,MENUCODE,ISLEAF,PARAMETER,UIENTRY,MENULEVEL,ROOTID,DISPLAYORDER,IMAGEPATH,EXPANDPATH,MENUSEQ,OPENMODE,SUBCOUNT,APPID,FUNCCODE,APP_ID,TENANT_ID,PARENTSID) VALUES ('9','工作流程','工作流程','menu_process','0',NULL,NULL,1,NULL,3,NULL,NULL,'.9.',NULL,4,NULL,NULL,NULL,'default',NULL);
INSERT INTO APP_MENU (MENUID,MENUNAME,MENULABEL,MENUCODE,ISLEAF,PARAMETER,UIENTRY,MENULEVEL,ROOTID,DISPLAYORDER,IMAGEPATH,EXPANDPATH,MENUSEQ,OPENMODE,SUBCOUNT,APPID,FUNCCODE,APP_ID,TENANT_ID,PARENTSID) VALUES ('11','我的任务','我的任务','menu_my_task','1',NULL,NULL,2,NULL,1,NULL,NULL,'.9.11.',NULL,0,NULL,'my_task',NULL,'default','9');
INSERT INTO APP_MENU (MENUID,MENUNAME,MENULABEL,MENUCODE,ISLEAF,PARAMETER,UIENTRY,MENULEVEL,ROOTID,DISPLAYORDER,IMAGEPATH,EXPANDPATH,MENUSEQ,OPENMODE,SUBCOUNT,APPID,FUNCCODE,APP_ID,TENANT_ID,PARENTSID) VALUES ('12','我的流程','我的流程','menu_my_process','1',NULL,NULL,2,NULL,2,NULL,NULL,'.9.12.',NULL,0,NULL,'my_process',NULL,'default','9');
INSERT INTO APP_MENU (MENUID,MENUNAME,MENULABEL,MENUCODE,ISLEAF,PARAMETER,UIENTRY,MENULEVEL,ROOTID,DISPLAYORDER,IMAGEPATH,EXPANDPATH,MENUSEQ,OPENMODE,SUBCOUNT,APPID,FUNCCODE,APP_ID,TENANT_ID,PARENTSID) VALUES ('13','启动流程','启动流程','menu_start_process','1',NULL,NULL,2,NULL,3,NULL,NULL,'.9.13.',NULL,0,NULL,'start_process',NULL,'default','9');
INSERT INTO APP_MENU (MENUID,MENUNAME,MENULABEL,MENUCODE,ISLEAF,PARAMETER,UIENTRY,MENULEVEL,ROOTID,DISPLAYORDER,IMAGEPATH,EXPANDPATH,MENUSEQ,OPENMODE,SUBCOUNT,APPID,FUNCCODE,APP_ID,TENANT_ID,PARENTSID) VALUES ('14','代理设置','代理设置','menu_set_agent','1',NULL,NULL,2,NULL,4,NULL,NULL,'.9.14.',NULL,0,NULL,'set_agent',NULL,'default','9');
INSERT INTO APP_MENU (MENUID,MENUNAME,MENULABEL,MENUCODE,ISLEAF,PARAMETER,UIENTRY,MENULEVEL,ROOTID,DISPLAYORDER,IMAGEPATH,EXPANDPATH,MENUSEQ,OPENMODE,SUBCOUNT,APPID,FUNCCODE,APP_ID,TENANT_ID,PARENTSID) VALUES ('15','查看代理','查看代理','menu_query_agent','1',NULL,NULL,2,NULL,5,NULL,NULL,'.9.15.',NULL,0,NULL,'query_agent',NULL,'default','9');

INSERT INTO CAP_RESAUTH (PARTY_ID,PARTY_TYPE,RES_ID,RES_TYPE,TENANT_ID,RES_STATE,PARTY_SCOPE,CREATEUSER,CREATETIME) VALUES ('1','role','my_task','function','default','1','0','sysadmin',null);
INSERT INTO CAP_RESAUTH (PARTY_ID,PARTY_TYPE,RES_ID,RES_TYPE,TENANT_ID,RES_STATE,PARTY_SCOPE,CREATEUSER,CREATETIME) VALUES ('1','role','my_process','function','default','1','0','sysadmin',null);
INSERT INTO CAP_RESAUTH (PARTY_ID,PARTY_TYPE,RES_ID,RES_TYPE,TENANT_ID,RES_STATE,PARTY_SCOPE,CREATEUSER,CREATETIME) VALUES ('1','role','start_process','function','default','1','0','sysadmin',null);
INSERT INTO CAP_RESAUTH (PARTY_ID,PARTY_TYPE,RES_ID,RES_TYPE,TENANT_ID,RES_STATE,PARTY_SCOPE,CREATEUSER,CREATETIME) VALUES ('1','role','set_agent','function','default','1','0','sysadmin',null);
INSERT INTO CAP_RESAUTH (PARTY_ID,PARTY_TYPE,RES_ID,RES_TYPE,TENANT_ID,RES_STATE,PARTY_SCOPE,CREATEUSER,CREATETIME) VALUES ('1','role','query_agent','function','default','1','0','sysadmin',null);

<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): liuyl
  - Date: 2019-12-10 10:25:40
  - Description:
-->
<head>
<title>入库记录选择</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
    <style type="text/css">
    html,body
    {
        padding:0;
        margin:0;
        border:0;     
        width:100%;
        height:100%;
        overflow:hidden;   
    }
    </style>
</head>
<body>
	 <div class="nui-toolbar" style="text-align:center;line-height:30px;" borderStyle="border:0;">
 		<div id="form">
				<div class="nui-hidden"  name="condition.userOrgId" id="userOrgId"></div>
				<div class="nui-hidden"  name="condition.userId"  id="userId" ></div>
		   			<table class="table" style="width:100%;">
		      		<tr>
						<td align="right">档案种类：</td>
						<td>
							<input  class="nui-dictcombobox" id="filesType" name="queryData.filesType"  emptyText="请选择"  dictTypeId="FILES_TYPE" style="width:160px;"/>
						</td>
						<td align="right">档案名称：</td>
						<td>
							<input name="queryData.filesName"  class="nui-textbox" style="width:160px;"/>
						</td>
						<td align="right">档案状态：</td>
						<td>
							<input  class="nui-dictcombobox" name="queryData.fileStatus"  emptyText="请选择" dictTypeId="FILES_STATUS" style="width:160px;"/>
						</td>
						</tr>
						<tr>
						<td align="right">库存地址：</td>
						<td>
							<input  class="nui-dictcombobox" name="queryData.storageAddr"  emptyText="请选择" dictTypeId="FILES_STORAGE_ADDRESS" style="width:160px;"/>
						</td>
			        	<td align="right">货架号/箱号：</td>
						<td>
							<input name="queryData.storageLocation"  class="nui-textbox" style="width:160px;"/>
						</td>
						<td align="right"></td>
						<td >
							<a class="nui-button" iconCls="icon-search" onclick="searchData();">查询</a>
							<a class="nui-button" iconCls="icon-reset"  onclick="clean()"/>重置</a>
						</td>
				
					</tr>
		    	</table>
		  	</div>
    </div>
    <div class="nui-fit">
	 	<div id="grid1"  dataField="resultList" idField="dicttypeid" class="nui-datagrid" style="width:100%;height:100%;" 
	  		sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" >
	    	<div property="columns">
	    		<div type="checkcolumn"></div>
	    		<div field="INFORMATION_ID" class="nui-hidden" visible="false">ID</div>
		        <div field="FILES_TYPE" headerAlign="center">档案种类</div>
		        <div field="FILES_NAME" headerAlign="center">档案名称</div>
		        <div field="DEAL_NAME" headerAlign="center">归属机构</div>
		        <div field="EMP_NAME" headerAlign="center">客户经理</div>
		        <div field="STORAGE_ADDRESS" headerAlign="center">库存地址</div>
		        <div field="STORAGE_LOCATION" headerAlign="center">货架号/箱号</div>
		        <div field="FILES_STATUS" headerAlign="center">档案状态</div>
		    </div>
	 	</div>
    
    </div>                
    <div class="nui-toolbar" style="text-align:center;padding-top:8px;padding-bottom:8px;" borderStyle="border:0;">
        <a class="nui-button" style="width:100px;" onclick="onOk()">入库/移库</a>
        <span style="display:inline-block;width:25px;"></span>
        <a class="nui-button" style="width:60px;" onclick="onCancel()">取消</a>
    </div>


	<script type="text/javascript">
    	 nui.parse();
	var form = new nui.Form("form");
    var grid = nui.get("grid1");

    //动态设置URL
    grid.setUrl("com.gotop.xmzg.files.fileList.queryFileStorageList.biz.ext");
    //也可以动态设置列 grid.setColumns([]);
	var data = form.getData(true,true);
	grid.load(data);
	
	function searchData(){	
		var data = form.getData(true,true);
    		grid.load(data);
    }



    function onKeyEnter(e) {
        search();
    }
    //////////////////////////////////
    function CloseWindow(action) {
        if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
        else window.close();
    }

    function onOk() {
    	var rows = grid.getSelecteds();
     	/* if(rows.length>1){
    		return nui.alert("请只选择一条记录进行编辑！","提示");
    	}  */
    	var STORAGE_ADDRESS = '';         
        var STORAGE_LOCATION =  '';    
       	if(rows.length>0){
		    nui.open({
		    	url:"<%=request.getContextPath()%>/files/fileList/fileStorageEdit.jsp",
	          	title:'入库/移库',
	          	width:400,
          		height:130,
		        ondestroy:function(action){
		        	if(action=="ok"){
		        		var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                        	STORAGE_ADDRESS =  data.STORAGE_ADDRESS;         
                        	STORAGE_LOCATION =  data.STORAGE_LOCATION;         
                        	var FILES_STATUS =  '3';         //入库/移库
                        	for(var i=0;i<rows.length;i++){
                        		rows[i].STORAGE_ADDRESS = STORAGE_ADDRESS;
                        		rows[i].STORAGE_LOCATION = STORAGE_LOCATION; 
						        rows[i].FILES_STATUS = FILES_STATUS;
                        	}
                        	var json = nui.encode({upDatas:rows});
					        var URL="com.gotop.xmzg.files.fileList.uptFilesStorage.biz.ext";
					        $.ajax({
					        	url: URL,
					            type: 'POST',
					            data: json,
					            cache: false,
					            contentType:'text/json',
					            success: function (text){
					                var returnJson = nui.decode(text);
									if(returnJson.flag == "1"){
										nui.alert("保存成功", "系统提示", function(action){
											if(action == "ok" || action == "close"){
												CloseWindow("saveSuccess");
											}
										});
									}else if(returnJson.flag == "0"){
										nui.alert("保存失败", "系统提示", function(action){
											if(action == "ok" || action == "close"){
												//CloseWindow("saveFailed");
											}
										});
									}else if(returnJson.flag == "noAccess"){
										nui.alert("对不起，您没有操作权限！", "系统提示", function(action){
											if(action == "ok" || action == "close"){
												//CloseWindow("saveFailed");
											}
										});
									}
							    }
					  		});   
                        	
                        	
                        }
		                //重定向
		               // window.location.href="<%=request.getContextPath() %>/files/fileList/storageInList.jsp";
		             }
		        }
		    });
       	}else{
        	nui.alert("请至少选中一条记录！","提示");
    	} 
      //  CloseWindow("ok");
    }
    
    //重置查询信息
	function clean(){
	   	//不能用form.reset(),否则会把隐藏域信息清空
	   	form.reset();  
    }
     
    function onCancel() {
        CloseWindow("cancel");
    }
    </script>
</body>
</html>
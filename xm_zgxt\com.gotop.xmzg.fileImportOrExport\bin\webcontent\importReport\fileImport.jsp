<%@page import="com.alibaba.fastjson.serializer.SerializerFeature"%>
<%@page import="com.alibaba.fastjson.JSON"%>
<%@page import="java.util.HashMap"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): zhanghongkai
  - Date: 2019-05-09 10:03:29
  - Description:
-->
<head>
	<%@include file="/coframe/tools/skins/common.jsp" %>
	<title>文件上传</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script type="text/javascript" src="<%=request.getContextPath() %>/js/jquery.form.js"></script>
</head>
<body>
	<div align="center">
		<div class="nui-toolbar" style=" width:900px;height:50px;line-height:20px" borderStyle="border:0;">
			<h1 align="center" style="margin-top:15px">文 件 上 传</h1>
		</div>
		<fieldset style="width:900px;border:solid 1px #aaa;position:relative;margin:5px 2px 0px 2px;" >
			<form id="import_form" action="com.gotop.xmzg.fileImportOrExport.importReport.flow" method="post" enctype="multipart/form-data">
	       		<input id="impType" class="nui-hidden"  name="paramMap/impType"  />  <!-- 导入文件类型，txt或excel -->
	       		<input id="impFileFlag" class="nui-hidden"  name="paramMap/impFileFlag" value="3" />  <!-- 导入文件标志，1：导入标题头， 2：导入数据 -->
	       		<input id="opWay" class="nui-hidden"  name="paramMap/opWay" value="1"  />  <!-- 操作方式（1：页面手动数据导入， 2：定时器自动导入） -->
	       		<input id="template_path" class="nui-hidden"/>
	       		<input id="menuId" class="nui-hidden" value="1" name="logMap/menuId" />
	       		<input id="menuName"  class="nui-hidden" value="空" name="logMap/menuName" />
	       		<input id="opContent"  class="nui-hidden" value="导入" name="logMap/opContent" />
				<input class="nui-hidden" id="import_type"  name="map/IMPORT_TYPE"/>
				<input class="nui-hidden" id="import_file_path"  name="map/IMPORT_FILE_PATH"/>
				<input class="nui-hidden" id="file_name"  name="map/FILE_NAME"/>
				<input class="nui-hidden" id="operation_cycle"  name="map/OPERATION_CYCLE"/>
				<input class="nui-hidden" id="file_type"  name="map/FILE_TYPE"/>
				<input class="nui-hidden" id="temp_file_path"  name="map/TEMP_FILE_PATH"/>
				<input class="nui-hidden" id="BUSINESSLINE"  name="map/BUSINESS_LINE"/>
				<input class="nui-hidden" id="is_save_no"  name="map/IS_SAVE_NO"/>
				<input class="nui-hidden" id="no_column_name"  name="map/NO_COLUMN_NAME"/>
				<input id="filePath" class="nui-hidden">
				<input id="table_name" class="nui-hidden">
				<table border="0" cellpadding="1" cellspacing="2" style="width:100%;table-layout:fixed;">
					<tr>
						<th style="text-align:right;">业务条线：</th>
						<td>
							<input id="businessLine" class="nui-dictcombobox" required="true" valueField="dictID" textField="dictName" style="width:100%;"
	          					dictTypeId="BUSINESS_LINE" name="map.BUSINESS_LINE" emptyText="请选择.." onValueChanged="businessLineChanged"/>
						</td>
						<td></td>
					</tr>
					<tr>
						<th style="text-align:right;">文件类别名称：</th>
						<td>
							<input class="nui-combobox" required="true" valueField="ID" textField="FILE_NAME" style="width:100%;"
	          					name="map/FILE_ID" emptyText="请选择.." id="fileName" dataField="resultList" onValueChanged="fileNameChanged"/>
						</td>
						<td></td>
					</tr>
					<tr>
						<th style="text-align:right;">日期：</th>
						<td>
							<input id="date" class="nui-datepicker" format="yyyyMMdd" style="width:100%;" 
								name="map/DATE" emptyText="请选择日期" allowInput="false" required="true"/>
						</td>
						<td></td>
					</tr>
					<tr>
						<th style="text-align:right;">报表类型：</th>
						<td>
							<input id="report_type" class="nui-combobox" textField="value" valueField="id" style="width:100%;" 
								name="map/REPORT_TYPE" emptyText="请选择报表类型" allowInput="false" 
								data="[{'id':'年报','value':'年报'},{'id':'季报','value':'季报'},{'id':'月报','value':'月报'},{'id':'日报','value':'日报'}]" />
						</td>
						<td></td>
					</tr>
					<tr>
						<th style="text-align:right;">文件选择：</th>
						<td>
							<input id="file" type="file" name="importFile" size="60" style="width:100%;" required="required"/>
						</td>
						<td></td>
					</tr>
					<!--<tr>
						<th style="text-align:right;">如无法上传请下载执行注册表：</th>
						<td>
							<a style="color:blue;cursor:pointer;" onClick="downloadFile()">下载注册表</a>
						</td>
						<td></td>
					</tr>-->
					<tr id="tr_separator" style="display:none;">
            			<th style="text-align:right;">
                        	数据文件数据分隔符：
            			</th>
            			<td >
            	  			<input id="separator" class="nui-textbox" name="paramMap/separator" style="width: 100%;" required="true"/>
            			</td>
           			</tr>	
					<tr>
						<td></td>
						<td style="text-align:center;">
							<a class="nui-button" iconCls="icon-ok" onClick="startUpload">上传</a>&nbsp
							<a id="save" class="nui-button" iconCls="icon-collapse" onclick="save">保存</a>&nbsp
							<a class="nui-button" iconCls="icon-download" onclick="downloadTemplate()">模版下载</a>&nbsp
							<a class="nui-button" iconCls="icon-reset" onclick="reset">重置</a>
						</td>
						<td></td>
					</tr>
				</table>
			</form>
			<div id="table" style="display:none;">
				<div class="nui-toolbar" style="border-bottom: 0; padding: 0px;">
					<table style="width: 100%;">
						<tr>
							<td style="width: 100%;">
								<a class="nui-button" iconCls="icon-remove" onclick="remove">删除</a>
							</td>
						</tr>
					</table>
				</div>
				<div id="grid" class="nui-fit"></div>
			</div>
		</fieldset>
	</div>


	<script type="text/javascript">
    	nui.parse();
    	
    	$("#save").hide();
    	$("#seasonTr").hide();
    	
    	var form = new nui.Form("import_form");
    	
    	<%
    		Object object = request.getAttribute("result");
    		HashMap resultMap = null;
    		HashMap paramMap = null;
    		String file_id = "";
    		String businessLine = "";
    		String result = "";
    		HashMap map = null;
    		String data = "";
    		String filePath = "";
    		String file_date = "";
    		String report_type = "";
    		String separator = "";
    		String message = "";
    		if(object != null){
    			resultMap = (HashMap)object;
    			result = resultMap.get("result").toString();
    			message = resultMap.get("message").toString();
    			filePath = resultMap.get("filePath").toString();
    			map = (HashMap)request.getAttribute("map");
    			file_id = map.get("FILE_ID").toString();
    			businessLine = map.get("BUSINESS_LINE").toString();
    			if(map.get("DATE") != null){
    				file_date = map.get("DATE").toString();
    			}
    			if(map.get("REPORT_TYPE") != null){
    				report_type = map.get("REPORT_TYPE").toString();
    			}
    			paramMap = (HashMap)request.getAttribute("paramMap");
    			if(paramMap.get("separator") != null){
    				separator = paramMap.get("separator").toString();
    			}
    			data = JSON.toJSONString(resultMap.get("data"), SerializerFeature.DisableCircularReferenceDetect);
    		}
    	%>
    	
    	var result = "<%=result %>";
    	if(result != null && result != ""){
    		var message = "<%=message %>";
    		if(message != null && message != ""){
    			result = "fail";
    		}
    		if(result == "ok"){
    			nui.alert("上传成功","系统提示",function(action){
	    			if(action == "ok" || action == "close"){
	    				var file_id = "<%=file_id %>";
	    				var businessLine = "<%=businessLine %>";
	    				var filePath = "<%=filePath %>";
	    				var file_date = "<%=file_date %>";
	    				var report_type = "<%=report_type %>";
	    				var separator = "<%=separator %>";
	    				nui.get("filePath").setValue(filePath);
	    				nui.get("businessLine").setValue(businessLine);
	    				nui.get("businessLine").doValueChanged();
	    				nui.get("fileName").setValue(file_id);
	    				nui.get("fileName").doValueChanged();
	    				nui.get("date").setValue(file_date);
	    				nui.get("report_type").setValue(report_type);
	    				nui.get("separator").setValue(separator);
	    				var data = '<%=data %>';
	    				if(data != null && data != '' && data != "null"){
	    					$("#grid").html("");
	    					showDataGrid(nui.decode(data));
	    				}
	    				/* $("#grid").html("");
	    				nui.get("filePath").setValue(text.result.filePath);
	    				if(text.result.data != null){
	    					showDataGrid(text.result.data);
	    				} */
	    			}
	    		});
    		}else if(result == "fail"){
    			nui.alert(message);
    		}else{
    			nui.alert("上传失败");
    		}
    	}
    	
    	function remove(){
    		var grid = nui.get("datagrid");
    		var rows = grid.getSelecteds();
    		nui.confirm("确定删除吗？","系统提示",function(action){
    			if("ok" == action){
    				grid.removeRows(rows);
    			}
    		});
    	}
    	
    	function downloadTemplate(){
    		var file_name = nui.get("fileName").getText();
    		var file_path = nui.get("template_path").getValue();
    		file_name = file_name + file_path.substring(file_path.lastIndexOf("."));
    		window.location.href = "<%=request.getContextPath() %>/fileImportOrExport/fileExport/download.jsp?file_name=" + file_name + "&file_path=" + file_path;
    	}
    	
    	/* function downloadFile(){
    		var file_name = "IE无法上传时执行的注册表.reg";
    		var file_path = nui.getDictText("IMPORT_FILE_PATH","03");
    		window.location.href = "<%=request.getContextPath() %>/fileImportOrExport/fileExport/download.jsp?file_name=" + file_name + "&file_path=" + file_path;
    	}*/
    	
    	function save(){
    		var map = {};
    		map.file_id = nui.get("fileName").getValue();
    		var filePath = nui.get("filePath").getValue();
    		var fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
    		map.filePath = filePath;
    		map.fileName = fileName;
    		map.fileDate = nui.get("date").getFormValue();
    		map.table_name = nui.get("table_name").getValue();
    		map.data = nui.get("datagrid").getData();
    		var formdata = form.getData(true,true);
    		map.formdata = formdata;
    		var json = nui.encode({map:map});
    		var b= nui.loading("正在操作中,请稍等...","提示");
    		$.ajax({
    			url:"com.gotop.xmzg.fileImportOrExport.fileImport.saveData.biz.ext",
    			type:"post",
    			data:json,
    			contentType:"text/json",
    			success:function(text){
    				nui.hideMessageBox(b);
    				if(text.result == "ok"){
    					nui.alert("保存成功","",function(action){
    						if("ok" == action || "close" == action){
    							window.location.href = "<%=request.getContextPath() %>/fileImportOrExport/importReport/fileImport.jsp";
    						}
    					});
    				}else{
    					nui.alert("保存失败");
    				}
    			}
    		});
    	}
    	
    	function showDataGrid(data){
    		var cols = [{type:"checkcolumn"}];
    		var columns = getCols();
    		if(data.length > 0){
    			var i = 1;
    			for(var j=0;j<columns.length;j++){
    				for(var temp in data[0]){
    					if(temp == columns[j].COLUMN_NAME){
    						cols[i] = {field:temp,align:"center",headerAlign:"center",header:columns[j].COLUMN_COMMENT};
    						i++;
    					}
    				}
    			}
    		}
    		
    		var grid = new nui.DataGrid();
    		grid.set({
    			style:"width:100%;height:400px;",
        		id:"datagrid",
        		columns: cols,
        		showPager:false,
        		multiSelect:true
    		});
    		grid.render(document.getElementById("grid"));
    		grid.setData(data);
    		$("#table").show();
    		$("#save").show();
    	}
    	
    	function getCols(){
    		var file_id = nui.get("fileName").getValue();
    		var resultList = null;
    		$.ajax({
    			url:"com.gotop.xmzg.fileImportOrExport.fileImport.getCols.biz.ext",
    			type:"post",
    			data:"file_id=" + file_id,
    			async:false,
    			success:function(text){
    				resultList = text.resultList;
    			}
    		});
    		return resultList;
    	}
    	
    	function reset(){
    		form.reset();
    		var file = document.getElementById('file');
    		file.outerHTML = file.outerHTML;
    	}
    	
    	function startUpload(){
    		//$("#import_form").submit();
    		if(form.validate() != true){
    			return;
    		}
    		var fileName = $("#file").val();
    		nui.get("file_name").setValue(fileName);
    		var sufFileName = fileName.substring(fileName.lastIndexOf("."));
    		var file_type = nui.get("file_type").getValue();
    		if(file_type == "1"){
    			if(sufFileName != ".xls" && sufFileName != ".xlsx"){
    				nui.alert("请选择.xls或.xlsx文件");
    				return;
    			}
    		}else if(file_type == "2"){
    			if(sufFileName != ".txt"){
    				nui.alert("请选择.txt文件");
    				return;
    			}
    		}
    		var b= nui.loading("正在操作中,请稍等...","提示");
    		$("#import_form").submit();
    		/* $("#import_form").ajaxSubmit({
    			url:"com.gotop.xmzg.fileImportOrExport.importReport.importFile.biz.ext",
    			type:"post",
    			dataType:"json",
    			success:function(text){
    				nui.hideMessageBox(b);
                	if(text.result.result == "ok"){
                		nui.alert("上传成功");
                		$("#grid").html("");
                		nui.get("filePath").setValue(text.result.filePath);
                		showDataGrid(text.result.data);
                	}else{
                		nui.alert("上传失败");
                	}
    			}
    		}); */
    		//var formData = new FormData($('#import_form')[0]);
            //$.ajax({
             //   type: 'post',
             //   url: "com.gotop.xmzg.fileImportOrExport.fileImport.importFile.biz.ext",
             //   data: formData,
             //   cache: false,
             //   processData: false,
             //   contentType: false,
             //   success:function(text){
             //   	nui.hideMessageBox(b);
             //   	if(text.result.result == "ok"){
             //   		nui.alert("上传成功");
             //   		$("#grid").html("");
             //   		showDataGrid(text.result.data);
             //   	}else{
              //  		nui.alert("上传失败");
            //    	}
            //    }
           // });
    	}
    	
    	function fileNameChanged(e){
    		var fileNameId = e.value;
    		var data = e.sender.data;
    		for(var i=0;i<data.length;i++){
    			if(data[i].ID == fileNameId){
    				nui.get("import_type").setValue(data[i].IMPORT_TYPE);
    				nui.get("import_file_path").setValue(data[i].IMPORT_FILE_PATH);
    				nui.get("operation_cycle").setValue(data[i].OPERATION_CYCLE);
    				nui.get("temp_file_path").setValue(data[i].TEMP_FILE_PATH);
    				nui.get("template_path").setValue(data[i].TEMPLATE_PATH);
    				nui.get("is_save_no").setValue(data[i].IS_SAVE_NO);
    				nui.get("no_column_name").setValue(data[i].NO_COLUMN_NAME);
    				nui.get("table_name").setValue(data[i].TABLE_NAME);
    				var fileType = data[i].FILE_TYPE;
    				nui.get("file_type").setValue(fileType);
    				if(fileType == 1){
    					nui.get("impType").setValue("excel");
    				}else if(fileType == 2){
    					nui.get("impType").setValue("txt");
    				}
    				if(fileType == 2){
    					$("#tr_separator").show();
    				}else{
    					$("#tr_separator").hide();
    				}
    			}
    		}
    	}
    	
    	function businessLineChanged(e){
    		var business_line = String(e.value);
    		var url = "com.gotop.xmzg.fileImportOrExport.fileImport.getFileNames.biz.ext?business_line=" + business_line;
    		nui.get("fileName").load(url);
    		nui.get("BUSINESSLINE").setValue(business_line);
    	}
    </script>
</body>
</html>
<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-01-10 10:10:11
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>单册请领-审批</title>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <table class="table" style="width:100%;">       
	        <tr>
	            	
				<th class="nui-form-label" >请领日期：</th>
				<td>
					<input id="queryData.trade_date1" name = "queryData.trade_date1" class="nui-datepicker "  style="width:100px;" allowInput="false" format="yyyy-MM-dd" />~
					<input id="queryData.trade_date2" name = "queryData.trade_date2" class="nui-datepicker "  style="width:100px;"  allowInput="false" format="yyyy-MM-dd" onvalidation="comparedate"/>
				</td>
			    <th class="nui-form-label">单册代码：</th>
				<td>
					<input id="item_no" name="queryData.item_no" class="nui-textbox"  style="width:160px;" vtype="maxLength:50"/>
				</td>
				<th class="tit">单册名称：</th>
				<td> 
					<input id="item_name" name="queryData.item_name" class="nui-textbox"  style="width:160px;" vtype="maxLength:100"/>
				</td> 
				
			</tr>	
			 <tr>
	            	
				<th  class="tit">审批状态：</th>
				<td>
					<input class="nui-dictcombobox" valueField="dictID" textField="dictName" emptyText="全部"
	          	      dictTypeId="APPLY_STATUS" id="queryData.approval_status" name="queryData.approval_status" showNullItem="true" nullItemText="全部" style="width:160px;" value="1"/>
				</td>		
				<th  class="tit">审批结果：</th>
				<td>
					<input class="nui-dictcombobox" valueField="dictID" textField="dictName" emptyText="全部"
	          	      dictTypeId="APPROVAL_RESULT_TYPE" id="queryData.approval_result" name="queryData.approval_result" showNullItem="true" nullItemText="全部" style="width:160px;"/>
				</td>
				<th class="tit">
					
				</th>
				<td>
				    <a class="nui-button" iconCls="icon-search" onclick="search">查询</a>
					<a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
				</td>
			</tr>        
	    </table>
	  </div>
    </div>
  </div>
  
 <!--  <div style="margin:10px 0px 0px 0px;">--> 
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
		     <a id="update" class="nui-button" iconCls="icon-edit" onclick="applicant">审批通过</a>
		     <a id="update1" class="nui-button" iconCls="icon-edit" onclick="applicant1">审批不通过</a>
           </td>
        </tr>
      </table>
    </div>
<!--  </div> --> 
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.dailydeal.itemApply.query_itemApplyApproval.biz.ext"
	  sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" onselectionchanged="selectionChanged">
	    <div property="columns" >
	    <div type="checkcolumn"></div>
	      <div field="APPLY_TIME" headerAlign="center" align="center">请领日期</div>
	      <div field="APPROVAL_STATUSNAME" headerAlign="center" align="center">审批状态</div>
	      <div field="ORGCODE" headerAlign="center" align="center">请领机构号</div>
	      <div field="ORGNAME" headerAlign="center" align="center" width=135>请领机构名称</div>
	      <div field="ITEM_NO" headerAlign="center" align="center">单册代码</div>
	      <div field="NO" headerAlign="center" align="center">编号</div>
	      <div field="ITEM_NAME" headerAlign="center" align="center" width=160>单册名称</div>
	      <div field="UNIT" headerAlign="center" align="center">单位</div>
	      <!--<div field="PRE_TAX_PRICE" headerAlign="center" align="center">税前单价</div>
	      <div field="TAX_PRICE" headerAlign="center" align="center">含税单价</div>-->
	      <div field="APPLY_NUM" headerAlign="center" align="center">请领数量</div> 
	      <!--<div field="REAL_NUM" headerAlign="center" align="center">实发数量</div>-->
	      <!--<div field="ITEM_ADDRESS" headerAlign="center" align="center">单册出处</div>-->
	      <div field="EMPNAME" headerAlign="center" align="center">请领人</div>
	      <div field="OPERATOR_DATE" headerAlign="center" align="center" width=135>申请时间</div>	  
		  <div field="APPROVAL_RESULTNAME" headerAlign="center" align="center">审批结果</div>
		  <div field="APPROVAL_EMPNAME" headerAlign="center" align="center">请领授权人</div>
		  <div field="APPLY_APPROVAL_REASON" headerAlign="center" align="center">审批理由</div>
		  <div field="APPLY_APPROVAL_DATE" headerAlign="center" align="center" width=130>审批时间</div> 
		  <div field="REMARK" headerAlign="center" align="center">备注</div>
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">
    nui.parse();
    var grid = nui.get("datagrid1");
    var form = new nui.Form("#form1");
    var data = form.getData(true,true);
    grid.load(data);
      
    function adjusttype(e){
      	    return nui.getDictText("ADJUST_TYPE", e.row.ADJUST_TYPE);
    }
    
    function search(){

       var form = new nui.Form("#form1");
            form.validate();
            if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
     
      function applicant(){  
      var rows = grid.getSelecteds();
      if(rows.length > 0){
      for(var i=0;i<rows.length;i++)
      {
      		if(rows[i].APPLY_RESULT!=""&&rows[i].APPLY_RESULT!=null)
      		{
      			nui.alert("该请领记录已做了审批，不能再操作！");
      			return false;
      		}
      }
         nui.confirm("确定审批通过选中记录？","系统提示",
           function(action){
             if(action=="ok"){
	            var json = nui.encode({maps:rows});
	           var a= nui.loading("正在执行中,请稍等...","提示");
		        $.ajax({
		          url:"com.gotop.xmzg.dailydeal.itemApply.submitApproval.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	nui.hideMessageBox(a);
		            var returnJson = nui.decode(text);
					if(returnJson.exception == null && returnJson.iRtn==1){
						nui.alert("审批成功", "系统提示", function(action){
							grid.reload();
						});
					}else{
						nui.alert("审批失败", "系统提示");
						grid.unmask();
					}
					
		          }
		        });
		     }
		   });
      }else{
        nui.alert("请选中一条记录！");
      }
    }
     
   function applicant1(){  
      var rows = grid.getSelecteds();
      if(rows.length > 0){
      for(var i=0;i<rows.length;i++)
      {
      		if(rows[i].APPLY_RESULT!=""&&rows[i].APPLY_RESULT!=null)
      		{
      			nui.alert("该请领记录已做了审批，不能再操作！");
      			return false;
      		}
      }
         nui.confirm("确定不审批通过选中记录？","系统提示",
           function(action){
             if(action=="ok"){
	            var json = nui.encode({maps:rows});
	           var a= nui.loading("正在执行中,请稍等...","提示");
		        $.ajax({
		          url:"com.gotop.xmzg.dailydeal.itemApply.refuseApproval.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	nui.hideMessageBox(a);
		            var returnJson = nui.decode(text);
					if(returnJson.exception == null && returnJson.iRtn==1){
						nui.alert("审批成功", "系统提示", function(action){
							grid.reload();
						});
					}else{
						nui.alert("审批失败", "系统提示");
						grid.unmask();
					}
					
		          }
		        });
		     }
		   });
      }else{
        nui.alert("请选中一条记录！");
      }
    }
    
    function selectionChanged(){
       var rows = grid.getSelecteds();
       if(rows.length>1){
           //nui.get("update").disable();
       }else{
           //nui.get("update").enable();
       }
    }
    
   
   
    function approvalType(e){
      	    return nui.getDictText("APPROVAL_TYPE", e.row.APPROVAL_TYPE);
    }
    
      //判断审批状态(已审批)
    function checkisExist(map){
      var bool;
      $.ajax({
        url:"com.gotop.xmzg.errorAdjust.errorAdjust.checkIsExitApproval.biz.ext",
        type: 'POST',
        data:map,
        cache: false,
        async:false,
        contentType:'text/json',
        success:function(text){
          bool = text.bool;
        }
      });
      return bool;
    }
    
    //时间判断开始时间不能大于结束时间
    function comparedate(e){
    //debugger;
      var startDate = nui.get("queryData.trade_date1").getFormValue();
      var endDate = nui.get("queryData.trade_date2").getFormValue();
      //if(startDate!="")
	    //startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
	  if(endDate!=""){
		//endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
        if(e.isValid){
          if(startDate>endDate){
            e.errorText="结束日期必须大于开始日期";
            e.isValid=false;
          }else
          {
          e.errorText="";
          e.isValid=true;
          }
        }
      }
    } 
    
    //重置
    function clean(){
	   //不能用form.reset(),否则会把隐藏域信息清空
	   nui.get("queryData.trade_date1").setValue("");
	   nui.get("queryData.trade_date2").setValue("");
       nui.get("item_no").setValue("");
       nui.get("item_name").setValue("");
       nui.get("queryData.approval_status").setValue("");
       nui.get("queryData.approval_result").setValue("");
       }
    
  </script>
</body>
</html>
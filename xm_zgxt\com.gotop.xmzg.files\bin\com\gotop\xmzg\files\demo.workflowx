<?xml version="1.0" encoding="UTF-8"?>
<workflowProcess productVersion="6.1" schemaVersion="6.0">
  <processHeader>
    <processBasicInfo>
      <processId>com.gotop.xmzg.files.demo</processId>
      <processName>demo</processName>
      <priority>60</priority>
      <author>Administrator</author>
      <department>psbc</department>
      <description></description>
    </processBasicInfo>
    <dataFields>
      <dataField>
        <name>line</name>
        <dataType>
          <typeClass>primitive</typeClass>
          <typeValue>String</typeValue>
        </dataType>
        <initialValue></initialValue>
        <description></description>
        <isArray>false</isArray>
      </dataField>
      <dataField>
        <name>joinPerson</name>
        <dataType>
          <typeClass>sdo</typeClass>
          <typeValue>com.pfpj.workflow.data.WFParticipant</typeValue>
        </dataType>
        <initialValue></initialValue>
        <description></description>
        <isArray>true</isArray>
      </dataField>
    </dataFields>
    <triggerEvents/>
    <timeLimit>
      <isTimeLimitSet>false</isTimeLimitSet>
      <calendarSet>
        <initType>appoint</initType>
        <calendarInfo>
          <resourceType>business-calendar</resourceType>
          <resourceID>default</resourceID>
          <resourceName>默认日历</resourceName>
          <parameters/>
        </calendarInfo>
      </calendarSet>
    </timeLimit>
    <procStarterLists>
      <processStarterType>all</processStarterType>
    </procStarterLists>
    <parameters/>
    <splitTransaction>false</splitTransaction>
    <longProcess>true</longProcess>
    <bizEntityInfo>
      <bizEntityName></bizEntityName>
      <relevantKey></relevantKey>
    </bizEntityInfo>
    <calendarInfo>
      <resourceType>business-calendar</resourceType>
      <resourceID>default</resourceID>
      <resourceName>默认日历</resourceName>
      <parameters/>
    </calendarInfo>
    <extendNodes><extendNode><key>IsForWardBiz</key><value>false</value><desc/></extendNode><extendNode><key>openLog</key><value>false</value><desc/></extendNode></extendNodes>
  </processHeader>
  <transitions>
    <transition>
      <from>startActivity</from>
      <to>manualActivity</to>
      <isDefault>true</isDefault>
      <priority>60</priority>
      <displayName></displayName>
      <type>simpleCondition</type>
      <bendPoints/>
    </transition>
    <transition>
      <from>manualActivity</from>
      <to>manualActivity1</to>
      <isDefault>true</isDefault>
      <priority>60</priority>
      <displayName></displayName>
      <type>simpleCondition</type>
      <bendPoints/>
    </transition>
    <transition>
      <from>manualActivity</from>
      <to>manualActivity2</to>
      <isDefault>false</isDefault>
      <priority>60</priority>
      <displayName></displayName>
      <type>simpleCondition</type>
      <simpleCondition>
        <leftValueType>variable</leftValueType>
        <leftValue>line</leftValue>
        <compType>EQ</compType>
        <rightValue>2</rightValue>
        <rightValueType>constant</rightValueType>
      </simpleCondition>
      <bendPoints>
        <bendPoint>
          <w1>155</w1>
          <h1>-97</h1>
          <w2>-159</w2>
          <h2>-97</h2>
        </bendPoint>
      </bendPoints>
    </transition>
    <transition>
      <from>manualActivity1</from>
      <to>manualActivity2</to>
      <isDefault>true</isDefault>
      <priority>60</priority>
      <displayName></displayName>
      <type>simpleCondition</type>
      <bendPoints/>
    </transition>
    <transition>
      <from>manualActivity2</from>
      <to>finishActivity</to>
      <isDefault>true</isDefault>
      <priority>60</priority>
      <displayName></displayName>
      <type>simpleCondition</type>
      <bendPoints/>
    </transition>
    <transition>
      <from>manualActivity2</from>
      <to>manualActivity1</to>
      <isDefault>false</isDefault>
      <priority>60</priority>
      <displayName></displayName>
      <type>simpleCondition</type>
      <simpleCondition>
        <leftValueType>variable</leftValueType>
        <leftValue>line</leftValue>
        <compType>EQ</compType>
        <rightValue>1</rightValue>
        <rightValueType>constant</rightValueType>
      </simpleCondition>
      <bendPoints/>
    </transition>
    <transition>
      <from>manualActivity2</from>
      <to>manualActivity</to>
      <isDefault>false</isDefault>
      <priority>60</priority>
      <displayName></displayName>
      <type>simpleCondition</type>
      <simpleCondition>
        <leftValueType>variable</leftValueType>
        <leftValue>line</leftValue>
        <compType>EQ</compType>
        <rightValue>0</rightValue>
        <rightValueType>constant</rightValueType>
      </simpleCondition>
      <bendPoints>
        <bendPoint>
          <w1>-133</w1>
          <h1>106</h1>
          <w2>181</w2>
          <h2>106</h2>
        </bendPoint>
      </bendPoints>
    </transition>
  </transitions>
  <activities>
    <activity>
      <activityId>startActivity</activityId>
      <activityName>开始</activityName>
      <description></description>
      <splitType>XOR</splitType>
      <joinType>XOR</joinType>
      <priority>60</priority>
      <activityType>start</activityType>
      <splitTransaction>false</splitTransaction>
      <implementation>
        <startActivity>
          <formFields/>
        </startActivity>
      </implementation>
      <isStartActivity>true</isStartActivity>
      <nodeGraphInfo>
        <color>16777215</color>
        <height>32</height>
        <width>32</width>
        <x>100</x>
        <y>150</y>
        <lookAndFeel>classic</lookAndFeel>
        <fontName>System</fontName>
        <fontSize>12</fontSize>
        <fontWidth>550</fontWidth>
        <foreColor>0</foreColor>
        <isItalic>0</isItalic>
        <isUnderline>0</isUnderline>
        <textHeight>60</textHeight>
      </nodeGraphInfo>
    </activity>
    <activity>
      <activityId>finishActivity</activityId>
      <activityName>结束</activityName>
      <description></description>
      <splitType>XOR</splitType>
      <joinType>XOR</joinType>
      <priority>60</priority>
      <activityType>finish</activityType>
      <splitTransaction>false</splitTransaction>
      <activateRule>
        <activateRuleType>directRunning</activateRuleType>
      </activateRule>
      <isStartActivity>false</isStartActivity>
      <nodeGraphInfo>
        <color>16777215</color>
        <height>32</height>
        <width>32</width>
        <x>660</x>
        <y>150</y>
        <lookAndFeel>classic</lookAndFeel>
        <fontName>System</fontName>
        <fontSize>12</fontSize>
        <fontWidth>550</fontWidth>
        <foreColor>0</foreColor>
        <isItalic>0</isItalic>
        <isUnderline>0</isUnderline>
        <textHeight>60</textHeight>
      </nodeGraphInfo>
    </activity>
    <activity>
      <activityId>manualActivity</activityId>
      <activityName>申请</activityName>
      <description></description>
      <splitType>XOR</splitType>
      <joinType>XOR</joinType>
      <priority>60</priority>
      <activityType>manual</activityType>
      <splitTransaction>false</splitTransaction>
      <activateRule>
        <activateRuleType>directRunning</activateRuleType>
        <applicationInfo>
          <actionType>service-component</actionType>
          <suppressJoinFailure>suppress</suppressJoinFailure>
          <application>
            <actionType>service-component</actionType>
            <applicationUri></applicationUri>
            <transactionType>join</transactionType>
            <exceptionStrategy>rollback</exceptionStrategy>
            <invokePattern>synchronous</invokePattern>
            <parameters/>
          </application>
          <assignments/>
        </applicationInfo>
      </activateRule>
      <implementation>
        <manualActivity>
          <allowAgent>true</allowAgent>
          <customURL>
            <isSpecifyURL>true</isSpecifyURL>
            <urlInfo>/files/demo/demoApply.jsp</urlInfo>
            <urlType>webpage</urlType>
          </customURL>
          <resetUrl>
            <isSpecifyURL>false</isSpecifyURL>
            <urlType>presentation-logic</urlType>
          </resetUrl>
          <participants>
            <participantType>relevantdata</participantType>
            <specialActivityID></specialActivityID>
            <specialPath>joinPerson</specialPath>
          </participants>
          <formFields/>
          <timeLimit>
            <isTimeLimitSet>false</isTimeLimitSet>
            <calendarSet>
              <initType>appoint</initType>
              <calendarInfo>
                <resourceType>business-calendar</resourceType>
                <resourceID>default</resourceID>
                <resourceName>默认日历</resourceName>
                <parameters/>
              </calendarInfo>
            </calendarSet>
          </timeLimit>
          <multiWorkItem>
            <isMulWIValid>false</isMulWIValid>
            <workitemNumStrategy>participant-number</workitemNumStrategy>
            <finishRule>all</finishRule>
            <finishRequiredPercent>0</finishRequiredPercent>
            <finishRquiredNum>0</finishRquiredNum>
            <isAutoCancel>false</isAutoCancel>
            <sequentialExecute>false</sequentialExecute>
          </multiWorkItem>
          <triggerEvents/>
          <rollBack>
            <applicationInfo>
              <actionType>service-component</actionType>
              <suppressJoinFailure>suppress</suppressJoinFailure>
              <application>
                <actionType>service-component</actionType>
                <applicationUri></applicationUri>
                <transactionType>join</transactionType>
                <exceptionStrategy>rollback</exceptionStrategy>
                <invokePattern>synchronous</invokePattern>
                <parameters/>
              </application>
              <assignments/>
            </applicationInfo>
          </rollBack>
          <freeFlowRule>
            <isFreeActivity>false</isFreeActivity>
            <freeRangeStrategy>freeWithinProcess</freeRangeStrategy>
            <isOnlyLimitedManualActivity>false</isOnlyLimitedManualActivity>
          </freeFlowRule>
          <resetParticipant>originalParticipant</resetParticipant>
          <notification>
            <isSendNotification>false</isSendNotification>
            <actionURL>
              <isSpecifyURL>false</isSpecifyURL>
              <urlType>presentation-logic</urlType>
            </actionURL>
            <participants>
              <participantType>process-starter</participantType>
              <specialActivityID></specialActivityID>
              <specialPath></specialPath>
            </participants>
            <isExpandParticipant>false</isExpandParticipant>
            <timeLimit>
              <isTimeLimitSet>false</isTimeLimitSet>
              <calendarSet>
                <initType>appoint</initType>
                <calendarInfo>
                  <resourceType>business-calendar</resourceType>
                  <resourceID>default</resourceID>
                  <resourceName>默认日历</resourceName>
                  <parameters/>
                </calendarInfo>
              </calendarSet>
            </timeLimit>
            <notificationImplType>workItem</notificationImplType>
          </notification>
        </manualActivity>
      </implementation>
      <extendNodes></extendNodes>
      <isStartActivity>false</isStartActivity>
      <nodeGraphInfo>
        <color>16777215</color>
        <height>45</height>
        <width>64</width>
        <x>218</x>
        <y>143</y>
        <lookAndFeel>classic</lookAndFeel>
        <fontName>System</fontName>
        <fontSize>12</fontSize>
        <fontWidth>550</fontWidth>
        <foreColor>0</foreColor>
        <isItalic>0</isItalic>
        <isUnderline>0</isUnderline>
        <textHeight>60</textHeight>
      </nodeGraphInfo>
    </activity>
    <activity>
      <activityId>manualActivity1</activityId>
      <activityName>审批1</activityName>
      <description></description>
      <splitType>XOR</splitType>
      <joinType>XOR</joinType>
      <priority>60</priority>
      <activityType>manual</activityType>
      <splitTransaction>false</splitTransaction>
      <activateRule>
        <activateRuleType>directRunning</activateRuleType>
        <applicationInfo>
          <actionType>service-component</actionType>
          <suppressJoinFailure>suppress</suppressJoinFailure>
          <application>
            <actionType>service-component</actionType>
            <applicationUri></applicationUri>
            <transactionType>join</transactionType>
            <exceptionStrategy>rollback</exceptionStrategy>
            <invokePattern>synchronous</invokePattern>
            <parameters/>
          </application>
          <assignments/>
        </applicationInfo>
      </activateRule>
      <implementation>
        <manualActivity>
          <allowAgent>true</allowAgent>
          <customURL>
            <isSpecifyURL>true</isSpecifyURL>
            <urlInfo>/files/demo/demoSp.jsp</urlInfo>
            <urlType>webpage</urlType>
          </customURL>
          <resetUrl>
            <isSpecifyURL>false</isSpecifyURL>
            <urlType>presentation-logic</urlType>
          </resetUrl>
          <participants>
            <participantType>relevantdata</participantType>
            <specialActivityID></specialActivityID>
            <specialPath>joinPerson</specialPath>
          </participants>
          <formFields/>
          <timeLimit>
            <isTimeLimitSet>false</isTimeLimitSet>
            <calendarSet>
              <initType>appoint</initType>
              <calendarInfo>
                <resourceType>business-calendar</resourceType>
                <resourceID>default</resourceID>
                <resourceName>默认日历</resourceName>
                <parameters/>
              </calendarInfo>
            </calendarSet>
          </timeLimit>
          <multiWorkItem>
            <isMulWIValid>false</isMulWIValid>
            <workitemNumStrategy>participant-number</workitemNumStrategy>
            <finishRule>all</finishRule>
            <finishRequiredPercent>0</finishRequiredPercent>
            <finishRquiredNum>0</finishRquiredNum>
            <isAutoCancel>false</isAutoCancel>
            <sequentialExecute>false</sequentialExecute>
          </multiWorkItem>
          <triggerEvents/>
          <rollBack>
            <applicationInfo>
              <actionType>service-component</actionType>
              <suppressJoinFailure>suppress</suppressJoinFailure>
              <application>
                <actionType>service-component</actionType>
                <applicationUri></applicationUri>
                <transactionType>join</transactionType>
                <exceptionStrategy>rollback</exceptionStrategy>
                <invokePattern>synchronous</invokePattern>
                <parameters/>
              </application>
              <assignments/>
            </applicationInfo>
          </rollBack>
          <freeFlowRule>
            <isFreeActivity>false</isFreeActivity>
            <freeRangeStrategy>freeWithinProcess</freeRangeStrategy>
            <isOnlyLimitedManualActivity>false</isOnlyLimitedManualActivity>
          </freeFlowRule>
          <resetParticipant>originalParticipant</resetParticipant>
          <notification>
            <isSendNotification>false</isSendNotification>
            <actionURL>
              <isSpecifyURL>false</isSpecifyURL>
              <urlType>presentation-logic</urlType>
            </actionURL>
            <participants>
              <participantType>process-starter</participantType>
              <specialActivityID></specialActivityID>
              <specialPath></specialPath>
            </participants>
            <isExpandParticipant>false</isExpandParticipant>
            <timeLimit>
              <isTimeLimitSet>false</isTimeLimitSet>
              <calendarSet>
                <initType>appoint</initType>
                <calendarInfo>
                  <resourceType>business-calendar</resourceType>
                  <resourceID>default</resourceID>
                  <resourceName>默认日历</resourceName>
                  <parameters/>
                </calendarInfo>
              </calendarSet>
            </timeLimit>
            <notificationImplType>workItem</notificationImplType>
          </notification>
        </manualActivity>
      </implementation>
      <extendNodes></extendNodes>
      <isStartActivity>false</isStartActivity>
      <nodeGraphInfo>
        <color>16777215</color>
        <height>45</height>
        <width>64</width>
        <x>390</x>
        <y>143</y>
        <lookAndFeel>classic</lookAndFeel>
        <fontName>System</fontName>
        <fontSize>12</fontSize>
        <fontWidth>550</fontWidth>
        <foreColor>0</foreColor>
        <isItalic>0</isItalic>
        <isUnderline>0</isUnderline>
        <textHeight>60</textHeight>
      </nodeGraphInfo>
    </activity>
    <activity>
      <activityId>manualActivity2</activityId>
      <activityName>审批2</activityName>
      <description></description>
      <splitType>XOR</splitType>
      <joinType>XOR</joinType>
      <priority>60</priority>
      <activityType>manual</activityType>
      <splitTransaction>false</splitTransaction>
      <activateRule>
        <activateRuleType>directRunning</activateRuleType>
        <applicationInfo>
          <actionType>service-component</actionType>
          <suppressJoinFailure>suppress</suppressJoinFailure>
          <application>
            <actionType>service-component</actionType>
            <applicationUri></applicationUri>
            <transactionType>join</transactionType>
            <exceptionStrategy>rollback</exceptionStrategy>
            <invokePattern>synchronous</invokePattern>
            <parameters/>
          </application>
          <assignments/>
        </applicationInfo>
      </activateRule>
      <implementation>
        <manualActivity>
          <allowAgent>true</allowAgent>
          <customURL>
            <isSpecifyURL>true</isSpecifyURL>
            <urlInfo>/files/demo/demoSp.jsp</urlInfo>
            <urlType>webpage</urlType>
          </customURL>
          <resetUrl>
            <isSpecifyURL>false</isSpecifyURL>
            <urlType>presentation-logic</urlType>
          </resetUrl>
          <participants>
            <participantType>relevantdata</participantType>
            <specialActivityID></specialActivityID>
            <specialPath>joinPerson</specialPath>
          </participants>
          <formFields/>
          <timeLimit>
            <isTimeLimitSet>false</isTimeLimitSet>
            <calendarSet>
              <initType>appoint</initType>
              <calendarInfo>
                <resourceType>business-calendar</resourceType>
                <resourceID>default</resourceID>
                <resourceName>默认日历</resourceName>
                <parameters/>
              </calendarInfo>
            </calendarSet>
          </timeLimit>
          <multiWorkItem>
            <isMulWIValid>false</isMulWIValid>
            <workitemNumStrategy>participant-number</workitemNumStrategy>
            <finishRule>all</finishRule>
            <finishRequiredPercent>0</finishRequiredPercent>
            <finishRquiredNum>0</finishRquiredNum>
            <isAutoCancel>false</isAutoCancel>
            <sequentialExecute>false</sequentialExecute>
          </multiWorkItem>
          <triggerEvents/>
          <rollBack>
            <applicationInfo>
              <actionType>service-component</actionType>
              <suppressJoinFailure>suppress</suppressJoinFailure>
              <application>
                <actionType>service-component</actionType>
                <applicationUri></applicationUri>
                <transactionType>join</transactionType>
                <exceptionStrategy>rollback</exceptionStrategy>
                <invokePattern>synchronous</invokePattern>
                <parameters/>
              </application>
              <assignments/>
            </applicationInfo>
          </rollBack>
          <freeFlowRule>
            <isFreeActivity>false</isFreeActivity>
            <freeRangeStrategy>freeWithinProcess</freeRangeStrategy>
            <isOnlyLimitedManualActivity>false</isOnlyLimitedManualActivity>
          </freeFlowRule>
          <resetParticipant>originalParticipant</resetParticipant>
          <notification>
            <isSendNotification>false</isSendNotification>
            <actionURL>
              <isSpecifyURL>false</isSpecifyURL>
              <urlType>presentation-logic</urlType>
            </actionURL>
            <participants>
              <participantType>process-starter</participantType>
              <specialActivityID></specialActivityID>
              <specialPath></specialPath>
            </participants>
            <isExpandParticipant>false</isExpandParticipant>
            <timeLimit>
              <isTimeLimitSet>false</isTimeLimitSet>
              <calendarSet>
                <initType>appoint</initType>
                <calendarInfo>
                  <resourceType>business-calendar</resourceType>
                  <resourceID>default</resourceID>
                  <resourceName>默认日历</resourceName>
                  <parameters/>
                </calendarInfo>
              </calendarSet>
            </timeLimit>
            <notificationImplType>workItem</notificationImplType>
          </notification>
        </manualActivity>
      </implementation>
      <extendNodes></extendNodes>
      <isStartActivity>false</isStartActivity>
      <nodeGraphInfo>
        <color>16777215</color>
        <height>45</height>
        <width>64</width>
        <x>532</x>
        <y>143</y>
        <lookAndFeel>classic</lookAndFeel>
        <fontName>System</fontName>
        <fontSize>12</fontSize>
        <fontWidth>550</fontWidth>
        <foreColor>0</foreColor>
        <isItalic>0</isItalic>
        <isUnderline>0</isUnderline>
        <textHeight>60</textHeight>
      </nodeGraphInfo>
    </activity>
  </activities>
  <notes/>
  <resource/>
</workflowProcess>
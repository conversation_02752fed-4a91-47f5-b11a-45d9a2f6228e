<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-01-05 11:41:00
  - Description:
-->
<head>
<%-- <%@include file="/coframe/tools/skins/common.jsp" %> --%>
<%@include file="/coframe/dict/common.jsp"%>
<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
    <%
		java.text.SimpleDateFormat formatter = new java.text.SimpleDateFormat("yyyyMMdd"); 
        java.util.Date currentTime = new java.util.Date();//得到当前系统时间 
        String str_date = formatter.format(currentTime); //将日期时间格式化 
        java.util.Calendar cal = java.util.Calendar.getInstance();
        int year = cal.get(java.util.Calendar.YEAR);
        String str_date_end = year + "1231";
    %>
</head>
<body>
<!-- JF_POSITION -->
<input  id="tree1" class="nui-treeselect" multiSelect="true" style="display: none;" dataField="treeNodes"
	url="com.gotop.xmzg.achieve.common.get_OrgTree.biz.ext" showTreeIcon="true" textField="TEXT" idField="ID" parentField="PID" />

<input class="nui-dictcombobox" valueField="dictID" textField="dictName" id="JF_POSITION" dictTypeId="JF_POSITION" style="display:none;"/>
  <div id="form1" style="padding-top:5px;">
   <input class="nui-hidden" id="TA_ID" name="map.TA_ID" /> 
   <input class="nui-hidden" id="TA_START" name="map.TA_START" value="1"/> 
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>考核方案名称：</label></th>
        <td colspan="3" >  
        	<input  id="item_no" name = "map.TA_NAME"  class="nui-textbox" required="true" style="width:100%;" vtype="maxLength:50"/>
        </td> 
      </tr>
      
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>考核时间：</label></th>
        <td  colspan="3">  
        	<input id="TA_TIME_START" name = "map.TA_TIME_START" class="nui-datepicker"  style="width:45%;" required="true" allowInput="false" format="yyyyMMdd" value="<%=str_date%>" onvalidation="comparedateTa" />
        <span style="width:8.5%; text-align:center;display: inline-block;">至</span>
        <input id="TA_TIME_END" name = "map.TA_TIME_END" class="nui-datepicker"  style="width:45%;" required="true" allowInput="false" format="yyyyMMdd" value="<%=str_date_end%>" onvalidation="comparedateTa" />
        </td>

      </tr>
      
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>考核对象：</label></th>
        <td colspan="3" >  
        	<input id="TA_OBJECT"  class="nui-dictcombobox" name="map.TA_OBJECT"  style="width:100%;" allowInput="false" 
                valueField="dictID" textField="dictName" dictTypeId="JF_KHDX"   nullItemText="请选择" emptyText="请选择" showNullItem="true"  required="true"                
             	value = "1" onvaluechanged="onObjectChanged"/>    
        </td> 
      </tr>
      
      <tr id="orgTr">
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>机构范围：</label></th>
        <td colspan="3" >  
        	<input id="btnEdit" name = "map.TA_ORGTYPE"  class="nui-textboxlist belongDownInput" style="width: 89%;" allowInput="false"/>  
        	<a class="nui-button " plain="true" iconCls="icon-edit" onclick="OrgonButtonEdit('btnEdit')">配置</a>  
        </td> 
      </tr>
      <tr id="empTr" >
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>人员类别：</label></th>
        <td colspan="3" >  
        	<div id="EMPTYPE" name = "map.TA_EMPTYPE" class="mini-combobox" style="width:100%;"  popupWidth="600" textField="dictName" valueField="dictID" 
		     multiSelect="true"  showClose="true" oncloseclick="onCloseClick" onclick="setDataEmp">     
		    <div property="columns">
		        <!-- <div header="code" field="dictID" width=60></div> -->
		        <div header="名称" field="dictName" ></div>
		    </div> 
		    
		</div>  
		<span style="color:red">提示：机构范围和人员类别需任选一项！</span>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>管理机构：</label></th>
        <td colspan="3" >  
        	<!-- <input  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:100%;" required="true"/>   
        	 -->
			<input id="btnEdit1" name = "map.TA_ORGCODES"  class="nui-textboxlist belongDownInput" style="width: 89%;" required="true" allowInput="false"/> 
			<a class="nui-button " plain="true" iconCls="icon-edit" onclick="OrgonButtonEdit('btnEdit1')">配置</a>
        </td> 
      </tr>
       <tr id="khzbTr">
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>考核指标：</label></th>
        <td colspan="3" >  
        	
        </td> 
      </tr>
    </table>
	</div>
    <div id="khzb">
    <center>
    <div style="width:95%; " >
		        <div class="mini-toolbar" style="border-bottom:0;padding:0px;">
		            <table style="width:100%;">
		                <tr>
		                    <td style="width:100%;">
		                        <a id="zb_btn1" class="mini-button" iconCls="icon-add" onclick="addRow()" plain="true">增加</a>
		                        <a id="zb_btn2" class="mini-button" iconCls="icon-remove" onclick="removeRow()" plain="true">删除</a>        
		                    	<div id="formQuery" style="float:right">
		                    		
					   				<input class="nui-hidden" id="q_TA_ID" name="queryData.TA_ID" /> 
					   				<input id="q_TID_NAME" name="queryData.TID_NAME"  class="nui-textbox" emptyText="请输入指标细项名称"/>
					   				 <a id="q_btn" class="nui-button"  iconCls="icon-search" onclick="search">查询</a>
					   			</div>
					   			
		                    </td>
		                    
		                </tr>
		            </table>           
		            
		        </div>
		    </div>
		    
		    <div id="datagrid1" class="nui-datagrid" style="width:95%;height:245px;" 
		        url = "com.gotop.xmzg.achieve.assess.assessIndicators_list.biz.ext" dataField="resultList"
		        allowResize="true" pageSize="20" 
		        allowCellEdit="true" allowCellSelect="true" multiSelect="true"
		        allowCellValid="true" oncellvalidation="onCellValidation" >
		        <div property="columns">
		            <div type="indexcolumn"></div>
            		<div type="checkcolumn"></div>
            		<div field="TIP_NAME" align="center"  headerAlign="center">业务条线名称</div>  
		            <div field="TI_NAME" align="center"  headerAlign="center">指标名称</div>                
		            <div field="TID_NAME"  align="center" headerAlign="center">指标细项名称</div>
		            <div field="TAI_TIME_START" vtype="required"  align="center" headerAlign="center" renderer="dateStr">开始时间 
		                <input property="editor" name = "map.TAI_TIME_START" class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd"/>
		            </div>
		            <div field="TAI_TIME_END" vtype="required"  align="center" headerAlign="center" renderer="dateStr">结束时间
		                <input property="editor" name = "map.TAI_TIME_END" class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd" />
		            </div>                               
		            <div field="TAI_PROPORTION_TYPE" vtype="required" align="center" headerAlign="center" renderer="dictBzlx">积分标准类型
		               <input property="editor" class="nui-dictcombobox"  name="map.TAI_PROPORTION_TYPE" id="JF_BZLX" dictTypeId="JF_BZLX"  valueField="dictID" textField="dictName"  required="true" style="width:100%;"/>
		            </div>  
		            <div field="TAI_PROPORTION" vtype="float"  align="center" headerAlign="center">积分标准
		               <input property="editor" name = "map.TAI_PROPORTION"  class="nui-textbox"  vtype="float" style="width:100%;"/>
		            </div>  
		            <div field="TAI_SCORE" vtype="float"  align="center" headerAlign="center">调整系数
		               <input property="editor" name = "map.TAI_SCORE" class="nui-textbox"  vtype="float" style="width:100%;"/>
		            </div>
		            <div field="TAI_SCORE1" vtype="float"  align="center" headerAlign="center">标准得分系数
		               <input property="editor" name = "map.TAI_SCORE1" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAI_SCORE2" vtype="float"  align="center" headerAlign="center">超计划得分系数
		               <input property="editor" name = "map.TAI_SCORE2" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAI_SCORE3" vtype="float"  align="center" headerAlign="center">超力争得分系数
		               <input property="editor" name = "map.TAI_SCORE3" class="nui-textbox"  vtype="float" style="width:100%;" />
		            </div>
		            <div field="TAI_BASE" vtype="float"  align="center" headerAlign="center">业绩基数
		               <input property="editor" name = "map.TAI_BASE"  class="nui-textbox"  vtype="float" style="width:100%;"/>
		            </div>
		        </div>
		    </div>
    </center>
    </div>
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a id="save" class="nui-button" iconCls="icon-save" onclick="onOk">保存</a>
	          <span style="display:inline-block;width:10px;"></span>
	          <a  class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>


  <script type="text/javascript">
  	var open_type = "add";
  	
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);
    
    function setTreeCheck(id,codeStr){	
    	var datas = nui.get("tree1").getList();
    	var nameStr = "";
    	var codes = codeStr.split(",");
    	 for (var i = 0, l = codes.length; i < l; i++) {
    	 	for (var x = 0, y = datas.length; x < y; x++) {
    	 		if(codes[i] == datas[x].ORGCODE) nameStr+=datas[x].TEXT+",";
    	 	}
    	 }
    	nui.get(id).setText(nameStr);
    }
    
    var saveUrl = "com.gotop.xmzg.achieve.assess.add_assess.biz.ext";
    function setData(data){
                   
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);
		open_type = infos.pageType;
        //如果是点击编辑类型页面
        if (infos.pageType != "add") {
          
          saveUrl = "com.gotop.xmzg.achieve.assess.update_assess.biz.ext";
	      //表单数据回显
          var json = infos.record;
          objectChanged(json.map.TA_OBJECT);
          form.setData(json);
          nui.get("q_TA_ID").setValue(json.map.TA_ID);
          //nui.get("btnEdit1").setText(json.map.ORGCODES);
          setTreeCheck("btnEdit1",json.map.TA_ORGCODES);
          if(json.map.TA_EMPTYPE){
          	nui.get("EMPTYPE").setText(json.map.EMPTYPE);
          }
          if(json.map.TA_ORGTYPE){
          	//nui.get("btnEdit").setText(json.map.ORGTYPE);
          	setTreeCheck("btnEdit",json.map.TA_ORGTYPE);
          }
          
	          if(infos.pageType == "copy"){
	         	$("#khzb").hide();
	         	$("#khzbTr").hide();
	          	saveUrl = "com.gotop.xmzg.achieve.assess.copy_assess.biz.ext";
	         }else{
	         	search();
	         }
	         
	         if(infos.pageType ==  "detail"){
	         	$("#save").hide();
	         	$("#zb_btn1").hide();
	         	$("#zb_btn2").hide();
				var controls=form.getFields();
				for(var i = 0 ; i < controls.length ; i++){
					controls[i].setEnabled(false);
				}
	         }
         }else{
         	$("#formQuery").hide();
         }
         
    }
    function search(){
       var formQuery = new nui.Form("#formQuery");
       formQuery.validate();
       if (formQuery.isValid() == false) return;
       var data = formQuery.getData(true,true);
       grid.load(data);
    }
    
    function onOk(){
      saveData();
    }
    
    //提交
    function saveData(){   
    console.log(form.getData(true,true));
      form.validate();
      if(form.isValid()==false) return;
      
      var data = form.getData(true,true);
      
      if((data.map.TA_ORGTYPE == null || data.map.TA_ORGTYPE == "") && (data.map.TA_EMPTYPE == null ||data.map.TA_EMPTYPE == "")){
      		nui.alert("机构类别和人员类别不能都为空！");
      		return;
      }
      
      grid.validate();
      if (grid.isValid() == false) {   
        var error = grid.getCellErrors()[0];
        grid.beginEditCell(error.record, error.column);
        return;
      }
      var tais = grid.getChanges();
      data.tais = tais;
      
      var json = nui.encode(data);
     
      $.ajax({
        url:saveUrl,
        type:'POST',
        data:json,
        cache:false,
        contentType:'text/json',
        success:function(text){
            var returnJson = nui.decode(text);
			if(returnJson.exception == null && returnJson.iRtn == 1){
				nui.alert("操作成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveSuccess");
					}
				});
			}else if(returnJson != null && returnJson.iRtn == -1){
				nui.alert("您没有操作管理权限！", "系统提示");
			}else{
				nui.alert("操作失败", "系统提示");
			}
        }
      });
    }
    
    function onObjectChanged(e){
    	objectChanged(e.value);
    }
    
    function objectChanged(val){
    	if(val == 0){
    		//$("#orgTr").show();
    		$("#empTr").hide();
    		nui.get("btnEdit").setRequired(true);
    	}else{
    		$("#empTr").show();
    		//$("#orgTr").hide();
    		nui.get("btnEdit").setRequired(false);
    	}
    }
    
    function setDataEmp(){
    	nui.get("EMPTYPE").setData(nui.get("JF_POSITION").data);
    }
    
    function onCloseClick(e){
    	var obj = e.sender;
    	obj.setText("");
        obj.setValue("");
    }
       
    function onCancel(){
      CloseWindow("cancel");
    }
    
    function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
  
  	//时间判断开始时间不能大于结束时间
    function comparedate(e,startDate,endDate){
    	if(startDate instanceof Date){
    		startDate = nui.formatDate ( startDate, 'yyyyMMdd' );
    	}
    	if(endDate instanceof Date){
    		endDate = nui.formatDate ( endDate, 'yyyyMMdd' );
    	}
      if(startDate!="" && endDate!=null && endDate.length > 8)
	    startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
	  if(endDate!="" && endDate!=null && endDate.length > 8)
		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
         if(e.isValid){
          if(startDate > endDate){
            e.errorText="结束日期必须大于开始日期";
            e.isValid=false;
          }else{
          e.errorText="";
          e.isValid=true;
        }
        }
    }
    
     
    var grid = nui.get("datagrid1");
    function addRow() {
     	nui.open({
            url:"<%=request.getContextPath() %>/achieve/assess/assess_tree.jsp",
            showMaxButton: false,
            title: "选择树",
            width: 350,
            height: 350,
            ondestroy: function (action) {                    
                if (action == "ok") {
                    var iframe = this.getIFrameEl();
                    var datas = iframe.contentWindow.GetData();
                    datas = nui.clone(datas);
                    if (datas) {
                    	var selDatas = [];
                    	var gridData = grid.getData();
                       	for(var i = 0; i< datas.length ; i++){
                       		var is = true;
                       		for(var x = 0; x< gridData.length ; x++){
                       			if(datas[i].TID_CODE == gridData[x].TID_CODE) is = false;
                       			
                       		}
                       		if(is){
                       			datas[i].TAI_TIME_START = nui.formatDate( new Date(),"yyyy-MM-dd HH:mm:ss" );
                       			datas[i].TAI_TIME_END = <%=year%>+"-12-31 00:00:00";
                       			datas[i].TAI_SCORE = 1;
                       			datas[i].TAI_PROPORTION_TYPE = 0;
                       			if(open_type == "edit"){
                       				datas[i].TA_ID = nui.get("q_TA_ID").getValue();                       				
                       			}
                       			selDatas.push(datas[i]);
                       		}
                       	}
                       	
				       grid.addRows(selDatas, 0);
				       grid.validateRow(selDatas); 
                    }
                }
            }
        });
        
    }
    function removeRow() {
        var rows = grid.getSelecteds();
        if (rows.length > 0) {
            grid.removeRows(rows, true);
        }
    }
    
    function onCellValidation(e) {
        if (e.field == "TAI_TIME_START" || e.field == "TAI_TIME_END") {
        	comparedate(e,e.row.TAI_TIME_START,e.row.TAI_TIME_END);
        }
        
        if (e.field == "TAI_PROPORTION" && e.row.TAI_PROPORTION_TYPE == 1) {
        	if(!e.row.TAI_PROPORTION){
        		e.errorText="积分标准类型为使用本方案时，必填";
            	e.isValid=false;
        	}
        	
        }
        if(e.field == "TAI_PROPORTION"){
	        if( e.row.TAI_PROPORTION == 0){
	    		e.errorText="积分标准不能为0";
	        	e.isValid=false;
	    	}
        }
        
        if (e.field == "TAI_PROPORTION" || e.field == "TAI_SCORE" || e.field == "TAI_SCORE1" || e.field == "TAI_SCORE2" || e.field == "TAI_SCORE3") {
        	if(e.value < 0){
        		e.errorText="不能输入负数";
            	e.isValid=false;
        	}
        }
    }
    function dateStr(e){
    	var endDate = e.value;
    	if(endDate instanceof Date){
    		return endDate = nui.formatDate ( endDate, 'yyyyMMdd' );
    	}
    	if(endDate!="" && endDate!=null && endDate.length > 8){
    		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
    	}
    	return endDate;
    }
    function dictBzlx(e){
    	return nui.getDictText("JF_BZLX", e.value);
    }
    
     function comparedateTa(e){
    //debugger;
      var startDate = nui.get("TA_TIME_START").getFormValue();
      var endDate = nui.get("TA_TIME_END").getFormValue();
    	 if(startDate!="" && startDate.length > 8)
	    startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
	  if(endDate!="" && startDate.length > 8){
		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
    	  
	 if(e.isValid){
          if(startDate>endDate){
            e.errorText="结束日期必须大于开始日期";
            e.isValid=false;
          }else
          {
          e.errorText="";
          e.isValid=true;
          }
        }
      }
    } 
    
	function OrgonButtonEdit(id) {
        var btnEdit = nui.get(id);
        nui.open({
            url:"<%=request.getContextPath() %>/achieve/common/org_multSelectTree.jsp",
            showMaxButton: false,
            title: "选择树",
            width: 350,
            height: 350,
            onload : function() {
				var iframe = this.getIFrameEl();
				iframe.contentWindow.setTreeCheck(btnEdit.getValue());
			},
            ondestroy: function (action) {                    
                if (action == "ok") {
                    var iframe = this.getIFrameEl();
                    var data = iframe.contentWindow.GetData();
                    data = nui.clone(data);
                    if (data) {
                    	btnEdit.setValue(data.ORGCODE);
						btnEdit.setText(data.TEXT);
                    }
                }
            }
        });
    }
  </script>
</body>
</html>
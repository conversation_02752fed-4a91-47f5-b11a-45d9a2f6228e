<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): wj
  - Date: 2018-08-17 11:24:50
  - Description:
-->
<head>
<title>员工拥有角色维护</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<%@include file="/coframe/tools/skins/common.jsp" %>
<%@page import="com.eos.system.utility.StringUtil"%>
<script type="text/javascript" src="<%=contextPath%>/coframe/org/js/org_common.js"></script>
<style type="text/css">
    fieldset
    {
        border:solid 1px #aaa;
    }        
    .hideFieldset
    {
        border-left:0;
        border-right:0;
        border-bottom:0;
    }
    .hideFieldset .fieldset-body
    {
        display:none;
    }
</style>
</head>
<body>
<div class="nui-fit" style="padding:5px;">
	 <table style="height:99%;">
	        <tr>
	            <td >
	                <div id="listbox1" class="nui-listbox" style="width:150px;height:150px;" textField="text" valueField="id" 
	                     showCheckBox="true" multiSelect="true" url="../data/countrys.txt" dataField="countrys">
	                </div>
	            </td>
	            <td style="width:120px;text-align:center;">
	                <input type="button" value="取消" onclick="add()" style="width:40px;"/><br />
	                <input type="button" value="全部取消" onclick="addAll()" style="width:40px;"/><br />
	                <input type="button" value="全部授权" onclick="removeAll()" style="width:40px;"/><br />
	                <input type="button" value="授权" onclick="remove()" style="width:40px;"/><br />
	
	            </td>
	            <td>
	                <div id="listbox2" class="nui-listbox" style="width:250px;height:150px;"                     
	                     showCheckBox="true" multiSelect="true" >     
	                    <div property="columns">
	                        <div header="ID" field="id"></div>
	                        <div header="国家" field="text"></div>
	                    </div>
	                </div>
	            </td>
	            <td style="width:50px;text-align:center;vertical-align:bottom">
	                <input type="button" value="Up" onclick="upItem()" style="width:55px;"/>
	                <input type="button" value="Down" onclick="downItem()" style="width:55px;"/>
	
	            </td>
	        </tr>
	    
	    </table>    
	    </div>
    <script type="text/javascript">
	        nui.parse();
	        var listbox1 = nui.get("listbox1");
	        var listbox2 = nui.get("listbox2");
 
	         //与父页面的方法2对应：页面间传输json数据
		    function setFormData(data){
		                   
		        //跨页面传递的数据对象，克隆后才可以安全使用
		        var infos = nui.clone(data);
		
		        //如果是点击编辑类型页面
		        if (infos.pageType == "edit") {
		        
			      //表单数据回显
		             var json = infos.record;
		             var form = new nui.Form("#form1");//将普通form转为nui的form
		             form.setData(json);
		             var btnEdit1 = nui.get("btnEdit1");
		             btnEdit1.setValue(json.map.ORGID);
		             btnEdit1.setText(json.map.ORGNAME);
		         }
		    }
	         
	
	        function add() {
	            var items = listbox1.getSelecteds();
	            listbox1.removeItems(items);
	            listbox2.addItems(items);
	        }
	        function addAll() {        
	            var items = listbox1.getData();
	            listbox1.removeItems(items);
	            listbox2.addItems(items);
	        }
	        function remove() {
	            var items = listbox2.getSelecteds();
	            listbox2.removeItems(items);
	            listbox1.addItems(items);
	        }
	        function removeAll() {
	            var items = listbox2.getData();
	            listbox2.removeItems(items);
	            listbox1.addItems(items);
	        }
	        function upItem() {
	            var items = listbox2.getSelecteds();
	            for (var i = 0, l = items.length; i < l; i++) {
	                var item = items[i];
	                var index = listbox2.indexOf(item);
	                listbox2.moveItem(item, index-1);
	            }
	        }
	        function downItem() {            
	            var items = listbox2.getSelecteds();            
	            for (var i = items.length-1; i >=0; i--) {
	                var item = items[i];
	                var index = listbox2.indexOf(item);
	                listbox2.moveItem(item, index + 1);
	            }
	        }
	        function saveData() {
	            var data = listbox2.getData();
	            var json = nui.encode(data);
	            alert(json);
	        }
	    </script>

</body>
</html>

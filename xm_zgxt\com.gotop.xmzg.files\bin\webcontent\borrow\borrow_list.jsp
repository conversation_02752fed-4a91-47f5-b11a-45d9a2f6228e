<%@page pageEncoding="UTF-8"%>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): zyp
  - Date: 2017-07-24 16:20:11
  - Description:
-->
<%
UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>


<style>
#table1 .tit{
	height: 10px;
    line-height: 10px;
}
#table1 td{
	height: 10px;
    line-height: 10px;
}
</style>
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>借阅清单查询</title>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	     <table class="table" id="table1" STYLE="width:100%;">
	    <tr>
	     <td class="tit" >申请人：</td>	     
	        <td >
	          <input id="title" name="queryData.apply_empid" class="nui-textbox " />
	        </td>
	        
	        <td class="tit" >申请时间：</td>
	        <td >
	        
	        <input id="queryData.startDate" class="nui-datepicker " name="queryData.startDate" allowInput="true"/>
		    ~
		    <input id="queryData.endDate" class="nui-datepicker " name="queryData.endDate" allowInput="true"/>
	          		
	        </td>
	              
	       <!--  <td class="tit" >申请流程：</td>
	        <td >
	       <input id="queryData.apply_process" class="nui-dictcombobox nui-form-input" name="queryData.apply_process"  emptyText="请选择"
          valueField="dictID" textField="dictName" dictTypeId="APPLY_PROCESS" showNullItem="true" nullItemText="请选择"  />
	        </td>  -->
	        
	        <td rowspan="4" class="btn-wrap">
					<a class="nui-button" iconCls="icon-search" onclick="search">查询</a>
					<a class="nui-button" iconCls="icon-reset"  onclick="clean()"/>重置</a>
				</td>
	        
	      </tr>
	        

	       
	    </table>
	  </div>
    </div>
  </div>
  
 <!--  <div style="margin:10px 0px 0px 0px;"> -->
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
             <a id="accept" class="nui-button" iconCls="icon-edit" onclick="add()">打印交接单</a>
		    <!--  <a id="cancel" class="nui-button" iconCls="icon-no" onclick="CancelPro()">流程撤销</a> -->
           </td>
        </tr>
      </table>
    </div>
 <!--  </div> -->
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.files.borrow.queryBorrowList.biz.ext"
	  sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" onselectionchanged="selectionChanged" >
	    <div property="columns" >
	      <div type="checkcolumn"></div>
	      <div field="APPLY_ORGNAME" headerAlign="center" >申请部门</div>
	      <div field="APPLY_EMPNAME" headerAlign="center" >申请人</div>      	      
	      <div field="PHONE_NUM" headerAlign="center" >联系电话</div>
	      <div field="BORROW_APPLY_REMARK" headerAlign="center" >借阅档案内容</div>
	      <div field="BORROW_APPLY_USE" headerAlign="center" >借阅档案用途</div>
	      <!-- <div field="PHONE_NUM" headerAlign="center" >联系电话</div> -->
	      <div field="APPLY_TIME" headerAlign="center" >申请日期</div>
	      <div field="BORROW_APPLY_WAY" headerAlign="center" renderer="onStatusRenderer">借阅方式</div>
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">
    nui.parse();
    var grid = nui.get("datagrid1");
   // grid.load();
    

    
  function onStatusRenderer(e){
    return nui.getDictText("BORROW_APPLY_WAY", e.row.BORROW_APPLY_WAY);
    }
    
      var form = new nui.Form("#form1"); 
    function search(){
    
       var data = form.getData(true,true);
       grid.load(data);
    }
    
    function reset(){
    var form = new nui.Form("#form1");//将普通form转为nui的form
    form.reset();
}

    function add(){
    
    var row = grid.getSelected();
      
       if(row!=null){
	       var json = nui.encode({map:row});
	       var url="<%=request.getContextPath()%>/files/borrow/exportWorld.jsp?json="+json;
	       window.location.replace(encodeURI(url));
	           /* var a= nui.loading("正在导出world文档中,请稍等...","提示");
		        $.ajax({
		          url:"com.gotop.xmzg.files.borrow.importWorld.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	nui.hideMessageBox(a);
		            var returnJson = nui.decode(text);
					if(returnJson.exception == null){
						nui.alert("导出成功！", "系统提示", function(action){
							grid.reload();
						});
					}else{
						nui.alert("导出失败！", "系统提示");
						grid.unmask();
					}
					
		          }
		        }); */
       }else{
           nui.alert("请选中一条记录！");
       }
    
    
    }


        
         //撤销流程
    function CancelPro()
    {
       var row = grid.getSelected();       
       if(row){
     
      var processInstId=row.PROCESSINSTID;
     
      var data = {map:row};
      var json = nui.encode(data);
      if(isExist(processInstId))
      {
      alert("当前流程已开始审核，无法进行撤销");
      }else
      {
      nui.confirm("确定撤销此流程？","系统提示",
       function(action){
        if(action=="ok"){
       
          $.ajax({
		       url:"com.gotop.xmzg.files.process.cancelProcessApply.biz.ext",
		       type:'POST',
               data:json,
               cache:false,
               contentType:'text/json',
		        success:function(text){
		          var returnJson = nui.decode(text);
		           if(returnJson.exception == null){
                        alert("撤销成功");
                         grid.reload();
                       
                     }else{
                         alert("撤销失败");
                          grid.reload();
                        
                        }
		        }
		      }); 
		      
		    
		    
		    }
		 });
		    
       }
      }
		   
       else{
           nui.alert("请选中一条记录！");
       }
    }    
    
       //判断流程是否已开始审核
    function isExist(value){
      var bool;
      $.ajax({
        url:"com.gotop.xmzg.files.process.isNotAudit.biz.ext",
        type:'POST',
        data:'processInstId='+value,
        cache:false,
        async:false,
        dataType:'json',
        success:function(text){
          bool = text.bool;
        }
      });
      return bool;
    } 
    
    //重置查询信息
	function clean(){
	   	//不能用form.reset(),否则会把隐藏域信息清空
	   	form.reset();  
    }    
  </script>
</body>
</html>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): lyl
  - Date: 2019-05-14 17:15:05
  - Description:
-->
<head>
<title>查看日志</title>
<%@include file="/coframe/tools/skins/common.jsp" %>
    
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <table class="table" style="width:100%;">
	        <tr>
                <th style="width:12%;">系统名称：&nbsp;&nbsp;</th> 
		        <td style="width:28%;"><input name="queryData.sysName" id="queryData.sysName" class="nui-textbox " style="width:170px;"   /></td>
                <th style="width:12%;">操作人：&nbsp;&nbsp;</th> 
		        <td style="width:28%;"><input name="queryData.opUserName" id="queryData.opUserName" class="nui-textbox " style="width:170px;"   /></td>
				<th style="width:60%;">
					<input class="nui-button" text="查询" iconCls="icon-search" onclick="search"/>&nbsp;&nbsp;&nbsp;
					<a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
				</th>
			</tr>
	    </table>
	  </div>
    </div>
  </div>
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.datasourceParam.utilLog.queryLogList.biz.ext"
	  sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" onselectionchanged="selectionChanged">
	    <div property="columns" >
	      <div field="SYS_NAME" headerAlign="center"  align="center" width="100px" >系统名称</div>
	      <div field="SYS_URL" headerAlign="center"  align="center"  width="160px" >系统网址</div>
	      <div field="USER_NAME" headerAlign="center"  align="center" width="40px" >用户名</div>
	      <div field="OP_USERNAME" headerAlign="center"  align="center" width="40px" >操作人</div>
	      <div field="OP_MOD" headerAlign="center"  align="center" width="100px" >操作模块</div>
	      <div field="OP_CONT" headerAlign="center"  align="center" width="150px"  >操作内容</div>
	      <div field="OP_TIME" headerAlign="center"  align="center" width="150px" >操作时间</div>
	    </div>
	 </div>
  </div>
  
 <script type="text/javascript">
    nui.parse();
    var grid = nui.get("datagrid1");
    grid.load();
    
    function search(){
       var form = new nui.Form("#form1");
       var data = form.getData(true,true);
       grid.load(data);
    }
    
    
    //重置
    function clean(){
     	var form = new nui.Form("#form1");
		form.reset();
    }
  </script>
</body>
</html>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): zhanghongkai
  - Date: 2019-06-10 16:29:27
  - Description:
-->
<head>
	<%@include file="/coframe/tools/skins/common.jsp" %>
	<title>Title</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
</head>
<body>

	<a class="nui-button" onClick="test1">设置值</a>
	<a class="nui-button" onClick="test2">获取值</a>
	<div id="form">
		<input id="test" class="nui-textboxlist" >
	</div>

	<script type="text/javascript">
    	nui.parse();
    	
    	var form = new nui.Form("form");
    	
    	function test1(){
    		var test = nui.get("test");
    		test.setText("1,2,3");
    		test.setValue("1,2,3");
    		
    	}
    	
    	function test2(){
    		alert(nui.get("test").getValue());
    	}
    </script>
</body>
</html>
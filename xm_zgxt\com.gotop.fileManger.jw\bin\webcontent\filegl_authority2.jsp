<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- $Header: svn://**************:7029/svn/xm_project/xm_zgxt/com.gotop.fileManger.jw/src/webcontent/filegl_authority2.jsp 1935 2018-09-12 03:44:13Z jw $ -->
<!-- 
  - Author(s): JW
  - Date: 2018-07-25 11:08:29
  -   
-->
<head>
	<% 
		UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
	 %>
<title>文件权限修改</title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<%@include file="/coframe/tools/skins/common.jsp"%>
<script src="<%=request.getContextPath()%>/common/nui/nui.js" type="text/javascript"></script>
<style type="text/css">
html,body {
	padding: 0;
	margin: 0;
	border: 0;
	height: 100%;
	overflow-y:auto;
}
</style>
</head>
<body>
	<div style="padding: 5px;">
		<table border="0" cellpadding="1" cellspacing="2" align="center" style="width: 100%">
			<tr>
				<td colspan="3" align="center">
					<div id="radio_powerType" class="nui-radiobuttonlist" repeatItems="1" repeatLayout="table" repeatDirection="vertical"
						 textField="DICTNAME" valueField="DICTID" dataField="msg.dict" value="1" style="font-size: 13px" onvaluechanged="onValuechanged"
			        	 url="com.gotop.fileManger.jw.action.filegl.ditcfind.biz.ext?dicttypeid=FILE_JURISDICTION_TYPE" value="0"></div>
				</td>
			</tr>
			<tr>
				<td style="width: 15%; text-align: right;">已分配人员：</td>
				<td style="width: 70%; height:40px;" id="check">
					<input id="check_empBox" class="nui-textboxlist belongCheckInput" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
					<input id="down_empBox" class="nui-textboxlist belongDownInput" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
				</td>
				<td style="width: 15%">
	            	<a value="emp" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
	            </td>
			</tr>
			<tr>
				<td style="width: 15%; text-align: right;">已分配角色：</td>
				<td style="width: 70%; height:40px;" id="down">
					<input id="check_roleBox" class="nui-textboxlist belongCheckInput" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
					<input id="down_roleBox" class="nui-textboxlist belongDownInput" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
				</td>
				<td style="width: 15%">
	            	<a value="role" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
	            </td>
			</tr>
			<tr>
				<td style="width: 15%; text-align: right;">已分配机构：</td>
				<td style="width: 70%; height:40px;" id="update">
					<input id="check_orgBox" class="nui-textboxlist belongCheckInput" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
					<input id="down_orgBox" class="nui-textboxlist belongDownInput" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
				</td>
				<td style="width: 15%">
	            	<a value="org" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
	            </td>
			</tr>
			<tr>
				<td style="width: 15%; text-align: right;">已分配群组：</td>
				<td style="width: 70%; height:40px;" >
					<input id="check_groupBox" class="nui-textboxlist belongCheckInput" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
					<input id="down_groupBox" class="nui-textboxlist belongDownInput" style="width: 100%;height:100%;" allowInput="false" valueField="id" textField="text" /> 
				</td >
				<td style="width: 15%">
	            	<a value="group" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
	            </td>
			</tr>
		</table>
	</div>
	<div style="text-align: center; padding: 10px;">
		<a class="nui-button" onclick="saveData()" style="width: 60px; margin-right: 20px;">保存</a>
		<a class="nui-button" onclick="onCancel()" style="width: 60px;">取消</a>
	</div>
	<div id="columnschart" style="height:400px">
</div>
	
	<script type="text/javascript">
		nui.parse();
		var path = '<%=request.getContextPath() %>';
    	var userid= <%=userObject.getUserId()%>;
    	var username="<%=userObject.getUserName()%>";
    	var radio_powerType = nui.get("radio_powerType");
    	var path="<%=request.getContextPath()%>";
//    	var ifChooseOrgRoot = false,orgList = null;
    	var oldCheckPower=[],oldDownPower=[];
    	
    	var checkInputs = $(".belongCheckInput");
		var downInputs = $(".belongDownInput");

		/* 弹出权限设置界面  */
		var powerType,boxType;
		function onClick(e) {
			powerType = radio_powerType.getValue();
			boxType = e.sender.defaultValue;
			nui.open({
	        	url: path + "/hyq/list_powertree.jsp?type="+boxType,
	        	title: "权限配置",
	            iconCls: "icon-edit", 
	            width: 350, 
	            height: 350,
	            onload: function () {
	                var iframe = this.getIFrameEl();  
	                var editTextBox = findEditTextBox(powerType, boxType);
	                iframe.contentWindow.setTreeCheck(editTextBox.getValue());
	            },
	            ondestroy: function (action) {
	            	
					if (action == "ok") {
	                    var iframe = this.getIFrameEl();
	                    var chooseList = iframe.contentWindow.GetData();
	                    chooseList = nui.clone(chooseList);
	                    if (chooseList) {
	                    	putDataTextBox(powerType, boxType, chooseList, "nodeId", "nodeName");
	                    }
	                }
	            }
	        });
		}
	
		/* 保存数据  */
	    function saveData(){	    
			var data = getSaveData();
			var content = "["+username+"]修改文件["+fileName+"]的权限";
	      	$.ajax({
	        	url:"com.gotop.fileManger.jw.action.filegl.addFilePowerInfo.biz.ext",
	        	type:'POST',
	            data:data,
	            traditional: true,
	            success:function(data){
	            	
	 	        	if(data.msg=="success"){
	 	        		sys_log(content, fileName);
	 	        		nui.alert("保存成功", "系统提示", function(action){
	                         if(action == "ok" || action == "close"){
	                             CloseWindow("saveFailed");
	                   		}
	                    });
	            	}else{
	             		nui.alert("保存失败", "系统提示", function(action){
	                         if(action == "ok" || action == "close"){

	                   		}
	                    });
	             	} 
	             }
	         });
	     }
	     
	    function getSaveData() {
	    	var checkList=[],downList=[];
			for (var i=0; i < 4; i++) {
	        	checkList[i] = nui.get(powerTypeName[0]+"_"+map[i]+"Box").getValue();
	        	downList[i] = nui.get(powerTypeName[1]+"_"+map[i]+"Box").getValue();
	        }
			var json = {fileNum:fileNum,
						oldCheckList:oldCheckPower,
						oldDownList:oldDownPower,
						newCheckList:checkList,
	            		newDownList:downList};
	        return json;	
	    }   
	     
		/**
		 * 往textboxlist中添加选择的数据
		 * @params powerType 权限类型
		 * @params boxType	    根据点击按钮的类型 添加到不同box里面
		 * @params list 	    获取Check选中的节点集合
		 */
		var map =["emp", "role", "org", "group"];
		function putDataTextBox(powerType, boxType, list){
			var text = "",value = "";
			var isEmp = (boxType == "emp"),isGroup = (boxType == "group");
			
			var editTextBox = findEditTextBox(powerType, boxType);
			for (var i = 0; i < list.length; i++) {
				var node = list[i];
				if (node.nodeType == "OrgOrganization" && isEmp) continue;
				if (node.nodeType != "Group" && isGroup) continue;
				if (i == list.length -1) {
					value += node["nodeId"];
					text  += node["nodeName"];
				} else {
					value += node["nodeId"] + ",";
					text  += node["nodeName"] + ",";
				}
			}
			if (value.endsWith(",")) value = strSplitLast(value);
			if (text.endsWith(",")) text = strSplitLast(text);
			editTextBox.setValue(value);
			editTextBox.setText(text);
		}
		
/* 		function doRepeatArray(array) {
			// 思路：获取没重复的最右一值放入新数组
			var arr = [];
			for(var i = 0, l = array.length; i < l; i++) {
			  	for(var j = i + 1; j < l; j++)
			   	if (array[i] === array[j]) j = ++i;
			  	arr.push(array[i]);
			}
	        var str = "";
	        for (var j=0; j<arr.length; j++) {
	        	if (j != arr.length -1) {
	        		str += arr[j] +",";
	        	} else {
	        		str += arr[j];
	        	}
	        }
	        return str;
		} */
		
/* 		function onValueChanged() {
			var EditTextBox = findEditTextBox(powerType, boxType);
		 	var editBox = findEditTextBox(2, boxType);
		 	var idStr = EditTextBox.getValue() + "," + editBox.getValue();
		 	var textStr = EditTextBox.getText() + "," + editBox.getText();
		 	EditTextBox.setValue(doRepeatArray(idStr.split(",")));
		 	EditTextBox.setText(doRepeatArray(textStr.split(","))); 
        } */
		
		var fileNum;
		var onlyName;//唯一文件名
		var fileName;
        function SetData(data,oName,fName){
        	fileNum = nui.clone(data);
        	onlyName = nui.clone(oName);
        	fileName = nui.clone(fName);
        	$.ajax({
	    		url:"com.gotop.fileManger.jw.action.filegl.queryFilePermissionById.biz.ext",
	    		type:"post",
	    		data:{fileId:fileNum},
	    		success:function(data){
	    			
					if (data.powerMap != null) {
						var powerIds = data.powerMap.powerIds;
						var powerNames = data.powerMap.powerNames;
						for (var i=0; i<2; i++) {
							var idStr = powerIds[i];
							var nameStr = powerNames[i];
							for (var j=0; j<4; j++) {
								var boxType = map[j];
								var box = findEditTextBox(i, boxType);
								var ids = strSplitLast(idStr[j]);
								box.setValue(ids);
								box.setText(strSplitLast(nameStr[j]));
								if (i == 0) oldCheckPower[j] = ids;
								if (i == 1) oldDownPower[j] = ids;
							}
						}
					} else {
						oldCheckPower=[];
						oldDownPower=[];
					}
	    		}
	    	});
		}
		
		var hideInputs = [checkInputs,downInputs];
		function onValuechanged() {
			var value = radio_powerType.getValue();
			for (var i=0; i<2; i++) {
				if (value == i){
					hideInputs[i].show();
				} else {
					hideInputs[i].hide();
				}
			}
		}
		onValuechanged();
		
		var powerTypeName =['check','down'];
		function findEditTextBox(powerType, boxType) {
		    
	    	var boxId = powerTypeName[powerType]+"_"+boxType+"Box";
	    	return nui.get(boxId);
	    }
	
		function filterOrgChooseList(chooseList) {
			var rootNode = [];
			ifChooseOrgRoot = false;
			for (var i=0; i<chooseList.length; i++) {
				var choosObj = chooseList[i];
				if (choosObj._level == 0) {
					ifChooseOrgRoot = true;
					orgList = chooseList;
					rootNode[0] = choosObj;
				} 
			}
			if (!ifChooseOrgRoot) orgList = null;
			if (rootNode == "" || rootNode == null) return  chooseList;
			else return rootNode;
		}
		
		function sys_log(content,name) {
			var json = nui.encode({
				"mod" : "目录模块",
				"content" : content,
				"name":name
			}); //序列化成JSON
			$.ajax({
				url : "com.gotop.xmzg.util.Excel.syslog.biz.ext",
				type : "post",
				data : json,
				cache : false,
				contentType : 'text/json',
				success : function(data) {
					
				}
			});
		}
		
		//判断当前字符串是否以str结束
	    if (typeof String.prototype.endsWith != 'function') {
	       String.prototype.endsWith = function (str){
	          return this.slice(-str.length) == str;
	       };
	    }
	    /* 切割ID字符串最后一位 */
	    function strSplitLast(obj) {
	    	return (obj).substring(0, obj.length-1);
	    }
		function CloseWindow(action) {
	        if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
	        else window.close();
	    }
	   	/* 确定保存或更新 */
	    function onOk() {
	        saveData();
	    }
	    /* 取消 */
	    function onCancel() {
	        CloseWindow("cancel");
	    }		
	</script>
</body>
</html>
<%@page import="com.alibaba.fastjson.JSONObject"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<%@include file="/coframe/dict/common.jsp"%>
</head>

<body>
  <form id="form1" action="com.gotop.xmzg.achieve.accBelongImp.flow" method="post" enctype="multipart/form-data" style="padding-top:5px;">
    <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
       <tr>
        <th class="nui-form-label"><label for="map.type$text">选择文件：</label></th>
        <td colspan="3" >    
      		<input id="file" type="file" name="importFile" size="60" style="width:100%;"/>
        </td> 
      </tr>
    </table>     
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save" onclick="onOk">导入</a>&nbsp;&nbsp;
	          <span style="display:inline-block;width:25px;"></span>&nbsp;&nbsp;
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  </form>

  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
	<%
    		JSONObject result = (JSONObject)request.getAttribute("result");
 	%>
	var result = <%=result %>;
	if(result != null && result != ""){
		console.log(result);
		nui.alert(result.msg);
		
	}
    	
    var objs = [];
    function setFormData(data){
    }
       
     
    function onOk(){
		var fileName = $("#file").val();
		if(fileName == null || fileName == "") {
			nui.alert("请选择导入的文件！");
			return;
		}
		var sufFileName = fileName.substring(fileName.lastIndexOf("."));
		if(sufFileName != ".xls" && sufFileName != ".xlsx"){
			nui.alert("请选择.xls或.xlsx文件");
			return;
		}
    	nui.loading("正在操作中,请稍等...","提示");
    	$("#form1").submit();	
    }
    
    function onCancel(){
        CloseWindow("close");
    }
    
    function CloseWindow(action){
     	var flag = form.isChanged();
		if(window.CloseOwnerWindow) 
		    return window.CloseOwnerWindow(action);
		else
		    return window.close();
    }
  </script>
</body>
</html>
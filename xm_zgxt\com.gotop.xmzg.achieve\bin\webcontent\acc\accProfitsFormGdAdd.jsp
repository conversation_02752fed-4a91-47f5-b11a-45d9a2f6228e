<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): wsd
  - Date: 2022-02-27 08:34:30
  - Description:
-->
<head>
<title>分润新增</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%
		java.text.SimpleDateFormat formatter = new java.text.SimpleDateFormat("yyyyMMdd"); 
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.add(java.util.Calendar.DAY_OF_MONTH,-1);
        String str_date = formatter.format(cal.getTime());
    %>
</head>
<body>
<div id="form1" style="padding-top:5px;">
	<input id="empTree" class="nui-treeselect" style="display: none;"
	     showTreeIcon="true" dataField="resultList" textField="ORGNAME" valueField="ORGID" parentField="PARENTORGID" />
    <input id="tac_zb_code" name = "tac_zb_code" class="nui-hidden"/>
    <input id="tp_pro" name = "tp_pro" class="nui-hidden"/>
    <input id="tab_begin" name = "tab_begin" class="nui-hidden" value="<%=str_date%>"/>
    <input id="tab_end" name = "tab_end" class="nui-hidden" value="21001231"/>
    <input id="optype" name = "optype" class="nui-hidden" value="add"/>
    <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
     <tr>
        <th class="nui-form-label"><label><span style="color:red">*</span>认领类型：</label></th>
        <td>  
         	<input id="tac_rl_type" name = "tac_rl_type" value="2"
         		class="nui-dictcombobox" required="true"
         		style="width:100%"  valueField="dictID" textField="dictName"
         		dictTypeId="JF_FPLX" onvaluechanged="onTypeChanged"/>  
        </td>
        <th class="nui-form-label"><label><span style="color:red">*</span>业务条线：</label></th>
        <td  >  
         	<div id="TIP_CODE" name="TIP_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TIP_NAME" valueField="TIP_CODE" 
		    	url="com.gotop.xmzg.achieve.configClaPro.tip_get.biz.ext?TP_CLAIM=0" multiSelect="false" allowInput="false" showNullItem="false" emptyText="请选择..."
		    	onvaluechanged="onTipChanged" required="true">     
			    <div property="columns">
			        <div header="业务条线代码" field="TIP_CODE" width="60"></div>
			        <div header="业务条线名称" field="TIP_NAME" width="120"></div>
			    </div>
		    </div>
        </td>
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>指标：</label></th>
        <td colspan="3">  
        	<div id="TI_CODE" name="TI_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TI_NAME" valueField="TI_CODE" 
		    	multiSelect="false" allowInput="false" showNullItem="false" emptyText="请先选择业务条线..." required="true"
		    	onvaluechanged="onTiChanged">     
			    <div property="columns">
			        <div header="指标代码" field="TI_CODE" width="60"></div>
			        <div header="指标名称" field="TI_NAME" width="120"></div>
			    </div>
		    </div>
        </td>
      </tr>
      <tr id="tidTr">
      <th class="nui-form-label"><label for="map.type$text"><span style="color:red">*</span>指标细项：</label></th>
        <td colspan="3">  
        	<div id="TID_CODE" name="TID_CODE" class="nui-combobox" style="width:100%;" dataField="datas" textField="TID_NAME" valueField="TID_CODE" 
		    	multiSelect="false" allowInput="false" showNullItem="false" emptyText="请先选择指标..." required="true"
		    	onvaluechanged="onTidChanged">     
			    <div property="columns">
			        <div header="指标细项代码" field="TID_CODE" width="60"></div>
			        <div header="指标细项名称" field="TID_NAME" width="120"></div>
			    </div>
		    </div>
        </td> 
      </tr>
      <tr >
        <th class="nui-form-label"><label><span style="color:red">*</span>产品编号：</label></th>
        	<td>  
         	<input id="tac_busi_no" required="true" name = "tac_busi_no" class="nui-textbox" style="width:100%" />  
        </td>
        <th class="nui-form-label"><label><span style="color:red">*</span>产品名称：</label></th>
        	<td >  
         	<input id="tac_busi_name" required="true" name = "tac_busi_name" class="nui-textbox" style="width:100%" />  
        </td>
      </tr>
      <tr>
        <th class="nui-form-label"><label>未分配比例：</label></th>
        <td  >  
         	<input id="not_cross_rate" name = "not_cross_rate" class="nui-textbox asLabel" readonly="readonly" style="width:100%"  /> 
         	
        </td> 
        <td colspan="2" >  
        <font color="red">归属客户经理持有全部剩余未分配比例</font> 
        </td> 
      </tr>
    </table>
    
     <div class="nui-fit" height="300px">
     <div class="nui-toolbar" style="border-bottom:0;padding:0px;"  id="btn">
     	<!--  <a id="zb_btn2" class="nui-button" iconCls="icon-remove" onclick="delAccProfits()" plain="true">删除全部分润</a>-->
     	<a id="zb_btn1" class="nui-button" iconCls="icon-add" onclick="addAccProfits()" plain="true">增加分润</a>
     	<a id="zb_btn2" class="nui-button" iconCls="icon-remove" onclick="delAccProfits()" plain="true">删除分润</a>
		<a class="nui-button" iconCls="icon-save" onclick="saveAccProfits()" plain="true">提交分润</a>
	</div>
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:90%;" idField="id"
	  url="com.gotop.xmzg.achieve.acc.queryAccCrossListGd.biz.ext" showPager="false" allowCellEdit="true" allowCellSelect="true" multiSelect="true" 
        editNextOnEnterKey="true"  editNextRowCell="true" oncellbeginedit="OnCellBeginEdit" oncellvalidation="onCellValidation"  onupdate="changeRate" oncellendedit="changeRate">
	  
	  <div name="accProfitsDiv" class="nui-hidden" ></div>
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      <div field="tac_cr_emp" name="tac_cr_emp" vtype="required" headerAlign="center" align="center" renderer="dictEmp">分润客户经理
	      <!-- <input property="editor" class="nui-textbox" style="width:100%;" minWidth="150" required="true"/> -->
	      <input property="editor" class="nui-treeselect"  
				 showTreeIcon="true" dataField="resultList" textField="ORGNAME" valueField="ORGID" parentField="PARENTORGID"  required="true"
	       		 onbeforenodeselect="beforenodeselect"  expandOnLoad="false" onclick="setTree"/>
	      </div>
	      <div field="tac_rate" name="tac_rate" headerAlign="center" align="center">分润比例（单位:%）
	      <input property="editor" class="nui-textbox" style="width:100%;" minWidth="150" required="true"/>
	      </div>
	      <div field="tac_begin" align="center" vtype="required" headerAlign="center" renderer="dateStr">开始时间 
            <input property="editor"  class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd"/>
        </div>
        <div field="tac_end" align="center" vtype="required" headerAlign="center" renderer="dateStr">结束时间
            <input property="editor" class="nui-datepicker" style="width:100%;" allowInput="false" format="yyyyMMdd" />
        </div>
	    </div>
	 </div>
  </div>
    
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel()">关闭</a>
	        </td>
	      </tr>
	    </table>
	 </div>


	<script type="text/javascript">
    	nui.parse();
    	var form = new nui.Form("#form1");
    	var grid = nui.get("datagrid1");
		
		//查询
    	function search(){
       		//获取form中的表单数据
    		var formData = form.getData(true,true);
       		grid.load({"queryData":formData});
    	}
    	
    	function CloseWindow(action){
      		if(window.CloseOwnerWindow) 
        		return window.CloseOwnerWindow(action);
      		else
        		return window.close();
    	}
    	
    	function onCancel(){
    		CloseWindow("cancel");
    	}
    	
    	//单元格编辑前，如果是比例，则不允许修改
    	function OnCellBeginEdit(e){
    		var field = e.field;
            if (field == "tac_rate") {
                //e.cancel = true;
            }
            
            
    	}
    	function onCellValidation(e){
    		if(e.field == "tac_begin"){
    			comparedateBegin(e);
    		}
    		
    		if(e.field == "tac_end"){
    			comparedateEnd(e);
    		}
    	
    		if (e.field == "tac_begin" || e.field == "tac_end") {
        		comparedate(e,e.row.tac_begin,e.row.tac_end);
        	}
        	
    		if(e.field == "tac_rate"){
    		 	if(e.value <= 0){
	        		e.errorText="不能输入负数和0";
	            	e.isValid=false;
        		}
    		}
    	}
    	//增加分润
    	function addAccProfits() {          
            var newRow = { name: "newTac" ,tac_begin: nui.get("tab_begin").getValue(),tac_end: nui.get("tab_end").getValue()};
            grid.addRow(newRow, 0);
            grid.beginEditCell(newRow, "tac_cr_emp");
        }
        
    	//提交分润结果
    	function saveAccProfits(){
    		var gridData = grid.getData();
			var row;
			var sum = 0;
			var empcodeArray = [];
			
			grid.validate();
		      if (grid.isValid() == false) {   
		        var error = grid.getCellErrors()[0];
		        grid.beginEditCell(error.record, error.column);
		        return;
		      }
				      
				//循环校验数据
				for(var i=0;i<gridData.length;i++){
					row = grid.getRow(i);
					var tac_cr_emp = row.tac_cr_emp;
					var tac_rate = row.tac_rate;
					if(tac_cr_emp == "" || tac_cr_emp == null){
						nui.alert("存在分润客户经理为空的分润数据，不能提交！");
						return;
					}
					empcodeArray.push(tac_cr_emp);
					sum=parseFloat(sum)+parseFloat(tac_rate);
				}
				
				//判断是否存在客户经理重复的情况
				/* 	var nary = empcodeArray.sort();
					for(var i = 0; i < nary.length - 1; i++) {
					  if(nary[i] == nary[i + 1]) {
					   nui.alert("存在分润客户经理重复的分润数据，不能提交！");
					    return;
					  }
					} */
					
				if(sum>100){
					nui.alert("当前分润比例总和大于100，不能提交！");
				}else{
					//获取grid中的所有变化的行数据
		    		var handleData = grid.getChanges(null,false);
		    		//获取form中的表单数据
		    		var formData = form.getData(true,true);
		    		
		    		
		    		if(handleData.length ==0){
		    			nui.alert("当前分润数据没有发生变化，不需要提交！");
		    		}else{
		    		$.ajax({
		                url: "com.gotop.xmzg.achieve.acc.handleAccCross.biz.ext",
		                type: 'POST',
		                data:nui.encode({"handleData":handleData,"formData":formData}),
		                cache: false,
		                contentType:'text/json',
		                success: function (json) {
			                var returnJson = nui.decode(json);
							if(returnJson.exception == null && returnJson.iRtn == 0){
								nui.alert("提交分润成功", "系统提示", function(action){
									if(action == "ok" || action == "close"){
										//search();
										onCancel();
									}
								});
							}else{
								var msg = "提交分润失败";
								if(returnJson.msg != null) msg = returnJson.msg;
								nui.alert(msg, "系统提示", function(action){
									if(action == "ok" || action == "close"){
										
									}
								});
							}
						}
		       		});
		    		}
				}
			
    	}
    	
    	//初始化表单数据
    	function setFormData(data) {
			nui.get("empTree").load("com.gotop.xmzg.achieve.acc.getAccEmpZtree.biz.ext?type=0");
			//search();
		}
		
		function beforenodeselect(e) {
			if (e.node.nodeType=="OrgOrganization") {
		        e.cancel = true;
		    }
		}
	
	function dictEmp(e){
    	var empTree=nui.get("empTree").getList();
    	//console.log(empTree);
    	//console.log(e.value);
	    if(e.value != null){
	    	for(var i=0 ; i < empTree.length ; i++){
				if(empTree[i].ORGID == e.value){
					return empTree[i].ORGNAME;
				}
	    	}
	    }
    } 
    function changeRate(e){
    	var gridData = grid.getData();
    	var sum = 0;
    	for(var i=0;i<gridData.length;i++){
			row = grid.getRow(i);
			//console.log(row);
			var tac_rate = row.tac_rate;
			if(tac_rate){
				sum=parseFloat(sum)+parseFloat(tac_rate);
			}
			//console.log(sum);
		}
		sum = 100 - sum;
		nui.get("not_cross_rate").setValue(sum);
    }
    function setTree(e){
    	 nui.get(this).setData ( nui.get("empTree").getData());
    }
    
	function delAccProfits() {
        	var rows = grid.getSelecteds();
        	if (rows.length > 0) {
            	grid.removeRows(rows, true);
            	changeRate();
        	}
    	}
	
	function comparedate(e,startDate,endDate){
    	if(startDate instanceof Date){
    		startDate = nui.formatDate ( startDate, 'yyyyMMdd' );
    	}
    	if(endDate instanceof Date){
    		endDate = nui.formatDate ( endDate, 'yyyyMMdd' );
    	}
      if(startDate!="" && startDate!=null && startDate.length > 10)
	    startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
	  if(endDate!="" && endDate!=null && endDate.length > 10)
		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
         if(e.isValid){
              if(startDate > endDate){
	            e.errorText="结束日期必须大于开始日期";
	            e.isValid=false;
             }else{
		          e.errorText="";
		          e.isValid=true;
		          if(e.row.tac_begin_old == startDate 
		          		&& e.row.tac_end_old == endDate
		          		&& e.row.tac_cr_emp_old == e.row.tac_cr_emp
		          		&& e.row.tac_rate_old == e.row.tac_rate) grid.acceptRecord(e.row);
             }
        }
    }
    
	    function comparedateBegin(e){
	    	var startDate = e.value;
	    	if(startDate instanceof Date)
	    		startDate = nui.formatDate ( startDate, 'yyyyMMdd' );
	    	
	    	if(startDate!="" && startDate!=null && startDate.length > 10)
		    	startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
		  
	    	var curr = nui.formatDate ( new Date(), 'yyyyMMdd' );
	         if(curr <= startDate){
	         	e.errorText="开始日期必须小于当前时间";
	            e.isValid=false;
	         }
	    }
	    function comparedateEnd(e){
	    	var startDate = e.value;
	    	if(startDate instanceof Date)
	    		startDate = nui.formatDate ( startDate, 'yyyyMMdd' );
	    	
	    	if(startDate!="" && startDate!=null && startDate.length > 10)
		    	startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
		  
	    	var curr = nui.formatDate ( new Date(), 'yyyyMMdd' );
	        if(curr >= startDate){
		     	e.errorText="结束日期必须大于当前时间";
		        e.isValid=false;
		     }
	    }
	    function dateStr(e){
	    	var endDate = e.value;
	    	if(endDate instanceof Date){
	    		endDate = nui.formatDate ( endDate, 'yyyyMMdd' );
	    	}
	    	if(endDate!="" && endDate!=null && endDate.length > 10){
	    		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
	    	}
	    	return endDate;
	    }
	    
	   	var tip = nui.get("TIP_CODE");
	    var ti = nui.get("TI_CODE");
	    var tid = nui.get("TID_CODE");
	    function onTipChanged(e,val){
	    	nui.get("tp_pro").setValue("");
	    	grid.setData(null);
	        ti.setValue("");
	        tid.setValue("");
	        tid.setData(null);
	        if(!val) val = tip.getValue();
	        var url = "com.gotop.xmzg.achieve.configClaPro.ti_get.biz.ext?code=" + val+"&TP_CLAIM="+nui.get("tac_rl_type").getValue();
	        ti.setUrl(url);
	    }
	    
	    function onTiChanged(e,val){
	        tid.setValue("");
	        if(!val) val = ti.getValue();
	        var url = "com.gotop.xmzg.achieve.configClaPro.tid_get.biz.ext?code=" + val+"&TP_CLAIM=0";
	        tid.setUrl(url);
	        
	        if(nui.get("tac_rl_type").getValue() == "1"){
	        	nui.get("tac_zb_code").setValue(val);
	        	nui.get("tp_pro").setValue(e.selected.TP_PRO);
	        	search();
	        }else{
	        	nui.get("tp_pro").setValue("");
	    		grid.setData(null);
	        }
	    }
    	function onTidChanged(e,val){
    		if(!val) val = tid.getValue();
    		if(nui.get("tac_rl_type").getValue() == "2"){
	        	nui.get("tac_zb_code").setValue(val);
	        	nui.get("tp_pro").setValue(e.selected.TP_PRO);
	        	search();
	        }else{
	        	nui.get("tp_pro").setValue("");
	    		grid.setData(null);
	        }
    	}
    	
    	function onTypeChanged(e) {
			typeChanged(e.value);
	    }
	    function typeChanged(val) {
	    	tip.setValue("");
	    	ti.setValue("");
	        tid.setValue("");
	        ti.setData(null);
	        tid.setData(null);
	    	nui.get("tp_pro").setValue("");
	    	grid.setData(null);
			 if(val == 2){
			 	$("#tidTr").show();
			 }else{
			 	$("#tidTr").hide();
			 }
	    }
    </script>
</body>
</html>
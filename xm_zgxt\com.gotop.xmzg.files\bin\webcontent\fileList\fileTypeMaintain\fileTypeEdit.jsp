<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%@page import="com.eos.data.datacontext.UserObject" %>
<%@include file="/coframe/dict/common.jsp"%>
<html>
<!-- 
  - Author(s): lyl
  - Date: 2019-12-04 09:36:10
  - Description:
-->
<head>
</head>
<%
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>
<body>
	<div id="fileTypeForm" style="padding-top:5px;">
  		<input id="MANAGER_ORG_NAME" name = "inputData.MANAGER_ORG_NAME"  class="nui-hidden"  />
		<input class="nui-hidden"  name="inputData.orgId" value="<%=userObject.getUserOrgId()%>" />
		<input class="nui-hidden"  name="inputData.TYPE_ID"  />
		<table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
	      	<tr>
				<th class="nui-form-label"><label for="type$text">档案种类名称：</label></th>
				<td colspan="4">
					<input id="files_type" class="nui-dictcombobox nui-form-input" name="inputData.FILES_TYPE"  emptyText="请选择"
  					valueField="dictID" textField="dictName" dictTypeId="FILES_TYPE" showNullItem="true" nullItemText="请选择"  style="width:150px;" required = "true" allowInput = "false"/>
  				</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">档案类别：</label></th>
				<td colspan="4">
					<input id="files_type" class="nui-dictcombobox nui-form-input" name="inputData.FILES_CATEGORY"  emptyText="请选择"
  					valueField="dictID" textField="dictName" dictTypeId="FILE_CATEGORY" showNullItem="true" nullItemText="请选择"  style="width:150px;" required = "true" allowInput = "false"/>
  				</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">分区类别：</label></th>
				<td colspan="4">
					<input id="files_type" class="nui-dictcombobox nui-form-input" name="inputData.PARTITION_CATEGORY"  emptyText="请选择"
  					valueField="dictID" textField="dictName" dictTypeId="PARTITION_CATEGORY" showNullItem="true" nullItemText="请选择"  style="width:150px;" required = "true" required = "true" allowInput = "false"/>
  				</td>
	      	</tr>
	      		<th class="nui-form-label"><label for="type$text">管理部门：</label></th>
  				<td colspan="4">
  					<input id="MANAGER_ORG" name = "inputData.MANAGER_ORG"  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;" required = "true"/>
	      		</td>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">说明：</label></th>
	      		<td colspan="4">
	      			<input name="inputData.REMARK" id="REMARK" class="nui-textbox" style="width:150px;"/>
	      		</td>
	      	</tr>
		</table>
	    <div class="nui-toolbar" style="padding:0px;"   borderStyle="border:0;">
			<table width="100%">
		    	<tr>
		        	<td style="text-align:center;">
		          		<a class="nui-button" iconCls="icon-save" id="saveButtorn"  onclick="saveData">保存</a>
		          		<span style="display:inline-block;width:25px;"></span>
		          		<a class="nui-button" iconCls="icon-cancel" onclick="CloseWindow('cancel')">取消</a>
		        	</td>
		      	</tr>
		   	</table>
		</div>
	</div>
<script type="text/javascript">
    nui.parse();
    var fileTypeForm = new nui.Form("#fileTypeForm");
    
    //与父页面的方法2对应：页面间传输json数据
    function setFormData(data){
                   
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);
        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
             var json = infos.record;
             var form = new nui.Form("#fileTypeForm");//将普通form转为nui的form
             form.setData(json);
             var orgName = json.inputData.MANAGER_ORG_NAME;
             nui.get("MANAGER_ORG").setText(orgName);
             nui.get("MANAGER_ORG_NAME").setValue(orgName);
             //showOrgName(orgId);
         }
    }
    
    //关闭添加窗口
 	function CloseWindow(action){
  		if(window.CloseOwnerWindow) 
    		return window.CloseOwnerWindow(action);
  		else
    		return window.close();
    }
    
    //显示机构名称
   /*  function showOrgName(e){
    	var orgId = e;
    	nui.get("MANAGER_ORG").setValue(orgId);
    	$.ajax({
				url:"com.gotop.xmzg.files.fileList.getOrgInfo.biz.ext",
				type:'post',
				data:nui.encode({orgId:orgId}),
				cache:false,
				async:false,
				contentType:'text/json',
				success: function (text) {
					var returnJson = nui.decode(text);
					var ORGCODE = returnJson.result.ORGCODE;
					var ORGNAME = returnJson.result.ORGNAME;
					 //机构回显
            		//nui.get("MANAGER_ORG").setValue(orgId);
					nui.get("MANAGER_ORG").setText(ORGNAME);
				}
				
			});
    }
     */
    //保存数据
    function saveData(){
    	fileTypeForm.validate();            
        if (fileTypeForm.isValid() == false) return;
        var inputData = fileTypeForm.getData(true,true);
        var json = nui.encode(inputData);
        var URL="com.gotop.xmzg.files.fileList.uptFileType.biz.ext";
        $.ajax({
        	url: URL,
            type: 'POST',
            data: json,
            cache: false,
            contentType:'text/json',
            success: function (text){
                var returnJson = nui.decode(text);
				if(returnJson.flag == "1"){
					nui.alert("编辑成功", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							CloseWindow("saveSuccess");
						}
					});
				}else if(returnJson.flag == "exist"){
					nui.alert("档案种类已存在！");
				}else{
					nui.alert("编辑失败", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							//CloseWindow("saveFailed");
						}
					});
				}
		    }
  		});   
    }
  
	//机构树选择回显
     function OrgonButtonEdit(e){
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/files/fileList/multi_org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                onload: function(){
                	var iframe = this.getIFrameEl();  
	                var editTextBox = nui.get("MANAGER_ORG");   
	                iframe.contentWindow.setTreeCheck(editTextBox.getValue());
                },
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.id);
                            btnEdit.setText(data.text);
                            nui.get("MANAGER_ORG_NAME").setValue(data.text);
                        }
                    }
                }
            });            
             
        } 
        
        //人员选择树回显
        function selectEmp(){
    		var emp = nui.get("EMPNAME");
            nui.open({
                url:"<%=request.getContextPath()%>/files/archiveList/empTree.jsp?type=emp",
                title: "选择人员",
                width: 600,
                height: 400,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = emp.getValue();
                	//frame.contentWindow.setData(data);
                	frame.contentWindow.setTreeCheck(data.value);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);
                         //必须
                        if (data) {
                            emp.setValue(data.nodeId);
                            emp.setText(data.nodeName);
                        }
                    }

                }
            });
    	}  
</script>
</body>
</html>
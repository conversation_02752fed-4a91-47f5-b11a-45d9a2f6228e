<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): zhanghongkai
  - Date: 2019-05-14 15:58:00
  - Description:
-->
<head>
	<%@include file="/coframe/tools/skins/common.jsp" %>
	<title>批量导入</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script type="text/javascript" src="<%=request.getContextPath() %>/js/jquery.form.js"></script>
</head>
<body>
	<form id="form" action="com.gotop.xmzg.fileImportOrExport.importColumns.flow" method="post" enctype="multipart/form-data">
		<input id="impType" class="nui-hidden"  name="paramMap/impType"  />  <!-- 导入文件类型，txt或excel -->
   		<input id="impFileFlag" class="nui-hidden"  name="paramMap/impFileFlag" value="2" />  <!-- 导入文件标志，1：导入标题头， 2：导入数据 -->
   		<input id="opWay" class="nui-hidden"  name="paramMap/opWay" value="1"  />  <!-- 操作方式（1：页面手动数据导入， 2：定时器自动导入） -->
   
   		<input id="menuId" class="nui-hidden" value="1" name="logMap/menuId" />
   		<input id="menuName"  class="nui-hidden" value="空" name="logMap/menuName" />
   		<input id="opContent"  class="nui-hidden" value="导入" name="logMap/opContent" />
		<table border="0" cellpadding="1" cellspacing="2" style="width:100%;table-layout:fixed;">
			<tr>
				<th style="text-align:right;">业务条线：</th>
				<td>
					<input class="nui-dictcombobox" required="true" valueField="dictID" textField="dictName" style="width:100%;"
      					dictTypeId="BUSINESS_LINE" name="map.BUSINESS_LINE" emptyText="请选择.." onValueChanged="businessLineChanged"/>
				</td>
				<td></td>
			</tr>
			<tr>
				<th style="text-align:right;">文件类别名称：</th>
				<td>
					<input class="nui-combobox" required="true" valueField="ID" textField="FILE_NAME" style="width:100%;"
      					name="paramMap/mId" emptyText="请选择.." id="fileName" dataField="resultList" onValueChanged="fileNameChanged"/>
				</td>
				<td></td>
			</tr>
			<tr>
				<th style="text-align:right;">文件选择：</th>
				<td>
					<input id="file" type="file" name="importFile" size="60" style="width:100%;" required="required"/>
				</td>
				<td></td>
			</tr>
			<tr id="tr_separator" style="display:none;">
    			<th style="text-align:right;">
                	数据文件数据分隔符：
    			</th>
    			<td >
    	  			<input class="nui-textbox" name="paramMap/separator" style="width: 100%;" required="true"/>
    			</td>
   			</tr>	
			<tr>
				<td></td>
				<td style="text-align:center;">
					<a class="nui-button" iconCls="icon-ok" onClick="startUpload">导入</a>&nbsp
					<a class="nui-button" iconCls="icon-reset" onclick="reset">重置</a>&nbsp
					<a class="nui-button" iconCls="icon-cancel" onClick="cancel">取消</a>
				</td>
				<td></td>
			</tr>
		</table>
	</form>


	<script type="text/javascript">
    	nui.parse();
    	
    	var form = new nui.Form("form");
    	
    	<%
    		Object object = request.getAttribute("result");
    		String result = "";
    		if(object != null){
    			result = object.toString();
    		}
    	%>
    	
    	var result = "<%=result %>";
    	if(result != null && result != ""){
    		if(result == "ok"){
    			nui.alert("导入成功","系统提示",function(action){
	    			closeWindow("ok");
	    		});
    		}else{
    			nui.alert("导入成功","系统提示",function(action){
        			closeWindow("ok");
        		});
    		}
    	}
    	
    	function startUpload(){
    		//$("#import_form").submit();
    		var fileName = $("#file").val();
    		var sufFileName = fileName.substring(fileName.lastIndexOf("."));
    		var impType = nui.get("impType").getValue();
    		if(impType == "excel"){
    			if(sufFileName != ".xls"){
    				nui.alert("请选择.xls文件");
    				return;
    			}
    		}else if(impType == "txt"){
    			if(sufFileName != ".txt"){
    				nui.alert("请选择.txt文件");
    				return;
    			}
    		}
    		b= nui.loading("正在操作中,请稍等...","提示");
    		$("#form").submit();
    		/* $("#form").ajaxSubmit({
    			url:"com.gotop.xmzg.fileImportOrExport.importModelMaintain.importColumns.biz.ext",
    			type:"post",
    			dataType:"json",
    			success:function(text){
    				nui.hideMessageBox(b);
                	if(text.result == "ok"){
                		nui.hideMessageBox(b);
                		nui.alert("导入成功","系统提示",function(action){
                			closeWindow("ok");
                		});
                	}else{
                		nui.hideMessageBox(b);
                		nui.alert("导入失败","系统提示",function(action){
                			closeWindow("fail");
                		});
                	}
    			}
    		}); */
    		//var formData = new FormData($("#form")[0]);
           // $.ajax({
             //   type: 'post',
             //   url: "com.gotop.xmzg.fileImportOrExport.importModelMaintain.importColumns.biz.ext",
             //   data: formData,
             //   cache: false,
              //  processData: false,
              //  contentType: false,
              //  success:function(text){
               // 	if(text.result == "ok"){
                //		nui.hideMessageBox(b);
                //		nui.alert("导入成功","系统提示",function(action){
                //			closeWindow("ok");
                	//	});
                	//}else{
                	//	nui.hideMessageBox(b);
                	//	nui.alert("导入失败","系统提示",function(action){
                	//		closeWindow("fail");
                	//	});
                	//}
                //}
            //});
    	}
    	
    	function reset(){
    		form.reset();
    		var file = document.getElementById('file');
    		file.outerHTML = file.outerHTML;
    	}
    	
    	function fileNameChanged(e){
    		var fileNameId = e.value;
    		var data = e.sender.data;
    		for(var i=0;i<data.length;i++){
    			if(data[i].ID == fileNameId){
    				var fileType = data[i].FILE_TYPE;
    				if(fileType == 1){
    					nui.get("impType").setValue("excel");
    				}else if(fileType == 2){
    					nui.get("impType").setValue("txt");
    				}
    				if(fileType == 2){
    					$("#tr_separator").show();
    				}else{
    					$("#tr_separator").hide();
    				}
    			}
    		}
    	}
    	
    	function businessLineChanged(e){
    		var business_line = String(e.value);
    		var url = "com.gotop.xmzg.fileImportOrExport.fileImport.getFileNames.biz.ext?business_line=" + business_line;
    		nui.get("fileName").load(url);
    	}
    	
    	function cancel(){
    		closeWindow("cancel");
    	}
    	
    	function closeWindow(action){
    		return window.CloseOwnerWindow(action);
    	}
    </script>
</body>
</html>
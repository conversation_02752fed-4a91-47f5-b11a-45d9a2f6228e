<%@page import="com.gotop.xmzg.files.FilesDownloadUtils"%>
<%@page import="java.io.File"%>
<%@page import="com.eos.server.dict.DictManager"%>
<%@page import="com.pfpj.foundation.database.DatabaseExt"%>
<%@page import="java.util.HashMap"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false" %>
<% 
	

	String dirPath = request.getParameter("filepath");//路径
	String dirName = request.getParameter("filename");//名称
   
 
    String path=dirPath;

   	//下载文件
   	File file = new File(path);
   	String exists = null;
   	if(file.exists()){
   		exists = "true";
		//普通文件下载方式
		FilesDownloadUtils.downloadFile2(path,dirName,response);
	 	out.clear();  
     	out = pageContext.pushBody();
   	}else{
   		exists = "false";
   	}
%>	

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<title>文件下载</title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
</head>
<body>
	<script type="text/javascript">
		var exists = "<%=exists%>";
		if(exists == "false"){
			alert("文件不存在");
		}
	</script>
</body>
</html>
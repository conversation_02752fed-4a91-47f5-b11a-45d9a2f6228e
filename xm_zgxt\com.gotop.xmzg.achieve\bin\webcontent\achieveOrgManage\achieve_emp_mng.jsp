<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2022-02-27 19:59:31
  - Description:
-->
<head>
<title>机构业绩分配</title>
<%@include file="/coframe/dict/common.jsp"%>
</head>
<body>
	<div id="form1" style="padding-top:5px;">
		<div class="nui-hidden"  name="map.TAO_ID" id="TAO_ID" ></div>
	    <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">     
	       <tr>
	        <th class="nui-form-label"><label for="map.TID_CODE$text">指标细项编号：</label></th>
	        <td colspan="3" >    
	        	<input id="map.TID_CODE" name="map.TID_CODE"   style="width:95%;" class="nui-textbox" required="true"  allowInput="false"/>
	        </td> 
	      	<th class="nui-form-label"><label for="map.TID_NAME$text">指标细项名称：</label></th>
	        <td colspan="3" >    
  		        <input id="map.TID_NAME" name="map.TID_NAME"   style="width:95%;" class="nui-textbox" required="true"  allowInput="false"/>
	        </td> 
	      </tr>
	      <tr>
	      	<th class="nui-form-label"><label for="map.TAO_ORG$text">机构号：</label></th>
	        <td colspan="3" >    
	          <input id="map.TAO_ORG" name="map.TAO_ORG"   style="width:95%;" class="nui-textbox" required="true"  allowInput="false"/>
	        </td> 
	      	<th class="nui-form-label"><label for="map.TAO_ORGNAME$text">机构名称：</label></th>
	        <td colspan="3" >    
	          <input id="map.TAO_ORGNAME" name="map.TAO_ORGNAME"   style="width:95%;" class="nui-textbox" required="true"  allowInput="false"/>
	        </td> 
	      </tr>
	      <tr>
	      	<th class="nui-form-label"><label for="map.TAO_NUM$text">业绩值：</label></th>
	        <td colspan="3" >    
	          <input id="map.TAO_NUM" name="map.TAO_NUM"   style="width:95%;" class="nui-textbox" required="true"  allowInput="false"/>
	        </td> 
	      	<th class="nui-form-label"><label for="map.TAO_CREATE_TIME$text">业绩日期：</label></th>
	        <td colspan="3" >    
	          <input id="map.TAO_CREATE_TIME" name="map.TAO_DATE" style="width:95%;"  class="nui-textbox" required="true" format="yyyy-MM-dd"  allowInput="false" enabled="false" />
	        </td> 
	      </tr>
	    </table>
    </div>
    
    <div>
		 <div class="nui-toolbar" style="border-bottom:0;" id="btnDiv">
			<table style="width:100%">
				<tr>
					<td>
					    <input id="empids" class="nui-hidden"  />
						<a class="nui-button" iconCls="icon-add" onclick="selectManager">添加客户经理</a>
						<a class="nui-button" iconCls="icon-remove" onclick="removeManagerRow">移除客户经理</a>
						<a class="nui-button" iconCls="icon-remove" onclick="removeAllManagerRow">移除全部客户经理</a>
					</td>
				</tr>
			</table>
		</div>
	</div>
	
	<div class="nui-fit">
		 <div id="datagrid1" idField="proInput" class="nui-datagrid" style="width:100%;height:330px;" showPager="false" 
	          allowCellEdit="true" allowCellSelect="true" multiSelect="true" allowCellValid="true" oncellvalidation="onCellValidation">
		    <div property="columns" >
		      <div field="EMPID" class="nui-hidden" visible="false">EMPID</div>
		      <div field="TAOD_ID" class="nui-hidden" visible="false">TAOD_ID</div>
		      <div field="EMPNAME" headerAlign="center"  align="center" >客户经理</div>
		      <div field="TAOD_NUM" headerAlign="center"  align="center">客户经理业绩值
		      	 <input property="editor" vtype="int;maxLength:9" class="nui-textbox" style="width:100%;"/>
		      </div>
		    </div>
		 </div>
	</div>
	   
	<div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%" >
	      <tr>
	        <td style="text-align:center;">
	          <a id="save" class="nui-button" iconCls="icon-save" onclick="onOk">保存</a>&nbsp;&nbsp;
	          <span style="display:inline-block;width:25px;"></span>&nbsp;&nbsp;
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
	 
	<script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    var datagrid1 = nui.get("#datagrid1");
    form.setChanged(false);
    var obj = [];
    function setFormData(data){      
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);
        if(infos.type == "detail"){
        	$("#save").hide();
        	$("#btnDiv").hide();
        	
        }
    	//表单数据回显
         var json = infos.record;
         var form = new nui.Form("#form1");//将普通form转为nui的form
         form.setData(json);
         getIndexList(json.map);
    }
    
    //获取输入项列表
    function getIndexList(map){	
        if(map!=null){               
	        json = nui.encode({queryData:map});     
	        $.ajax({
	            url: "com.gotop.xmzg.achieve.achieveOrgMng.queryEmpNumlist.biz.ext",
	            type: 'POST',
	            data:json,
	            cache: false,
	            contentType:'text/json',
	            success: function (json) {      
		            var returnJson = nui.decode(json);
					if(returnJson.exception == null){
					    var obje = nui.decode(returnJson.resultList);
					    var empids = "";
						for(var i=0;i<obje.length;i++){
							var obj = obje[i];
							if (i == obje.length -1) {
								empids += obj["EMPID"];
							} else {
								empids += obj["EMPID"] + ",";
							}
							datagrid1.addRow(obj);
						}
						nui.get("empids").setValue(empids);
					}else if(returnJson.exception == null){
						return 2;
					}
			    }
	       });
       }
    }
    function addRow(){
    	var newRow = { name: "New Row" };
        datagrid1.addRow(newRow, 0);
    }
    
    function onCellValidation(){
    }
    
    function onOk(){
        saveData();
    }
    
    function saveData() {
        form.validate();
        if (form.isValid() == false) return;       
       	var json = form.getData(false,true);
		var dataDetail = datagrid1.getData(false,true);
		json.dataDetail=dataDetail;
        //JIAOYAN
        var detail = dataDetail;
        if(detail && detail.length >0 ){
    		var allnum = 0;
    		var tao_num = parseFloat(json.map.TAO_NUM);
	    	for(var i = 0 ;i < detail.length ;i++){
	    		var TAOD_NUM = detail[i].TAOD_NUM;
	    		if(TAOD_NUM!=null&&typeof(TAOD_NUM)!="undefined"&&TAOD_NUM!=""){
		    		var num = parseFloat(TAOD_NUM);
		    		if(isNaN(num)){
		    			nui.alert("第"+(i+1)+"行业绩值必须为数字", "系统提示");
		    			return false;
		    		}else{
		    			allnum = allnum + num;
		    		}
		    		/* if(tao_num < 0 ){
		    			if(num >= 0){
		    				nui.alert("总业绩为负数，第"+(i+1)+"行业绩值必须为负数", "系统提示");
		    				return false;
		    			}
		    		} */
	    		}
	    	}
    		//alert("all="+allnum);
    		
    		if(isNaN(tao_num)){
    			nui.alert("机构业绩值异常，保存失败", "系统提示");
    			return false;
    		}
    		
    		if(tao_num >=0 && allnum > tao_num){
    			nui.alert("已分配业绩超过总业绩，保存失败", "系统提示");
    			return false;
    		}
    		
    		if(tao_num <0){
    			if(allnum < tao_num){
	    			nui.alert("已分配业绩超过总业绩，保存失败", "系统提示");
	    			return false;
    			}
    		}
    	}
    	//else{
    	//}
        json = nui.encode(json);  
        
        $.ajax({
            url: "com.gotop.xmzg.achieve.achieveOrgMng.insertEmpNum.biz.ext",
            type: 'POST',
            data:json,
            cache: false,
            contentType:'text/json',
            success: function (json) { 
	            var returnJson = nui.decode(json);
				if(returnJson.exception == null && returnJson.flag=="1"){			
					nui.alert("保存成功", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							CloseWindow("saveSuccess");
						}
					});
				}else if(returnJson.exception != null){
					nui.alert("保存失败", "系统提示");
				}else {
					nui.alert("保存失败", "系统提示");
				}
		    }
       }); 
	} 
       
    function onCancel(){
        CloseWindow("cancel");
    }
    
    function CloseWindow(action){
     	var flag = form.isChanged();
		if(window.CloseOwnerWindow) 
		    return window.CloseOwnerWindow(action);
		else
		    return window.close();
    }
    
	//人员、机构回显
     function selectManager() {
     		//var orgcode = nui.get("map.TAO_ORG").getValue();
            var btnEdit1 = nui.get("empids");
            nui.open({
                url:"<%=request.getContextPath() %>/achieve/achieveOrgManage/EmpAndOrg_tree.jsp?orgcode=",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                onload : function() {
					var iframe = this.getIFrameEl();
					iframe.contentWindow.setTreeCheck(btnEdit1.getValue());
				},
                ondestroy : function(action) {
							if (action == "ok") {
								var iframe = this.getIFrameEl();
								var list = iframe.contentWindow.GetData();
								list = nui.clone(list);
								if (list) {
									// 将树选中节点设置到box显示
									putDataTextBox(list);
								}
							}
						}
            });            
             
        } 
        
         /**
		 * 往textboxlist中添加选择的数据
		 * @params list 	 获取Check选中的节点集合
		 */
		function putDataTextBox(list){
			var text = "",value = "";
			var boxObj = nui.get("empids");
			for (var i = 0; i < list.length; i++) {
				var node = list[i];
				if (node.nodeType == "OrgOrganization") continue;
				if (i == list.length -1) {
					value += node["id"];
					text += node["text"];
				} else {
					value += node["id"] + ",";
					text += node["text"] + ",";
				}
			}
			if (value.endsWith(",")) value = strSplitLast(value);
			boxObj.setValue(value);
			addManagerRow(list);
		}  
		
		
		function addManagerRow(list){
			var dataDetail = datagrid1.getData(false,true);
			for (var i = 0; i < list.length; i++) {
				var node = list[i];
				if (node.nodeType == "OrgOrganization") continue;
				if(!hasOwnEmp(dataDetail,node)){
					datagrid1.addRow({"EMPID":node["id"],"EMPNAME":node["text"]});
				}
			}
		}
		
		function removeManagerRow(){
			var row = datagrid1.getSelected();
			if(row){
				datagrid1.removeRow( row, true );
				var empids = nui.get("empids");
				//移除empid
				var replacestr = removeEmpidsUnit(empids , row["EMPID"]);
				empids.setValue(replacestr);
			}else{
				nui.alert("请选择一行进行移除", "系统提示");
			}
		}
		
		function removeEmpidsUnit(empids , empid){
			var empidstr = ","+empids.getValue()+",";
			if(empidstr.indexOf(","+empid+",", 0) > -1){
				empidstr = empidstr.replace(","+empid+"," , ",");
			}
			return empidstr.substring(1, empidstr.length -1);
		}
		
		function removeAllManagerRow(){
			//清空grid
			datagrid1.setData({count:0});
			//移除empid
			nui.get("empids").setValue("");
		}
		
		function hasOwnEmp(dataDetail,node){
			for(var j = 0; j < dataDetail.length; j++){
				var row = dataDetail[j];
				if(node["id"] == row["EMPID"]){
					return true;
				}
			}
			return false;
		}
		
		//判断当前字符串是否以str结束
	     if (typeof String.prototype.endsWith != 'function') {
	       String.prototype.endsWith = function (str){
	          return this.slice(-str.length) == str;
	       };
	     }
	     
	     /* 切割ID字符串最后一位 */
		function strSplitLast(obj) {
			return (obj).substring(0, obj.length - 1);
		}
  </script>
</body>
</html>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): 54305
  - Date: 2022-02-26 12:27:15
  - Description:
-->
<head>
<style>
#table1 .tit{
	height: 10px;
    line-height: 10px;
    	text-align: right;
}
#table1 td{
	height: 10px;
    line-height: 10px;
}
#table1 .roleLabel{
	text-align: right;
}
#table1 .roleText{
	padding-left: 5px;
}
</style>
<%@include file="/coframe/tools/skins/common.jsp" %>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<title>机构业绩查询</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <link id="css_icon" rel="stylesheet" type="text/css" href="/default/coframe/tools/icons/icon.css"/>
	<script type="text/javascript" src="/default/common/nui/nui.js"></script>
	<script type="text/javascript" src="/default/common/nui/locale/zh_CN.js"></script>
	<link id="css_skin" rel="stylesheet" type="text/css" href="/default/coframe/tools/skins/skin1/css/style.css" />
</head>
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");

 %>
<body>
	<div class="search-condition">
		<div class="list">
			<div id="form1">
				<table class="table" style="width:100%;">							
					<tr>			
						<td class="tit" STYLE="width:100px;">考核方案：</td>
						<td class="">
						<input  id="queryData.TA_ID" name="queryData.TA_ID" class="nui-combobox"   onvaluechanged="tAChange" textField="TEXT" valueField="ID"  dataField="list"style="width:150px;"
                  			  required="true"   />
						</td>
						<td class="tit" STYLE="width:100px;">汇总类型：</td>
						<td class="">
						<input  id="queryData.RAIS_DATA_TYPE" name="queryData.RAIS_DATA_TYPE" class="nui-combobox"   data="TYPE_DICT"  textField="text" valueField="id" dataField="list"style="width:150px;"
                  			  required="true"   />
						</td>
						
						<td><td>
						<td><td>
						  <th rowspan="2"><a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp; 
				            <input class="nui-button" text="重置" iconCls="icon-reset" onclick="clean"/>
				       </th>			      
					</tr>
					
					<tr>			
						<td class="tit" STYLE="width:100px;">统计日期：</td>
						<td>
							<input id="queryData.DATE_BEGIN" class="nui-datepicker" name="queryData.DATE_BEGIN"  style="width:150px;" allowInput="false" required="true"/>
			                
						</td>
						
						<td class="tit" STYLE="width:100px;">机构：</td>
						<td>
							<input id="queryData.ORGCODE" name = "queryData.ORGCODE"  class="nui-buttonedit" allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;" required="true"/>
						</td>
						
						
						<td class="tit" STYLE="width:100px;">统计周期：</td>
						<td class="">
						<input  id="queryData.SORT_TYPE" name="queryData.SORT_TYPE" class="nui-combobox"   data="SORT_DICT"  textField="text" valueField="id" dataField="list"style="width:150px;"
                  			  required="true"   />	
						</td>
						
					</tr>
					
					
				</table>
			</div>
		</div>
	</div>
		 <div class="nui-toolbar" style="border-bottom:0;">
			<table style="width:100%">
				<tr>
					<td>
						<a class="nui-button" iconCls="icon-add" onclick="excel">导出Excel</a>								
					</td>
				</tr>
			</table>
	</div>
    <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.achieve.report.org_integral_report.biz.ext"   
	  sizeList=[5,10,20,50,100] multiSelect="false" pageSize="10"
	  >
	    <div property="columns" >
	             <div field="TAIS_DATE" >统计日期</div>
	            <div field="YJZH" >一级支行</div>
	            <div field="TAIS_ORGNAME" >二级支行</div>
	            <div field="TAIS_EMPNAME" >客户经理名称</div>
	            <div field="TAIS_EMPCODE" >客户经理编号</div>
	            <div field="TA_NAME" >考核方案</div>
	            <div field="TIP_NAME" >业务条线</div>
	            <div field="TI_NAME" >指标</div>
	            <div field="TID_NAME" >指标细项</div>
	            <div field="HU" >人数</div>
				<div field="TAIS_ACHIEVE_PRA" >业绩</div>
				<div field="TAIS_INTEGRAL" >积分</div>		
	    </div>
	 </div>
  </div>


	
</body>
<script type="text/javascript">
	var TYPE_DICT =[{ id: "0", text: '指标细项合计' },{ id: "1", text: '指标合计'},{ id: "2", text: '业务条线合计'},{ id: "3", text: '业务条线统计'}];
	var SORT_DICT =[{ id: "0", text: '月度' },{ id: "1", text: '季度'},{ id: "2", text: '年度'}];
	nui.parse();
	var grid = nui.get("datagrid1");
	
   
	
	//初始化加载
	$(function(){
		loadAccess();
		nui.get("queryData.RAIS_DATA_TYPE").setValue("0");
		nui.get("queryData.SORT_TYPE").select(1);
	
	});
	
	
	//隐藏下拉

	//加载考核方案
	function loadAccess(){
			nui.get("queryData.TA_ID").load("com.gotop.xmzg.achieve.report.loadTACombox.biz.ext");
			nui.get("queryData.TA_ID").select(0);
			tAChange();
			setOrgInfo();
	}
	
	function search(){
		loadGrids();
        var form = new nui.Form("#form1");
        form.validate();
        if (form.isValid() == false) return;
        var data = form.getData(true,true);   
        
        grid.load(data);
    }
    	
	//机构树点击事件
	function OrgonButtonEdit(e) {	
        var btnEdit = this;
        nui.open({
            url:"<%=request.getContextPath() %>/achieve/report/org_tree.jsp",
        showMaxButton: false,
        title: "选择树",
        width: 350,
        height: 350,
        ondestroy: function (action) {                    
            if (action == "ok") {
                var iframe = this.getIFrameEl();
                var data = iframe.contentWindow.GetData();
                data = nui.clone(data);
                if (data) {
                    btnEdit.setValue(data.CODE);
                    btnEdit.setText(data.TEXT);
                    var form = new nui.Form("#form1");
       				form.validate();
                    
                }
            }
        }
    	});            
	}
	
	//导出Excel
	function excel(){
		      
		var form=new nui.Form("form1");
		form.validate();
	    if (form.isValid() == false) return;
		var data=form.getData(true, true);
		var fileName="机构业绩查询报表";
		var type = nui.get("queryData.RAIS_DATA_TYPE").getValue();
		var queryPath="";
		if(type == 0){
			queryPath ="com.gotop.xmzg.achieve.report.query_org_integral_report_0";
		}else if(type == 1){
			queryPath ="com.gotop.xmzg.achieve.report.query_org_integral_report_1";
		}else if(type == 2){
			queryPath ="com.gotop.xmzg.achieve.report.query_org_integral_report_2";
		}else if(type == 3){
			queryPath ="com.gotop.xmzg.achieve.report.query_org_integral_report_3";
		}
		var columns=grid.getBottomColumns();
		columns=columns.clone();
		for(var i=0;i<columns.length;i++){
			var column=columns[i];
			if(!column.field){
				columns.removeAt(i);
			}else{
				var c={header:column.header,field:column.field };
				columns[i]=c;
			}
		}
		var sheet = nui.get("queryData.RAIS_DATA_TYPE").getText();
		columns=nui.encode(columns);
		data=nui.encode(data);
	    var url="<%=request.getContextPath()%>/achieve/report/exportExcelAc.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data+"&sheet="+sheet;
		window.location.replace(encodeURI(url));
		 
		//设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
	    setMask();
	} 
	
	function clean(){
	   var form = new nui.Form("#form1");
	   form.clear();
	}
	
	function loadGrids(){
	
		var RAIS_DATA_TYPE = nui.get("queryData.RAIS_DATA_TYPE").getValue();
		var columns = [];
		if(RAIS_DATA_TYPE == 0){
		columns= [
		{ field: "TA_NAME", headerAlign: "center",align: "center", header: "考核方案" },
		 { field: "TAIS_DATE", headerAlign: "center",align: "center", header: "统计日期" },
		 { field: "YJZH", headerAlign: "center",align: "center", header: "一级支行" },
		 { field: "TAIS_ORGNAME", headerAlign: "center",align: "center", header: "二级支行" },
		 { field: "TIP_NAME", headerAlign: "center",align: "center", header: "业务条线" },
		 { field: "TI_NAME", headerAlign: "center",align: "center", header: "指标" },
		 { field: "TID_NAME", headerAlign: "center",align: "center", header: "指标细项" },
		 { field: "HU", headerAlign: "center",align: "center", header: "人数" },
		 { field: "TAIS_ACHIEVE_PRA", headerAlign: "center",align: "center", header: "业绩" },
		 { field: "TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "积分" }
		]
		}else if (RAIS_DATA_TYPE == 1){
		columns= [
		{ field: "TA_NAME", headerAlign: "center",align: "center", header: "考核方案" },
		 { field: "TAIS_DATE", headerAlign: "center",align: "center", header: "统计日期" },
		 { field: "YJZH", headerAlign: "center",align: "center", header: "一级支行" },
		 { field: "TAIS_ORGNAME", headerAlign: "center",align: "center", header: "二级支行" },
		 { field: "TIP_NAME", headerAlign: "center",align: "center", header: "业务条线" },
		 { field: "TI_NAME", headerAlign: "center",align: "center", header: "指标" },
		 { field: "TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "积分" }
		]
		}else if (RAIS_DATA_TYPE == 2){
		columns= [
		 { field: "TA_NAME", headerAlign: "center",align: "center", header: "考核方案" },
		 { field: "TAIS_DATE", headerAlign: "center",align: "center", header: "统计日期" },
		 { field: "YJZH", headerAlign: "center",align: "center", header: "一级支行" },
		 { field: "TAIS_ORGNAME", headerAlign: "center",align: "center", header: "二级支行" },
		 { field: "TIP_NAME", headerAlign: "center",align: "center", header: "业务条线" },
		 { field: "TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "积分" }
		]
		}else if (RAIS_DATA_TYPE == 3){
		columns= [
		 { field: "TA_NAME", headerAlign: "center",align: "center", header: "考核方案" },
		 { field: "TAIS_DATE", headerAlign: "center",align: "center", header: "统计日期" },
		 { field: "TIP_NAME", headerAlign: "center",align: "center", header: "业务条线" },
		 { field: "YJZH", headerAlign: "center",align: "center", header: "一级支行" },
		 { field: "TAIS_ORGNAME", headerAlign: "center",align: "center", header: "二级支行" },
		 { field: "TAIS_INTEGRAL", headerAlign: "center",align: "center", header: "积分" }
		]
		}
		
		
		grid.set({
		    		 "columns":columns
		    	});
	}
	
	
	
	function tAChange(){
		var TA_ID = nui.get("queryData.TA_ID").getValue();
		var json= {"TA_ID":TA_ID};
		$.ajax({
			url: "com.gotop.xmzg.achieve.report.getTaMaxDate.biz.ext",
            type: 'POST',
            data: nui.encode(json),
            async:false,
            cache: false,
            contentType:'text/json',
            success: function (text){
           	 	var returnJson = nui.decode(text);
           	 	console.log(returnJson.result.length);
           	 
           	 	if(returnJson.result.length>0){
           	 		var BDATE = returnJson.result[0]["BDATE"];
           	 		var EDATE = returnJson.result[0]["EDATE"];
           	 		console.log(BDATE);
           	 		console.log(EDATE);
           	 		nui.get("queryData.DATE_BEGIN").setValue(BDATE);
           	 	}
           	 	
           
			}
		});
		
	}
	
	
	function setOrgInfo(){
		$.ajax({
				url: "com.gotop.xmzg.achieve.report.getUserInfo.biz.ext",
	            type: 'POST',
	            
	            async:false,
	            cache: false,
	            contentType:'text/json',
	            success: function (text){
	           	 	var returnJson = nui.decode(text);
	           	 	console.log(returnJson.userInfo);
	           	 	
	       	 		var ORGCODE = returnJson.userInfo.ORGCODE;
	       	 		var ORGNAME = returnJson.userInfo.ORGNAME;
	       	 		console.log(ORGCODE);
	       	 		console.log(ORGNAME);
	       	 		nui.get("queryData.ORGCODE").setValue(ORGCODE);
	       	 		nui.get("queryData.ORGCODE").setText(ORGNAME);
	           	 		
	           	 		
	           	 		
	           	 	
	           	 	
	           
				}
			});
	}
    </script>
</html>
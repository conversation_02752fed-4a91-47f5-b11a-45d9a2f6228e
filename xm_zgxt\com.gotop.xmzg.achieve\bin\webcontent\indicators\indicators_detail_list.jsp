<%@page pageEncoding="UTF-8"%>	
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2019-06-04 14:51:16
  - Description:
-->
<head>
	<title>指标细项查询</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%@include file="/achieve/init.jsp"%>
</head>

<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="send_bw" class="nui-form">
	    <table class="table" style="width:100%;">
	        <tr>
				<th class="tit">业务条线：</th> 
		        <td>
			      	<div id="tip_code" name="obj/tip_code" class="nui-combobox" allowInput="false" 
				      	dataField="list" textField="TIP_NAME" valueField="TIP_CODE" style="width:300px;"
				      	showNullItem="true" nullItemText="全部" nullItemText="全部" emptyText="全部"
				      	onvaluechanged="onDeptChanged"
				      	url="com.gotop.xmzg.achieve.indicators.indicators_plate_choice.biz.ext">
			      		<div property="columns">
					        <div header="条线代码" field="TIP_CODE" width="60"></div>
					        <div header="条线名称" field="TIP_NAME" width="120"></div>
					    </div>
					</div>
		        </td>
		        <th class="tit">指标：</th> 
		        <td>
			      	<div id="ti_code" name="obj/ti_code" class="nui-combobox" allowInput="false" 
				      	dataField="list" textField="TI_NAME" valueField="TI_CODE" style="width:300px;"
				      	showNullItem="true" nullItemText="全部" nullItemText="全部" emptyText="全部"
				      	>
				     	<div property="columns">
					        <div header="指标代码" field="TI_CODE" width="60"></div>
					        <div header="指标名称" field="TI_NAME" width="120"></div>
					    </div>
					</div>
		        </td>
		    </tr>
		    <tr>
		    	<th class="tit">数据来源：</th> 
		        <td colspan="3">
		        	<input class="nui-dictcombobox" id="tid_datasource" valueField="dictID" textField="dictName" 
		        	showNullItem="true" nullItemText="全部" nullItemText="全部" emptyText="全部"
		        	style="width:300px;"
					dictTypeId="JF_DATASOURCE" name="obj/tid_datasource"/>
		        </td>
				<td>
					<a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;
					<a class="nui-button" iconCls="icon-reset"  onclick="clean">重置</a>
				</td>
		    </tr>
	    </table>
	  </div>
    </div>
  </div>
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
             <a class="nui-button" iconCls="icon-add" onclick="add">新增</a>
             <a class="nui-button" iconCls="icon-edit" onclick="update">修改</a>
             <a class="nui-button" iconCls="icon-remove" onclick="del">删除</a>
             <a class="nui-button" iconCls="icon-ok" onclick="qy">启用</a> 
		     <a class="nui-button" iconCls="icon-no" onclick="jy">禁用</a>
		     <a class="nui-button" iconCls="icon-node" onclick="updJf">修改积分标准</a>
		     <input class="nui-hidden" id="orgs"/>
           </td>
        </tr>
      </table>
    </div>

  <div class="nui-fit">
	 <div id="bw_grid" class="nui-datagrid"
			 style="height: 100%;"
			 idField="id" 
			 totalField="page.count"  
			 showPageInfo="true"
			 multiSelect="false"  
			 pageSize="20"
			 sizeList=[5,10,20,50,100]
	         dataField="list"
			 url="com.gotop.xmzg.achieve.indicators.indicators_detail_list.biz.ext"
			 >
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      <div field="TIP_ORGCODES" visible="false"></div>
	      <div field="TI_CODE" visible="false"></div>
	      <div field="TI_SORTING" visible="false"></div>
	      <div field="TIP_CODE" headerAlign="center" align="center" allowSort="true" >业务条线代码</div>
		  <div field="TIP_NAME" headerAlign="center" align="center" allowSort="true" >业务条线名称</div>
		  <div field="TI_CODE" headerAlign="center" align="center" allowSort="true" >指标</div>
		  <div field="TI_NAME" headerAlign="center" align="center" allowSort="true" >指标名称</div>
		  <div field="TID_CODE" headerAlign="center" align="center" allowSort="true" >指标细项</div>
		  <div field="TID_NAME" headerAlign="center" align="center" allowSort="true" >指标细项名称</div>
		  <div field="TID_REMARK" headerAlign="center" align="center" allowSort="true" >指标细项说明</div>
		  <div field="TID_PROPORTION" headerAlign="center" align="center" allowSort="true">积分标准</div>
		  <div field="TID_UNIT" headerAlign="center" align="center" allowSort="true" renderer="tid_unit">指标单位</div>
		  <div field="TID_PROTECTION" headerAlign="center" align="center" allowSort="true" renderer="tid_protection">是否设置保护期</div>
		  <div field="TID_PROTECTION_DATE" headerAlign="center" align="center" allowSort="true" >保护期天数</div>
		  <div field="TID_INTEGRAL_CALCUL" headerAlign="center" align="center" allowSort="true" renderer="tid_integral_calcul">是否参与积分计算</div>
		  <div field="TID_DATASOURCE" headerAlign="center" align="center" allowSort="true" renderer="tid_datasource">数据来源</div>
		  <div field="TID_FREQUENCY" headerAlign="center" align="center" allowSort="true" renderer="tid_frequency">计算频率</div>
		  <div field="TID_START" headerAlign="center" align="center" allowSort="true" renderer="tid_start">指标细项状态</div>
		  <div field="TID_CREATETIME" headerAlign="center" align="center" allowSort="true" renderer="setdate">创建时间</div>
		  <div field="CREATEORGNAME" headerAlign="center" align="center" allowSort="true" >创建机构</div>
		  <div field="CREATORNAME" headerAlign="center" align="center" allowSort="true" >创建人</div>
	    </div>
	 </div>
  </div>
 <script type="text/javascript">
    nui.parse();
    var grid = nui.get("bw_grid");
	
	//切换数据
	function onDeptChanged(){
        var json = nui.encode({tip_code:nui.get("tip_code").getValue(),type:2});
	   	//提交数据
        nui.ajax({
           url: "com.gotop.xmzg.achieve.indicators.indicators_choice.biz.ext",
           type: "post",
           data: json,
           contentType:'text/json',
           success: function (text) {
        	  var obj = text.list;
              nui.get("ti_code").setData(obj);
           }
       });
	}
	
	search();
	//查询
    function search() {
    	var form = new nui.Form("#send_bw");
 		form.validate();
      	if (form.isValid() == false) return;
      	grid.load(form.getData());
      	//插入系统操作日志
        var OPE_MOD = "指标细项管理维护";
    	var OPE_CONTENT = "查询,输入项:"+nui.get("tip_code").getValue()+"|"+nui.get("ti_code").getValue()+"|"+nui.get("tid_datasource").getValue();
      	insert_sysope_log(OPE_MOD,OPE_CONTENT);
	}
	//转换时间
    function setdate(e){
 		var date = e.record.TID_CREATETIME;
 		if(!isNullOrEmpty(date) && date.length == 14){
 			return changeDate(date);
 		}else{
 			return "";
 		}
 	}
  	//指标单位
    function tid_unit(e){
		var tid_unit = e.record.TID_UNIT;
		return nui.getDictText("JF_UINT",tid_unit);
	}
  	//是否设置保护期 
    function tid_protection(e){
		var tid_protection = e.record.TID_PROTECTION;
		return nui.getDictText("JF_YES_NO",tid_protection);
	}
  	//是否参与积分计算
    function tid_integral_calcul(e){
		var tid_integral_calcul = e.record.TID_INTEGRAL_CALCUL;
		return nui.getDictText("JF_YES_NO",tid_integral_calcul);
	}
  	//数据来源
    function tid_datasource(e){
		var tid_datasource = e.record.TID_DATASOURCE;
		return nui.getDictText("JF_DATASOURCE",tid_datasource);
	}
  	//计算频率
    function tid_frequency(e){
		var tid_frequency = e.record.TID_FREQUENCY;
		return nui.getDictText("JF_FREQUENCY",tid_frequency);
	}
  	//状态
    function tid_start(e){
		var tid_start = e.record.TID_START;
		return nui.getDictText("JF_STATE",tid_start);
	}
    
  	
	
    //打开添加页面
	function add(){
		nui.open({
			url:bactpath+"/achieve/indicators/indicators_detail_add.jsp",
			title:"指标明细新增",
			width:800,
			height:500,
			onload:function(){},
			ondestroy:function(action){
				if(action=="saveSuccess"){
	                grid.reload();
	            }
			}
		});
	}
	
	//打开修改页面
	function update(){
		var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
			nui.get("orgs").setValue(rows[0].TIP_ORGCODES);
			contain(function(){
				nui.open({
					url:bactpath+"/achieve/indicators/indicators_detail_update.jsp",
					title:"指标明细修改",
					width:800,
					height:500,
					onload:function(){
						var iframe = this.getIFrameEl();
	 	      	    	iframe.contentWindow.setData(rows[0]);
					},
					ondestroy:function(action){
						if(action=="saveSuccess"){
			                grid.reload();
			            }
					}
				});
			});
		}
	}
	//删除
	function del(){
		var rows = grid.getSelecteds();
		if(rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
			nui.get("orgs").setValue(rows[0].TIP_ORGCODES);
			contain(function(){
				nui.confirm("确定删除以下选择的数据？", "确定？",
		           function (action) {
		             if (action == "ok") {
		            	 var newMaps = [];
		            	 var str = "";
		            	 for(var i = 0;i<rows.length;i++){
		            		 if(rows[i].TID_START=="1"){
		            			 var ms = rows[i].TI_NAME+"指标明细已启用，无法删除操作!";
		            			 nui.alert(ms, "系统提示", function(action){});
		            			 return ;
		            		 }
		            		 var ids = {tid_code:rows[i].TID_CODE,tip_name:rows[i].TID_NAME};
		            		 newMaps.push(ids);
		            		 if(i==rows.length-1){
		            			 str+=rows[i].TIP_CODE;
		            		 }else{
		            			 str+=rows[i].TIP_CODE+",";
		            		 }
		            	 }
		            	 str = "删除以下【"+str+"】指标明细";
		            	 var json = nui.encode({maps:newMaps,str:str});
		            	 var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
		            	 //提交数据
	                     nui.ajax({
	                        url: "com.gotop.xmzg.achieve.indicators.indicators_detail_del.biz.ext",
	                        type: "post",
	                        data: json,
	                        contentType:'text/json',
	                        success: function (text) {
	                           nui.hideMessageBox(load);  //隐藏遮罩层
	                     	   var msg = text.msg;
	                     	   nui.alert(msg, "系统提示", function(action){});
	                     	   grid.reload();
	                        }
	                    });
		             }
				});
			});
		}
	}
	
	function qy(){
    	qyjy(1,"启用");
    }
    function jy(){
    	qyjy(0,"禁用");
    }
    function qyjy(TID_START,text){
      	var rows = grid.getSelecteds();
      	if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
			nui.get("orgs").setValue(rows[0].TIP_ORGCODES);
			contain(function(){
	      	  	var newMaps = [];
	      	  	var str = "";
	      	   	for(var i = 0 ; i < rows.length ; i++){
	      	   	  if(rows[i].TID_START == TID_START){
	      	   	    nui.alert(rows[i].TID_NAME+"已"+text);
	      	   	  	return ;
	      	   	  }
	      	   	  var ids = {tid_code:rows[i].TID_CODE,tid_name:rows[i].TID_NAME,tid_start:TID_START};
		      	  newMaps.push(ids);
		      	  if(i==rows.length-1){
		   			 str+=rows[i].TID_CODE;
		   		  }else{
		   			 str+=rows[i].TID_CODE+",";
		   		  }
	      	   	}
	      	  	str = text+"以下【"+str+"】指标明细";
		       	var json = nui.encode({maps:newMaps,text:text,str:str});
		       	var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
		        $.ajax({
		          url:"com.gotop.xmzg.achieve.indicators.update_indicators_detail_start.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	nui.hideMessageBox(load);
	          	    var msg = text.msg;
	          	    nui.alert(msg, "系统提示", function(action){});
	          	    grid.reload();
		          }
		        });
			});
      }
    }
	
	function updJf(){
		var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
			nui.open({
				url:bactpath+"/achieve/indicators/indicatorsDetail_dt_list.jsp",
				title:"积分标准修改",
				width:950,
				height:600,
				onload:function(){
					var iframe = this.getIFrameEl();
 	      	    	iframe.contentWindow.setData(rows[0]);
				}
			});
		}
	}
	
  //插入系统操作日志
    function insert_sysope_log(OPE_MOD,OPE_CONTENT){
		$.ajax({
				url: "com.gotop.xmzg.achieve.indicators.insert_sysope_log.biz.ext",
				type: 'POST',
				data : nui.encode({"OPE_MOD":OPE_MOD,"OPE_CONTENT":OPE_CONTENT}),
				cache: false,
				contentType:'text/json',
				success: function (res) {  
				}
		});
	}
	//重置
	function clean(){
        nui.get("tip_code").setValue("");
        nui.get("ti_code").setValue("");
        nui.get("tid_datasource").setValue("");
 	}
  </script>
</body>

</html>
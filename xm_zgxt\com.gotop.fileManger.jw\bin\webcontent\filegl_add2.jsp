<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="com.eos.data.datacontext.UserObject"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- $Header: svn://**************:7029/svn/xm_project/xm_zgxt/com.gotop.fileManger.jw/src/webcontent/filegl_add2.jsp 1463 2018-08-14 01:33:12Z jw $ -->
<!-- 
  - Author(s): JW
  - Date: 2018-07-25 11:08:29
  -   
-->
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
 %>
<head>
<title>文件添加</title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<%@include file="/coframe/tools/skins/common.jsp"%>
<script src="<%=request.getContextPath()%>/common/nui/nui.js" type="text/javascript"></script>
<style type="text/css">
html,body {
	padding: 0;
	margin: 0;
	border: 0;
	height: 100%;
	overflow-y: auto;
}

.mini-textarea {
	width: 100%;
}

#ss {
	border: 1px solid black;
}

.mini-popup .mini-shadow {
	height: 150px;
}

.mini-textboxlist {
	width: 100%;
	height: 20px;
}

.mini-radiobuttonlist-table .mini-radiobuttonlist-td {
	width: 50px;
	padding: 5px;
	border: 1px solid #ccc;
	border-radius: 5px;
	background-color: #ccc;
}
/* .mini-radiobuttonlist-table .mini-radiobuttonlist-td:nth-child(1){
	background-color: #ccc;
} 
.mini-radiobuttonlist-table .mini-radiobuttonlist-td:nth-child(2){
	background-color: blue;
}
.mini-radiobuttonlist-table .mini-radiobuttonlist-td:nth-child(3){
	background-color: red;
}
.mini-radiobuttonlist-table .mini-radiobuttonlist-td:nth-child(4){
	background-color: aqua;
}*/
.mini-radiobuttonlist-item {
	text-align: center;
}

.mini-radiobuttonlist-item label {
	font-size: 15px;
}
.errorText{
	color:red;
	font-size: 12px;
}
</style>
</head>
<body>
	<fieldset style="border: solid 1px #aaa; padding: 3px;">
		<legend>文件基本信息</legend>
		<div style="padding: 5px;">
			<form id="filefrom" method="post">
				<table border="0" cellpadding="1" cellspacing="2" align="center" style="width: 100%">
					<tr>
						<td style="width: 15%; text-align: right;">文号：</td>
						<td style="width: 70%"><input id="fileNo" name="file.fileNo" class="mini-textbox" required="true" style="width: 100%;" onblur="checkFileNo"/></td>
						<td style="width: 15%;" id="fileno_error" class="errorText"><input id="fileno_cheack" class="mini-hidden" /></td>
					</tr>
					<tr>
						<td style="width: 15%; text-align: right;">关键字：</td>
						<td style="width: 70%"><input name="file.fileKey" class="mini-textbox" required="true" style="width: 100%;" /></td>
						<td style="width: 15%;"></td>
					</tr>
					<tr>
						<td style="width: 15%; text-align: right;">所属目录：</td>
						<td style="width: 70%"><input id="vest_meun" class="mini-treeselect" multiSelect="false" valueFromSelect="false" textField="LISTNAME"
							name="file.listId" valueField="LISTID" parentField="LISTPID" allowInput="true" showRadioButton="true" showFolderCheckBox="true" style="width: 100%;"
							onvaluechanged="valuechanged"/></td>
						<td style="width: 15%;"><input class="mini-hidden" id="dirname" name="dirname"></td>
					</tr>
					<tr>
						<td style="width: 15%; text-align: right;">文件名：</td>
						<td style="width: 70%"><input id="fileName" name="file.fileName" class="mini-textbox" required="true" style="width: 100%;" allowInput="false"/></td>
						<td style="width: 15%;" id="filename_error" class="errorText"><input id="filename_cheack" class="mini-hidden" /></td>
					</tr>
				</table>
				<input id="uploadname" name="file.uploadName" class="mini-hidden" /> <input name="file.fileStatus" class="mini-hidden" value="1" />
				<input id="fileid" name="file.fileId" class="mini-hidden" />
				<input id="path" class="mini-hidden" />
				<input id="previewname" class="mini-hidden" />
			</form>
			<form id="upload" method="post" enctype="multipart/form-data">
				<table border="0" cellpadding="1" cellspacing="2" align="center" style="width: 100%">
					<tr>
						<td style="width: 15%; text-align: right;">文件：</td>
						<td style="width: 70%"><input class="mini-htmlfile" name="Fdata" id="uploadfile" style="width: 100%;" onfileselect="onFileSelect" /></td>
						<td style="width: 15%;"></td>
					</tr>
				</table>
			</form>
		</div>
	</fieldset>
	<fieldset style="border: solid 1px #aaa; padding: 3px;">
		<legend>权限信息</legend>
		<div style="padding: 5px;">
			<table border="0" cellpadding="1" cellspacing="2" align="center" style="width: 100%">
				<tr>
					<td colspan="2" align="center">
						<div id="radio_type" class="mini-checkboxlist" repeatItems="4" repeatLayout="table"
						 textField="DICTNAME" valueField="DICTID" dataField="msg.dict" onvaluechanged="onValuechanged"
							url="com.gotop.fileManger.jw.action.filegl.ditcfind.biz.ext?dicttypeid=FILE_JURISDICTION_TYPE" value="0"></div>
					</td>
				</tr>
				<tr>
					<td style="width: 15%; text-align: right;">已分配人员：</td>
					<td style="width: 70%"><input id="empBox" class="mini-textboxlist hide" name="check" textName="tblName" required="true" valueField="id"
						textField="text" /></td>
					<td style="width: 15%"><a value="emp" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a></td>
				</tr>
				<tr>
					<td style="width: 15%; text-align: right;">已分配角色：</td>
					<td style="width: 70%" id="down"><input id="roleBox" class="mini-textboxlist hide" name="role" textName="tblName" required="true" valueField="id"
						textField="text" /></td>
					<td style="width: 15%"><a value="role" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a></td>
				</tr>
				<tr>
					<td style="width: 15%; text-align: right;">已分配机构：</td>
					<td style="width: 70%"><input id="orgBox" class="mini-textboxlist hide" name="emp" textName="tblName" required="true" valueField="id" textField="text" />
					</td>
					<td style="width: 70%"><a value="org" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a></td>
				</tr>
				<tr>
					<td style="width: 15%; text-align: right;">已分配群组：</td>
					<td style="width: 70%"><input id="groupBox" class="mini-textboxlist hide" name="emp" textName="tblName" required="true" valueField="id"
						textField="text" /></td>
					<td style="width: 15%"><a value="group" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a></td>
				</tr>
			</table>
		</div>
	</fieldset>
	<div style="text-align: center; padding: 10px;">
		<a class="mini-button" onclick="submitForm()" style="width: 60px; margin-right: 20px;">确定</a> 
		<a class="mini-button" onclick="onCancel()" style="width: 60px;">取消</a>
	</div>
	<script src="<%= request.getContextPath() %>/js/jquery.form.js" type="text/javascript"></script>
	
	<script type="text/javascript">
			nui.parse();
			
			/* 0（预览） 1（修改） 2（删除） 3（下载）  */
			var username="<%=userObject.getUserName()%>";
			//console.log(username);
			//路径
			var path="<%= request.getContextPath() %>";
			var powerBox = nui.get("radio_type");
			var map =["emp", "role", "org", "group"];
			//所属目录id
			var vestmeun=nui.get("vest_meun");
			
			var form = new nui.Form("#filefrom");
			var fileinfo=nui.get("#uploadfile");
			
			var powerObj = {emp: nui.get("empBox"),role: nui.get("roleBox"),
					    org: nui.get("orgBox"),group: nui.get("groupBox")};
			var oldType,oldData = [];
			
			//标准方法接口定义
	        function SetData(data,datalist,username){
	        	//跨页面传递的数据对象，克隆后才可以安全使用
	        	data = nui.clone(data);	        	
	        	vestmeun.loadList(datalist, "LISTID", "LISTPID");
	        	vestmeun.setValue(data.id);
	        	nui.get("uploadname").setValue(username);
	        	filenames();   
			}
			 //获取文件名
			function filenames(){
				nui.get("dirname").setValue(vestmeun.getSelectedNode().LISTNAME);
			}
			//下拉框值改变事件
			function valuechanged(e){
				filenames();
				checkFileName(nui.get("fileName").getValue());
			}
	        //文件浏览按钮触发事件
			function onFileSelect(e){				
				var path=fileinfo.value;								
				filename=path.substring(path.lastIndexOf("\\")+1,path.length);
				nui.get("fileName").setValue(filename);
				checkFileName(filename);
			} 
			//动态查询文件文号是否重复
	        function checkFileNo(e){
	        	var filenoerror =document.getElementById("fileno_error");
				filenoerror.innerHTML ="";
				nui.get("fileno_cheack").setValue(true);
	        	var meunid=vestmeun.getSelectedNode().LISTID;//获取id
	        	//console.log(e.sender.value);
	        	var json = nui.encode({"fileno":e.sender.value,"meunid":meunid}); //序列化成JSON
	        	$.ajax({
					url : "com.gotop.fileManger.jw.action.filegl.checkFileNo.biz.ext",
					type : "post",
					data : json,
					cache : false,
					contentType : 'text/json',
					success : function(data) {
						//console.log(data);
						if(data.msg.resCode==1){			          				           
				            filenoerror.innerHTML = data.msg.resDes;
				            nui.get("fileno_cheack").setValue(false);
						}
					}			
				}); 
	        }
	      	//动态查询文件名是否重复
	        function checkFileName(filename){
	        	nui.get("fileid").setValue("");
	        	nui.get("path").setValue("");
	        	nui.get("previewname").setValue("");
	        	var filenameerror =document.getElementById("filename_error");
	        	filenameerror.innerHTML ="";
	        	nui.get("filename_cheack").setValue(true);
	        	var meunid=vestmeun.getSelectedNode().LISTID;//获取id
	        	var filename=nui.get("fileName").getValue();//文件名
	        	var json = nui.encode({"filename":filename,"meunid":meunid}); //序列化成JSON
	        	$.ajax({
					url : "com.gotop.fileManger.jw.action.filegl.checkFileName.biz.ext",
					type : "post",
					data : json,
					cache : false,
					contentType : 'text/json',
					success : function(data) {
						//console.log(data);
						if(data.msg.resCode==1){			          				           
							filenameerror.innerHTML = "已存在文件名";
							nui.get("filename_cheack").setValue(false);
							var file=data.msg.file[0];
							nui.get("path").setValue(file.FILEPATH);
							nui.get("previewname").setValue(file.PREVIEWNAME);
							nui.get("fileid").setValue(file.FILEID);
							getSameData(file.FILEID);
						}else{
							getSameData("");
						}
						
					}			
				}); 
	        }
		
			//文件添加事件
			function submitForm() {
				var listjs=vestmeun.getSelectedNode().LISTPID;//获取pid
				var meunid=vestmeun.getSelectedNode().LISTID;//获取id
				var meunName=vestmeun.getSelectedNode().LISTNAME;//获取目录名称
				var fileno=nui.get("fileNo").getValue();//文号
				var filename=nui.get("fileName").getValue();//文件名
				var fileid=nui.get("fileid").getValue();//文件id
				if(listjs===0){
					nui.alert("请选择二级目录添加文件", "系统提示", function(action){});
				}else{
					checkfilenoorname(fileid,fileno,filename,meunid,meunName);
				}
			}
			//文件重复查询（文号和文件名）
			function checkfilenoorname(fileid,fileno,filename,meunid,meunName){
				var nock=nui.get("fileno_cheack").getValue();
				var nameck=nui.get("filename_cheack").getValue();
				if(!nock){
					nui.alert("请查看填写项", "系统提示", function(action){});
					return false;
				}else{
					if(!nameck){
						nui.confirm("该文件已存在是否要覆盖", "确定？",
				            function (action) {
				                if (action == "ok") {				           
				                    FileUpload(fileid,fileno,meunName,nui.get("path").getValue(),nui.get("previewname").getValue(),2,0);				                 
				                }
				            }
				        );
					}else{
						FileUpload(fileid,fileno,meunName,"","",1,2);
					}
				}
				
			}
			
			
		/* 弹出权限设置界面  */
		function onClick(e) {
			var powerType = powerBox.getValue();
			var boxType = e.sender.defaultValue;

			//console.log("boxType:"+boxType+"   powerType:"+powerType);
			nui.open({
						url : path + "/hyq/list_powertree.jsp?type=" + boxType,
						title : "权限配置",
						iconCls : "icon-edit",
						width : 350,
						height : 350,
						onload : function() {
							var iframe = this.getIFrameEl();
							// 给树设置上已选择的节点字符串     
							var boxObj = nui.get(boxType + "Box");
							iframe.contentWindow.setTreeCheck(boxObj.getValue());
						},
						ondestroy : function(action) {
							if (action == "ok") {
								var iframe = this.getIFrameEl();
								var list = iframe.contentWindow.GetData();
								list = mini.clone(list);
								if (list) {
									// 将树选中节点设置到box显示
									putDataTextBox(boxType, list, "nodeId","nodeName");
								}
							}
						}
					});
		}


		/**
		 * 往textboxlist中添加选择的数据
		 * @params boxType	根据点击按钮的类型 添加到不同box里面
		 * @params list 	 获取Check选中的节点集合
		 */
		function putDataTextBox(boxType, list){
			var text = "",value = "";
			var isEmp = (boxType == "emp");
			var boxObj = nui.get(boxType + "Box");
			for (var i = 0; i < list.length; i++) {
				var node = list[i];
				if (node.nodeType == "OrgOrganization" && isEmp) continue;
				if (i == list.length -1) {
					value += node["nodeId"];
					text  += node["nodeName"];
				} else {
					value += node["nodeId"] + ",";
					text  += node["nodeName"] + ",";
				}
			}
			if (value.endsWith(",")) value = strSplitLast(value);
			if (text.endsWith(",")) text = strSplitLast(text);
			boxObj.setValue(value);
			boxObj.setText(text);
		}
					
		//判断当前字符串是否以str结束
	     if (typeof String.prototype.endsWith != 'function') {
	       String.prototype.endsWith = function (str){
	          return this.slice(-str.length) == str;
	       };
	     }
	     
		/* 确保至少有一种权限，以及选中修改 查看必须同时选中且无法修改 */
	    function onValuechanged() {
	
	    	var checkValue = powerBox.getValue();
	    	if (checkValue != "") {
	    		var values = checkValue.split(",");
		    	var ifHaveUpdate = false;
		    	for (var i=0; i<values.length; i++) {
		    		var value = values[i];
		    		if (value==1 || value==2 || value==3) ifHaveUpdate = true;
		    	}
		    	if (ifHaveUpdate) {
		    		powerBox.setValue("0,"+checkValue);
	    			var boxs = $(".mini-checkboxlist-table").find("input");
					$(boxs[0]).attr("disabled", true);
		    	} else {
		    		var boxs = $(".mini-checkboxlist-table").find("input");
					$(boxs[0]).attr("disabled", false);
		    	}
	    	} else {
	    		powerBox.setValue("0");
	    		alert("请至少选择一种权限！");
	    	}	
	    }
    
	     
	     
		/* 获取添加/修改的数据  */
		function getSaveData(fileId) {
			var powerData = [];
			for (var i = 0; i < 4; i++) {
				powerData[i] = nui.get(map[i] + "Box").getValue();
			}

			var json = {
				fileId : fileId,
				powerData : powerData,
				powerType : powerBox.getValue()
			};
			return json;
		}

		//关闭窗口
		function CloseWindow(action) {
			if (action == "close" && form.isChanged()) {
				if (confirm("数据被修改了，是否先保存？")) {
					saveData();
				}
			}
			if (window.CloseOwnerWindow)
				return window.CloseOwnerWindow(action);
			else
				window.close();
		}

		/* 切割ID字符串最后一位 */
		function strSplitLast(obj) {
			return (obj).substring(0, obj.length - 1);
		}

		function CloseWindow(action) {
			if (window.CloseOwnerWindow)
				return window.CloseOwnerWindow(action);
			else
				window.close();
		}
		/* 确定保存或更新 */
		function onOk() {
		
			//saveData();
		}
		/* 取消 */
		function onCancel() {
			CloseWindow("cancel");
		}
	</script>
	<script src="<%= request.getContextPath() %>/js/filecs.js" type="text/javascript"></script>
</body>
</html>

<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-01-05 11:41:00
  - Description:
-->
<head>
<%-- <%@include file="/coframe/tools/skins/common.jsp" %> --%>
<%@include file="/coframe/dict/common.jsp"%>
<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
    <%

		java.text.SimpleDateFormat formatter = new java.text.SimpleDateFormat("yyyy/MM/dd"); 
        java.util.Date currentTime = new java.util.Date();//得到当前系统时间 
        String str_date = formatter.format(currentTime); //将日期时间格式化 
    %>
</head>
<body>
  <div id="form1" style="padding-top:5px;">
  <input class="nui-hidden" name="map.T_LID" id="t_lid"/>
  <input class="nui-hidden" name="map.TAX_RATE"/>
     <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">    
      
     
      <tr>
        <th class="nui-form-label"><label for="map.type$text">单册代码：</label></th>
        <td colspan="3" >  
        <input  id="item_no" name = "map.ITEM_NO"  class="nui-textbox asLabel" required="true" readOnly="true" style="width:150px;" vtype="maxLength:50"/>
        </td> 
      </tr>
      
      <tr>
        <th class="nui-form-label"><label for="map.type$text">编号：</label></th>
        <td colspan="3" >  
        <input  id="no" name = "map.NO"  class="nui-textbox asLabel" readOnly="true" style="width:150px;" vtype="maxLength:50"/>
        </td> 
      </tr>
      
      <tr>
        <th class="nui-form-label"><label for="map.type$text">单册名称：</label></th>
        <td colspan="3" >  
        <input  id="item_name" name = "map.ITEM_NAME"  class="nui-textbox asLabel"  style="width:150px;" readOnly="true"/>
        </td> 
      </tr>
      
      <tr>
        <th class="nui-form-label"><label for="map.type$text">单位：</label></th>
        <td colspan="3" >  
        <input  id="unit" name = "map.UNIT"  class="nui-textbox asLabel"  style="width:150px;"  readOnly="true"/>
        </td> 
      </tr>
      
       <tr>
      	<th class="nui-form-label"><label for="map.type$text">含税价格：</label></th>
        <td colspan="3" >    
          <input id="map.TAX_PRICE" name="map.TAX_PRICE"   readOnly="true" style="width:150px;" class="nui-textbox asLabel"/>
        </td> 
      </tr>
      
      <tr>
      	<th class="nui-form-label"><label for="map.type$text">税前价格：</label></th>
        <td colspan="3" >    
          <input id="map.PRE_TAX_PRICE" name="map.PRE_TAX_PRICE"  readOnly="true" style="width:150px;" class="nui-textbox asLabel"/>
        </td> 
      </tr>
      
      
      <tr>
       <th class="nui-form-label"><label for="map.type$text">库存：</label></th>
        <td colspan="3" >  
        	<input id="num"  name="map.NUM"  style="width:150px;"  readOnly="true" class="nui-textbox asLabel" />
	     </td>
       </tr>
       
        <tr>
        <th class="nui-form-label"><label for="map.type$text">上缴日期：</label></th>
        <td colspan="3" >  
         <input id="txndate" name = "map.TXNDATE" class="nui-datepicker"  style="width:150px;" required="true"  allowInput="true" format="yyyy/MM/dd"/>   
        </td> 
      </tr>
       
       <tr>
       <th class="nui-form-label"><label for="map.type$text">上缴数量：</label></th>
        <td colspan="3" >  
        	<input id="handin_num"  name="map.HANDIN_NUM" vtype="float;" style="width:150px;" required="true" class="nui-textbox" />
	     </td>
       </tr>
      
    </table>
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <!--<a class="nui-button" iconCls="icon-save" onclick="onBc">暂存草稿</a>
	          <span style="display:inline-block;width:10px;"></span>-->
	          <a class="nui-button" iconCls="icon-save" onclick="onOk">提交</a>
	          <span style="display:inline-block;width:10px;"></span>
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  </div>

  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);
    
    //自定义vtype
     nui.VTypes["englishErrorText"] = "请输入大于0的整数";
        nui.VTypes["english"] = function (v) {
            //var re = new RegExp("^\\+?[1-9]\\d*$");^[1-9]\d*$
            var re = new RegExp("^\\+?[0-9]*[1-9][0-9]*$");
            if (re.test(v)) return true;
            return false;
     }
    
    
     //与父页面的方法2对应：页面间传输json数据
    function setFormData(data){
                   
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);

        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
        
	      //表单数据回显
             var json = infos.record;
             var form = new nui.Form("#form1");//将普通form转为nui的form
             form.setData(json);
             
             //默认当月
		   /* var date=new Date();
		    var year = date.getFullYear();  
		    var month = date.getMonth();   
		    var day = date.getDate();
		      month=parseInt(month)+1;         
		      if(month==0){
		       year=parseInt(year)-1;
		       month=12;
		      }
		      if (month < 10) month = '0' + month;  
		      var str = year + '/' + month + '/' + day;
		      nui.get("txndate").setValue(str);*/
             //debugger;
             
             nui.get("txndate").setValue("<%=str_date%>");
         }
     }
         
    
    function onOk(){
      saveData();
    }
    
    //提交
    function saveData(){   
      form.validate();
      if(form.isValid()==false) return;
        
      var data = form.getData(true,true);
      var json = nui.encode(data);
      
      var t_lid = nui.get("t_lid").getValue();
      var xjson = nui.encode({t_lid:t_lid});
	   $.ajax({
	        url:"com.gotop.xmzg.dailydeal.itemDestroy.queryLibraryNumByID.biz.ext",
	        type:'POST',
	        data:xjson,
	        cache:false,
	        contentType:'text/json',
	        success:function(text){
	          obj = nui.decode(text);
	          //import_dict_form.setData(obj);
	          //import_dict_form.setChanged(false);
	          //debugger;
	          var handin_num = nui.get("handin_num").getValue();
		      if(Number(obj.map.NUM)<Number(handin_num))
		      {
		         nui.alert("上缴数量不能超过库存数量！");
		         return false;
		      }else{
		        $.ajax({
			        url:"com.gotop.xmzg.dailydeal.itemHandIn.add_handin.biz.ext",
			        type:'POST',
			        data:json,
			        cache:false,
			        contentType:'text/json',
			        success:function(text){
			            var returnJson = nui.decode(text);
						if(returnJson.exception == null && returnJson.iRtn == 1){
						
							nui.alert("上缴成功", "系统提示", function(action){
								if(action == "ok" || action == "close"){
								 
									CloseWindow("saveSuccess");
								}
							});
						}else{
							nui.alert("上缴失败", "系统提示", function(action){
								if(action == "ok" || action == "close"){
									CloseWindow("saveFailed");
								}
							});
						}
			        }
			      });
				      
		      
		      }    
	         
	        }
	      });
    }
    
    function onReset(){
      form.setData(obj);
      form.setChanged(false);
    } 
    
    function onCancel(){
      CloseWindow("cancel");
    }
    
     function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
    
  </script>
</body>
</html>
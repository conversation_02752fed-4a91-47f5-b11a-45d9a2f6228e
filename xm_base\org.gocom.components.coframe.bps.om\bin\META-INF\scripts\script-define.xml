<?xml version="1.0" encoding="UTF-8"?>
<scripts>	
	<component name="coframe_bps" index="2003" test-table="bps_RESAUTH">
		<group type="oracle">
			<script uri="META-INF/scripts/coframe_bps/Oracle/coframe_bps_init_data.sql" encoding="UTF-8" />
		</group>
		<group type="db2">
			<script uri="META-INF/scripts/coframe_bps/DB2/coframe_bps_init_data.sql" encoding="UTF-8" />
		</group>
		<group type="sybase">
			<script uri="META-INF/scripts/coframe_bps/Sysbase/coframe_bps_init_data.sql" encoding="UTF-8" />
		</group>
		<group type="sqlserver">
			<script uri="META-INF/scripts/coframe_bps/SQLServer/coframe_bps_init_data.sql" encoding="UTF-8" />
		</group>
		<group type="mysql">
			<script uri="META-INF/scripts/coframe_bps/Mysql/coframe_bps_init_data.sql" encoding="UTF-8" />
		</group>
		<group type="kingbasees">
			<script uri="META-INF/scripts/coframe_bps/KingbaseES/coframe_bps_init_data.sql" encoding="UTF-8" />
		</group>
		<group type="DM">
			<script uri="META-INF/scripts/coframe_bps/DaMeng/coframe_bps_init_data.sql" encoding="UTF-8" />
		</group>
		<group type="gbase">
			<script uri="META-INF/scripts/coframe_bps/GBase/coframe_bps_init_data.sql" encoding="UTF-8" />
		</group>
		<group type="oscar">
			<script uri="META-INF/scripts/coframe_bps/Oscar/coframe_bps_init_data.sql" encoding="UTF-8" />
		</group>
	</component>
</scripts>

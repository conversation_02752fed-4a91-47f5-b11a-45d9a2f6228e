<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): zhanghongkai
  - Date: 2019-05-09 10:02:12
  - Description:
-->
<head>
	<%@include file="/coframe/tools/skins/common.jsp" %>
	<title>导入数据模版配置</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
</head>
<body>
	<div class="search-condition" style="height: 30px;">
		<div id="form">
			<table border="0" style="width: 100%;" align="center">
				<tr>
					<td align="right">
						业务条线：
					</td>
					<td>
						<input class="nui-dictcombobox" valueField="dictID" textField="dictName" style="width:200px;"
	          				dictTypeId="BUSINESS_LINE" name="queryData.business_line" emptyText="请选择.."/>
					</td>
					<td align="right">
						文件类别名称：
					</td>
					<td>
						<input name="queryData.file_name" class="nui-textbox" style="width: 200px;" onenter="onKeyEnter" />
					</td>
					<td>
						<a class="nui-button" iconCls="icon-search" onclick="search">查询</a>&nbsp
						<a class="nui-button" iconCls="icon-reset" onclick="reset">清除</a>
					</td>
				</tr>
				<tr height="3px;">
					<td colspan="4"></td>
				</tr>
			</table>
		</div>
	</div>
	<div class="nui-toolbar" style="border-bottom: 0; padding: 0px;">
		<table style="width: 100%;">
			<tr>
				<td style="width: 100%;">
					<a class="nui-button" iconCls="icon-add" onclick="add">新增</a>
					<a id="edit" class="nui-button" iconCls="icon-edit" onclick="edit">修改</a> 
					<a id="show" class="nui-button" iconCls="icon-collapse" onclick="show">查看详情</a>
					<a class="nui-button" iconCls="icon-remove" onclick="remove">删除</a>
					<a class="nui-button" iconCls="icon-upload" onclick="imports">批量导入</a>
				</td>
			</tr>
		</table>
	</div>
	<div class="nui-fit">
		<div id="grid" class="nui-datagrid" style="width: 100%; height: 100%;" showPager="true" 
			pageSize="15" sizeList="[15,25,50]" multiSelect="true" onselectionchanged="onSelectChange"
			url="com.gotop.xmzg.fileImportOrExport.importModelMaintain.getImportModelList.biz.ext" dataField="resultList">
			<div property="columns">
				<div type="checkcolumn"></div>
				<div field="BUSINESS_LINE" align="center" headerAlign="center" renderer="getBusinessLine">业务条线</div>
				<div field="FILE_NAME" align="center" headerAlign="center">文件类别名称</div>
				<div field="FILE_TYPE" align="center" headerAlign="center" renderer="getFileType">文件类型</div>
				<div field="TABLE_NAME" align="center" headerAlign="center">对应数据库表名</div>
				<div field="FILE_COLUMN_NO" align="center" headerAlign="center">文件列序号</div>
				<div field="COLUMN_COMMENT" align="center" headerAlign="center">字段描述</div>
				<div field="COLUMN_NAME" align="center" headerAlign="center">对应数据库字段名</div>
			</div>
		</div>
	</div>
	
	
	<script type="text/javascript">
    	nui.parse();
    	
    	var form = new nui.Form("form");
    	var grid = nui.get("grid");
    	
    	grid.load();
    	
    	function add(){
    		nui.open({
    			url:"<%=request.getContextPath() %>/fileImportOrExport/importModelMaintain/addOrUpdate.jsp",
    			title:"导入数据模版配置参数维护",
    			width:900,
    			height:250,
    			onload:function(){
    			
    			},
    			ondestroy:function(action){
    				if("ok" == action){
    					grid.reload();
    				}
    			}
    		});
    	}
    	
    	function edit(){
    		var row = grid.getSelected();
    		if(row){
    			nui.open({
    				url:"<%=request.getContextPath() %>/fileImportOrExport/importModelMaintain/addOrUpdate.jsp",
    				title:"修改导入数据模版配置参数",
    				width:900,
    				height:255,
    				onload:function(){
    					var frame = this.getIFrameEl();
    					var data = {};
    					data.row = row;
    					data.type = "edit";
    					frame.contentWindow.setData(data);
    				},
    				ondestroy:function(action){
    					if(action == "ok"){
    						grid.reload();
    					}
    				}
    			});
    		}else{
    			nui.alert("请选择一条数据");
    		}
    	}
    	
    	function show(){
    		var row = grid.getSelected();
    		if(row){
    			nui.open({
    				url:"<%=request.getContextPath() %>/fileImportOrExport/importModelMaintain/addOrUpdate.jsp",
    				title:"导入数据模版配置参数详情",
    				width:900,
    				height:250,
    				onload:function(){
    					var frame = this.getIFrameEl();
    					var data = {};
    					data.row = row;
    					data.type = "show";
    					frame.contentWindow.setData(data);
    				},
    				ondestroy:function(action){
    					
    				}
    			});
    		}else{
    			nui.alert("请选择一条数据");
    		}
    	}
    	
    	function imports(){
    		nui.open({
				url:"<%=request.getContextPath() %>/fileImportOrExport/importModelMaintain/importColumns.jsp",
				title:"批量导入",
				width:800,
				height:300,
				onload:function(){
					
				},
				ondestroy:function(action){
					if(action == "ok"){
						grid.reload();
					}
				}
			});
    	}
    	
    	function remove(){
    		var rows = grid.getSelecteds();
    		if(rows.length > 0){
    			nui.confirm("确定删除吗？","系统提示",function(action){
    				if(action == "ok"){
    					var json = nui.encode({maps:rows});
    					$.ajax({
    						url:"com.gotop.xmzg.fileImportOrExport.importModelMaintain.deleteImportModel.biz.ext",
    						type:"post",
    						data:json,
    						contentType:"text/json",
    						async:false,
    						success:function(text){
    							if(text.result == "ok"){
    								nui.alert("删除成功","系统提示",function(action){
    									grid.reload();
    								});
    							}else{
    								nui.alert("删除失败");
    							}
    						}
    					});
    				}
    			});
    		}else{
    			nui.alert("请至少选择一条数据");
    		}
    	}
    	
    	function onSelectChange(){
    		var rows = grid.getSelecteds();
    		if(rows.length > 1){
    			nui.get("edit").disable();
    			nui.get("show").disable();
    		}else{
    			nui.get("edit").enable();
    			nui.get("show").enable();
    		}
    	}
    	
    	function getBusinessLine(e){
    		var value = String(e.value);
    		return nui.getDictText("BUSINESS_LINE",value);
    	}
    	
    	function getFileType(e){
    		var value = String(e.value);
    		return nui.getDictText("FILE_TYPE",value);
    	}
    	
    	function onKeyEnter(){
    		search();
    	}
    	
    	function search(){
    		var data = form.getData(true,true);
    		grid.load(data);
    	}
    	
    	function reset(){
    		form.reset();
    	}
    </script>
</body>
</html>
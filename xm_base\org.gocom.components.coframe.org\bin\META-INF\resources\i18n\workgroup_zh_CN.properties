
#所属职务
groupPositionMaintain_l_OmPosition.omDuty.dutyid(dutyname) = \u6240\u5C5E\u804C\u52A1\uFF1A
#岗位代码
groupPositionMaintain_l_OmPosition.posicode                = \u5C97\u4F4D\u4EE3\u7801\uFF1A

#岗位有效截止日期
groupPositionMaintain_l_Omposition.enddate   = \u5C97\u4F4D\u6709\u6548\u622A\u6B62\u65E5\u671F\uFF1A
#岗位名称
groupPositionMaintain_l_Omposition.posiname  = \u5C97\u4F4D\u540D\u79F0\uFF1A
#岗位类别
groupPositionMaintain_l_Omposition.positype  = \u5C97\u4F4D\u7C7B\u522B\uFF1A
#岗位有效开始日期
groupPositionMaintain_l_Omposition.startdate = \u5C97\u4F4D\u6709\u6548\u5F00\u59CB\u65E5\u671F\uFF1A
#岗位状态
groupPositionMaintain_l_Omposition.status    = \u5C97\u4F4D\u72B6\u6001\uFF1A

#工作组岗位录入
groupPositionMaintain_l_title_groupPositionInput = \u5DE5\u4F5C\u7EC4\u5C97\u4F4D\u5F55\u5165

#下级岗位列表
groupPositionMaintain_l_title_subPosition_list = \u4E0B\u7EA7\u5C97\u4F4D\u5217\u8868

#修改工作组岗位
groupPositionMaintain_l_updateGroupPosition = \u4FEE\u6539\u5DE5\u4F5C\u7EC4\u5C97\u4F4D

#工作组列表
groupQuery_l_groupList = \u5DE5\u4F5C\u7EC4\u5217\u8868

#请选择...
groupQuery_l_selectPrompt = \u8BF7\u9009\u62E9...

#选择隶属机构
groupUpdate_l_selectOrg = \u9009\u62E9\u96B6\u5C5E\u673A\u6784

#工作组录入
groupUpdate_l_title_group_update = \u5DE5\u4F5C\u7EC4\u5F55\u5165

#创建时间
workgroupManager_OmGroup.createtime                  = \u521B\u5EFA\u65F6\u95F4
#有效截止日期
workgroupManager_OmGroup.enddate                     = \u6709\u6548\u622A\u6B62\u65E5\u671F
#工作组描述
workgroupManager_OmGroup.groupdesc                   = \u5DE5\u4F5C\u7EC4\u63CF\u8FF0
#omgroup表字段
#工作组名称
workgroupManager_OmGroup.groupname                   = \u5DE5\u4F5C\u7EC4\u540D\u79F0
#工作组状态
workgroupManager_OmGroup.groupstatus                 = \u5DE5\u4F5C\u7EC4\u72B6\u6001
#工作组类型
workgroupManager_OmGroup.grouptype                   = \u5DE5\u4F5C\u7EC4\u7C7B\u578B
#最近更新时间
workgroupManager_OmGroup.lastupdate                  = \u6700\u8FD1\u66F4\u65B0\u65F6\u95F4
#负责人
workgroupManager_OmGroup.manager                     = \u8D1F\u8D23\u4EBA
#隶属机构
workgroupManager_OmGroup.orgid(Organization.orgname) = \u96B6\u5C5E\u673A\u6784
#有效开始日期
workgroupManager_OmGroup.startdate                   = \u6709\u6548\u5F00\u59CB\u65E5\u671F
#最近更新人员
workgroupManager_OmGroup.updator                     = \u6700\u8FD1\u66F4\u65B0\u4EBA\u5458

#新增员工
workgroupManager_l_addEmp = \u65B0\u589E\u5458\u5DE5

#新增工作组
workgroupManager_l_addGroup = \u65B0\u589E\u5DE5\u4F5C\u7EC4

#增加工作组员工
workgroupManager_l_addGroupEmp = \u589E\u52A0\u5DE5\u4F5C\u7EC4\u5458\u5DE5

#增加工作组岗位
workgroupManager_l_addGroupPosition = \u589E\u52A0\u5DE5\u4F5C\u7EC4\u5C97\u4F4D

#新增岗位
workgroupManager_l_addPosition = \u65B0\u589E\u5C97\u4F4D

#增加岗位员工
workgroupManager_l_addPositionEmp = \u589E\u52A0\u5C97\u4F4D\u5458\u5DE5

#增加子工作组
workgroupManager_l_addSubGroup = \u589E\u52A0\u5B50\u5DE5\u4F5C\u7EC4

#本岗位信息
workgroupManager_l_basePosition = \u672C\u5C97\u4F4D\u4FE1\u606F

#本级工作组
workgroupManager_l_basegroup = \u672C\u7EA7\u5DE5\u4F5C\u7EC4

#删除工作组
workgroupManager_l_deleteGroup = \u5220\u9664\u5DE5\u4F5C\u7EC4

#删除岗位
workgroupManager_l_deletePosition = \u5220\u9664\u5C97\u4F4D

#人员信息
workgroupManager_l_empInfo = \u4EBA\u5458\u4FE1\u606F

#工作组查询
workgroupManager_l_groupQuery = \u5DE5\u4F5C\u7EC4\u67E5\u8BE2

#操作员姓名
workgroupManager_l_omEmployee.empname   = \u64CD\u4F5C\u5458\u59D3\u540D
#状态
workgroupManager_l_omEmployee.empstatus = \u72B6\u6001
#性别
workgroupManager_l_omEmployee.gender    = \u6027\u522B
#登陆名
workgroupManager_l_omEmployee.userid    = \u767B\u9646\u540D

#权限信息
workgroupManager_l_roleInfo = \u6743\u9650\u4FE1\u606F

#请选择操作
workgroupManager_l_selectOperation = \u8BF7\u9009\u62E9\u64CD\u4F5C

#下级工作组
workgroupManager_l_subgroup = \u4E0B\u7EA7\u5DE5\u4F5C\u7EC4

#下级岗位
workgroupManager_l_subposition = \u4E0B\u7EA7\u5C97\u4F4D

#人员列表
workgroupManager_l_title_EmpList = \u4EBA\u5458\u5217\u8868

#人员查询
workgroupManager_l_title_EmpQuery = \u4EBA\u5458\u67E5\u8BE2

#工作组员工查询
workgroupManager_l_title_groupEmpList = \u5DE5\u4F5C\u7EC4\u5458\u5DE5\u67E5\u8BE2

#工作组管理
workgroupManager_l_title_group_manager = \u5DE5\u4F5C\u7EC4\u7BA1\u7406

#工作组树
workgroupManager_l_title_group_tree = \u5DE5\u4F5C\u7EC4\u6811

#工作组
workgroupManager_l_treeRoot_group = \u5DE5\u4F5C\u7EC4

#修改工作组
workgroupManager_l_updateGroup = \u4FEE\u6539\u5DE5\u4F5C\u7EC4

#您确认要删除选中的员工？
workgroupManager_m_delete_emp_confirm = \u60A8\u786E\u8BA4\u8981\u5220\u9664\u9009\u4E2D\u7684\u5458\u5DE5\uFF1F

#您确认要删除选中的工作组？
workgroupManager_m_delete_group_confirm = \u60A8\u786E\u8BA4\u8981\u5220\u9664\u9009\u4E2D\u7684\u5DE5\u4F5C\u7EC4\uFF1F

#注意：删除工作组将删除该工作组下的所有子工作组、岗位及人员
workgroupManager_m_delete_group_note = \u6CE8\u610F\uFF1A\u5220\u9664\u5DE5\u4F5C\u7EC4\u5C06\u5220\u9664\u8BE5\u5DE5\u4F5C\u7EC4\u4E0B\u7684\u6240\u6709\u5B50\u5DE5\u4F5C\u7EC4\u3001\u5C97\u4F4D\u53CA\u4EBA\u5458

#您确认要删除选中的工作组岗位？
workgroupManager_m_delete_position_confirm = \u60A8\u786E\u8BA4\u8981\u5220\u9664\u9009\u4E2D\u7684\u5DE5\u4F5C\u7EC4\u5C97\u4F4D\uFF1F

#注意：删除工作组岗位将删除该岗位下的所有人员
workgroupManager_m_delete_position_note = \u6CE8\u610F\uFF1A\u5220\u9664\u5DE5\u4F5C\u7EC4\u5C97\u4F4D\u5C06\u5220\u9664\u8BE5\u5C97\u4F4D\u4E0B\u7684\u6240\u6709\u4EBA\u5458

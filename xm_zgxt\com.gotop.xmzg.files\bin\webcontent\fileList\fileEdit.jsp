<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%@page import="com.eos.data.datacontext.UserObject" %>
<%@include file="/coframe/dict/common.jsp"%>
<html>
<!-- 
  - Author(s): lyl
  - Date: 2019-12-04 09:36:10
  - Description:
-->
<head>
<!-- stream插件 -->
<link href="../../css/stream-v1.css" rel="stylesheet" type="text/css">

</head>
<%
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>
<body>
	<div id="interBusiForm" style="padding-top:5px;">
		<input class="nui-hidden" name="editData.INFORMATION_ID"/>
		<input name="editData.FILES_TYPE_OLD" id="FILES_TYPE_OLD"  class="nui-hidden" /><!-- 原档案种类 -->
		<input name="editData.BUSINESS_TYPE_OLD" id="BUSINESS_TYPE_OLD"  class="nui-hidden" /><!-- 原业务种类 -->
	    <input name="editData.SUB_ORG_OLD" id="SUB_ORG_OLD"  class="nui-hidden" /><!--原区支行  -->
	    <input name="editData.DEAL_ORG_OLD" id="DEAL_ORG_OLD"  class="nui-hidden" /><!-- 原经办机构编码 -->
	    <input name="editData.DEAL_NAME_OLD" id="DEAL_NAME_OLD"  class="nui-hidden" /><!-- 原经办机构名称 -->
	    <input name="editData.EMP_NAME_OLD" id="EMP_NAME_OLD"  class="nui-hidden" /><!-- 客户经理编码 -->
	    <input name="editData.EMP_CODE_OLD" id="EMP_CODE_OLD"  class="nui-hidden" /><!-- 客户经理工号 -->
	    <input name="editData.CUSTOMER_NAME_OLD" id="CUSTOMER_NAME_OLD"  class="nui-hidden" /><!--客户名称 -->
	    <input name="editData.STORAGE_ADDRESS_OLD" id="STORAGE_ADDRESS_OLD"  class="nui-hidden" /><!-- 库存地址 -->
	    <input name="editData.STORAGE_LOCATION_OLD" id="STORAGE_LOCATION_OLD"  class="nui-hidden" /><!-- 货架号、箱号 -->
	    <input name="editData.CONTRACT_NUMBER_OLD" id="CONTRACT_NUMBER_OLD"  class="nui-hidden" /><!-- 旧的编号 -->
	    <input name="editData.CONTRACT_PRICE_OLD" id="CONTRACT_PRICE_OLD"  class="nui-hidden" /><!-- 旧的金额 -->
	    <input name="editData.START_TIME_OLD" id="START_TIME_OLD"  class="nui-hidden" /><!-- 旧的起期-->
	    <input name="editData.END_TIME_OLD" id="END_TIME_OLD"  class="nui-hidden" /><!-- 旧的止期 -->
	    <input name="editData.BUSS_LINE_OLD" id="BUSS_LINE_OLD"  class="nui-hidden" /><!-- 原业务条线 -->
	    <input name="editData.CHECK_TIME_OLD" id="CHECK_TIME_OLD"  class="nui-hidden" /><!-- 贷后检查时间 -->
	    <input name="editData.CHECK_TYPE_OLD" id="CHECK_TYPE_OLD"  class="nui-hidden" /><!-- 贷后检查类型 -->
	    <input name="editData.FILES_NAME" id="FILES_NAME"  class="nui-hidden" /><!-- 从档案主表获取,原档案名称  -->
	    <input name="editData.FILES_NAME_OLD" id="FILES_NAME_OLD"  class="nui-hidden" /><!-- 修改后新的档案名称  -->
		<table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
			<div class="nui-hidden"  name="editData.orgId" value="<%=userObject.getUserOrgId()%>" ></div>
	      	<tr>
	       		
				<th class="nui-form-label"><label for="type$text">档案种类：</label></th>
				<td colspan="4">
					<input id="files_type" class="nui-dictcombobox" name="editData.FILES_TYPE"  emptyText="请选择"
  					valueField="dictID" textField="dictName" dictTypeId="FILES_TYPE" showNullItem="true" nullItemText="请选择" onvaluechanged="onFilesTypeChanged" style="width:150px;"/>
  				</td>
	      		<th class="nui-form-label"><label for="type$text">业务种类：</label></th>
	      		<td colspan="4">
	      			<input name="editData.BUSINESS_TYPE" id="BUSINESS_TYPE" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
	      		</td>
	      		<th class="nui-form-label"><label for="type$text">区支行：</label></th>
  				<td colspan="4">
  					<input id="SUB_ORG" name = "editData.SUB_ORG"  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;"/>
	      		</td>
  				<th class="nui-form-label"><label for="type$text">经办机构：</label></th>
  				<td colspan="4">
  					<input id="DEAL_ORG" name = "editData.DEAL_ORG"  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;"/>
	      		</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">分管客户经理：</label></th>
  				<td colspan="4">
  					<input id="EMPNAME" name = "editData.EMPNAME"  class="nui-buttonedit"  allowInput="false" onbuttonclick="selectEmp" style="width:150px;"/>
	      		</td>
	      		<th class="nui-form-label"><label for="type$text">客户名称：</label></th>
	      		<td colspan="4">
	      			<input name="editData.CUSTOMER_NAME" id="CUSTOMER_NAME" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
	      		</td>
	      		<th class="nui-form-label"><label for="type$text">客户号码：</label></th>
	      		<td colspan="4">
	      			<input name="editData.CUSTOMER_NO" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
	      		</td>
	      		<th class="nui-form-label"><label for="type$text">编号：</label></th>
	      		<td colspan="4">
	      			<input name="editData.CONTRACT_NUMBER" id="CONTRACT_NUMBER" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
	      		</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">金额：</label></th>
	      		<td colspan="4">
	      			<input name="editData.CONTRACT_PRICE" vtype="float;maxLength:19;" class="nui-textbox" style="width:150px;"/>
	      		</td>
	      		<th class="nui-form-label"><label for="type$text">起期：</label></th>
	      		<td colspan="4">  
			 		<input name="editData.START_TIME" id="START_TIME" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:150px;"/>
	        	</td>
	      		<th class="nui-form-label"><label for="type$text">止期：</label></th>
	      		<td colspan="4">  
			 		<input name="editData.END_TIME" id="END_TIME" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:150px;"/>
	        	</td>
	      		<th class="nui-form-label"><label for="type$text">业务条线：</label></th>
				<td colspan="4">
					<input id="BUSINESS_LINE" class="nui-dictcombobox" name="editData.BUSINESS_LINE"  emptyText="请选择"
  					valueField="dictID" textField="dictName" dictTypeId="FILES_BUSINESS_LINE" showNullItem="true" nullItemText="请选择" style="width:150px;"/>
  				</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">存放地址：</label></th>
		      		<td colspan="4">
		      			<input id="STORAGE_ADDRESS" class="nui-dictcombobox" name="editData.STORAGE_ADDRESS"  emptyText="请选择"
	  					valueField="dictID" textField="dictName"  dictTypeId="FILES_STORAGE_ADDRESS" showNullItem="true" nullItemText="请选择" style="width:150px;"/>
		      		</td>
		      	<th class="nui-form-label"><label for="type$text">货架号/箱号：</label></th>
		      		<td colspan="4">
		      			<input name="editData.STORAGE_LOCATION"  id="STORAGE_LOCATION" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
		      		</td>
	      		<th class="nui-form-label"><label for="type$text">档案盒号</label></th>
		      		<td colspan="4">
		      		<input name="editData.BOX_NUM"  id="BOX_NUM" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
		      		
		      		</td>
		      	<th class="nui-form-label"><label for="type$text"></label></th>
		      		<td colspan="4">
		      		</td>
	      	</tr>
	      	<tr >
				<th class="nui-form-label"><label for="type$text">修改原因：</label></th>
				<td colspan="19">
					<input class="nui-textarea" name="editData.REMARK" id="REMARK" style="width:600px;"/>
  				</td>
	      	</tr>
	      	<tr >
				<th class="nui-form-label"><label for="type$text">附件：</label></th>
				<td colspan="19">
					<input class="nui-textarea" name="editData.AFFILIATED_NAME" id="AFFILIATED_NAME" style="width:550px;"/>
  				</td>
	      	</tr>
	      	<tr>
		      	<th class="nui-form-label"><label for="inputData.type$text">附件上传：</label></th>
		      	<td >
		      		<!-- <div id="i_select_files" >
						</div> -->
					<input type="button" class="btn btn-default" id="i_select_files"  value="添加文件"/>
	        	</td>
	        	<th class="nui-form-label"><label for="type$text"></label></th>
	      		<td >
	      		</td>
	      	</tr>
	      	<tr >
	      		<th class="nui-form-label"><label for="inputData.type$text">上传进度：</label></th>
	      		<td colspan="19">
					<!-- 回显进度 -->
					<div id="i_stream_files_queue" ></div>
	      		</td>
	      	</tr>
		</table>
	    <div class="nui-toolbar" style="padding:0px;"   borderStyle="border:0;">
			<table width="100%">
		    	<tr>
		        	<td style="text-align:center;">
		          		<a class="nui-button" iconCls="icon-save" id="saveButtorn" onclick="saveData">保存</a>
		          		<span style="display:inline-block;width:25px;"></span>
		          		<a class="nui-button" iconCls="icon-cancel" onclick="CloseWindow('cancel')">取消</a>
		        	</td>
		      	</tr>
		   	</table>
		</div>
	</div>
<script type="text/javascript" src="../../js/stream-v1.js"></script> 
<script type="text/javascript">
	var isLoan  = [{ id: "是", text: '是' },{ id: "否", text: '否' }];
    nui.parse();
    var interBusiForm = new nui.Form("interBusiForm");
    
    nui.get("files_type").setEnabled(false);
    function notRequired(){
    	nui.get("CUSTOMER_NAME").setRequired(false);
		nui.get("BUSINESS_TYPE").setRequired(false);
		nui.get("CONTRACT_NUMBER").setRequired(false);
		nui.get("START_TIME").setRequired(false);
    }
    
    var files=new Array();
    //stream 插件配置
	var config = {
		browseFileId : "i_select_files", /** 选择文件的ID, 默认: i_select_files */
		browseFileBtn : "<div>请选择文件</div>", /** 显示选择文件的样式, 默认: `<div>请选择文件</div>` */
		dragAndDropArea: "i_select_files", /** 拖拽上传区域，Id（字符类型"i_select_files"）或者DOM对象, 默认: `i_select_files` */
		dragAndDropTips: "<span>把文件(文件夹)拖拽到这里</span>", /** 拖拽提示, 默认: `<span>把文件(文件夹)拖拽到这里</span>` */
		filesQueueId : "i_stream_files_queue", /** 文件上传容器的ID, 默认: i_stream_files_queue */
		filesQueueHeight : 200, /** 文件上传容器的高度（px）, 默认: 450 */
		messagerId : "i_stream_message_container", /** 消息显示容器的ID, 默认: i_stream_message_container */
		multipleFiles: true, /** 多个文件一起上传, 默认: false */
		onRepeatedFile: function(f) {
			alert("文件："+f.name +" 大小："+f.size + " 已存在于上传队列中。");
			return false;	
		},
//		autoUploading: false, /** 选择文件后是否自动上传, 默认: true */
//		autoRemoveCompleted : true, /** 是否自动删除容器中已上传完毕的文件, 默认: false */
//		maxSize: 104857600//, /** 单个文件的最大大小，默认:2G */
//		retryCount : 5, /** HTML5上传失败的重试次数 */
//		postVarsPerFile : { /** 上传文件时传入的参数，默认: {} */
//			param1: "val1",
//			param2: "val2"
//		},
		swfURL : "/swf/FlashUploader.swf" ,/** SWF文件的位置 */
		tokenURL : "<%=request.getContextPath()%>/tk", /** 根据文件名、大小等信息获取Token的URI（用于生成断点续传、跨域的令牌） */
		frmUploadURL : "<%=request.getContextPath()%>/fd", /** Flash上传的URI */
		uploadURL : "<%=request.getContextPath()%>/upload" ,/** HTML5上传的URI */
		filesQueueHeight :100,
//		simLimit: 200, /** 单次最大上传文件个数, */
//		extFilters: [".txt", ".rpm", ".rmvb", ".gz", ".rar", ".zip", ".avi", ".mkv", ".mp3"], /** 允许的文件扩展名, 默认: [] */
//		onSelect: function(list) {alert('onSelect')}, /** 选择文件后的响应事件 */
//		onMaxSizeExceed: function(size, limited, name) {alert('onMaxSizeExceed')}, /** 文件大小超出的响应事件 */
//		onFileCountExceed: function(selected, limit) {alert('onFileCountExceed')}, /** 文件数量超出的响应事件 */
//		onExtNameMismatch: function(name, filters) {alert('onExtNameMismatch')}, /** 文件的扩展名不匹配的响应事件 */
//		onCancel : function(file) {alert('Canceled:  ' + file.name)}, /** 取消上传文件的响应事件 */
		onComplete: function(file) {
			//alert(file.name);
			files.push(file);
			console.log(files);
		
		} /** 单个文件上传完毕的响应事件 */
//		onQueueComplete: function() {alert('onQueueComplete')} /** 所以文件上传完毕的响应事件 */
//		onUploadError: function(status, msg) {alert('onUploadError')} /** 文件上传出错的响应事件 */
//		onDestroy: function() {alert('onDestroy')} /** 文件上传出错的响应事件 */
	};
	//启动stream
	var _t = new Stream(config);
    
     //关闭添加窗口,删除临时文件
 	function CloseWindow(action){
 		if((action == 'cancel'||action == 'close') && files.length != 0){
 			var data = {};
 			data.files = files;
 			var json = nui.encode(data);
        	$.ajax({
                url:"com.gotop.xmzg.files.fileList.delTempFiles.biz.ext",
                type:'POST',
                data:json,
                cache:false,
                async:false,
                contentType:'text/json',
                success:function(text){
                   var returnJson = nui.decode(text);
                   if(returnJson.exception == null && returnJson.flag == 1){
                        files.splice(0, files.length);//文件上传成功清空文件对象数组避免重复提交
                     }else{
                         alert("临时文件删除失败！");
                        }
                   }
             });
 		}
  		if(window.CloseOwnerWindow) 
    		return window.CloseOwnerWindow(action);
  		else
    		return window.close();
    }
    
    //保存数据
    function saveData(){
    	interBusiForm.validate();            
        if (interBusiForm.isValid() == false) return;
        var data = interBusiForm.getData(true,true);
        var files_type = nui.get("files_type").getValue();
       	var temp=nui.get("FILES_NAME").getValue();//档案名称
        if(files_type == '01'){
        	temp= nui.get("CUSTOMER_NAME").getValue()+'-'
				+nui.get("BUSINESS_TYPE").getValue()+'-'
				+nui.get("CONTRACT_NUMBER").getValue();
        	
        }else if(files_type == '02'){
        	temp = nui.get("CUSTOMER_NAME").getValue()+'-'
				+nui.get("BUSINESS_TYPE").getValue()+'-'
				+nui.get("CONTRACT_NUMBER").getValue();
        }else if(files_type == '03'){
        	temp = nui.get("CUSTOMER_NAME").getValue()+'-'
				+nui.get("BUSINESS_TYPE").getValue()+'-'
				+nui.get("START_TIME").getFormValue();
        }else if(files_type == '04'){
        	temp = nui.get("CUSTOMER_NAME").getValue()+'-'
				+nui.get("BUSINESS_TYPE").getValue()+'-'
				+nui.get("CONTRACT_NUMBER").getValue();
        }else if(files_type == '05'){
        	temp = nui.get("CUSTOMER_NAME").getValue()+'-票据承兑业务-'
				+nui.get("CONTRACT_NUMBER").getValue();
        }else if(files_type == '06'){
        	temp = nui.get("CUSTOMER_NAME").getValue()+'-票据贴现业务-'
				+nui.get("CONTRACT_NUMBER").getValue();
        }else if(files_type == '07'){
        	temp = '合作机构档案-'+nui.get("CUSTOMER_NAME").getValue()+'-'
				+nui.get("BUSINESS_TYPE").getValue();
        }else if(files_type == '08'){
        	temp = '拒贷档案-'+nui.get("CUSTOMER_NAME").getValue()+'-'
				+nui.get("BUSINESS_TYPE").getValue();
        }else if(files_type == '09'){
        	temp = '征信档案-'+nui.get("CUSTOMER_NAME").getValue()+'-'
				+nui.get("START_TIME").getFormValue();
        }
        data.editData.FILES_NAME = temp;
       	data.files=files;
       	data.filesType = files_type;
        var json = nui.encode(data);
        var URL="com.gotop.xmzg.files.fileList.uptFileRecordList.biz.ext";
        $.ajax({
        	url: URL,
            type: 'POST',
            data: json,
            cache: false,
            contentType:'text/json',
            success: function (text){
                var returnJson = nui.decode(text);
				if(returnJson.exception == null && returnJson.flag == "1"){
					nui.alert("保存成功", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							CloseWindow("saveSuccess"+","+temp);
						}
					});
				}else if(returnJson.exception == null && returnJson.flag == "exist"){
					nui.alert("档案名称已存在！");
				}else{
					nui.alert("保存失败", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							//CloseWindow("saveFailed");
						}
					});
				}
		    }
  		});   
    }
	
	//机构树回显
     function OrgonButtonEdit(e){
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/files/archiveList/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            //将值机构id变成机构code
                            var data={orgId:data.ID};
					        var json = nui.encode(data);
					        var URL="com.gotop.xmzg.files.fileLedger.getOrgcode.biz.ext";
					        $.ajax({
					        	url: URL,
					            type: 'POST',
					            data: json,
					            async:false,
					            cache: false,
					            contentType:'text/json',
					            success: function (text){
					                var returnJson = nui.decode(text);
					                var result = returnJson.resultList;
					                btnEdit.setValue(result[0].ORGCODE);
							    }
					  		}); 
                        }
                    }
                }
            });            
             
        } 
        
        //人员树回显
        function selectEmp(e){
        	var ele = e.sender.id;
    		var emp = nui.get(ele);
            nui.open({
                url:"<%=request.getContextPath()%>/files/archiveList/empTree.jsp?type=emp",
                title: "选择人员",
                width: 600,
                height: 400,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = emp.getValue();
                	//frame.contentWindow.setData(data);
                	frame.contentWindow.setTreeCheck(data.value);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);
                        debugger;    //必须
                        if (data) {
                            emp.setValue(data.nodeId);
                            emp.setText(data.nodeName);
                            //将值人员id转换成人员code
                            var data={empId:data.nodeId};
					        var json = nui.encode(data);
					        var URL="com.gotop.xmzg.files.fileLedger.getEmpcode.biz.ext";
					        $.ajax({
					        	url: URL,
					            type: 'POST',
					            data: json,
					            async:false,
					            cache: false,
					            contentType:'text/json',
					            success: function (text){
					                var returnJson = nui.decode(text);
					                var result = returnJson.resultList;
					                emp.setValue(result[0].EMPCODE);
							    }
					  		});
                        }
                    }

                }
            });
    	}  
    	
    	//与父页面的方法2对应：页面间传输json数据
    function setFormData(data){
        //跨页面传递的数据对象，克隆后才可以安全使用
    	var infos = nui.clone(data);
        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
	      	//表单数据回显
        	var json = infos.record;
		
        	 json.editData.FILES_TYPE_OLD = json.editData.FILES_TYPE;
        	 json.editData.BUSINESS_TYPE_OLD = json.editData.BUSINESS_TYPE;
             json.editData.SUB_ORG_OLD = json.editData.SUB_ORG;
             json.editData.DEAL_ORG_OLD = json.editData.DEAL_ORG;
             json.editData.DEAL_NAME_OLD = json.editData.DEAL_NAME;
             json.editData.EMP_CODE_OLD = json.editData.EMPNAME;
             json.editData.EMP_NAME_OLD = json.editData.EMP_NAME;
             json.editData.CUSTOMER_NAME_OLD = json.editData.CUSTOMER_NAME;
             json.editData.CONTRACT_NUMBER_OLD = json.editData.CONTRACT_NUMBER;
             json.editData.CONTRACT_PRICE_OLD = json.editData.CONTRACT_PRICE;
             json.editData.STORAGE_ADDRESS_OLD = json.editData.STORAGE_ADDRESS;
             json.editData.STORAGE_LOCATION_OLD = json.editData.STORAGE_LOCATION;
             json.editData.START_TIME_OLD = json.editData.START_TIME;
             json.editData.END_TIME_OLD = json.editData.END_TIME;
             json.editData.BUSS_LINE_OLD = json.editData.BUSS_LINE;
             json.editData.CHECK_TIME_OLD = json.editData.CHECK_TIME;
             json.editData.CHECK_TYPE_OLD = json.editData.CHECK_TYPE;
             json.editData.FILES_NAME_OLD = json.editData.FILES_NAME;
         	var form = new nui.Form("#interBusiForm");//将普通form转为nui的form
         	form.setData(json);
         	//机构回显
         	nui.get("SUB_ORG").setText(json.editData.SUB_NAME);
         	nui.get("DEAL_ORG").setText(json.editData.DEAL_NAME);
         	nui.get("EMPNAME").setText(json.editData.EMP_NAME);
            nui.get("REMARK").setValue(json.editData.RESERVE_REMARK2);
       		
            var filesType = infos.filesType;
			if(filesType == '01'){
			notRequired();
			nui.get("CUSTOMER_NAME").setRequired(true);
			nui.get("BUSINESS_TYPE").setRequired(true);
			nui.get("CONTRACT_NUMBER").setRequired(true);
		}else if(filesType == '02'){
			notRequired();
			nui.get("CUSTOMER_NAME").setRequired(true);
			nui.get("BUSINESS_TYPE").setRequired(true);
			nui.get("CONTRACT_NUMBER").setRequired(true);
		}else if(filesType == '03'){
			notRequired();
			nui.get("CUSTOMER_NAME").setRequired(true);
			nui.get("BUSINESS_TYPE").setRequired(true);
			nui.get("START_TIME").setRequired(true);
		}else if(filesType == '04'){
			notRequired();
			nui.get("CUSTOMER_NAME").setRequired(true);
			nui.get("BUSINESS_TYPE").setRequired(true);
			nui.get("CONTRACT_NUMBER").setRequired(true);
		}else if(filesType == '05'){
			notRequired();
			nui.get("CUSTOMER_NAME").setRequired(true);
			nui.get("CONTRACT_NUMBER").setRequired(true);
		}else if(filesType == '06'){
			notRequired();
			nui.get("CUSTOMER_NAME").setRequired(true);
			nui.get("CONTRACT_NUMBER").setRequired(true);
		}else if(filesType == '07'){
			notRequired();
			nui.get("CUSTOMER_NAME").setRequired(true);
			nui.get("BUSINESS_TYPE").setRequired(true);
		}else if(filesType == '08'){
			notRequired();
			nui.get("CUSTOMER_NAME").setRequired(true);
			nui.get("BUSINESS_TYPE").setRequired(true);
		}else if(filesType == '09'){
			notRequired();
			nui.get("CUSTOMER_NAME").setRequired(true);
			nui.get("START_TIME").setRequired(true);
		}else{
			$('#saveButtorn').css('display','');
		}
		
		//附件相关
        var id = json.editData.AFFILIATED_IDS;
        var name = json.editData.AFFILIATED_NAMES;
	    fj(id,name);
        }
    }
    
    
    
     //操作列：查看/下载附件
      function fj(id,name){
      var s = "";
      if(name!=null){
      		var names = name.split(",");
      		var ids = id.split(",");
      		var a = [];
      		for(var i=0;i<names.length;i++){
		           var s1=	"<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\"  onclick=\"downAccessory(\'"+ids[i]+"\',\'"+names[i]+"\')\">"+names[i]+"</a>";
		           a.push(s1);
	        }
	        s = a; 
      	}else{
      		s="无附件";
      	}
      	$("#AFFILIATED_NAME").html(""+s+"");
      }
     
     //下载附件
	 function downAccessory(id,name){
       	var fileId = id;
       	var fileName = name;
       	var url="<%=request.getContextPath()%>/files/fileList/downloadFile.jsp?fileId="+fileId+"&fileName="+fileName;
		window.location.replace(url);
	  }
</script>
</body>
</html>
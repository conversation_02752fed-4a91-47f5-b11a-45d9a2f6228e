<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): wsd
  - Date: 2022-02-27 08:34:30
  - Description:
-->
<head>
<title>个金业绩账户认领/移交审核</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <style type="text/css">
    	.nui-form-label{
    		width: 130px;
    	}
    
    </style>
</head>
<body>
<div id="form1" style="padding-top:5px;">
    <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
    <input id="taa_pre_id" name = "formData.taa_pre_id" class="nui-hidden"/>
    <input id="taa_id" name = "formData.taa_id" class="nui-hidden"/>
    <input id="flag" name = "formData.flag" class="nui-hidden"/>
    <input id="taa_emp" name = "formData.taa_emp" class="nui-hidden"/>
    <input id="taa_change_type" name = "formData.taa_change_type" class="nui-hidden"/>
    <input id="taa_busi_org" name = "formData.taa_busi_org" class="nui-hidden"/>
    <input id="taa_end_gs" name = "formData.taa_end_gs" class="nui-hidden"/>
    <input id="num" name = "formData.num" class="nui-hidden" value="1"/>
    <input id="tgnum" name = "formData.tgnum" class="nui-hidden" value="1"/>
     <tr>
        <th class="nui-form-label"><label>产品编号：</label></th>
        <td colspan="3" >  
         	<input id=taa_busi_no name = "formData.taa_busi_no" class="nui-textbox" readonly="readonly" style="width:100%;" />  
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label>产品名称：</label></th>
        <td colspan="3" >  
         	<input id="taa_busi_name" name = "formData.taa_busi_name" class="nui-textbox asLabel" readonly="readonly" style="width:100%;" />  
        </td>
      </tr>
      <tr>
        <th class="nui-form-label"><label>认领类型：</label></th>
        <td colspan="3" >  
         	<input id="taa_rl_type" name = "formData.taa_rl_type" class="nui-dictcombobox asLabel" readonly="readonly" disabled="true" style="width:100%;"  valueField="dictID" textField="dictName" dictTypeId="JF_FPLX"/>  
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label>指标或指标细项：</label></th>
        <td colspan="3" >  
         	<input id="taa_zb_code" name = "formData.taa_zb_code" class="nui-textbox asLabel" readonly="readonly" style="width:100%;"  />  
        </td> 
      </tr>
       <tr>
        <th class="nui-form-label"><label>指标或指标细项名称：</label></th>
        <td colspan="3" >  
         	<input id="taa_zb_name" name = "formData.taa_zb_name" class="nui-textbox asLabel" readonly="readonly" style="width:100%;" />  
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label>操作类型：</label></th>
        <td colspan="3" >  
         	<input id="taa_type" name = "formData.taa_type" class="nui-dictcombobox asLabel" readonly="readonly" disabled="true" style="width:100%;"  valueField="dictID" textField="dictName" dictTypeId="JF_BGLX"/>  
        </td> 
      </tr>
      <tr id="taa_emp_name_gs_tr">
        <th class="nui-form-label"><label>归属客户经理：</label></th>
        <td colspan="3" >  
         	<input id="taa_emp_name_gs" name = "formData.taa_emp_name_gs" class="nui-textbox asLabel" readonly="readonly" style="width:100%;" />  
        </td> 
      </tr>
      <tr id="taa_begin_gs_tr">
        <th class="nui-form-label"><label>归属开始时间：</label></th>
        <td colspan="3" >  
         	<input id="taa_begin_gs" name = "formData.taa_begin_gs" class="nui-textbox asLabel" readonly="readonly" style="width:100%;"  />  
        </td> 
      </tr>
      <tr id="taa_rate_tr">
        <th class="nui-form-label"><label>分润比例：</label></th>
        <td colspan="3" >  
         	<input id="taa_rate" name = "formData.taa_rate" class="nui-textbox asLabel" readonly="readonly" style="width:100%;"  />  
        </td> 
      </tr>
      <tr id="taa_emp_name_fr_tr">
        <th class="nui-form-label"><label>分润客户经理：</label></th>
        <td colspan="3" >  
         	<input id="taa_emp_name_fr" name = "formData.taa_emp_name_fr" class="nui-textbox asLabel" readonly="readonly" style="width:100%;" />  
        </td> 
      </tr>
       <tr id="taa_begin_fr_tr">
        <th class="nui-form-label"><label>分润开始时间：</label></th>
        <td colspan="3" >  
         	<input id="taa_begin_fr" name = "formData.taa_begin_fr" class="nui-textbox asLabel" readonly="readonly" style="width:100%;" />  
        </td> 
      </tr>
    </table>
    <div class="nui-fit">
    <div id="form2">
	    <input id="tab_zb_code" name = "map.TAB_ZB_CODE" class="nui-textbox" readonly="readonly" style="display: none;"  />  
	    <input id="tab_busi_no" name = "map.TAB_BUSI_NO" class="nui-textbox" readonly="readonly"  style="display: none;" />  
    </div>
    <div id="datagrid2" dataField="datas" class="nui-datagrid" style="width:100%;height:100px;"
	  allowCellEdit="true" allowCellSelect="true" multiSelect="true" showPager="false"
	  url="com.gotop.xmzg.achieve.accNew.accBelong_detail_yj.biz.ext" >
	    <div property="columns" >
	      <!-- <div type="checkcolumn"></div>  -->
	      <div field="TZAD_DATE" headerAlign="center" width="40" align="center">统计日期</div>
	      <div field="TZAD_NAME" headerAlign="center"  align="center" >指标细项名称</div>
	      <div field="TZAD_ACHIEVE" headerAlign="center" width="60"  align="center" >业绩值</div>
	      <div field="TID_UNIT" headerAlign="center" width="40" align="center" renderer="tid_unit">单位</div>
	    </div>
	 </div>
	 </div>
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save" onclick="onOk(1)">审核通过</a>
	          <span style="display:inline-block;width:25px;"></span>
	           <a class="nui-button" iconCls="icon-save" onclick="onOk(2)">审核不通过</a>
	          <span style="display:inline-block;width:25px;"></span>
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel()">关闭</a>
	        </td>
	      </tr>
	    </table>
	 </div>


	<script type="text/javascript">
    	nui.parse();
    	function tid_unit(e){
			return nui.getDictText("JF_UINT",e.value);
		}
    	 function CloseWindow(action){
      		if(window.CloseOwnerWindow) 
        		return window.CloseOwnerWindow(action);
      		else
        		return window.close();
    	}
    	
    	function onCancel(){
    		CloseWindow("cancel");
    	}
    	
    	function onOk(flag){
    		nui.get("flag").setValue(flag);
    		var form = new nui.Form("#form1");
    		var data = form.getData(true,true);
            var json = nui.encode(data);

    		$.ajax({
                url: "com.gotop.xmzg.achieve.accNew.checkGjAcc.biz.ext",
                type: 'POST',
                data:json,
                cache: false,
                contentType:'text/json',
                success: function (json) {
                var returnJson = nui.decode(json);
				if(returnJson.exception == null && returnJson.iRtn == 0){
					nui.alert("操作成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("ok");
					}
				});
				}else{
					nui.alert(returnJson.msg, "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("fail");
					}
				});
			}
			}
       }); 
    	}
    	
    	//初始化表单数据
    	function setFormData(data) {
			var info = nui.clone(data);
			console.log("个金认领审核："+nui.encode(info));
			var taa_type = info.taa_type;
			nui.get("tab_zb_code").setValue(info.taa_zb_code);
			nui.get("tab_busi_no").setValue(info.taa_busi_no);
			
			nui.get("taa_busi_no").setValue(info.taa_busi_no);
			nui.get("taa_busi_name").setValue(info.taa_busi_name);
			nui.get("taa_rl_type").setValue(info.taa_rl_type);
			nui.get("taa_zb_code").setValue(info.taa_zb_code);
			nui.get("taa_zb_name").setValue(info.taa_zb_name);
			nui.get("taa_type").setValue(taa_type);
			
			nui.get("taa_pre_id").setValue(info.taa_pre_id);
			nui.get("taa_id").setValue(info.taa_id);
			nui.get("taa_emp").setValue(info.taa_emp);
			nui.get("taa_change_type").setValue(info.taa_change_type);
			nui.get("taa_busi_org").setValue(info.taa_busi_org);
			nui.get("taa_end_gs").setValue(info.taa_end);
			if("0" == taa_type||"6" == taa_type){//6-新个金的归属
				//归属
				document.getElementById("taa_emp_name_gs_tr").style.display = "";
				document.getElementById("taa_emp_name_gs").style.display = "";
				document.getElementById("taa_begin_gs_tr").style.display = "";
				document.getElementById("taa_begin_gs").style.display = "";
				nui.get("taa_emp_name_gs").setValue(info.taa_emp_name);
				nui.get("taa_begin_gs").setValue(info.taa_begin);
				
				document.getElementById("taa_emp_name_fr_tr").style.display = "none";
				document.getElementById("taa_emp_name_fr").style.display = "none";
				document.getElementById("taa_begin_fr_tr").style.display = "none";
				document.getElementById("taa_begin_fr").style.display = "none";
				document.getElementById("taa_rate_tr").style.display = "none";
				document.getElementById("taa_rate").style.display = "none";
				
				nui.get("taa_emp_name_fr").setValue("");
				nui.get("taa_begin_fr").setValue("");
				nui.get("taa_rate").setValue("");
			}else if ("1" == taa_type || "7" == taa_type){//7-新个金的分润
				//分润
				document.getElementById("taa_emp_name_fr_tr").style.display = "";
				document.getElementById("taa_emp_name_fr").style.display = "";
				document.getElementById("taa_begin_fr_tr").style.display = "";
				document.getElementById("taa_begin_fr").style.display = "";
				document.getElementById("taa_rate_tr").style.display = "";
				document.getElementById("taa_rate").style.display = "";
				
				nui.get("taa_emp_name_fr").setValue(info.taa_emp_name);
				nui.get("taa_begin_fr").setValue(info.taa_begin);
				nui.get("taa_rate").setValue(info.taa_rate);
				
				document.getElementById("taa_emp_name_gs_tr").style.display = "none";
				document.getElementById("taa_emp_name_gs").style.display = "none";
				document.getElementById("taa_begin_gs_tr").style.display = "none";
				document.getElementById("taa_begin_gs").style.display = "none";
				nui.get("taa_emp_name_gs").setValue("");
				nui.get("taa_begin_gs").setValue("");
			}
			
			var grid2 = nui.get("datagrid2");
		    var data2 = new nui.Form("#form2").getData(true,true);
		    grid2.load(data2);
		}
		
		
    </script>
</body>
</html>
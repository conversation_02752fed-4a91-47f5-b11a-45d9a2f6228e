<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false" %>
<%@ page import="com.eos.data.datacontext.UserObject" %>	
<%@page import="com.eos.foundation.database.DatabaseExt"%>
<%@page import="java.util.HashMap"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<%--
- Author(s): Administrator
- Date: 2017-07-05 16:21:53
- Description:
    --%>
    <head>
    <%@include file="/coframe/tools/skins/common.jsp" %>
        <script src="<%=request.getContextPath() %>/files/process/pdfjs/html2canvas.js"></script>
<script src="<%=request.getContextPath() %>/files/process/pdfjs/jspdf.debug.js"></script>
        <title>
            	通用流程
        </title>
        <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
        
        
        <style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style> 
    </head>
    
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
	Long workItemID = Long.parseLong(request.getParameter("workItemID"));
 %>
 
    <body>
    <div style="position:relative;margin:10px 20px 20px 20px;">
    <button id="downLoad">生成PDF</button>
    </div>
    <div align="center" >
    <h2 style="margin-top:10px;margin-bottom:0px;">通用流程详情</h2>
    <fieldset style="width:800px;border:solid 1px #aaa;position:relative;margin:0px 2px 0px 2px;" >
    	 <legend>申请详情</legend>
    	 <form  id="form1" > 
    	 
    	 <input class="nui-hidden" name="workItemID"/>
		<input id="processInstID" class="nui-hidden" name="map.PROCESSINSTID"/>
		<input id="APPLY_EMPID" class="nui-hidden" name="map.APPLY_EMPID"/>
		
    	  <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
  
     <tr>
        
        <th class="nui-form-label" style="width:12.5%"><label for="map.type$text">标题：</label></th>
        <td colspan="3"  style="width:30%">
             <input id="map.TITLE" class="nui-textbox asLabel" name="map.TITLE" readOnly="true"  style="width:300px;"/>
        </td>

    
     <th class="nui-form-label" style="width:12.5%"><label for="map.type$text">联系电话：</label></th>
        <td colspan="3"  style="width:30%">
           <input id="map.PHONE_NUM" class="nui-textbox asLabel" name="map.PHONE_NUM" readOnly="true" />
      </td>
    
    
      </tr>
     
      <tr >
      
     <th class="nui-form-label" style="width:12.5%"><label for="map.device_name_id$text">申请人：</label></th>
        <td colspan="3"  style="width:30%">
          <input id="map.EMPNAME" class="nui-textbox asLabel " name="map.EMPNAME" readOnly="true" />
        </td>

    
      <th class="nui-form-label" style="width:12.5%"><label for="map.device_name_id$text">申请人所在机构：</label></th>
        <td colspan="3"  style="width:30%">
          <input id="map.ORGNAME" class="nui-textbox asLabel " name="map.ORGNAME" readOnly="true" />
        </td>

    
    
      </tr>
      
      <tr >
       
        <!-- <th class="nui-form-label"><label for="map.brandmodel_id$text">申请内容：</label></th>
        <td colspan="3"  >
           <input id="map.APPLY_REMARK" class="nui-textarea asLabel" name="map.APPLY_REMARK" readOnly="true"/>
        </td> -->
        
         <th class="nui-form-label"><label for="map.brandmodel_id$text">申请时间：</label></th>
        <td colspan="3"  > 
           <input id="APPLY_TIME" class="nui-datepicker asLabel" name="map.APPLY_TIME"  allowInput="false" readOnly="true" format="yyyyMMdd"/>
        </td>

       
        <th class="nui-form-label"><label for="map.device_serial$text">申请内容：</label></th>
        <td colspan="3"  >
          <input id="map.APPLY_REMARK" class="nui-textarea asLabel" name="map.APPLY_REMARK" readOnly="true"/>
        </td>

       
      </tr>
      
      
     <!--  <tr>
        <th class="nui-form-label"><label for="map.device_serial$text">借阅档案用途：</label></th>
        <td colspan="3"  >
          <input id="map.BORROW_APPLY_USE" class="nui-textarea asLabel" name="map.BORROW_APPLY_USE" readOnly="true"/>
        </td>

       <th class="nui-form-label"><label for="map.price$text">借阅方式：</label></th>
        <td colspan="3"  >
            <input id="deductscoreReason" name="map.BORROW_APPLY_WAY" class="nui-dictcheckboxgroup asLabel" dictTypeId="BORROW_APPLY_WAY"  readOnly="true"/>
        </td>

        
      </tr> -->
      
        <tr >
      
      <th class="nui-form-label"><label for="map.device_serial$text">操作处理选择：</label></th>
        <td colspan="3"  >
             <input id="map.NEXT_DEAL_NODENAME" class="nui-textbox asLabel " name="map.NEXT_DEAL_NODENAME" readOnly="true" />
        </td>
      
        <th class="nui-form-label"><label for="map.device_serial$text">下一步处理人：</label></th>
        <td colspan="3"  >      
         <input id="map.NEXT_DEAL_EMPNAME" class="nui-textbox asLabel " name="map.NEXT_DEAL_EMPNAME" readOnly="true" />      
        </td>
        
      </tr>
      
      <tr>
        <th class="nui-form-label"><label>附件列表：</label></th>
        <td colspan="7">
	        <div id="listbox1" class="nui-listbox" style="width:100%;height:80px;" textField="AFFILIATED_NAME" valueField="AFFILIATED_ID" 
		           dataField="resultList"  onvaluechanged="onListBoxValueChanged">
		    </div>
        </td>

     </tr> 
  <!--     
      <tr >
        <th class="nui-form-label"><label for="map.device_serial$text">下一审批人：</label></th>
        <td colspan="3"  >
        
        <input id="btnEdit1" name = "map.NEXT_SPEMPID"  class="nui-textboxlist asLabel"   allowInput="false" required="true"  style="width:400px;"/><br/>  
         <a href="#" onclick="EmponButtonEdit()" style="color:blue;text-decoration:underline;">人员选择</a>    
         <a href="#" onclick="cleanEmp()" style="color:blue;text-decoration:underline;">清空</a>
        
        </td>
        
        
        <th class="nui-form-label"><label for="map.device_serial$text"></label></th>
        <td colspan="3"  >
          
        </td>
        
      </tr> -->
      
     <!--  <tr>
        <th class="nui-form-label"><label for="map.device_serial$text">上传附件：</label></th>
        <td colspan="7"  >
           <div style="display: inline; border: solid 1px #7FAAFF; background-color: #C5D9FF; padding: 2px;width:200px;">   
	            <span id="spanButtonPlaceholder1"></span>
	         </div>
			<div id="divFileProgressContainer1"></div>
	        <div id="thumbnails">
	             <table id="infoTable" border="1" width="700" style="display: inline; border: none; padding: 2px;margin-top:8px;"> 
	            
	            </table>
	        </div>
        </td>
  
        
      </tr>
 -->
      
      
    </table>
    </form>
         </fieldset>
         
          <%
          
			Object[] obj=DatabaseExt.queryByNamedSql("default", "com.gotop.xmzg.files.process.queryProcessSpDetil",workItemID);
			for(int i=0;i<obj.length;i++)
			{
			HashMap result=(HashMap) obj[i];
		 %>
         
         <fieldset style="width:800px;border:solid 1px #aaa;position:relative;margin:5px 2px 0px 2px;" >
    	 <legend><%=result.get("ACTIVITYINSTNAME") %></legend>
    	 <div align="left" >
    	 <div id="dataform3" style="padding-top:0px;">
    	
    	 <table style="width:100%;height:95%;table-layout:fixed;" class="nui-form-table">
    	 
    	   <tr>
			        <th class="nui-form-label"  >审核说明：</th>
			        <td style="text-align:left;" colspan="3">
			           <%--  <%=result.get("APPROVAL_REMARK") %> --%>
			           
			            <%
                     
                     if(result.get("APPROVAL_REMARK")!=null)
                     {
                     
                     result.put("AUDIT", result.get("APPROVAL_REMARK"));
                    
                     }else
                     {
                      result.put("AUDIT", "");
                     }
                     
                     %>
			          <%=result.get("AUDIT") %>
			           
			           
			        </td>
			      </tr>
    	   <tr>
			        <th class="nui-form-label" style="width:15%;">处理人：</th>
			        <td style="text-align:left;width:35%;" >
			            <%=result.get("EMPNAME") %>
			        </td>

			        <th class="nui-form-label"  style="width:15%;" >处理人所在机构：</th>
			        <td style="text-align:left;width:35%;">
			            <%=result.get("ORGNAME") %>
			        </td>
			      </tr>
			      <tr>
			        <th class="nui-form-label" >处理时间：</th>
			        <td style="text-align:left;" >
			            <%=result.get("APPROVAL_TIME") %>
			        </td>
		
			        <th class="nui-form-label" >操作处理选择：</th>
			        <td style="text-align:left;" >
			          <%=result.get("RESULT_AUDIT") %>
			        </td>
			      </tr>
			       <tr>
			        <th class="nui-form-label"  >下一步处理人：</th>
			        <td style="text-align:left;" colspan="3">
			            <%-- <%=result.get("NEXT_DEAL_EMPNAME") %> --%>
			            
			              <%
                     
                     if(result.get("NEXT_DEAL_EMPNAME")!=null)
                     {
                     
                     result.put("NDEAL_EMPNAME", result.get("NEXT_DEAL_EMPNAME"));
                    
                     }else
                     {
                      result.put("NDEAL_EMPNAME", "");
                     }
                     
                     %>
			          <%=result.get("NDEAL_EMPNAME") %>
			        </td>
			      </tr>
			      
			    </table>
    	
        </div>
       </div>
      </fieldset> 
         <%
			}
		 %> 
         
        <%-- <fieldset style="width:800px;border:solid 1px #aaa;position:relative;margin:5px 2px 0px 2px;" >
    	 <legend>历史审核意见</legend>
    	 <div align="left" >
    	 <div id="dataform3" style="padding-top:0px;">
    	<div id="listbox2" class="nui-datagrid" style="width:100%;height:200px;" showPager="false"
		     dataField="List" url="com.gotop.device.process.circuit.circuit.queryCirAuditDetil.biz.ext?workItemID=<%=workItemID %>" multiSelect="true" >     
		  <div property="columns">
		         <div header="审核环节" field="WORKITEMNAME"  ></div>
		        <div header="审核结果" field="RESULT_AUDIT" ></div>
		        <div header="审核说明" field="AUDIT_DESC" ></div>
		        <div header="审核人" field="EMPNAME"  ></div>
		        <div header="审核人所在单位" field="ORGNAME"></div>
		        <div header="审核人所在机构" field="PARENTORGNAME"  ></div>
		        <div header="第几次审核" field="COUNT" ></div>
		        <div header="审核时间" field="AUDIT_TIME" ></div>
		  </div>
		 </div>
        </div>
       </div>
      </fieldset> --%>

       
       </div>  
       
    <script type="text/javascript">
        
        
 
         var workItemID = <%=workItemID %>;
         
         

        nui.parse();
         var form = new nui.Form("#form1");
         //var form2 = new nui.Form("#form2");
 //nui.get("listbox2").load();
 
 var listbox1 = nui.get("listbox1");
 var con_id;
    
    if(workItemID != null){
    //初始化加载数据 
	    $.ajax({
		        url:"com.gotop.xmzg.files.process.queryProcessApplyDetil.biz.ext",
		        type:'POST',
		        data:'workItemID='+workItemID,
		        cache:false,
		        async:false,
        		dataType:'json',
		        success:function(text){
		
		          var map = nui.decode(text);
		          form.setData(map);
		          

		          con_id=map.map.APPLY_ID;
				 listbox1.load("com.gotop.xmzg.files.process.queryAttachment.biz.ext?con_id="+con_id);
				 if(listbox1.getCount()==0){
				 	listbox1.setVisible(false);
				 }
		          
		          form.setChanged(false);
		        }
		      });
   } 
       
//下载附件
function onListBoxValueChanged(e) {
	            var listbox = e.sender;
	            var at_id = listbox.getValue();
	            var items=listbox.getSelecteds();
	            
	            //var json = nui.encode({AT_ID:at_id});
				nui.confirm("确定下载该附件？","系统提示",function(action){
		             if(action=="ok"){
			             var filepath=items[0].AFFILIATED_ADDRESS;
		            	 var name=items[0].AFFILIATED_NAME;
			             var url="<%=request.getContextPath() %>/files/process/AttachmentDownload.jsp?filepath="+filepath+"&filename="+name+" ";
		            	 window.location.replace(encodeURI(url)); 
		            	 
		            	 }
		             });
	           listbox.deselectAll();
	            }
    
        
          function onCancel(){
      //CloseWindow("cancel");
       window.history.go(-1) ;
    } 
    
    
        //生成pdf
    var downPdf = document.getElementById("downLoad");
    downPdf.onclick = function() {
        $("#downLoad").hide();
        html2canvas(document.body, {
            onrendered:function(canvas) {

                var contentWidth = canvas.width;
                var contentHeight = canvas.height;

                //一页pdf显示html页面生成的canvas高度;
                var pageHeight = contentWidth / 592.28 * 841.89;
                //未生成pdf的html页面高度
                var leftHeight = contentHeight;
                //pdf页面偏移
                var position = 0;
                //a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
                var imgWidth = 595.28;
                var imgHeight = 592.28/contentWidth * contentHeight;

                var pageData = canvas.toDataURL('image/jpeg', 1.0);

                var pdf = new jsPDF('', 'pt', 'a4');

                //有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
                //当内容未超过pdf一页显示的范围，无需分页
                if (leftHeight < pageHeight) {
                    pdf.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight );
                } else {
                    while(leftHeight > 0) {
                        pdf.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight)
                        leftHeight -= pageHeight;
                        position -= 841.89;
                        //避免添加空白页
                        if(leftHeight > 0) {
                            pdf.addPage();
                        }
                    }
                }
                pdf.save('档案借阅流程.pdf');//生成pdf
            }
        })
 		
   }
 setTimeout(function(){$("#downLoad").show();},1000);
    </script>

</body>
</html>
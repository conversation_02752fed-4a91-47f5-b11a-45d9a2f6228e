<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%@page import="com.eos.data.datacontext.UserObject" %>
<%@include file="/coframe/dict/common.jsp"%>
<html>
<!-- 
  - Author(s): lyl
  - Date: 2019-12-03 09:36:10
  - Description:
-->
<head>
<!-- stream插件 -->
<link href="../../css/stream-v1.css" rel="stylesheet" type="text/css">

</head>
<%
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>
<body>
	<div id="fileForm" style="padding-top:5px;">
	    <input name="inputData.INFORMATION_ID" id="INFORMATION_ID"  class="nui-hidden" />
	    <input name="inputData.STORAGE_ADDRESS_OLD" id="STORAGE_ADDRESS_OLD"  class="nui-hidden" />
	    <input name="inputData.STORAGE_LOCATION_OLD" id="STORAGE_LOCATION_OLD"  class="nui-hidden" />
	    <input name="inputData.FILES_NAME" id="FILES_NAME"  class="nui-hidden" />
	    <input name="inputData.FILES_TYPE" id="FILES_TYPE"  class="nui-hidden" />
	    <input name="inputData.BUSINESS_TYPE" id=" BUSINESS_TYPE"  class="nui-hidden" />
	    <input name="inputData.SUB_ORG" id="SUB_ORG"  class="nui-hidden" />
	    <input name="inputData.DEAL_ORG" id="DEAL_ORG"  class="nui-hidden" />
	    <input name="inputData.EMPNAME" id="EMPNAME"  class="nui-hidden" />
	    <input name="inputData.CUSTOMER_NAME" id="CUSTOMER_NAME"  class="nui-hidden" />
	    <input name="inputData.CUSTOMER_NO" id="CUSTOMER_NO"  class="nui-hidden" />
	    <input name="inputData.CONTRACT_NUMBER" id="CONTRACT_NUMBER"  class="nui-hidden" />
	    <input name="inputData.CONTRACT_PRICE" id="CONTRACT_PRICE"  class="nui-hidden" />
	    <input name="inputData.START_TIME" id="START_TIME"  class="nui-hidden" />
	    <input name="inputData.END_TIME" id="END_TIME"  class="nui-hidden" />
	    <input name="inputData.BUSINESS_LINE" id="BUSINESS_LINE"  class="nui-hidden" />
	    <input name="inputData.CHECK_TYPE" id="CHECK_TYPE"  class="nui-hidden" />
	    <input name="inputData.CHECK_TIME" id="CHECK_TIME"  class="nui-hidden" />
	    
	    
		<table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
	      	<tr>
				<th class="nui-form-label"><label for="type$text">库存地址：</label></th>
				<td >
					<input class="nui-dictcombobox" name="inputData.STORAGE_ADDRESS" id="STORAGE_ADDRESS"  emptyText="请选择" dictTypeId="FILES_STORAGE_ADDRESS"  style="width:150px;" required = "true" allowInput = "false"/>
  				</td>
	      		<th class="nui-form-label"><label for="type$text">货架号/箱号：</label></th>
	      		<td >
	      			<input name="inputData.STORAGE_LOCATION" id="STORAGE_LOCATION"  class="nui-textbox" style="width:150px;" required = "true" />
	      		</td>
	      		<th class="nui-form-label"><label for="type$text">档案盒号：</label></th>
	      		<td >
	      			<input name="inputData.BOX_NUM" id="BOX_NUM"  class="nui-textbox" style="width:150px;" required = "true" />
	      		</td>
	      	</tr>
	      	<tr >
				<th class="nui-form-label"><label for="type$text">附件：</label></th>
				<td colspan="5">
					<input class="nui-textarea" name="map.AFFILIATED_NAME" id="AFFILIATED_NAME" style="width:550px;" readOnly="true"/>
  				</td>
	      	</tr>
	      	<tr>
		      	<th class="nui-form-label"><label for="inputData.type$text">附件上传：</label></th>
		      	<td >
		      		<!-- <div id="i_select_files" >
						</div> -->
					<input type="button" class="btn btn-default" id="i_select_files"  value="添加文件"/>
	        	</td>
	        	<th class="nui-form-label"><label for="type$text"></label></th>
	      		<td >
	      		</td>
	      	</tr>
	      	<tr align="center">
	      		<th class="nui-form-label"><label for="inputData.type$text">上传进度：</label></th>
	      		<td colspan="5">
					<!-- 回显进度 -->
					<div id="i_stream_files_queue" ></div>
	      		</td>
	      	</tr>
		</table>
	    <div class="nui-toolbar" style="padding:0px;"   borderStyle="border:0;">
			<table width="100%">
		    	<tr>
		        	<td style="text-align:center;">
		          		<a class="nui-button" iconCls="icon-save" id="saveButtorn"  onclick="onOk()">确定</a>
		          		<span style="display:inline-block;width:25px;"></span>
		          	<!-- 	<a class="nui-button" iconCls="icon-reset" onclick="clean()">重置</a> -->
		          		<a class="nui-button" iconCls="icon-reset" onclick="back()">返回</a>
		        	</td>
		      	</tr>
		   	</table>
		</div>
	</div>
<script type="text/javascript" src="../../js/stream-v1.js"></script> 
<script type="text/javascript">
    nui.parse();
    var fileForm = new nui.Form("#fileForm");
    
  /*   //关闭添加窗口
 	function CloseWindow(action){
  		if(window.CloseOwnerWindow) 
    		return window.CloseOwnerWindow(action);
  		else
    		return window.close();
    }
     */
    
     //与父页面的方法2对应：页面间传输json数据
    function setFormData(data){
                   
        //跨页面传递的数据对象，克隆后才可以安全使用
        var infos = nui.clone(data);
        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
             var json = infos.record;
             var form = new nui.Form("#fileForm");//将普通form转为nui的form
             form.setData(json);
             var STORAGE_ADDRESS = json.inputData.STORAGE_ADDRESS;
             var STORAGE_LOCATION = json.inputData.STORAGE_LOCATION;
             var BOX_NUM = json.inputData.BOX_NUM;
             nui.get("STORAGE_ADDRESS_OLD").setValue(STORAGE_ADDRESS);
             nui.get("STORAGE_LOCATION_OLD").setValue(STORAGE_LOCATION);
             nui.get("BOX_NUM").setValue(BOX_NUM);
        	 //附件相关
             var id = json.inputData.AFFILIATED_IDS;
             var name = json.inputData.AFFILIATED_NAMES;
	         fj(id,name);
         }
    }
      
      //操作列：查看/下载附件
      function fj(id,name){
      var s = "";
      if(name!=null){
      		var names = name.split(",");
      		var ids = id.split(",");
      		var a = [];
      		for(var i=0;i<names.length;i++){
		           var s1=	"<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\"  onclick=\"downAccessory(\'"+ids[i]+"\',\'"+names[i]+"\')\">"+names[i]+"</a>";
		           a.push(s1);
	        }
	        s = a; 
      	}else{
      		s="无附件";
      	}
      	$("#AFFILIATED_NAME").html(""+s+"");
      }
     
     //下载附件
	 function downAccessory(id,name){
       	var fileId = id;
       	var fileName = name;
       	var url="<%=request.getContextPath()%>/files/fileList/downloadFile.jsp?fileId="+fileId+"&fileName="+fileName;
		window.location.replace(url);
	  }
    
    function back(){
    	CloseWindow("cancel");
    }
    
    var files=new Array();
    //stream 插件配置
	var config = {
		browseFileId : "i_select_files", /** 选择文件的ID, 默认: i_select_files */
		browseFileBtn : "<div>请选择文件</div>", /** 显示选择文件的样式, 默认: `<div>请选择文件</div>` */
		dragAndDropArea: "i_select_files", /** 拖拽上传区域，Id（字符类型"i_select_files"）或者DOM对象, 默认: `i_select_files` */
		dragAndDropTips: "<span>把文件(文件夹)拖拽到这里</span>", /** 拖拽提示, 默认: `<span>把文件(文件夹)拖拽到这里</span>` */
		filesQueueId : "i_stream_files_queue", /** 文件上传容器的ID, 默认: i_stream_files_queue */
		filesQueueHeight : 200, /** 文件上传容器的高度（px）, 默认: 450 */
		messagerId : "i_stream_message_container", /** 消息显示容器的ID, 默认: i_stream_message_container */
		multipleFiles: true, /** 多个文件一起上传, 默认: false */
		onRepeatedFile: function(f) {
			alert("文件："+f.name +" 大小："+f.size + " 已存在于上传队列中。");
			return false;	
		},
//		autoUploading: false, /** 选择文件后是否自动上传, 默认: true */
//		autoRemoveCompleted : true, /** 是否自动删除容器中已上传完毕的文件, 默认: false */
//		maxSize: 104857600//, /** 单个文件的最大大小，默认:2G */
//		retryCount : 5, /** HTML5上传失败的重试次数 */
//		postVarsPerFile : { /** 上传文件时传入的参数，默认: {} */
//			param1: "val1",
//			param2: "val2"
//		},
		swfURL : "/swf/FlashUploader.swf" ,/** SWF文件的位置 */
		tokenURL : "<%=request.getContextPath()%>/tk", /** 根据文件名、大小等信息获取Token的URI（用于生成断点续传、跨域的令牌） */
		frmUploadURL : "<%=request.getContextPath()%>/fd", /** Flash上传的URI */
		uploadURL : "<%=request.getContextPath()%>/upload" ,/** HTML5上传的URI */
		filesQueueHeight :100,
//		simLimit: 200, /** 单次最大上传文件个数, */
//		extFilters: [".txt", ".rpm", ".rmvb", ".gz", ".rar", ".zip", ".avi", ".mkv", ".mp3"], /** 允许的文件扩展名, 默认: [] */
//		onSelect: function(list) {alert('onSelect')}, /** 选择文件后的响应事件 */
//		onMaxSizeExceed: function(size, limited, name) {alert('onMaxSizeExceed')}, /** 文件大小超出的响应事件 */
//		onFileCountExceed: function(selected, limit) {alert('onFileCountExceed')}, /** 文件数量超出的响应事件 */
//		onExtNameMismatch: function(name, filters) {alert('onExtNameMismatch')}, /** 文件的扩展名不匹配的响应事件 */
//		onCancel : function(file) {alert('Canceled:  ' + file.name)}, /** 取消上传文件的响应事件 */
		onComplete: function(file) {
			//alert(file.name);
			files.push(file);
			console.log(files);
		
		} /** 单个文件上传完毕的响应事件 */
//		onQueueComplete: function() {alert('onQueueComplete')} /** 所以文件上传完毕的响应事件 */
//		onUploadError: function(status, msg) {alert('onUploadError')} /** 文件上传出错的响应事件 */
//		onDestroy: function() {alert('onDestroy')} /** 文件上传出错的响应事件 */
	};
	//启动stream
	var _t = new Stream(config);
    
    
    /* 
     function GetData() {  
       	var STORAGE_ADDRESS = nui.get("STORAGE_ADDRESS").getValue();
       	var STORAGE_LOCATION = nui.get("STORAGE_LOCATION").getValue();
        var data = {};
        data.STORAGE_ADDRESS = STORAGE_ADDRESS;
        data.STORAGE_LOCATION = STORAGE_LOCATION;
        return data;
    } */
    
    function onOk(){
    
    	fileForm.validate();            
        if (fileForm.isValid() == false) return;
        var map = fileForm.getData(false,true);
        	map.files=files;
        var json = nui.encode(map);
        var URL="com.gotop.xmzg.files.fileList.uptFilesStorage.biz.ext";
        $.ajax({
        	url: URL,
            type: 'POST',
            data: json,
            cache: false,
            contentType:'text/json',
            success: function (text){
                var returnJson = nui.decode(text);
				if(returnJson.flag == "1"){
							CloseWindow("saveSuccess");
				}else if(returnJson.flag == "0"){
					nui.alert("保存失败", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							//CloseWindow("saveFailed");
						}
					});
				}
		    }
  		});   
    }
    
  /*    //重置
	function clean(){
	   	//不能用form.reset(),否则会把隐藏域信息清空
	   	var STORAGE_ADDRESS =  nui.get("STORAGE_ADDRESS_OLD").getValue();
        var STORAGE_LOCATION =  nui.get("STORAGE_LOCATION_OLD").getValue();
	   	fileForm.reset();
        nui.get("STORAGE_ADDRESS").setValue(STORAGE_ADDRESS);
        nui.get("STORAGE_LOCATION").setValue(STORAGE_LOCATION);
    }
     */
   //关闭添加窗口,删除临时文件
 	function CloseWindow(action){
 		if((action == 'cancel'||action == 'close') && files.length != 0){
 			var data = {};
 			data.files = files;
 			var json = nui.encode(data);
        	$.ajax({
                url:"com.gotop.xmzg.files.fileList.delTempFiles.biz.ext",
                type:'POST',
                data:json,
                cache:false,
                async:false,
                contentType:'text/json',
                success:function(text){
                   var returnJson = nui.decode(text);
                   if(returnJson.exception == null && returnJson.flag == 1){
                        files.splice(0, files.length);//文件上传成功清空文件对象数组避免重复提交
                     }else{
                         alert("临时文件删除失败！");
                        }
                   }
             });
 		}
  		if(window.CloseOwnerWindow) 
    		return window.CloseOwnerWindow(action);
  		else
    		return window.close();
    }
    
</script>
</body>
</html>
<%@page import="com.eos.system.utility.StringUtil"%>
<%@page import="com.eos.access.http.security.config.HttpSecurityConfig"%>
<%@page pageEncoding="UTF-8"%>
<%@page import="com.primeton.cap.AppUserManager"%>
<%@page import="com.eos.data.datacontext.UserObject"%>
<%@page import="java.util.HashMap" %>
<%@page import="java.util.Map" %>
<%@page import="com.pfpj.foundation.database.DatabaseExt" %>
<%@page import="com.primeton.ext.engine.component.LogicComponentFactory" %>
<%@page import="com.eos.engine.component.ILogicComponent" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): shitf
  - Date: 2013-03-07 15:24:13
  - Description:
-->
<head>
<title>用户登录</title>
<%
   String contextPath=request.getContextPath();
   String url = null;
   HttpSecurityConfig securityConfig = new HttpSecurityConfig();
   boolean isOpenSecurity = securityConfig.isOpenSecurity();
   if(isOpenSecurity){
   		boolean isAllInHttps = securityConfig.isAllInHttps();
   		if(!isAllInHttps){
   			String ip = securityConfig.getHost();
   			String https_port = securityConfig.getHttps_port();
   			url = "https://" + ip + ":" + https_port + contextPath + "/coframe/auth/login/org.gocom.components.coframe.auth.login.login.flow";
   		}else{
   			url = "org.gocom.components.coframe.auth.login.login.flow"; 
   		}
   }else{
   		url = "org.gocom.components.coframe.auth.login.login.flow";
   }
 %>
<script type="text/javascript" src="<%=contextPath%>/common/nui/nui.js"></script>
<link rel="stylesheet" type="text/css" href="<%=contextPath %>/coframe/auth/login/css/style.css" />
</head>
<%
	String original_url=null;
	Object objAttr=request.getAttribute("original_url");
	if(objAttr != null){
		original_url=StringUtil.htmlFilter((String)objAttr);
	}

 %>
<body class="login">

<div id="warpper" class="wrap">
		<div class="main">
			<div id="form1" class="login-box">
				<h3>厦门分行新一代综合管理系统</h3>
				<form method="post"	name="loginForm" onsubmit="return login();" action="<%=url%>" >
					<input id="original_url" class="nui-hidden" name="original_url" value="<%=original_url %>"/>
					<p class="login-item">
					  <em>员工号：</em>
					  <input class="nui-textbox" id="userId" name="userId" style="width:247px;height:26px;"
					   onenter="keyboardLogin" onvalidation="onCheckUserId"/>
					</p>
					<p class="login-item">
					  <em>密　码：</em>
					  <input name="password" id="password"  class="nui-password" vtype="minLength:6" minLengthErrorText="密码不能少于6个字符"
			                onenter="keyboardLogin" style="width:247px;height:26px;" onvalidation="onCheckPassword" 
			                autocomplete="off" />
					</p>
					<p id="error" class="login-error" style="display:inline-block;height:20px;color:red;"></p>
					<p class="login-btn center">
						<input class="log" type="submit" value="登 录" />
					</p>
				</form>
			</div>
		</div>
		<div class="foot">
			<p>中国邮政储蓄银行厦门分行</p>
		</div>
	</div>

<script type="text/javascript">
     if(window.top!=window){
		window.top.location = window.location;
	 }
     nui.parse();
  
     var form = new nui.Form("#form1");
     
     nui.get("userId").focus();
     
     function onCheckUserId(e){
       if (e.isValid) {
         if(e.value==""){
           e.errorText = "用户名不能为空";
           e.isValid = false;
         }
       }
     }
     
     function onCheckPassword(e){
       if (e.isValid) {
         if(e.value==""){
           e.errorText = "密码不能为空";
           e.isValid = false;
         }
       }
     }
     <% 
     	Object result = request.getAttribute("result");
     	String userName = (String)request.getAttribute("userId");
     	if (userName==null)userName="";
     	String password = (String)request.getAttribute("password");
     	if (password==null)password="";
		  String content = "";
		       if(result != null){
		     		Integer resultCode = (Integer)result;
		     		 if(resultCode == 0){
				     	out.println("showError('密码错误！')");
				     	content = userName+"-密码错误";
				     }else if(resultCode == -1){
				     	out.println("showError('用户不存在！')");
				     	content = userName+"-用户不存在";
				     }else if(resultCode == -2){
				     	out.println("showError('用户无权限登录，请联系系统管理员！')");
				     	content = userName+"-用户无权限登录，请联系系统管理员";
				     }else if(resultCode == 3){
				     	out.println("showError('用户已过期！')");
				     	content = userName+"-用户已过期";
				     }else if(resultCode == 4){
				     	out.println("showError('用户未到开始使用时间！')");
				     	content = userName+"-用户未到开始使用时间";
				     }else if(resultCode == 5){
				     	out.println("showError('密码已过期！')");
				     	content = userName+"-密码已过期";
				     }else if(resultCode == -3){
		      			out.println("showError('查询用户信息失败，请联系系统管理员检查数据库连接！')");
		      			content = userName+"-查询用户信息失败，请联系系统管理员检查数据库连接";
		     		 }else{
		      			out.println("showError('未知的异常，请联系系统管理员！')");
		      			content = userName+"-未知的异常，请联系系统管理员";
		     		 }
     
			//Java里调用biz
			HashMap<String, Object> map2 = new HashMap<String, Object>();
			String OPE_MOD = "用户登录";
			String OPE_CONTENT = "["+request.getRemoteAddr()+"]-"+content;
			try{
				 String componentName = "org.gocom.components.coframe.auth.login.login";// 逻辑构件名称
	     		 String operationName = "insert_sysope_log";// 逻辑流名称
	     		 ILogicComponent logicComponent = LogicComponentFactory.create(componentName);
			     int size = 2;  //表示2个参数
			     Object[] params = new Object[size];// 逻辑流的输入参数
			     params[0] = OPE_MOD;
			     params[1] = OPE_CONTENT;
			     logicComponent.invoke(operationName, params);
			}catch(Throwable e){
			}
     	}
     	
	  %>
	           
      function showError(msg){
      	 $("#error").html(msg);
      }
      
      //获取键盘 Enter 键事件并响应登录
     function keyboardLogin(e){
       login();
     }
     function login(){
     	var form = new nui.Form("#form1");
        form.validate();
        if (form.isValid() == false) 
        	return false;
        document.loginForm.submit();
     }
 	 
 	//判断密码是否超过设定天数
    function checkisExist(){
       var mcode = nui.get("userId").getValue();	
		    
				  nui.open({
			          url:"<%=contextPath%>/coframe/auth/expire/update_password_qz.jsp",
			          title:'密码修改',
			          width:600,
			          height:400,
			          onload:function(){
			             var iframe = this.getIFrameEl();
			             
			             //方法1：后台查询一次，获取信息回显
			             /* var data = row;
			             iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
			             
			             //方法2：直接从页面获取，不用去后台获取
			               var data = {pageType:"edit",record:{mcode:mcode}};
			              iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
			          },
			          ondestroy:function(action){
			             if(action=="saveSuccess"){
			                grid.reload();
			             }
			          }
			       });
				
		    
		 }
     
 	 
</script>
 </body>
  <%
 	request.getSession().invalidate();
 	Cookie[] cookies = request.getCookies();
 	if(cookies != null){
 		for(int i=0;i<cookies.length;i++){
 			if(StringUtil.equals("jsessioinid", cookies[i].getName())){
 				cookies[i].setMaxAge(0);
 			}
 		}
 	
 	}
  %>
</html>

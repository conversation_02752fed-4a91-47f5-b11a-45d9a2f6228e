<%@page pageEncoding="UTF-8"%>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-07-04 14:44:11
  - Description:
-->
<%
UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>

<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>会计内部账户信息查询</title>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <div class="nui-hidden"  name="queryData.userOrgId" id="userOrgId"></div>
		<div class="nui-hidden"  name="queryData.userId"  id="userId" ></div>
	    <table class="table" style="width:100%;">
	        <tr>
	           <th class="tit" >查询日期：</th>
				<td>
					<input id="queryData.file_date" name = "queryData.file_date" class="nui-datepicker " required="true" style="width:110px;" allowInput="false" format="yyyyMMdd" />
				</td>
				<th  class="tit">账套：</th>
				<td>
					<input class="nui-dictcombobox" valueField="dictID" textField="dictName"
	          	      dictTypeId="ACC_SET" id="queryData.acc_set" name="queryData.acc_set"  required="true" style="width:130px;" value="001156"/>
				</td>
				<th class="tit" >科目代码：</th>
				<td>
					<input id="queryData.itm_no" name="queryData.itm_no" class="nui-textbox" required="true" style="width:130px;" vtype="maxLength:50"/>
				</td>	
			</tr>	
			 <tr>	
			    <th class="tit" >机构代码：</th>
				<td>
					<input id="queryData.acc_inst" name="queryData.acc_inst" class="nui-textbox" style="width:130px;" vtype="maxLength:10"/>
				</td>
				<th class="tit" >内部账户账号：</th>
				<td>
					<input id="queryData.inn_acc" name="queryData.inn_acc" class="nui-textbox" style="width:130px;" vtype="maxLength:20"/>
				</td>
				<th class="tit" >户名：</th>
				<td>
					<input id="queryData.acc_name" name="queryData.acc_name" class="nui-textbox" style="width:130px;" vtype="maxLength:50"/>
				</td>	
				<th  class="btn-wrap">
					<input class="nui-button" text="查询" iconCls="icon-search" onclick="search"/>&nbsp;&nbsp;
					<input class="nui-button" text="重置" iconCls="icon-reset" onclick="clean"/>&nbsp;&nbsp;
					<input class="nui-button" text="导出报表" iconCls="icon-download" onclick="excel"/>
				</th>
				
			</tr>
	    </table>
	  </div>
    </div>
  </div>
   <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.customReport.account_depart.account_rpt.qry_inn_acc.biz.ext"
	  sizeList=[5,10,20,50,100] multiSelect="true" pageSize="20">
	    <div property="columns" >
	        <div field="INN_ACC"            headerAlign="center"  align="center" width=150>内部账户账号</div>
			<div field="ACC_NAME"           headerAlign="center"  align="center" width=180>账户名称</div>
			<div field="OP_INST"            headerAlign="center"  align="center" width=80>开户机构代码</div>
			<div field="ACC_INST"           headerAlign="center"  align="center" width=80>核算机构代码</div>
			<div field="CURR_TYPE"          headerAlign="center"  align="center" width=50>币种</div>
			<div field="DR_BAL"             headerAlign="center"  align="center" width=80>借方余额</div>
			<div field="CR_BAL"             headerAlign="center"  align="center" width=80>贷方余额</div>
			<div field="DR_NUM_BAL"         headerAlign="center"  align="center" width=80>收方结余</div>
			<div field="CR_NUM_BAL"         headerAlign="center"  align="center" width=80>付方结余</div>
			<div field="LST_BAL"            headerAlign="center"  align="center" width=80>上日余额</div>
			<div field="LST_TRAN_DATE"      headerAlign="center"  align="center" width=80>末笔交易日期</div>
			<div field="ITM_NO"             headerAlign="center"  align="center" width=100>科目代码</div>
			<div field="STAT"               headerAlign="center"  align="center" width=60>账户状态</div>
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">
     nui.parse();
    var grid = nui.get("datagrid1");
    var  userOrgId="<%=userObject.getUserOrgId()%>";
	var  userId="<%=userObject.getUserId()%>";
    
	nui.get("userOrgId").setValue(userOrgId);
    nui.get("userId").setValue(userId);
    
    var form = new nui.Form("#form1");
	var data = form.getData(true,true);
    //grid.load(data);
    
    function search(){
       form = new nui.Form("#form1");
       form.validate();
       if (form.isValid() == false) return;
       data = form.getData(true,true);
       grid.load(data);
    }
    
//导出Excel
function excel(){     
	var form=new nui.Form("form1");
	form.validate();
    if (form.isValid() == false) return;
	var data=form.getData(true, true);
	var fileName="会计内部账户信息导出";
	var reportName="account_depart_inn_acc";
	var queryPath="com.gotop.xmzg.customReport.account_depart.account_rpt.qry_inn_acc";
	
	data=nui.encode(data);
    
     var url="<%=request.getContextPath()%>/customReport/solvLimitReport/limitExportExcel.jsp?fileName="+fileName+"&reportName="+reportName+"&queryPath="+queryPath+"&data="+data;
	 window.location.replace(encodeURI(url));
	 
	 //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
     setMask();
} 

    //机构树回显
     function onButtonEdit(e) {
            var btnEdit = this;
            nui.open({
                <%--  url:"<%=request.getContextPath() %>/kpiCheckManage/ProOrgKpiManage/Prokpiorg_tree.jsp",  //初始未展开的机构树，加载快  --%>
                url:"<%=request.getContextPath() %>/report/tree/ReportOrg_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            
                        }
                    }
                }
            });            
             
        }  


 //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)	
function setMask(){
	 
	 var a= nui.loading("正在操作中,请稍等...","提示"); //显示遮罩层
	 
	 var icount = setInterval(function(){  
	
		  if(document.attachEvent){   //IE浏览器
	
		 	if(document.readyState=='interactive'){

	              nui.hideMessageBox(a);  //隐藏遮罩层
	              clearTimeout(icount);
	        }
		 }else{ //谷歌浏览器
		 	if(document.readyState=='complete'){

	              nui.hideMessageBox(a);  //隐藏遮罩层
	              clearTimeout(icount);
	        }
		 } 
	 
	 }, 1); 
	 
}

  function adjusttype(e){
      	    return nui.getDictText("PER_ADJUST_TYPE", e.row.ADJUST_TYPE);
  }
   
   function clean(){  

        nui.get("queryData.file_date").setValue("");
		nui.get("queryData.acc_set").setValue("");
		nui.get("queryData.itm_no").setValue("");
		nui.get("queryData.acc_inst").setValue("");
		nui.get("queryData.inn_acc").setValue("");
		nui.get("queryData.acc_name").setValue("");
   }

  </script>
</body>
</html>
<%@page pageEncoding="UTF-8"%>	
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2019-06-04 14:51:16
  - Description:
-->
<head>
	<title>指标查询</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%@include file="/achieve/init.jsp"%>
</head>

<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="send_bw" class="nui-form">
	    <table class="table" style="width:100%;">
	        <tr>
				<th class="tit">业务条线：</th> 
		        <td>
			      	<div id="tip_code" name="obj/tip_code" class="nui-combobox" allowInput="false" 
				      	dataField="list" textField="TIP_NAME" valueField="TIP_CODE" style="width:300px;"
				      	showNullItem="true" nullItemText="全部" nullItemText="全部" emptyText="全部"
				      	url="com.gotop.xmzg.achieve.indicators.indicators_plate_choice.biz.ext">
			      		<div property="columns">
					        <div header="条线代码" field="TIP_CODE" width="60"></div>
					        <div header="条线名称" field="TIP_NAME" width="120"></div>
					    </div>
				    </div>
		        </td>
		        <th class="tit">指标名称：</th> 
		        <td><input class="nui-textbox" id="ti_name" name="obj/ti_name"/></td>
		        <th class="tit">指标说明：</th> 
		        <td><input class="nui-textbox" id="ti_remark" name="obj/ti_remark"/></td>
		        <th class="tit">是否启用：</th> 
		        <td>
		        	<input class="nui-dictcombobox" id="ti_start" valueField="dictID" textField="dictName" 
					dictTypeId="JF_STATE" name="obj/ti_start"
					showNullItem="true" nullItemText="全部" nullItemText="全部" emptyText="全部"
					/>
		        </td>
				<th><a class="nui-button"  iconCls="icon-search" onclick="search">查询</a></th>
				<th><a class="nui-button" iconCls="icon-reset"  onclick="clean">重置</a></th>
		    </tr>
	    </table>
	  </div>
    </div>
  </div>
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
             <a class="nui-button" iconCls="icon-add" onclick="add">新增</a>
             <a class="nui-button" iconCls="icon-edit" onclick="update">修改</a>
             <a class="nui-button" iconCls="icon-remove" onclick="del">删除</a>
             <a class="nui-button" iconCls="icon-collapse" onclick="detail">详情</a>
             <a class="nui-button" iconCls="icon-ok" onclick="qy">启用</a> 
		     <a class="nui-button" iconCls="icon-no" onclick="jy">禁用</a>
		     <input class="nui-hidden" id="orgs"/>
           </td>
        </tr>
      </table>
    </div>

  <div class="nui-fit">
	 <div id="bw_grid" class="nui-datagrid"
			 style="width:100%;height: 100%;"
			 idField="id" 
			 totalField="page.count"  
			 showPageInfo="true"
			 allowResize="false"  
			 pageSize="20"  
			 allowUnselect="true"
			 multiSelect="false"
			 sizeList=[5,10,20,50,100] 
	         dataField="list"
			 url="com.gotop.xmzg.achieve.indicators.indicators_list.biz.ext"
			 >
	    <div property="columns" >
	      <div type="checkcolumn"></div>
	      <div field="TIP_ORGCODES" visible="false"></div>
	      <div field="TI_SORTING" visible="false"></div>
	      <div field="TIP_CODE" headerAlign="center" align="center" allowSort="true" >业务条线代码</div>
		  <div field="TIP_NAME" headerAlign="center" align="center" allowSort="true" >业务条线名称</div>
		  <div field="TI_CODE" headerAlign="center" align="center" allowSort="true" >指标代码</div>
		  <div field="TI_NAME" headerAlign="center" align="center" allowSort="true" >指标名称</div>
		  <div field="TI_REMARK" headerAlign="center" align="center" allowSort="true" >指标说明</div>
		  <div field="TI_INTEGRAL" headerAlign="center" align="center" allowSort="true" >积分系数</div>
		  <div field="TI_START" headerAlign="center" align="center" allowSort="true" renderer="ti_start">指标状态</div>
		  <div field="TI_CREATETIME" headerAlign="center" align="center" allowSort="true" renderer="setdate">创建时间</div>
		  <div field="CREATEORGNAME" headerAlign="center" align="center" allowSort="true">创建机构</div>
		  <div field="CREATORNAME" headerAlign="center" align="center" allowSort="true">创建人</div>
	    </div>
	 </div>
  </div>
 
 <script type="text/javascript">
    nui.parse();
    var grid = nui.get("bw_grid");
	search();
	//查询
    function search() {
    	var form = new nui.Form("#send_bw");
 		form.validate();
      	if (form.isValid() == false) return;
      	grid.load(form.getData());
      	//插入系统操作日志
        var OPE_MOD = "指标管理维护";
    	var OPE_CONTENT = "查询,输入项:"+nui.get("tip_code").getValue()+"|"+nui.get("ti_name").getValue()+"|"+nui.get("ti_remark").getValue()+"|"+nui.get("ti_start").getValue();
      	insert_sysope_log(OPE_MOD,OPE_CONTENT);
	}
	//转换时间
    function setdate(e){
 		var date = e.record.TI_CREATETIME;
 		if(!isNullOrEmpty(date) && date.length == 14){
 			return changeDate(date);
 		}else{
 			return "";
 		}
 	}
	//指标状态
    function ti_start(e){
		var ti_start = e.record.TI_START;
		return nui.getDictText("JF_STATE",ti_start);
	}
	
	//打开添加页面
	function add(){
		nui.open({
			url:bactpath+"/achieve/indicators/indicators_add.jsp",
			title:"指标新增",
			width:500,
			height:300,
			onload:function(){},
			ondestroy:function(action){
				if(action=="saveSuccess"){
	                grid.reload();
	            }
			}
		});
	}
	
	//打开修改页面
	function update(){
		var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
			nui.get("orgs").setValue(rows[0].TIP_ORGCODES);
			contain(function(){
				nui.open({
					url:bactpath+"/achieve/indicators/indicators_update.jsp",
					title:"指标修改",
					width:500,
					height:300,
					onload:function(){
						var iframe = this.getIFrameEl();
	 	      	    	iframe.contentWindow.setData(rows[0]);
					},
					ondestroy:function(action){
						if(action=="saveSuccess"){
			                grid.reload();
			            }
					}
				});
			});
		}
	}
	//详情
	function detail(){
       var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
	       nui.open({
	          url:bactpath+"/achieve/indicators/indicators_detail_info.jsp",
	          title:'详情',
	          width:900,
	          height:600,
	          onload:function(){
	        	  var iframe = this.getIFrameEl();
	      	      iframe.contentWindow.setData(rows[0]);
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }
    }
	
	//删除
	function del(){
		var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
			nui.get("orgs").setValue(rows[0].TIP_ORGCODES);
			contain(function(){
				nui.confirm("确定删除以下选择的数据？", "确定？",
		           function (action) {
		             if (action == "ok") {
		            	 var newMaps = [];
		            	 var arr = [];
		            	 var str = "";
		            	 for(var i = 0;i<rows.length;i++){
		            		 if(rows[i].TI_START=="1"){
		            			 var ms = rows[i].TI_NAME+"指标已启用，无法删除操作!";
		            			 nui.alert(ms, "系统提示", function(action){});
		            			 return ;
		            		 }
		            		 var ids = {ti_code:rows[i].TI_CODE,ti_name:rows[i].TI_NAME};
		            		 newMaps.push(ids);
		            		 arr.push(rows[i].TI_CODE);
		            		 if(i==rows.length-1){
		            			 str+=rows[i].TI_CODE;
		            		 }else{
		            			 str+=rows[i].TI_CODE+",";
		            		 }
		            	 }
		            	 str = "删除以下【"+str+"】指标";
		            	 var json = nui.encode({maps:newMaps,str:str,arr:arr});
		            	 var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
		            	 //提交数据
	                     nui.ajax({
	                        url: "com.gotop.xmzg.achieve.indicators.indicators_del.biz.ext",
	                        type: "post",
	                        data: json,
	                        contentType:'text/json',
	                        success: function (text) {
	                           nui.hideMessageBox(load);  //隐藏遮罩层
	                     	   var msg = text.msg;
	                     	   nui.alert(msg, "系统提示", function(action){});
	                     	   grid.reload();
	                        }
	                    });
		             }
				});
			});
		}
	}
	function qy(){
    	qyjy(1,"启用");
    }
    function jy(){
    	qyjy(0,"禁用");
    }
    function qyjy(TI_START,text){
    	var rows = grid.getSelecteds();
    	if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
			nui.get("orgs").setValue(rows[0].TIP_ORGCODES);
			contain(function(){
				if(rows[0].TI_START == TI_START){
	      	   	    nui.alert(rows[0].TI_NAME+"已"+text);
	      	   	  	return ;
	      	   	}
				var str = "";
				str+=rows[0].TI_CODE;
				var ids = {
						ti_code:rows[0].TI_CODE,
						ti_name:rows[0].TI_NAME,
						ti_start:TI_START};
		      	str = text+"以下【"+str+"】指标";
				
	        	var json = nui.encode({map:ids,start:TI_START,type:1});
	        	var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^=");
	        	$.ajax({
	                url:"com.gotop.xmzg.achieve.indicators.update_indicators_start.biz.ext",
	                type:'POST',
	                data:json,
	                cache: false,
	                contentType:'text/json',
	                success:function(text){
	                	nui.hideMessageBox(load);
	                	var code = text.code;
	                	var msg = text.msg;
	                	if(code == "0000"){
	                		json = nui.encode({map:ids,start:TI_START,str:str,type:2});
	                		load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^=");
	                		$.ajax({
	                            url:"com.gotop.xmzg.achieve.indicators.update_indicators_start.biz.ext",
	                            type:'POST',
	                            data:json,
	                            cache: false,
	                            contentType:'text/json',
	                            success:function(text){
	                            	nui.hideMessageBox(load);
	                            	var msg = text.msg;
	                          	    nui.alert(msg, "系统提示", function(action){});
	                          	    grid.reload();
	                            }
	                		});
	                	}else if(code == "2001"){//禁用时有数据要提示
	                		var s = msg.split("|");
	                		nui.confirm(s[1], "确定是否禁用？",
	            		        function (action) {
	            		           if (action == "ok") {
	            		        	    var arr = s[0].split(",");
	            		        	    str += str +"并"+ text+"以下【"+s[0]+"】考核指标";
	            		        	    json = nui.encode({map:ids,start:TI_START,arr:arr,str:str,type:3});
		                           		load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^=");
		                           		$.ajax({
		                                       url:"com.gotop.xmzg.achieve.indicators.update_indicators_start.biz.ext",
		                                       type:'POST',
		                                       data:json,
		                                       cache: false,
		                                       contentType:'text/json',
		                                       success:function(text){
		                                       	nui.hideMessageBox(load);
		                                       	var msg = text.msg;
		                                     	    nui.alert(msg, "系统提示", function(action){});
		                                     	    grid.reload();
		                                       }
		                           		});
	            		           }
	        				});
	                	}else{
	                		nui.alert(msg, "系统提示", function(action){});
	                	}
	                }
	              });
			});
        }
    }
    
	
  //插入系统操作日志
    function insert_sysope_log(OPE_MOD,OPE_CONTENT){
		$.ajax({
				url: "com.gotop.xmzg.achieve.indicators.insert_sysope_log.biz.ext",
				type: 'POST',
				data : nui.encode({"OPE_MOD":OPE_MOD,"OPE_CONTENT":OPE_CONTENT}),
				cache: false,
				contentType:'text/json',
				success: function (res) {  
				}
		});
	}
	//重置
	function clean(){
		nui.get("btnEdit1").setValue("");
        nui.get("btnEdit1").setText("");
		nui.get("tip_code").setValue("");
		nui.get("ti_name").setValue("");
		nui.get("ti_remark").setValue("");
		nui.get("ti_start").setValue("0");
 	}
  </script>
</body>

</html>
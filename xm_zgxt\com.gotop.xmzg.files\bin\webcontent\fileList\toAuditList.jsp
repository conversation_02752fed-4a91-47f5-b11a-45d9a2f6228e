<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): lyl
  - Date: 2019-11-25
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<script type="text/javascript" src="<%=request.getContextPath() %>/files/process/js/swfupload/swfupload.js"></script>
<script type="text/javascript" src="<%=request.getContextPath() %>/files/process/js/swfupload/handlers.js"></script>

<title>待审核清单</title>
</head>
<body style="margin:0px;width:100%;overflow:hidden;" >
	<div class="search-condition">
		<div class="list">
			<div id="filesForm">
				<div class="nui-hidden"  name="condition.userOrgId" id="userOrgId"></div>
				<div class="nui-hidden"  name="condition.userId"  id="userId" ></div>
		   		<table class="table" style="width:100%;">
		      		<tr>
						<td align="right">档案种类：</td>
						<td>
							<input id="files_type" class="nui-dictcombobox nui-form-input" name="queryData.FILES_TYPE"  emptyText="请选择"
          					valueField="dictID" textField="dictName" dictTypeId="FILES_TYPE" showNullItem="true" nullItemText="请选择"/>
						</td>
						<td align="right">档案名称：</td>
						<td>
							<input name="queryData.FILES_NAME" class="nui-textbox" style="width:180px;"/>
						</td>
			        	<td align="right">客户/产品经理名称：</td>
						<td>
							<input name="queryData.EMPNAME"  class="nui-textbox" style="width:180px;"/>
						</td>
						<td rowspan="12" class="btn-wrap">
							<a class="nui-button" iconCls="icon-search" onclick="searchData();">查询</a>
							<a class="nui-button" iconCls="icon-reset"  onclick="clean()"/>重置</a>
						</td>
				
					</tr>
					<tr>
						<td align="right">起期区间：</td>
						<td>
							<input name="queryData.startTime" id="startTime" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:100px;"/>
							-
							<input name="queryData.startTime1" id="startTime1" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:100px;"/>
						</td>
					</tr>
		    	</table>
		  	</div>
		</div>
	</div>
	<div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      	<table style="width:100%;">
        	<tr>
           		<td style="width:100%;">
			     	<a class="nui-button" iconCls="icon-add" onclick="agree()" >同意</a>
					<a class="nui-button" iconCls="icon-edit" onclick="back();">退回</a> 
					<a class="nui-button" iconCls="icon-node" onclick="fileDetail();">档案明细</a>  
	             	<a class="nui-button" iconCls="icon-node" onclick="operateDetail">操作明细</a>   
           		</td>
        	</tr>
      	</table>
    </div>
    <!-- 档案主表信息 -->
  	<div class="nui-fit" id="fieldset1">
	 	<div id="grid"  dataField="resultList" idField="dicttypeid" class="nui-datagrid" style="width:100%;height:100%;" 
	 	allowCellWrap = "true"
	  	url="com.gotop.xmzg.files.fileList.queryAuditFileList.biz.ext" sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" >
	    	<div property="columns">
	    		<div type="checkcolumn"></div>
	    		<div field="INFORMATION_ID" class="nui-hidden" visible="false">ID</div>
		        <div field="FILES_TYPE_NAME" headerAlign="center" align="center" width="100px">档案种类</div>
		        <div field="FILES_NAME" headerAlign="center" align="center" width="150px">档案名称</div>
		        <div field="DEAL_NAME" headerAlign="center" align="center" width="100px">归属机构</div>
		        <div field="EMP_NAME" headerAlign="center" align="center" width="80px">客户经理</div>
		        <div field="START_TIME" headerAlign="center" align="center" width="100px">起期</div>
		        <div field="END_TIME" headerAlign="center" align="center" width="100px">止期</div>
		        <div field="CONTRACT_PRICE" headerAlign="center" align="center" width="100px">金额</div>
		        <div field="SUB_TIME" headerAlign="center" align="center" width="150px">提交时间</div>
		        <div field="AFFILIATED_NAMES" headerAlign="center" renderer="onActionRender" align="center" width="200px">附件</div>
		    </div>
	 	</div>
  	</div>
  	<!-- 新增附件窗口 -->
  	<!-- <div id="addAcsWindow" class="nui-window" title="新增附件" style="width:80%;height:60%;" 
	    >    
	   <div >
	    	<form id="acsForm" method="post" enctype="multipart/form-data">
		     <input class="nui-hidden" name="map.INFORMATION_ID" id="map.INFORMATION_ID"/>
			<table border="0" style="width:500px;height:100px;" align="center">
				<tr>
					<td>
						选择多文件
						<div id="i_select_files" >
						</div> 
						<div type="button" class="btn btn-default" id="i_select_files"><span class="glyphicon glyphicon-plus-sign">添加文件</span> </div>
						<input type="button" class="btn btn-default" id="i_select_files"  value="添加文件"/>
						回显进度
						<div id="i_stream_files_queue" ></div>
		        	</td>
	        	<tr>
	        	<tr align="center">
					<td>
						<a class="nui-button" onclick="startUp()">确认</a>&nbsp;
						<a class="nui-button" onclick="cancelUp()">取消</a>&nbsp;
		        	</td>
	        	<tr>
	        	</table>
	        </form>
	   </div>
	</div> -->
</body>
</html>
<script type="text/javascript">
	nui.parse();
	var form = new nui.Form("filesForm");
	var grid = nui.get("grid");
	/* var data = form.getData(true,true);
	grid.load(data); */
	var files=new Array();
	
	function searchData(){	
		var form = new nui.Form("filesForm");
		var data = form.getData(true,true);
    		grid.load(data);
    }
    
    //新增附件	
    function addAccessory(value){
   		var row = grid.getRow(value);
   		nui.open({
		    	url:"<%=request.getContextPath()%>/files/fileList/addAccessory.jsp",
	          	title:'新增附件',
	          	width:800,
          		height:400,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            //方法1：后台查询一次，获取信息回显
		            /* var data = row;
		            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
		            //方法2：直接从页面获取，不用去后台获取
		            var data={};
		            data = {pageType:"add",record:{map:row}};
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		          	 if(action=="saveSuccess"){
            	 		 searchData();
		             }
		        }
		    });
    }
    
    //审核同意
    function agree(){
    	var rows = grid.getSelecteds();
      	if(rows.length > 0){
	        nui.confirm("确定审核同意选中档案？","系统提示",function(action){
	        	if(action=="ok"){
		        	var spStatus = '3';//同意审核，审批状态变更为已审核
		      		for (var i=0;i<rows.length;i++){
			        		rows[i].SP_STATUS = spStatus;
		        		}
		            var json = nui.encode({upDatas:rows});
		           	var a= nui.loading("正在审核中,请稍等...","提示");
		           	var URL="com.gotop.xmzg.files.fileList.updateFileStatus.biz.ext";
			        	$.ajax({
			          		url:URL,
			          		type:'POST',
			          		data:json,
			          		cache: false,
			          		contentType:'text/json',
			          		success:function(text){
				          		nui.hideMessageBox(a);
				            	var returnJson = nui.decode(text);
								if(returnJson.flag == 1){
									nui.alert("审核通过", "系统提示", function(action){
										searchData();
									});
								}else{
									nui.alert("操作失败", "系统提示");
									grid.unmask();
								}
			          		}
			        	});
		     	}
			});
      	}else{
        	nui.alert("请至少选中一条记录！");
      	}
    }
    //审核退回
    function back(){
    	var rows = grid.getSelecteds();
      	if(rows.length > 0){
	        nui.confirm("确定退回选中档案？","系统提示",function(action){
	        	if(action=="ok"){
		        	var spStatus = '1';//审核退回，审批状态变更为待复核
		      		for (var i=0;i<rows.length;i++){
			        		rows[i].SP_STATUS = spStatus;
		        		}
		            var json = nui.encode({upDatas:rows});
		           	var a= nui.loading("正在审核中,请稍等...","提示");
		           	var URL="com.gotop.xmzg.files.fileList.updateFileStatus.biz.ext";
			        	$.ajax({
			          		url:URL,
			          		type:'POST',
			          		data:json,
			          		cache: false,
			          		contentType:'text/json',
			          		success:function(text){
				          		nui.hideMessageBox(a);
				            	var returnJson = nui.decode(text);
								if(returnJson.flag == 1){
									nui.alert("已退回档案，请重新复核", "系统提示", function(action){
										searchData();
									});
								}else{
									nui.alert("操作失败", "系统提示");
									grid.unmask();
								}
			          		}
			        	});
		     	}
			});
      	}else{
        	nui.alert("请至少选中一条记录！");
      	}
    }
   
    
    //重置查询信息
	function clean(){
	   	//不能用form.reset(),否则会把隐藏域信息清空
	   	form.reset();  
    }
    
    //机构树回显
     function OrgonButtonEdit(e){
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/files/archiveList/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            
                        }
                    }
                }
            });            
             
        }   
        
      //操作列：查看/新增附件
      function onActionRender(e){
      		var row  = grid.getRow(e.rowIndex);
      		var s =e.value;
      		var d = row.AFFILIATED_IDS;
      		 if(s==null){
	           	s="<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\"  onclick=\"addAccessory("+e.rowIndex+")\">"+"新增附件"+"</a>";
      		}else{
      			var names = s.split(",");
      			var ids = d.split(",");
      			var a = [];
      			for(var i=0;i<names.length;i++){
		           var s1=	"<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\"  onclick=\"downAccessory(\'"+ids[i]+"\',\'"+names[i]+"\')\">"+names[i]+"</a>";
		           a.push(s1);
	            }
	            s = a; 
      		} 
           	return s;
      }
        
    //档案明细
     function fileDetail(){
    	var rows = grid.getSelecteds();
    	var row = grid.getSelected();
    	var detail = '';
    	if(rows.length>1){
    		return nui.alert("请只选择一条记录进行明细查看！","提示");
    	}
       	if(row!=null){
       		var fy = row.FILES_TYPE;
       		//综合类档案
       		if(noun(fy)){
       			other(fy,row);
       		}else {     //基础类档案
       			oneToNine(row,fy);
       		}
       	}else{
        	nui.alert("请选中一条记录进行查看！","提示");
    	}
    }
    
     function oneToNine(row,fy){
		  nui.open({
		    	url:"<%=request.getContextPath()%>/files/fileList/fileDetail.jsp",
	          	title:'档案详情',
	          	width:1150,
          		height:300,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            //方法1：后台查询一次，获取信息回显
		            /* var data = row;
		            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
		            //方法2：直接从页面获取，不用去后台获取
		            var filesType=row.FILES_TYPE;
		            var data={};
		            if(filesType == '01'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '02'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '03'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '04'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '05'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '06'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '09'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else{
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		        	
		        }
		    });
    }
    
     function other(fy,row){
		if(fy == '15'){
			//信审档案详情界面
			path = "<%=request.getContextPath()%>/files/fileList/xsDetail.jsp";
		}else {
			//综合类档案的其它档案详情界面
			var path = "<%=request.getContextPath()%>/files/fileList/otherDetail.jsp";
		}
		
	    nui.open({
	    	url:path,
          	title:'编辑',
          	width:1150,
      		height:300,
          	onload:function(){
	        	var iframe = this.getIFrameEl();
	            //方法1：后台查询一次，获取信息回显
	            /* var data = row;
	            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
	            //方法2：直接从页面获取，不用去后台获取
	            var filesType=row.FILES_TYPE;
	            var data = {pageType:'edit',filesType:filesType,record:{editData:row}};
            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	        },
	        ondestroy:function(action){
	        	
	        }
	    });
    }
    
    
   /*  //根据档案种类获取详情表名
	function getDetailTableName(filesType){
		if(filesType == '01'){
				return 'T_FILES_RETAIL_CREDIT';
			}else if(filesType == '02'){
				return 'T_FILES_RETAIL_DISBURSE';
			}else if(filesType == '03'){
				return 'T_FILES_PERSON_CREDIT';
			}else if(filesType == '04'){
				return 'T_FILES_PERSON_DISBURSE';
			}else if(filesType == '05'){
				return 'T_FILES_BILL_HONOUR';
			}else if(filesType == '06'){
				return 'T_FILES_BILL_DISCOUNT';
			}else if(filesType == '07'){
				return 'T_FILES_COOPERATE_ORG';
			}else if(filesType == '08'){
				return 'T_FILES_REFUSE_LOAN';
			}else if(filesType == '09'){
				return 'T_FILES_CREDIT';
			}
	} */
	
	
	//操作明细
   function operateDetail(){
       var row = grid.getSelected();
       var rows = grid.getSelecteds();
       if(rows.length>1){
    		return nui.alert("请只选择一条记录进行查看！","提示");
    	}
       if(row!=null){
       var INFORMATION_ID=row.INFORMATION_ID;
	       nui.open({
	          url:"<%=request.getContextPath() %>/files/fileList/operationLog.jsp?infoId="+INFORMATION_ID,
	          title:'档案操作流水',
	          width:1200,
	          height:500,
	          onload:function(){
	             /* var iframe = this.getIFrameEl();
	               var data = {pageType:"edit",record:{map:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法 */
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	            	 searchData();
	             }
	          }
	       });
       }else{
           nui.alert("请选中一条记录！");
       }
    }
    
	//下载附件
	function downAccessory(id,name){
       	var fileId = id;
       	var fileName = name;
       	var url="<%=request.getContextPath()%>/files/fileList/downloadFile.jsp?fileId="+fileId+"&fileName="+fileName;
		window.location.replace(url);
	  }
	  
    function noun(fy){
		if(fy == '10'){
			return true;
		}else if(fy == '11'){
			return true;
		}else if(fy == '12'){
			return true;
		}else if(fy == '13'){
			return true;
		}else if(fy == '14'){
			return true;
		}else if(fy == '15'){
			return true;
		}else {
			return false;
		}
	}
</script>
<%@page pageEncoding="UTF-8"%>	
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2019-06-04 14:51:16
  - Description:
-->
<head>
	<title>业绩基数修改</title>
    <%@include file="/coframe/tools/skins/common.jsp" %>
    <%@include file="/achieve/init.jsp"%>
	<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
    </style>
</head>
<body>
  <div id="prameter" style="padding-top:5px;"> 
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table" align="center"> 
    <tbody>
     <tr> 
     	<th class="nui-form-label" style="width:20%"><font class="b_red">*</font>考核方案：</th> 
      <td style="width:30%">
      	<input class="nui-textbox" id="ta_name" name="ta_name" style="width:200px;"/>
      	<input class="nui-hidden" id="ta_id" name="ta_id"/>
      </td>
      <th class="nui-form-label" ><font class="b_red">*</font>业务条线：</th> 
      <td >
		<input class="nui-textbox" id="tip_name" name="tip_name" style="width:200px;"/>
      	<input class="nui-hidden" id="tip_code" name="tip_code"/>
      </td>
      
     </tr>
     <tr>
     <th class="nui-form-label" style="width:20%"><font class="b_red">*</font>指标代码：</th> 
      <td style="width:30%">
		<input class="nui-textbox" id="ti_name" name="ti_name" style="width:200px;"/>
      	<input class="nui-hidden" id="ti_code" name="ti_code"/>
      </td>
     <th class="nui-form-label"><font class="b_red">*</font>指标明细代码：</th>
      <td>
		<input class="nui-textbox" id="tid_name" name="tid_name"  style="width:200px;"/>
      	<input class="nui-hidden" id="tid_code" name="tid_code"/>
      </td>
     </tr>
     <tr> 
      <th class="nui-form-label"><font class="b_red">*</font>调整类型：</th> 
      <td>
      	<input class="nui-dictcombobox" id="tba_type" valueField="dictID" textField="dictName" 
      		style="width:200px;" onvaluechanged="onTbaTypeChanged"
			dictTypeId="JF_TZLX" name="tba_type"  required="true"/>
      </td> 
      <th class="nui-form-label txt"><font class="empcode b_red">*</font>客户经理：</th> 
      <td>
      	<div class="empcode">
      		<input class="nui-textbox" id="tba_empname" name="tba_empname" style="width:200px;"/>
      		<input class="nui-hidden" id="tba_empcode" name="tba_empcode"/>
      	</div>
      	<div class="orgcode">
      		<input class="nui-textbox" id="tba_orgname" name="tba_orgname" style="width:200px;"/>
      		<input class="nui-hidden" id="tba_orgcode" name="tba_orgcode"/>
      	</div>
      	<div class="userno">
      		<input class="nui-textbox" id="tba_userno" name="tba_userno" style="width:200px;" required="true"/>
      	</div>
      </td> 
     </tr>
     <tr> 
      <th class="nui-form-label">开始日期：</th> 
      <td>
      	<input id="tba_time_start" name = "tba_time_start" class="nui-datepicker" style="width:200px;" allowInput="false"/>
      </td>
      <th class="nui-form-label">结束日期：</th> 
      <td>
      	<input id="tba_time_end" name = "tba_time_end" class="nui-datepicker"  style="width:200px;"  allowInput="false"/>
      </td> 
     </tr>
     <tr> 
     <th class="nui-form-label">调整基数：</th> 
      <td colspan="3">
      	<input class="nui-spinner" id="tba_base" name="tba_base" style="width:200px;" minValue="0" maxValue="99999999" format="#,0.00"/>
      	<input class="nui-hidden" id="tba_id" name="tba_id"/>
      </td> 
     </tr>
     <th class="nui-form-label">调整原因：</th> 
      <td colspan="3">
      	<input class="nui-textarea" id="tba_remark" name="tba_remark" style="width:600px;"/>
      </td> 
     </tr>
    </tbody>
   </table> 
   <div class="nui-toolbar" style="padding:0px;" borderstyle="border:0;"> 
    <table width="100%"> 
     <tbody>
      <tr> 
       	<td style="text-align:center;"> 
	       	<a class="nui-button" iconcls="icon-save" onclick="seve">提交</a> 
	       	<span style="display:inline-block;width:25px;"></span> 
	       	<a class="nui-button" iconcls="icon-cancel" onclick="onCancel">取消</a>
       	</td> 
      </tr> 
     </tbody>
    </table> 
   </div> 
  </div>


  <script type="text/javascript">
  	nui.parse();
  	function setData(data){
  		//跨页面传递的数据对象，克隆后才可以安全使用
        data = nui.clone(data);
        data = lowerJson(data);
        var form = new nui.Form("#prameter");
        form.setData(data);
        nui.get("tip_name").disable();
        nui.get("ti_name").disable();
        nui.get("tid_name").disable();
        nui.get("ta_name").disable();
        nui.get("tba_type").disable();
        nui.get("tba_empname").disable();
        nui.get("tba_orgname").disable();
        nui.get("tba_userno").disable();
        onTbaTypeChanged();
  	}
  	function onTbaTypeChanged(){
    	var obj = nui.get("tba_type").getValue();
    	if(obj == "0"){
    		$(".userno").show();
    		$(".empcode,.orgcode").hide();
    		$(".txt").html("<font class=\"b_red\">*</font>产品编号：");
    	}else if(obj == "1"){
    		$(".empcode").show();
    		$(".userno,.orgcode").hide();
    		$(".txt").html("<font class=\"b_red\">*</font>客户经理：");
    	}else if(obj == "2"){
    		$(".orgcode").show();
    		$(".userno,.empcode").hide();
    		$(".txt").html("<font class=\"b_red\">*</font>机构号：");
    	}
    }
  	
  	
  	//获取指标
  	function onTipCodeChanged(){
  		var json = nui.encode({tip_code:nui.get("tip_code").getValue(),type:2});
	   	//提交数据
        nui.ajax({
           url: "com.gotop.xmzg.achieve.indicators.indicators_choice.biz.ext",
           type: "post",
           data: json,
           contentType:'text/json',
           success: function (text) {
        	  var obj = text.list;
              nui.get("ti_code").setData(obj);
           }
       });
  	}
  	//获取指标明细
  	function onTiCodeChanged(){
  		var json = nui.encode({ti_code:nui.get("ti_code").getValue()});
	   	//提交数据
        nui.ajax({
           url: "com.gotop.xmzg.achieve.indicators.indicators_detail_choice.biz.ext",
           type: "post",
           data: json,
           contentType:'text/json',
           success: function (text) {
        	  var obj = text.list;
              nui.get("tid_code").setData(obj);
           }
       });
  	}
  	
  	//获取考核方案
  	function onTidCodeChanged(){
  		var json = nui.encode({tid_code:nui.get("tid_code").getValue()});
	   	//提交数据
        nui.ajax({
           url: "com.gotop.xmzg.achieve.base.base_adjust_choice.biz.ext",
           type: "post",
           data: json,
           contentType:'text/json',
           success: function (text) {
        	  var obj = text.list;
              nui.get("ta_id").setData(obj);
           }
       });
  	}
  	
  	function seve(){
  		var form = new nui.Form("#prameter");
		form.validate();
		if (form.isValid() == false) return;
		var startDate = nui.get("tba_time_start").getFormValue();
    	var endDate = nui.get("tba_time_end").getFormValue();
		if(!isNullOrEmpty(startDate) && !isNullOrEmpty(endDate)){
			if(startDate>endDate){
	   			nui.alert("结束日期必须大于开始日期");
	   			return;
	       	}
		}
		var num = nui.get("tba_base").getValue();
		if(num == "0"){
			nui.alert("调整基数不能为0");
			return;
		}
		var load= nui.loading("正在数据处理请稍侯...","温馨提示 =^_^="); //显示遮罩层
		var tba_type = form.getData().tba_type;
		var tba_empcode = form.getData().tba_empcode;
		var tba_orgcode = form.getData().tba_orgcode;
		var tba_userno = form.getData().tba_userno;
		var str = "";
		if(tba_type==0){
			str="修改【0-产品 - "+tba_userno+"】业绩基数";
		}else if(tba_type==1){
			str="修改【1-客户经理 - "+tba_empcode+"】业绩基数";
		}else if(tba_type==2){
			str="修改【2-机构 - "+tba_orgcode+"】业绩基数";
		}
		//提交数据
        nui.ajax({
       		url: "com.gotop.xmzg.achieve.base.base_adjust_update.biz.ext",
           	type: "post",
           	data: nui.encode({"obj":form.getData(true,true),"str":str}),
           	contentType:'text/json',
           	success: function (text) {
        	   nui.hideMessageBox(load);  //隐藏遮罩层
        	   var code = text.code;
        	   var msg = text.msg;
        	   if(code != "1"){
        		  	nui.alert(msg, "系统提示");
	           }else{
          			nui.alert(msg, "系统提示", function(action){
						if(action == "ok" || action == "close"){
							CloseWindow("saveSuccess");
						}
					});
	          }
           }
       });
  	}
  	
  	
  	
  	
  	//机构树回显
    function OrgonButtonEdit(e) {
       var btnEdit = this;
       nui.open({
           url:bactpath+"/achieve/common/org_tree.jsp",
           showMaxButton: false,
           title: "选择树",
           width: 350,
           height: 350,
           ondestroy: function (action) {                    
               if (action == "ok") {
                   var iframe = this.getIFrameEl();
                   var data = iframe.contentWindow.GetData();
                   data = nui.clone(data);
                   if (data) {
                       btnEdit.setValue(data.ORGCODE);
                       btnEdit.setText(data.TEXT);
                       nui.get("tba_orgcode").setValue(data.ORGCODE);
                   }
               }
           }
       });
     }
  	
  	//人员树回显
    function selectEmp(e){
    	var ele = e.sender.id;
		var emp = nui.get(ele);
        nui.open({
            url:bactpath+"/files/archiveList/empTree.jsp?type=emp",
            title: "选择人员",
            width: 600,
            height: 400,
            onload:function(){
            	var frame = this.getIFrameEl();
            	var data = {};
            	data.value = emp.getValue();
            	//frame.contentWindow.setData(data);
            	frame.contentWindow.setTreeCheck(data.value);
            },
            ondestroy: function (action) {
                //if (action == "close") return false;
                if (action == "ok") {
                    var iframe = this.getIFrameEl();
                    var data = iframe.contentWindow.getData();
                    data = nui.clone(data);
                    //debugger;    //必须
                    if (data) {
                        emp.setValue(data.nodeId);
                        emp.setText(data.nodeName);
                        //将值人员id转换成人员code
                        var data={empId:data.nodeId};
				        var json = nui.encode(data);
				        var URL="com.gotop.xmzg.files.fileLedger.getEmpcode.biz.ext";
				        $.ajax({
				        	url: URL,
				            type: 'POST',
				            data: json,
				            async:false,
				            cache: false,
				            contentType:'text/json',
				            success: function (text){
				                var returnJson = nui.decode(text);
				                var result = returnJson.resultList;
				                emp.setValue(result[0].EMPCODE);
						    }
				  		});
                    }
                }

            }
        });
	}  
  	
  </script>
</body>
</html>
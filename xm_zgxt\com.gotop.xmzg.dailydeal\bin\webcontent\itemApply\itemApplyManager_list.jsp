<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-05-23 12:01:11
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>单册请领查询</title>
</head>
<body style="width:100%;overflow:hidden;">
 
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <table class="table" style="width:100%;">
	      <tr >
	            <th class="tit">机构名称：</th>
				<td>
					<input id="btnEdit1" name = "queryData.orgid"  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:200px;"/>
				</td>
		        <th class="tit">请领日期：</th>	
				<td> 
					<input id="queryData.startDate" class="nui-datepicker" name="queryData.apply_time1"  style="width:120px;" allowInput="false"/>
	                 ~
	                <input id="queryData.endDate" class="nui-datepicker" name="queryData.apply_time2"  style="width:120px;" allowInput="false" onvalidation="comparedate"/>
				</td>
				<th class="tit">单册代码：</th>
				<td>
					<input id="item_no" name="queryData.item_no" class="nui-textbox" style="width:150px;" vtype="maxLength:250"/>
				</td>	
		</tr>
		<tr>
		     <th class="tit">单册名称：</th>
				<td>
					<input id="item_name" name="queryData.item_name" class="nui-textbox" style="width:150px;" vtype="maxLength:10"/>
				</td>
		    <th class="tit">状态：</th>
				<td>
				    <!--<input class="nui-combobox" name="queryData.APPROVAL_STATUS" id="res" data="Opinions" style="width:200px;" value="4"
		                   emptyText="请选择"  nullItemText="请选择" showNullItem="true"/>-->
		            <input class="nui-combobox"emptyText="全部"id="res" data="Opinions" name="queryData.APPROVAL_STATUS" showNullItem="true" nullItemText="全部" 
	          	      style="width:200px;"/>		   					
				</td>
		     <td>		  
	            <a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;&nbsp;
	            <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
	         </td>
	        </tr>
	    </table>
	  </div>
    </div>
  </div>
  
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.dailydeal.itemApply.Query_itemApplys.biz.ext"
	  sizeList=[5,10,20,50,100]  pageSize="10">
	    <div property="columns" >
	      <!--<div type="checkcolumn"></div>-->
	      <div field="APPLY_TIME" headerAlign="center" align="center">请领日期</div>
	      <div field="APPROVAL_STATUSNAME" headerAlign="center" align="center">审批状态</div>
	      <div field="ORGCODE" headerAlign="center" align="center">请领机构号</div>
	      <div field="ORGNAME" headerAlign="center" align="center" width=135>请领机构名称</div>
	      <div field="ITEM_NO" headerAlign="center" align="center">单册代码</div>
	      <div field="NO" headerAlign="center" align="center">编号</div>
	      <div field="ITEM_NAME" headerAlign="center" align="center" width=160>单册名称</div>
	      <div field="UNIT" headerAlign="center" align="center">单位</div>
	     <!-- <div field="PRE_TAX_PRICE" headerAlign="center" align="center">税前单价</div>
	      <div field="TAX_PRICE" headerAlign="center" align="center">含税单价</div>-->
	      <div field="APPLY_NUM" headerAlign="center" align="center">请领数量</div> 
	      <div field="REAL_NUM" headerAlign="center" align="center">实发数量</div>
	      <!--<div field="ITEM_ADDRESS" headerAlign="center" align="center">单册出处</div>-->
	      <!--<div field="EMPNAME" headerAlign="center" align="center">请领人</div>-->
	      <div field="OPERATOR_DATE" headerAlign="center" align="center" width=135>申请时间</div>	  
		  <div field="APPLY_RESULTNAME" headerAlign="center" align="center">审批结果</div>
		  <div field="APPROVAL_EMPNAME" headerAlign="center" align="center">请领授权人</div>
		  <div field="APPLY_APPROVAL_DATE" headerAlign="center" align="center" width=130>审批时间</div>  
		  <div field="REMARK" headerAlign="center" align="center">备注</div>  
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">
    nui.parse();
    
    var Opinions = [{ id: "4", text: '已下发' }, { id: "2", text: '待下发'},{ id: "5", text: '下发已收到'},
    { id: "1", text: '请领待审批'},{ id: "3", text: '请领审批拒绝'},{ id: "6", text: '请领拒绝'},{ id: "7", text: '下发待审批'}];
    nui.get("res").load(Opinions);
    
    var grid = nui.get("datagrid1");    
    //grid.load();
    var a;
    
    //机构树回显
     function OrgonButtonEdit(e) {
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/dailydeal/itemGrant/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            
                        }
                    }
                }
            });            
             
        }    
    
    
    function search(){

       var form = new nui.Form("#form1");
            form.validate();
            if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
    
   function add(){
       nui.open({
          url:"<%=request.getContextPath() %>/dailydeal/itemApply/itemApply_add.jsp",
          title:'新增',
          width:450,
          height:400,
          onload:function(){
          },
          ondestroy:function(action){
             if(action=="saveSuccess"){
	            grid.reload();
	         }
          }
       });
    }
    
    function update(){
       var row = grid.getSelected();
       if(row!=null){
	       nui.open({
	          url:"<%=request.getContextPath() %>/storeManage/storeManage/storeManage_update.jsp",
	          title:'编辑',
	          width:400,
	          height:250,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             
	             //方法1：后台查询一次，获取信息回显
	             /* var data = row;
	             iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
	             
	             //方法2：直接从页面获取，不用去后台获取
	               var data = {pageType:"edit",record:{map:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }else{
           nui.alert("请选中一条记录！");
       }
    }
    
 
  /*  
    function selectionChanged(){
       var rows = grid.getSelecteds();
       if(rows.length>1){
           nui.get("update").disable();
       }else{
           nui.get("update").enable();
       }
    }*/
    
    
    function remove(){
      var rows = grid.getSelecteds();
      if(rows.length > 0){
        var json = nui.encode({maps:rows});
        var a= nui.loading("正在检测删除的数据是否有【在用】状态的，请稍等...","提示");
          if(checkisExist(json)!= 0)
	        {
	         nui.hideMessageBox(a);
	         nui.alert("删除失败，只能删除【停用】状态的数据！");
	         return false;
	        }
	     nui.hideMessageBox(a);   
	     
         nui.confirm("确定删除选中记录？","系统提示",
           function(action){
             if(action=="ok"){
	            var json = nui.encode({maps:rows});
	           var a= nui.loading("正在删除中,请稍等...","提示");
		        $.ajax({
		          url:"com.gotop.xmzg.storeManage.sroreManage.delete_storeManage.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	nui.hideMessageBox(a);
		            var returnJson = nui.decode(text);
					if(returnJson.exception == null){
						nui.alert("删除成功", "系统提示", function(action){
							grid.reload();
						});
					}else{
						nui.alert("删除失败", "系统提示");
						grid.unmask();
					}
					
		          }
		        });
		     }
		   });
      }else{
        nui.alert("请选中一条记录！");
      }
    }
   
   
    function statustype(e){
      	    return nui.getDictText("STORE_STATUS", e.row.STATUS);
    }
    
    //判断要删除的数据是否有【在用】状态的
    function checkisExist(map){
      var vala;
      $.ajax({
        url:"com.gotop.xmzg.storeManage.sroreManage.checkIsExit.biz.ext",
        type: 'POST',
        data:map,
        cache: false,
        async:false,
        contentType:'text/json',
        success:function(text){
          vala = text.a;
          str=text.str;
        }
      });
      return vala;
    }
    
    //重置
    function clean(){
	   //不能用form.reset(),否则会把隐藏域信息清空
	   nui.get("btnEdit1").setValue("");
       nui.get("btnEdit1").setText("");
       nui.get("queryData.startDate").setValue("");
       nui.get("queryData.endDate").setValue("");
       nui.get("item_no").setValue("");
       nui.get("item_name").setValue("");
       nui.get("res").setValue("");
     }
     
   
       //时间判断开始时间不能大于结束时间
      function comparedate(e){
      var startDate = nui.get("queryData.startDate").getFormValue();
      var endDate = nui.get("queryData.endDate").getFormValue();
      if(startDate!="")
	    startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
	  if(endDate!=""){
		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
        if(e.isValid){
          if(startDate>endDate){
            e.errorText="结束日期必须大于或等于开始日期";
            e.isValid=false;
          }else
          {
          e.errorText="";
          e.isValid=true;
          }
        }
      }
    } 
     
     
     
    
  </script>
</body>
</html>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- $Header: svn://**************:7029/svn/xm_project/xm_zgxt/com.gotop.fileManger.jw/src/webcontent/filegl_authority.jsp 1463 2018-08-14 01:33:12Z jw $ -->
<!-- 
  - Author(s): JW
  - Date: 2018-07-25 11:08:29
  -   
-->
<head>
<title>文件权限修改</title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<%@include file="/coframe/tools/skins/common.jsp"%>
<script src="<%=request.getContextPath()%>/common/nui/nui.js" type="text/javascript"></script>
<style type="text/css">
html,body {
	padding: 0;
	margin: 0;
	border: 0;
	height: 100%;
	overflow-y:auto;
}

.mini-textarea {
	width: 100%;
}

.mini-popup .mini-shadow {
	height: 150px;
}

.mini-textboxlist {
	width: 100%;
	height: 40px;
}
.mini-radiobuttonlist-table .mini-radiobuttonlist-td{
	width: 50px;
	padding: 5px;
	border: 1px solid #ccc;
	border-radius:5px;
	background-color: #ccc;
}

.mini-radiobuttonlist-item{
	text-align: center;
}
.mini-radiobuttonlist-item label {
	font-size: 15px;
}
</style>
</head>
<body>
	<fieldset style="border: solid 1px #aaa; padding: 3px;">
		<legend>权限信息</legend>
		<div style="padding: 5px;">
					<table border="0" cellpadding="1" cellspacing="2" align="center" style="width: 100%">
				<tr>
					<td colspan="2" align="center">
					<div id="radio_type" class="mini-checkboxlist" repeatItems="4" repeatLayout="table"
			        textField="DICTNAME" valueField="DICTID" dataField="msg.dict" value="0" onvaluechanged="onValuechanged"
			        url="com.gotop.fileManger.jw.action.filegl.ditcfind.biz.ext?dicttypeid=FILE_JURISDICTION_TYPE" >
			    </div> 
		
					</td>
				</tr>
				<tr>
					<td style="width: 15%; text-align: right;">已分配人员：</td>
					<td style="width: 70%">
						<input id="empBox" class="mini-textboxlist box" name="check" textName="tblName" required="true" valueField="id" textField="text" /> 
					</td>
					<td style="width: 15%">
		            	<a value="emp" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
		            </td>
				</tr>
				<tr>
					<td style="width: 15%; text-align: right;">已分配岗位：</td>
					<td style="width: 70%" id="down">
						<input id="roleBox" class="mini-textboxlist box" name="role" textName="tblName" required="true" valueField="id" textField="text" /> 
					</td>
					<td style="width: 15%">
		            	<a value="role" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
		            </td>
				</tr>
				<tr>
					<td style="width: 15%; text-align: right;">已分配机构：</td>
					<td style="width: 70%">
						<input id="orgBox" class="mini-textboxlist box" name="emp" textName="tblName" required="true" valueField="id" textField="text" /> 
					</td>
					<td style="width: 70%">
		            	<a value="org" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
		            </td>
				</tr>
				<tr>
					<td style="width: 15%; text-align: right;">已分配群主：</td>
					<td style="width: 70%">
						<input id="groupBox" class="mini-textboxlist box" name="emp" textName="tblName" required="true" valueField="id" textField="text" /> 
						</td >
					<td style="width: 15%">
		            	<a value="group" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
		            </td>
				</tr>
			</table>
		</div>
	</fieldset>
	<div style="text-align: center; padding: 10px;">
		<a class="mini-button" onclick="onOk()" style="width: 60px; margin-right: 20px;">确定</a> 
		<a class="mini-button" onclick="onCancel()" style="width: 60px;">取消</a>
	</div>
	<script type="text/javascript">
		nui.parse();
		
		/* 0（预览） 1（修改） 2（删除） 3（下载）  */
		var path="<%= request.getContextPath() %>";
		var powerBox = nui.get("radio_type");
		var map =["emp", "role", "org", "group"];
		var fileId,oldType,oldData = [];
		var powerObj = {emp: nui.get("empBox"),role: nui.get("roleBox"),
					    org: nui.get("orgBox"),group: nui.get("groupBox")};
		
		//标准方法接口定义
        function SetData(data){
        	fileId = nui.clone(data);
        	$.ajax({
	    		url:"com.gotop.fileManger.jw.action.filegl.queryFilePermissionById.biz.ext",
	    		type:"post",
	    		data:{fileId:fileId},
	    		success:function(data){
	    			//console.log(data);
					if (data.powerMap != null) {
						var powerType = data.powerMap.powerType;
						var powerIds = data.powerMap.powerIds;
						var powerNames = data.powerMap.powerNames;
						oldType = powerType;
		
		        		for (var i=0; i<4; i ++ ) {
		        			var ids = strSplitLast(powerIds[i]);
		        			oldData.push(ids);
		        			powerObj[map[i]].setValue(ids);
		        			powerObj[map[i]].setText(strSplitLast(powerNames[i]));
		        		}	
		    			powerBox.setValue(powerType);
		    			onValuechanged();
					} else{
						oldType = "0";
						/* nui.showTips({
				            content: "你还未配置权限",
				            state: "danger",
				            x: "center",
				            y: "top",
				            timeout: 2000
				        }); */
					}
	    		}
	    	});
		}
		// 
		/* 保存数据  */
   		function saveData(){
	   		var json = getJson(fileId);
	      	$.ajax({
	        	url:"com.gotop.fileManger.jw.action.filegl.updateFilePowerInfo.biz.ext",
	        	type:'POST',
	            data:json,
	            traditional: true,
	            success:function(data){
	 	        	if(data.msg=="success"){
	 	        		nui.alert("保存成功", "系统提示", function(action){
	                         if(action == "ok" || action == "close"){
	                             CloseWindow("ok");
	                   		}
	                    });
	            	}else{
	             		nui.alert("保存失败", "系统提示", function(action){
	                         if(action == "ok" || action == "close"){
	                   		}
	                    });
	             	} 
	             }
	         });   
	     }
	     
	     function getJson(fileId) {
	     	var changeType,newData = [];
	   		var newType = powerBox.getValue();
	        for (var i=0; i < 4; i++) {
	        	newData[i] = nui.get(map[i]+"Box").getValue();
	        }
	        
	        if (oldType == newType) changeType = "notChange&" + newType;
	        else changeType = newType;
	        
			var data = {fileId:fileId,
						oldData:oldData,
						newData:newData,
						changeType:changeType};
			return data;
	     }
		/* 弹出权限设置界面  */
		function onClick(e) {
			var powerType = powerBox.getValue();
			var boxType = e.sender.defaultValue;
			nui.open({
	        	url: path + "/hyq/list_powertree.jsp?type="+boxType,
	        	title: "权限配置",
	            iconCls: "icon-edit", 
	            width: 350, 
	            height: 350,
	            onload: function () {
	                var iframe = this.getIFrameEl(); 
	                // 给树设置上已选择的节点字符串    
	                var boxObj = nui.get(boxType+"Box"); 
	              	iframe.contentWindow.setTreeCheck(boxObj.getValue());
	            },
	            ondestroy: function (action) {
					if (action == "ok") {
	                    var iframe = this.getIFrameEl();
	                    var list = iframe.contentWindow.GetData();
	                    list = mini.clone(list);    
	                    if (list) {
	                    	// 将树选中节点设置到box显示
	                    	putDataTextBox(boxType, list, "nodeId", "nodeName");
	                    }
	                }
	            }
	        });
		}
				/**
		 * 往textboxlist中添加选择的数据
		 * @params boxType	根据点击按钮的类型 添加到不同box里面
		 * @params list 	 获取Check选中的节点集合
		 */
		function putDataTextBox(boxType, list){
			var text = "",value = "";
			var isEmp = (boxType == "emp");
			var boxObj = nui.get(boxType + "Box");
			for (var i = 0; i < list.length; i++) {
				var node = list[i];
				//console.log(isEmp+":"+node.nodeType);
				if (node.nodeType == "OrgOrganization" && isEmp) continue;
				if (i == list.length -1) {
					value += node["nodeId"];
					text  += node["nodeName"];
				} else {
					value += node["nodeId"] + ",";
					text  += node["nodeName"] + ",";
				}
			}
			if (value.endsWith(",")) value = strSplitLast(value);
			if (text.endsWith(",")) text = strSplitLast(text);
			boxObj.setValue(value);
			boxObj.setText(text);
		}
					
		/* 确保至少有一种权限，以及选中修改 查看必须同时选中且无法修改 */
	    function onValuechanged() {
	
	    	var checkValue = powerBox.getValue();
	    	if (checkValue != "") {
	    		var values = checkValue.split(",");
		    	var ifHaveUpdate = false;
		    	for (var i=0; i<values.length; i++) {
		    		var value = values[i];
		    		if (value==1 || value==2 || value==3) ifHaveUpdate = true;
		    	}
		    	if (ifHaveUpdate) {
		    		powerBox.setValue("0,"+checkValue);
	    			var boxs = $(".mini-checkboxlist-table").find("input");
					$(boxs[0]).attr("disabled", true);
		    	} else {
		    		var boxs = $(".mini-checkboxlist-table").find("input");
					$(boxs[0]).attr("disabled", false);
		    	}
	    	} else {
	    		powerBox.setValue("0");
	    		alert("请至少选择一种权限！");
	    	}	
	    }
    
		//判断当前字符串是否以str结束
	     if (typeof String.prototype.endsWith != 'function') {
	       String.prototype.endsWith = function (str){
	          return this.slice(-str.length) == str;
	       };
	     }
	    /* 切割ID字符串最后一位 */
	    function strSplitLast(obj) {
	    	return (obj).substring(0, obj.length-1);
	    }
		function CloseWindow(action) {
	        if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
	        else window.close();
	    }
	   	/* 确定保存或更新 */
	    function onOk() {
	        saveData();
	    }
	    /* 取消 */
	    function onCancel() {
	        CloseWindow("cancel");
	    }		
	</script>
</body>
</html>
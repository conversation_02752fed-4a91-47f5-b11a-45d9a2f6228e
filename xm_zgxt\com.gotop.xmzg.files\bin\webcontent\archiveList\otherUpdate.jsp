<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%@page import="com.eos.data.datacontext.UserObject" %>
<%@include file="/coframe/dict/common.jsp"%>
<html>
<!-- 
  - Author(s): lzd
  - Date: 2018-06-04 09:36:10
  - Description:
-->
<head>
</head>
<%
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>
<body>
	<div id="interBusiForm" style="padding-top:5px;">
		<input class="nui-hidden" name="editData.INFORMATION_ID"/>
		<input class="nui-hidden" name="editData.IMP_EMPID"/>
		<table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
			<div class="nui-hidden"  name="editData.orgId" value="<%=userObject.getUserOrgId()%>" ></div>
				<tr>	        
		        <td style="text-align:left;" colspan="26">
			    	<fieldset style="border: solid 1px #aaa; position: relative;">
						<legend style="text-align:left;">
							<label style="text-align:left;"></label>
						</legend>
						<table style="width:99%;height:100%;table-layout:fixed;" class="nui-form-table">
							<tr>
					<th class="nui-form-label"><label for="type$text">档案种类：</label></th>
					<td colspan="3">
						<input id="files_type" class="nui-dictcombobox" name="editData.FILES_TYPE"  emptyText="请选择"
	  					valueField="dictID" textField="dictName" dictTypeId="FILES_TYPE" readonly = "true" showNullItem="true" nullItemText="请选择" onvaluechanged="onFilesTypeChanged" style="width:150px;"/>
	  				</td>
		      		<th class="nui-form-label"><label for="type$text">原档案名称：</label></th>
		      		<td colspan="3">
		      			<input name="editData.BUSINESS_TYPE" id="BUSINESS_TYPE" readonly = "true" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
		      		</td>
		      		<th class="nui-form-label"><label for="type$text">区支行：</label></th>
	  				<td colspan="3">
	  					<input id="SUB_ORG" name = "editData.SUB_ORG"  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;"/>
		      		</td>
		      		<th class="nui-form-label"><label for="type$text">经办机构：</label></th>
	  				<td colspan="3">
	  					<input id="DEAL_ORG" name = "editData.DEAL_ORG"  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;"/>
		      		</td>
		      	</tr>
		      	<tr>
		      		
		      		<th class="nui-form-label"><label for="type$text">管户信贷员：</label></th>
	  				<td colspan="3">
	  					<input id="EMPNAME" name = "editData.EMPNAME"  class="nui-buttonedit"  allowInput="false" onbuttonclick="selectEmp" style="width:150px;"/>
		      		</td>
		      		<th class="nui-form-label"><label for="type$text">客户名称：</label></th>
		      		<td colspan="3">
		      			<input name="editData.CUSTOMER_NAME" id="CUSTOMER_NAME" required="true" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
		      		</td>
		      		<th class="nui-form-label"><label for="type$text">客户号码：</label></th>
		      		<td colspan="3">
		      			<input name="editData.CUSTOMER_NO" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
		      		</td>
		      		<th class="nui-form-label"><label for="type$text">存放地址：</label></th>
		      		<td colspan="3">
		      			<input id="STORAGE_ADDRESS" class="nui-dictcombobox" name="editData.STORAGE_ADDRESS"  emptyText="请选择"
	  					valueField="dictID" textField="dictName" readOnly="true" dictTypeId="FILES_STORAGE_ADDRESS" showNullItem="true" nullItemText="请选择" style="width:150px;"/>
		      		</td>
		      	</tr>
		      	<tr>
		      		
		      		<th class="nui-form-label"><label for="type$text">货架号/箱号：</label></th>
		      		<td colspan="3">
		      			<input name="editData.STORAGE_LOCATION" readOnly="true" id="STORAGE_LOCATION" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
		      		</td>
		      		<th class="nui-form-label"><label for="type$text">档案盒号：</label></th>
		      		<td colspan="3">
		      			<input name="editData.BOX_NUM" readOnly="true" id="BOX_NUM" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
		      		</td>
		      		<th class="nui-form-label"><label for="type$text">编号：</label></th>
		      		<td colspan="3">
		      			<input name="editData.CONTRACT_NUMBER" id="CONTRACT_NUMBER" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
		      		</td>
		      		<th class="nui-form-label"><label for="type$text">金额：</label></th>
		      		<td colspan="3">
		      			<input name="editData.CONTRACT_PRICE" vtype="float;maxLength:19;"  class="nui-textbox" style="width:150px;"/>
		      		</td>
		      	</tr>
		      	<tr>
		      		<th class="nui-form-label"><label for="type$text">起期：</label></th>
		      		<td colspan="3">  
				 		<input name="editData.START_TIME" id="START_TIME" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:150px;"/>
		        	</td>
		      		<th class="nui-form-label"><label for="type$text">止期：</label></th>
		      		<td colspan="3">  
				 		<input name="editData.END_TIME" id="END_TIME" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:150px;"/>
		        	</td>
		      		<th class="nui-form-label"><label for="type$text">业务条线：</label></th>
					<td colspan="3">
						<input id="BUSINESS_LINE" class="nui-dictcombobox" name="editData.BUSINESS_LINE"  emptyText="请选择"
	  					valueField="dictID" textField="dictName" dictTypeId="CHECK_TYPE" showNullItem="true" nullItemText="请选择" style="width:150px;"/>
	  				</td>
	  				<th class="nui-form-label"><label for="type$text">贷后检查时间：</label></th>
		      		<td colspan="3">  
				 		<input name="editData.CHECK_TIME" id="CHECK_TIME" required="true" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:150px;"/>
		        	</td>
		      	</tr>
		      	<tr>
		      		<th class="nui-form-label"><label for="type$text">贷后检查类型：</label></th>
					<td colspan="3">
						<input id="check_type" class="nui-dictcombobox" name="editData.CHECK_TYPE"  emptyText="请选择"
	  					valueField="dictID" textField="dictName" dictTypeId="CHECK_TYPE" showNullItem="true" nullItemText="请选择" style="width:150px;"/>
	  				</td>
		      	</tr>
						</table>
					</fieldset>
				</td>
			</tr>
		   	</table>
		   	<div class="nui-toolbar" style="padding:0px;"   borderStyle="border:0;">
			<table width="100%">
		    	<tr>
		        	<td style="text-align:center;">
		          		<a class="nui-button" iconCls="icon-save" id="saveButtorn" onclick="saveData">保存</a>
		          		<span style="display:inline-block;width:25px;"></span>
		          		<a class="nui-button" iconCls="icon-cancel" onclick="CloseWindow('cancel')">取消</a>
		        	</td>
		      	</tr>
		   	</table>
		</div>
	</div>
<script type="text/javascript">
    nui.parse();
    var interBusiForm = new nui.Form("interBusiForm");
    nui.get("files_type").setEnabled(false);
    function notRequired(){
    	nui.get("CUSTOMER_NAME").setRequired(false);
		nui.get("BUSINESS_TYPE").setRequired(false);
		nui.get("CONTRACT_NUMBER").setRequired(false);
		nui.get("START_TIME").setRequired(false);
    }
    
    //关闭添加窗口
 	function CloseWindow(action){
  		if(window.CloseOwnerWindow) 
    		return window.CloseOwnerWindow(action);
  		else
    		return window.close();
    }
    //保存数据
    function saveData(){
    	interBusiForm.validate();            
        if (interBusiForm.isValid() == false) return;
        var data = interBusiForm.getData(true,true);
        var files_type = nui.get("files_type").getValue();
       
        if(files_type == '10'){
        	var temp1 = nui.get("CUSTOMER_NAME").getValue()+'-零售授信贷后档案-'
			+nui.get("CHECK_TIME").getFormValue();
        	data.editData.FILES_NAME = temp1;
        }else if(files_type == '11'){
        	var temp2 = nui.get("CUSTOMER_NAME").getValue()+'-零售支用贷后档案-'
			+nui.get("CHECK_TIME").getFormValue();
        	data.editData.FILES_NAME = temp2;
        }else if(files_type == '12'){
        	var temp3 = nui.get("CUSTOMER_NAME").getValue()+'-法人授信贷后档案-'
			+nui.get("CHECK_TIME").getFormValue();
        	data.editData.FILES_NAME = temp3;
        }else if(files_type == '13'){
        	var temp4 = nui.get("CUSTOMER_NAME").getValue()+'-法人支用贷后档案-'
			+nui.get("CHECK_TIME").getFormValue();
        	data.editData.FILES_NAME = temp4;
        }else if(files_type == '14'){
        	var temp5 = nui.get("CUSTOMER_NAME").getValue()+'-票据承兑贷后档案-'
			+nui.get("CHECK_TIME").getFormValue();
        	data.editData.FILES_NAME = temp5;
        }
        var data1={data:data,filesType:files_type};
        var json = nui.encode(data1);
        var URL="com.gotop.xmzg.files.archiveList.updateVerbFiles.biz.ext";
        $.ajax({
        	url: URL,
            type: 'POST',
            data: json,
            cache: false,
            contentType:'text/json',
            success: function (text){
                var returnJson = nui.decode(text);
				if(returnJson.exception == null && returnJson.flag == "1"){
					nui.alert("保存成功", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							CloseWindow("saveSuccess"+","+files_type);
						}
					});
				}else if(returnJson.exception == null && returnJson.flag == "exist"){
					nui.alert("档案名称已存在！");
				}else if(returnJson.exception == null && returnJson.flag == "2"){
					nui.alert("无权修改！");
				}else{
					nui.alert("保存失败", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							//CloseWindow("saveFailed");
						}
					});
				}
		    }
  		});   
    }
	
	//机构树回显
     function OrgonButtonEdit(e){
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/files/archiveList/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            //将值机构id变成机构code
                            var data={orgId:data.ID};
					        var json = nui.encode(data);
					        var URL="com.gotop.xmzg.files.fileLedger.getOrgcode.biz.ext";
					        $.ajax({
					        	url: URL,
					            type: 'POST',
					            data: json,
					            async:false,
					            cache: false,
					            contentType:'text/json',
					            success: function (text){
					                var returnJson = nui.decode(text);
					                var result = returnJson.resultList;
					                btnEdit.setValue(result[0].ORGCODE);
							    }
					  		}); 
                        }
                    }
                }
            });            
             
        } 
        
        //人员树回显
        function selectEmp(e){
        	var ele = e.sender.id;
    		var emp = nui.get(ele);
            nui.open({
                url:"<%=request.getContextPath()%>/files/archiveList/empTree.jsp?type=emp",
                title: "选择人员",
                width: 600,
                height: 400,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = emp.getValue();
                	//frame.contentWindow.setData(data);
                	frame.contentWindow.setTreeCheck(data.value);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);
                        debugger;    //必须
                        if (data) {
                            emp.setValue(data.nodeId);
                            emp.setText(data.nodeName);
                            //将值人员id转换成人员code
                            var data={empId:data.nodeId};
					        var json = nui.encode(data);
					        var URL="com.gotop.xmzg.files.fileLedger.getEmpcode.biz.ext";
					        $.ajax({
					        	url: URL,
					            type: 'POST',
					            data: json,
					            async:false,
					            cache: false,
					            contentType:'text/json',
					            success: function (text){
					                var returnJson = nui.decode(text);
					                var result = returnJson.resultList;
					                emp.setValue(result[0].EMPCODE);
							    }
					  		});
                        }
                    }

                }
            });
    	}  
    	
    	//与父页面的方法2对应：页面间传输json数据
    function setFormData(data){
        //跨页面传递的数据对象，克隆后才可以安全使用
    	var infos = nui.clone(data);
        //如果是点击编辑类型页面
        if (infos.pageType == "edit") {
	      	//表单数据回显
        	var json = infos.record;
         	var form = new nui.Form("#interBusiForm");//将普通form转为nui的form
         	form.setData(json);
         	debugger;
         	nui.get("SUB_ORG").setText(json.editData.SUB_NAME);
         	nui.get("DEAL_ORG").setText(json.editData.DEAL_NAME);
         	nui.get("EMPNAME").setText(json.editData.EMP_NAME);
        }
    }
</script>
</body>
</html>
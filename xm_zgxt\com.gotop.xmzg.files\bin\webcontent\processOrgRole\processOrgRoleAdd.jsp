<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%@page import="com.eos.data.datacontext.UserObject" %>
<%@include file="/coframe/dict/common.jsp"%>
<html>
<!-- 
  - Author(s): lzd
  - Date: 2018-06-04 09:36:10
  - Description:
-->
<head>
</head>
<%
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>
<body>
	<div id="fileTypeForm" style="padding-top:5px;">
		<table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
			<div class="nui-hidden"  name="inputData.orgId" value="<%=userObject.getUserOrgId()%>" ></div>
	      	<tr>
				<th class="nui-form-label"><label for="type$text">流程名称：</label></th>
				<td colspan="4">
					<input id="PROCESS_NAME" class="nui-dictcombobox nui-form-input" name="inputData.PROCESS_NAME"  emptyText="请选择"
  					valueField="dictID" textField="dictName" dictTypeId="APPLY_PROCESS" showNullItem="true" nullItemText="请选择"  required="true"/>
  				</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">活动节点ID：</label></th>
				<td colspan="4">
					<input name="inputData.ACTIVITYINSTID" id="ACTIVITYINSTID"  class="nui-textbox" style="width: 100%;" required="true"/>
  				</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">活动节点名称：</label></th>
				<td colspan="4">
					<input name="inputData.ACTIVITYINSTNAME" id="ACTIVITYINSTNAME"  class="nui-textbox" style="width: 100%;" required="true"/>
  				</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">角色：</label></th>
				<td colspan="4" style=" height:40px;">
				   <!--  <input class="nui-hidden" name="inputData.DEAL_ROLE_NAME" id="check_roleNameBox"/> -->
					<input id="check_roleBox" name="inputData.DEAL_ROLE" class="nui-textboxlist belongCheckInput" style="width: 100%;height:35px;" allowInput="false" valueField="id" textField="text" /> 
	
					<a value="role" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
  				</td>
	      	</tr>
	      		<th class="nui-form-label"><label for="type$text">机构模式：</label></th>
  				<td colspan="4">
  					<input id="ORG_TYPE" class="nui-dictcombobox nui-form-input" name="inputData.ORG_TYPE"  emptyText="请选择"
  					valueField="dictID" textField="dictName" dictTypeId="PROCESS_ORG_TYPE" showNullItem="true" nullItemText="请选择"  />
	      		</td>
	      	<tr id="ORG_ID" style="display:none">
	      		<th class="nui-form-label"><label for="type$text">指定机构：</label></th>
	      		<td colspan="4"  style=" height:40px;">
	      	  	   <!--  <input class="nui-hidden" name="inputData.ORG_NAME" id="check_orgNameBox"/> -->
	      			<input id="check_orgBox" name="inputData.ORG_ID" class="nui-textboxlist belongCheckInput" style="width: 100%;height:35px;" allowInput="false" valueField="id" textField="text" /> 
	      		    <a value="org" class="nui-button " plain="true" iconCls="icon-edit" onclick="onClick">配置</a>
	      		</td>
	      	</tr>
	      	
	      	<tr id="APPOINT_ACTIVITYINSTID" style="display:none">
	      		<th class="nui-form-label"><label for="type$text">指定节点：</label></th>
	      		<td colspan="4">
	      			<input name="inputData.APPOINT_ACTIVITYINSTID" id="APPOINT_ACTIVITYINSTID"  class="nui-textbox"  style="width: 100%;"/>
	      		</td>
	      	</tr>
	      	<tr>
	      		<th class="nui-form-label"><label for="type$text">排序：</label></th>
				<td colspan="4">
					<input name="inputData.SORT" id="SORT"  class="nui-textbox" style="width: 100%;" />
  				</td>
	      	</tr>
		</table>
	    <div class="nui-toolbar" style="padding:0px;"   borderStyle="border:0;">
			<table width="100%">
		    	<tr>
		        	<td style="text-align:center;">
		          		<a class="nui-button" iconCls="icon-save" id="saveButtorn"  onclick="saveData">保存</a>
		          		<span style="display:inline-block;width:25px;"></span>
		          		<a class="nui-button" iconCls="icon-cancel" onclick="CloseWindow('cancel')">取消</a>
		        	</td>
		      	</tr>
		   	</table>
		</div>
	</div>
<script type="text/javascript">
    nui.parse();
    var fileTypeForm = new nui.Form("fileTypeForm");
    
    
      //当选择不通过时，理由不能为空
      		var res = nui.get("ORG_TYPE");
		    res.on("valuechanged", function (e) {
		    if(this.getValue()=="5" ||this.getValue()=="10")
		    {
		   nui.get("APPOINT_ACTIVITYINSTID").setRequired(false);
		   nui.get("check_orgBox").setRequired(true);
		    document.getElementById("ORG_ID").style.display = ""; 
		    document.getElementById("APPOINT_ACTIVITYINSTID").style.display = "none"; 
		    
		     //指定节点置空
		    nui.get("APPOINT_ACTIVITYINSTID").setValue(""); 
		    
		    }else if(this.getValue()=="6" ||this.getValue()=="7" ||this.getValue()=="8" ||this.getValue()=="9")
		    {
		    nui.get("APPOINT_ACTIVITYINSTID").setRequired(true);
		    nui.get("check_orgBox").setRequired(false);
		      document.getElementById("ORG_ID").style.display = "none"; 
		      document.getElementById("APPOINT_ACTIVITYINSTID").style.display = ""; 
		      
		        //指定机构置空
		      nui.get("check_orgBox").setValue(""); 
			 nui.get("check_orgBox").setText("");  
			// nui.get("check_orgNameBox").setValue(""); 
		      
		    }else
		    {
		      nui.get("APPOINT_ACTIVITYINSTID").setRequired(false);
		    nui.get("check_orgBox").setRequired(false);
		    document.getElementById("ORG_ID").style.display = "none"; 
		    document.getElementById("APPOINT_ACTIVITYINSTID").style.display = "none"; 
		    
		    
		     //指定节点置空
		    nui.get("APPOINT_ACTIVITYINSTID").setValue(""); 
		    
			 //指定机构置空
			 nui.get("check_orgBox").setValue(""); 
			 nui.get("check_orgBox").setText("");  
			 //nui.get("check_orgNameBox").setValue(""); 
		    
		    }
		      
		    }); 
    
    //关闭添加窗口
 	function CloseWindow(action){
  		if(window.CloseOwnerWindow) 
    		return window.CloseOwnerWindow(action);
  		else
    		return window.close();
    }
    //保存数据
    function saveData(){
    	fileTypeForm.validate();            
        if (fileTypeForm.isValid() == false) return;
        var inputData = fileTypeForm.getData(true,true);
        
         var check_roleBox=nui.get("check_roleBox").getText();

        inputData.inputData.DEAL_ROLE_NAME=check_roleBox;
        
         var check_orgBox=nui.get("check_orgBox").getText();
        
        inputData.inputData.ORG_NAME=check_orgBox;
        
        var json = nui.encode(inputData);
        var URL="com.gotop.xmzg.files.processOrgRole.addProcessOrgRole.biz.ext";
        $.ajax({
        	url: URL,
            type: 'POST',
            data: json,
            cache: false,
            contentType:'text/json',
            success: function (text){
                var returnJson = nui.decode(text);
				if(returnJson.flag == "1"){
					nui.alert("保存成功", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							CloseWindow("saveSuccess");
						}
					});
				}else if(returnJson.exception == null && returnJson.flag == "exist"){
					nui.alert("该流程的该节点已存在！");
				}else{
					nui.alert("保存失败", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							//CloseWindow("saveFailed");
						}
					});
				}
		    }
  		});   
    }
  
/* 弹出权限设置界面  */
		function onClick(e) {
			//var powerType = radio_powerType.getValue();
			var boxType = e.sender.defaultValue;
			nui.open({
	        	url: "<%=request.getContextPath() %>/files/processOrgRole/list_powertree.jsp?type="+boxType,
	        	title: "权限配置",
	            iconCls: "icon-edit", 
	            width: 350, 
	            height: 350,
	            onload: function () {
	                var iframe = this.getIFrameEl();  
	                var editTextBox = findEditTextBox( boxType);   
	                iframe.contentWindow.setTreeCheck(editTextBox.getValue());
	            },
	            ondestroy: function (action) {
					if (action == "ok") {
	                    var iframe = this.getIFrameEl();
	                    var chooseList = iframe.contentWindow.GetData();
	                    chooseList = nui.clone(chooseList);
/* 	                    if ("org" == boxType) {
	                    	chooseList = filterOrgChooseList(chooseList);  
	                    } */
	                    if (chooseList) {
	                    	putDataTextBox( boxType, chooseList, "nodeId", "nodeName");
	                    }
	                }
	            }
	        });
		}
		
		/**
		 * 往textboxlist中添加选择的数据
		 * @params powerType 权限类型
		 * @params boxType	    根据点击按钮的类型 添加到不同box里面
		 * @params list 	    获取Check选中的节点集合
		 */
		var map =["emp", "role", "org", "group"];
		function putDataTextBox( boxType, list){
			var text = "",value = "";
			var isEmp = (boxType == "emp"),isGroup = (boxType == "group");
			
			var editTextBox = findEditTextBox( boxType);
			
			//var editHiddenBox = findEditHiddentBox( boxType);
			
			for (var i = 0; i < list.length; i++) {
				var node = list[i];
				if (node.nodeType == "OrgOrganization" && isEmp) continue;
				if (node.nodeType != "Group" && isGroup) continue;
				if (i == list.length -1) {
					value += node["nodeId"];
					text  += node["nodeName"];
				} else {
					value += node["nodeId"] + ",";
					text  += node["nodeName"] + ",";
				}
			}
			if (value.endsWith(",")) value = strSplitLast(value);
			if (text.endsWith(",")) text = strSplitLast(text);
			editTextBox.setValue(value);
			editTextBox.setText(text);
			
			//editHiddenBox.setValue(text);
		}  
	
	
		function findEditTextBox(boxType) {
		    
	    	var boxId = "check_"+boxType+"Box";
	    	return nui.get(boxId);
	    }
	    
	    
	    function findEditHiddentBox(boxType) {
		    
	    	var boxId = "check_"+boxType+"NameBox";
	    	return nui.get(boxId);
	    }
</script>
</body>
</html>
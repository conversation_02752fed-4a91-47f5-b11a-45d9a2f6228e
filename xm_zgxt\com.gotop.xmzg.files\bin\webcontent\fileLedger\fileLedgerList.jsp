<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<%@page import="com.eos.data.datacontext.UserObject" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): liuzd
  - Date: 2018-05-18
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>档案清单查询</title>
</head>
<%
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>
<body style="margin:0px;width:100%;overflow:hidden;" >
	<div class="search-condition">
		<div class="list">
			<div id="filesForm">
				<div class="nui-hidden"  name="condition.userOrgId" id="userOrgId"></div>
				<div class="nui-hidden"  name="condition.userId"  id="userId" ></div>
		   		<table class="table" style="width:100%;">
		      		<tr>
						<td align="right">档案种类：</td>
						<td>
							<input id="files_type" class="nui-dictcombobox" name="queryData.FILES_TYPE"  emptyText="请选择"
          					valueField="dictID" textField="dictName" dictTypeId="FILES_TYPE" showNullItem="true" nullItemText="请选择" style="width:180px;"/>
						</td>
						<td align="right">档案名称：</td>
						<td>
							<input name="queryData.FILES_NAME" class="nui-textbox" style="width:180px;"/>
						</td>
						<td align="right">档案状态：</td>
						<td>
							<input id="files_status" class="nui-dictcombobox" name="queryData.FILES_STATUS"  emptyText="请选择"
          					valueField="dictID" textField="dictName" dictTypeId="FILES_STATUS" showNullItem="true" nullItemText="请选择" style="width:180px;"/>
						</td>
						<td rowspan="12" class="btn-wrap">
							<a class="nui-button" iconCls="icon-search" onclick="searchData();">查询</a>
							<a class="nui-button" iconCls="icon-reset"  onclick="clean()"/>重置</a>
							<input class="nui-button" text="导出" iconCls="icon-download" onclick="excel"/>
						</td>
					</tr>
					<tr>
						<td align="right">存放地址：</td>
						<td>
							<input id="sto_addr" class="nui-dictcombobox" name="queryData.STORAGE_ADDRESS"  emptyText="请选择"
          					valueField="dictID" textField="dictName" dictTypeId="FILES_STORAGE_ADDRESS" showNullItem="true" nullItemText="请选择" style="width:180px;"/>
						</td>
						<td align="right">货架号/箱号：</td>
						<td>
							<input name="queryData.STORAGE_LOCATION" class="nui-textbox" style="width:180px;"/>
						</td>
						<td align="right">档案盒号：</td>
						<td>
							<input name="queryData.BOX_NUM" class="nui-textbox" style="width:180px;"/>
						</td>
					</tr>
					<tr>
						<td align="right">档案审批状态：</td>
						<td>
							<input id="files_status" class="nui-dictcombobox" name="queryData.SP_STATUS"  emptyText="请选择"
          					valueField="dictID" textField="dictName" dictTypeId="FILES_TO_ARCHIVE_STATUS" showNullItem="true" nullItemText="请选择" style="width:180px;"/>
						</td>
						<td align="right">起期区间：</td>
						<td>
							<input name="queryData.startTime" id="startTime" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:90px;"/>
							-
							<input name="queryData.startTime1" id="startTime1" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:90px;"/>
						</td>
						<td align="right">经办机构：</td>
						<td>
							<input id="btnEdit1" name = "queryData.DEAL_ORG"  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:180px;"/>
						</td>
					</tr>
					<tr>
						<td align="right">分管客户经理名称：</td>
						<td>
							<input name="queryData.EMPNAME" class="nui-textbox" style="width:180px;"/>
						</td>
					</tr>
		    	</table>
		  	</div>
		</div>
	</div>
	<div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      	<table style="width:100%;">
        	<tr>
           		<td style="width:100%;">
					<a class="nui-button" iconCls="icon-goto" onclick="apply()" >贷后归档申请</a>
					<a id="fileDetail" class="nui-button" iconCls="icon-node" onclick="fileDetail();">档案详情</a>
					<a id="operationDetail" class="nui-button" iconCls="icon-node" onclick="operateDetail()">操作记录</a>
           		</td>
        	</tr>
      	</table>
    </div>
    <!-- 档案主表信息 -->
  	<div class="nui-fit" id="fieldset1">
	 	<div id="grid"  dataField="resultList" idField="dicttypeid" class="nui-datagrid" style="width:100%;height:100%;" 
	 	allowCellWrap = "true" 
	  	url="com.gotop.xmzg.files.fileLedger.getFilesInfo.biz.ext" sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10" >
	    	<div property="columns">
	    		<div type="checkcolumn"></div>
	    		<%--<div type="indexcolumn">序号</div>--%>
	    		<div field="ROWN" headerAlign="center" width="30px">序号</div>
	    		<div field="FILES_TYPE" headerAlign="center" renderer="filesType">档案种类</div>
	    		<div field="BUSINESS_TYPE" headerAlign="center">业务种类</div>
	    		<div field="DEAL_NAME" headerAlign="center">经办机构</div>
	    		<div field="DEAL_ORG" headerAlign="center">经办机构号</div>
	    		<div field="EMP_NAME" headerAlign="center">分管客户经理</div>
	    		<div field="EMPNAME" headerAlign="center">分管客户经理号</div>
	    		<div field="CUSTOMER_NAME" headerAlign="center">客户名称</div>
	    		<div field="CUSTOMER_NO" headerAlign="center">客户号码</div>
	    		<div field="CONTRACT_NUMBER" headerAlign="center">编号</div>
		        <div field="CONTRACT_PRICE" headerAlign="center">金额</div>
		        <div field="START_TIME" headerAlign="center">起期</div>
		        <div field="END_TIME" headerAlign="center">止期</div>
		        <div field="BUSINESS_LINE" headerAlign="center">业务条线</div>
		        <div field="CHECK_TIME" headerAlign="center">归档申请日期</div>
		        <div field="SP_STATUS" headerAlign="center" renderer="getTextSPStatus">档案审批状态</div>
		        <div field="AFFILIATED_NAMES" headerAlign="center" renderer="onActionRender" align="center" width="150px">附件</div>
		    </div>
	 	</div>
  	</div>
</body>
</html>
<script type="text/javascript">
	nui.parse();
	var form = new nui.Form("filesForm");
	var grid = nui.get("grid");
	var data = form.getData(true,true);
	var sql='';
	var total = 0;
	//grid.load(data);
	
	function searchData(){
        	var data = form.getData(true,true);
    		grid.load(data);
    	}
    	
    	//统计数据条数
		function getDataCount(){
			var data = form.getData(true,true);
			nui.ajax({
				url: "com.gotop.xmzg.files.fileLedger.getFilesInfo.biz.ext",
				type: "post",
				data : {queryData : data},
				cache: false,
				contentType: 'text/json',
				async : false,
				success: function (json) {
					if(json.total > 0){
						sql = json.sql;
						total = json.total;
					}
					else total = 0;
				},
				error: function () {
					nui.alert("失败！");
				}
			});
		}
    	
    
    function fileDetail(){
    	var rows = grid.getSelecteds();
    	var row = grid.getSelected();
    	var detail = '';
    	if(rows.length>1){
    		return nui.alert("请只选择一条记录进行明细查看！","提示");
    	}
       	if(row!=null){
       		var fy = row.FILES_TYPE;
       		//综合类档案
       		if(noun(fy)){
       			other(fy,row);
       		}else {     //基础类档案
       			oneToNine(row,fy);
       		}
       	}
    }
    
    function oneToNine(row,fy){
		    nui.open({
		    	url:"<%=request.getContextPath()%>/files/fileLedger/archiveDetail.jsp",
	          	title:'档案详情',
	          	width:1150,
          		height:300,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            //方法1：后台查询一次，获取信息回显
		            /* var data = row;
		            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
		            //方法2：直接从页面获取，不用去后台获取
		            var filesType=row.FILES_TYPE;
		            var data={};
		            if(filesType == '01'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '02'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '03'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '04'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '05'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '06'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else if(filesType == '09'){
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}else{
						data = {pageType:"edit",filesType:filesType,record:{editData:row}};
					}
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		        	
		        }
		    });
    }
    
    function other(fy,row){
		if(fy == '15'){
			//信审档案详情界面
			path = "<%=request.getContextPath()%>/files/archiveList/xsDetail.jsp";
		}else {
			//综合类档案的其它档案详情界面
			var path = "<%=request.getContextPath()%>/files/archiveList/otherDetail.jsp";
		}
		
	    nui.open({
	    	url:path,
          	title:'编辑',
          	width:1150,
      		height:300,
          	onload:function(){
	        	var iframe = this.getIFrameEl();
	            //方法1：后台查询一次，获取信息回显
	            /* var data = row;
	            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
	            //方法2：直接从页面获取，不用去后台获取
	            var filesType=row.FILES_TYPE;
	            var data = {pageType:'edit',filesType:filesType,record:{editData:row}};
            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
	        },
	        ondestroy:function(action){
	        	
	        }
	    });
    }
    
    //根据档案种类获取详情表名
	function getDetailTableName(filesType){
		if(filesType == '01'){
				return 'T_FILES_RETAIL_CREDIT';
			}else if(filesType == '02'){
				return 'T_FILES_RETAIL_DISBURSE';
			}else if(filesType == '03'){
				return 'T_FILES_PERSON_CREDIT';
			}else if(filesType == '04'){
				return 'T_FILES_PERSON_DISBURSE';
			}else if(filesType == '05'){
				return 'T_FILES_BILL_HONOUR';
			}else if(filesType == '06'){
				return 'T_FILES_BILL_DISCOUNT';
			}else if(filesType == '07'){
				return 'T_FILES_COOPERATE_ORG';
			}else if(filesType == '08'){
				return 'T_FILES_REFUSE_LOAN';
			}else if(filesType == '09'){
				return 'T_FILES_CREDIT';
			}
	}
    
    function apply(){
    	var rows = grid.getSelecteds();
    	var row = grid.getSelected();
    	var detail = '';
    	if(rows.length>1){
    		return nui.alert("请只选择一条记录进行申请！","提示");
    	}
       	if(row!=null){
       		//if(row.FILES_STATUS != 3){
       		//	return nui.alert("请选择档案状态为入库的档案！","提示");
       		//}
       		debugger;
		    nui.open({
		    	url:"<%=request.getContextPath()%>/files/fileLedger/archiveApply.jsp",
	          	title:'归档申请',
	          	width:1150,
          		height:450,
	          	onload:function(){
		        	var iframe = this.getIFrameEl();
		            //方法1：后台查询一次，获取信息回显
		            /* var data = row;
		            iframe.contentWindow.SetData(data); //调用弹出窗口的 SetData方法 */ 
		            //方法2：直接从页面获取，不用去后台获取
		            var filesType=row.FILES_TYPE;
		            var data={};
		            data = {pageType:"apply",record:row};
	            	iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法
		        },
		        ondestroy:function(action){
		          	 if(action=="saveSuccess"){
            	 		 searchData();
		             }
		        }
		    });
       	}
    }
    
    
    //操作记录
       function operateDetail(){
       var row = grid.getSelected();
       var rows = grid.getSelecteds();
       if(rows.length>1){
    		return nui.alert("请只选择一条记录进行查看！","提示");
    	}
       if(row!=null){
       var INFORMATION_ID=row.INFORMATION_ID;
       debugger;
	       nui.open({
	          url:"<%=request.getContextPath() %>/files/archiveList/operationLog.jsp?infoId="+INFORMATION_ID,
	          title:'档案操作流水',
	          width:1200,
	          height:550,
	          onload:function(){
	             /* var iframe = this.getIFrameEl();
	               var data = {pageType:"edit",record:{map:row}};
                  iframe.contentWindow.setFormData(data);   //调用弹出窗口的 setFormData方法 */
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	             }
	          }
	       });
       }else{
           nui.alert("请选中一条记录！");
       }
    }
   
    
    //重置查询信息
	function clean(){
	   	//不能用form.reset(),否则会把隐藏域信息清空
	   	form.reset();  
    }
    
    //机构树回显
     function OrgonButtonEdit(e){
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/files/archiveList/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            
                        }
                    }
                }
            });            
             
        }   
        
    
    function filesType(e){
	    return nui.getDictText("FILES_TYPE", e.row.FILES_TYPE);
	}
	
	function getTextStatus(e){
	    return nui.getDictText("FILES_STATUS", e.row.FILES_STATUS);
	}
	
	function getTextSPStatus(e){
	    return nui.getDictText("FILES_TO_ARCHIVE_STATUS", e.row.SP_STATUS);
	}
	
	
	function getAddress(e){
	    return nui.getDictText("FILES_STORAGE_ADDRESS", e.row.STORAGE_ADDRESS);
	}
	
	//导出Excel
	function excel(){
		var form=new nui.Form("filesForm");
	    form.validate();
	    if (form.isValid() == false) return;
		var data=form.getData(true, true);
		data.queryData.userOrgId = "<%=userObject.getUserOrgId()%>";
		data.queryData.userId = "<%=userObject.getUserId()%>";
		getDataCount();
		if(total == 0){
	    	return nui.alert("查无报表数据");
	    }
		var fileName="档案清单";
		var queryPath=sql;
		debugger;
		/* var columns=grid.getBottomColumns();
		columns=columns.clone();
		for(var i=0;i<columns.length;i++){
			var column=columns[i];
			if(!column.field){
				columns.removeAt(i);
			}else{
				var c={header:column.header,field:column.field };
				columns[i]=c;
			}
		}
		columns=nui.encode(columns); */
		data=nui.encode(data);
		var url="<%=request.getContextPath()%>/util/exportExcel/exportExcel.jsp?fileName="+fileName+"&queryPath="+queryPath+"&data="+data;
		 window.location.replace(encodeURI(url));
		 
		 //设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
	     setMask();
	} 
	
	//设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)	
	function setMask(){
 		var a= nui.loading("正在操作中,请稍等...","提示"); //显示遮罩层
 		var icount = setInterval(function(){  
	  	if(document.attachEvent){   //IE浏览器
	 		if(document.readyState=='interactive'){
              	nui.hideMessageBox(a);  //隐藏遮罩层
              	clearTimeout(icount);
        	}
	 	}else{ //谷歌浏览器
	 		if(document.readyState=='complete'){
              	nui.hideMessageBox(a);  //隐藏遮罩层
              	clearTimeout(icount);
        	}
	 	} 
 		}, 1); 
	}
	
	function noun(fy){
		if(fy == '10'){
			return true;
		}else if(fy == '11'){
			return true;
		}else if(fy == '12'){
			return true;
		}else if(fy == '13'){
			return true;
		}else if(fy == '14'){
			return true;
		}else if(fy == '15'){
			return true;
		}else {
			return false;
		}
	}
	
	//操作列：查看/下载附件
      function onActionRender(e){
      		var row  = grid.getRow(e.rowIndex);
      		var s =e.value;
      		console.log(e.value);
      		var d = row.AFFILIATED_IDS;
      		 if(s==null){
	           	s="无附件";
      		}else{
      			var names = s.split(",");
      			var ids = d.split(",");
      			var a = [];
      			for(var i=0;i<names.length;i++){
		           var s1=	"<a class=\"dgBtn\" style=\"color:blue\" href=\"javascript:void(0);\"  onclick=\"downAccessory(\'"+ids[i]+"\',\'"+names[i]+"\')\">"+names[i]+"</a>";
		           a.push(s1);
	            }
	            s = a; 
      		} 
           	return s;
      }
	
	
	//下载附件
	function downAccessory(id,name){
       	var fileId = id;
       	var fileName = name;
       	var url="<%=request.getContextPath()%>/files/fileList/downloadFile.jsp?fileId="+fileId+"&fileName="+fileName;
		window.location.replace(url);
	  }
</script>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): xwl
  - Date: 2023-05-05 14:38:16
  - Description:
-->
<head>
<%@include file="/coframe/dict/common.jsp"%>
<style type="text/css">
    	.asLabel .mini-textbox-border,
	    .asLabel .mini-textbox-input,
	    .asLabel .mini-buttonedit-border,
	    .asLabel .mini-buttonedit-input,
	    .asLabel .mini-textboxlist-border
	    {
	        border:0;background:none;cursor:default;
	    }
	    .asLabel .mini-buttonedit-button,
	    .asLabel .mini-textboxlist-close
	    {
	        display:none;
	    }
	    .asLabel .mini-textboxlist-item
	    {
	        padding-right:8px;
	    }    
	    .nui-form-label{
	    	width: 135px;
	    }
    </style>
</head>
<body>
<div id="form1" style="padding-top:5px;">
   <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
   	  <input id="TJQ_ID" name = "map.TJQ_ID" class="nui-hidden" />
   	  <input id="TJQ_YJ_ORG_CODE" name = "map.TJQ_YJ_ORG_CODE" class="nui-hidden" />
   	  <input id="TJQ_YJ_ORG_NAME" name = "map.TJQ_YJ_ORG_NAME" class="nui-hidden" />
   	  <input id="map.TJQ_ORG_NAME" name = "map.TJQ_ORG_NAME" class="nui-hidden" />
   	  <tr>
        <th class="nui-form-label"><label for="map.type$text">推广机构：</label></th>
        <td colspan="3" > 
        	<input id="TJQ_ORG_CODE" name = "map.TJQ_ORG_CODE"  class="nui-buttonedit" allowInput="false"  style="width:150px;" readonly="true" />
        </td> 
      </tr>
     <!--  <tr>
        <th class="nui-form-label"><label for="map.type$text">机构名称：</label></th>
        <td colspan="3" >
        	<input id="TJQ_ORG_NAME" name = "map.TJQ_ORG_NAME"  class="nui-textbox" style="width:150px;"  readonly="true" />
        </td> 
      </tr> -->
      <tr>
        <th class="nui-form-label"><label for="map.type$text">商户号：</label></th>
        <td colspan="3" >  
        	<input id="TJQ_MERCH_CODE" name = "map.TJQ_MERCH_CODE"  class="nui-textbox" style="width:150px;" readonly="true" />
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text">商户名称：</label></th>
        <td colspan="3" >  
            <input id="TJQ_MERCH_NAME" name = "map.TJQ_MERCH_NAME"  class="nui-textbox" style="width:150px;" readonly="true" />        
        </td> 
      </tr>
       <tr>
        <th class="nui-form-label"><label for="map.type$text">推荐人姓名：</label></th>
        <td colspan="3" >  
            <input class="nui-textbox" id="TJQ_YJ_EMP_NAME" name="map.TJQ_YJ_EMP_NAME"  style="width:150px;" />                  
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text">推荐人工号：</label></th>
        <td colspan="3" > 
        	<input id="TJQ_YJ_EMP_CODE" name = "map.TJQ_YJ_EMP_CODE"  class="nui-textbox" style="width:150px;" />
        </td> 
      </tr>
    </table>
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save" onclick="onOk">保存</a>
	          <span style="display:inline-block;width:10px;"></span>
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  </div>

  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
    form.setChanged(false);
    var saveUrl = "com.gotop.xmzg.achieve.config.qcfqyjr_upd.biz.ext";
    function setData(data){     	      	  
	    //跨页面传递的数据对象，克隆后才可以安全使用
	    var infos = nui.clone(data);	 
	    //表单数据回显
	    var json = infos.record;
	    form.setData(json);
	    var orgTree = nui.get("TJQ_ORG_CODE");
	    orgTree.setValue(json.map.TJQ_ORG_CODE);
	    orgTree.setText(json.map.TJQ_ORG_NAME);
    }
    
    function onOk(){
      	saveData();
    }
    
    //机构树点击事件
	function OrgonButtonEdit(e) {	
        var btnEdit = this;
        nui.open({
            url:"<%=request.getContextPath() %>/achieve/report/org_tree.jsp",
        showMaxButton: false,
        title: "选择树",
        width: 350,
        height: 350,
        ondestroy: function (action) {                    
            if (action == "ok") {
                var iframe = this.getIFrameEl();
                var data = iframe.contentWindow.GetData();
                data = nui.clone(data);
                if (data) {
                    btnEdit.setValue(data.CODE);
                    btnEdit.setText(data.TEXT);
                    nui.get("map.TJQ_ORG_NAME").setValue(data.TEXT);
                   /*  var form = new nui.Form("#form1");
       				form.validate(); */
                    
                }
            }
        }
    	});            
	}
    
    //提交
    function saveData(){   
      form.validate();
      
      if(form.isValid()==false) return;
      
      var data = form.getData(true,true);

	  if(data.map.TJQ_YJ_EMP_CODE==""&&data.map.TJQ_YJ_EMP_NAME==""){
         data.map.TJQ_YJ_ORG_CODE="";
         data.map.TJQ_YJ_ORG_NAME="";
	  }else if(data.map.TJQ_YJ_EMP_CODE!=""&&data.map.TJQ_YJ_EMP_NAME!=""){

	  }else{
	  	 nui.alert("推荐人姓名和推荐人工号必须同时有值或者同时为空","系统提示");
	  	 return false;
	  }
      var json = nui.encode(data);
     
      $.ajax({
        url:saveUrl,
        type:'POST',
        data:json,
        cache:false,
        contentType:'text/json',
        success:function(text){
            var returnJson = nui.decode(text);
			if(returnJson.exception == null && returnJson.iRtn == 1){
				nui.alert("操作成功", "系统提示", function(action){
					if(action == "ok" || action == "close"){
						CloseWindow("saveSuccess");
					}
				});
			}else if(returnJson!= null && returnJson.msg != null && returnJson.iRtn != 1){
				nui.alert(returnJson.msg, "系统提示");
			}else{
				nui.alert("操作失败", "系统提示");
			}
        }
      });
    }
    
   
    function onCancel(){
      CloseWindow("cancel");
    }
    
    function CloseWindow(action){
     var flag = form.isChanged();
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
	
  </script>
</body>
</html>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): xwl
  - Date: 2023-05-11 16:22:18
  - Description:
-->
<style>
#table1 .tit{
	height: 10px;
    line-height: 10px;
    	text-align: right;
}
#table1 td{
	height: 10px;
    line-height: 10px;
}
#table1 .roleLabel{
	text-align: right;
}
#table1 .roleText{
	padding-left: 5px;
}
</style>
<%@include file="/coframe/tools/skins/common.jsp" %>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<title>AUM资产细项</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
  <script type="text/javascript" src="/default/common/nui/nui.js"></script>
	<script type="text/javascript" src="/default/common/nui/locale/zh_CN.js"></script>
	<link id="css_skin" rel="stylesheet" type="text/css" href="/default/coframe/tools/skins/skin1/css/style.css" />
    
</head>
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");

 %>
<body>
<div class="search-condition">
		<div class="list">
			<div id="form1">
			<table class="table" style="width:100%;">							
				<tr>	
					<td class="tit" STYLE="width:100px;">机构：</td>
					<td>
						<input id="queryData.ORGCODE" name = "queryData.ORGCODE"  class="nui-buttonedit" allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;" required="true"/>
					</td>
					<td class="tit" STYLE="width:100px;">客户号:</td>
					<td>
						<input id="queryData.CUST_NO" name = "queryData.CUST_NO"  class="nui-textbox"  style="width:150px;" />
                    </td>
                    <th rowspan="3"><a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp; 
			        <input class="nui-button" text="重置" iconCls="icon-reset" onclick="clean"/>  
                    
               </tr>
               <tr>           
                    <td class="tit" STYLE="width:100px;">客户等级:</td>
					<td>
						<input id="queryData.CUST_LVL" name = "queryData.CUST_LVL" class="nui-combobox"   data="CUST_LVL"  textField="text" valueField="id" dataField="list" style="width:150px;"/>
					</td>
					<td class="tit" STYLE="width:100px;">管户客户经理:</td>
					<td>
						<input id="queryData.CUST_MSG_NO" name = "queryData.CUST_MSG_NO" class="nui-textbox"  style="width:150px;"/>
					</td>

                </tr>			
			</table>
		</div>
	</div>
</div>
<div class="nui-toolbar" style="border-bottom:0;">
		<table style="width:100%">
			<tr>
				<td>
					<a class="nui-button" iconCls="icon-add" onclick="excel">导出Excel</a>								
				</td>
			</tr>
		</table>
</div>	
	
<div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.achieve.report.aum_detail.biz.ext" 
	  sizeList=[5,10,20,50,100] multiSelect="false" pageSize="10">	  
	    <div property="columns" >           
	            <div field="DATA_DT" >数据日期</div>
			    <div field="CUST_NO">客户号</div>
			    <div field="CUST_NAME">客户名称</div>
			    <div field="INST_NO">机构</div>	
			    <div field="CK">本月存款</div>
			    <div field="LC">本月理财</div>
			    <div field="ZG">本月资管</div>
			    <div field="JJ">本月基金</div>
			    <div field="GZ">本月国债</div>
			    <div field="CK_L">上月存款</div>   
			    <div field="LC_L">上月理财</div>
			    <div field="ZG_L">上月资管</div>
			    <div field="JJ_L">上月基金</div>
			    <div field="GZ_L">上月国债</div>
				<div field="CUST_LVL" >客户等级</div>
				<div field="CUST_LVL_L">上月等级</div>	
				<div field="CUST_MSG_NO">管户客户经理</div>
				<div field="RJ_CHANGE">月日均变动</div>	
	    </div>
	 </div>
  </div>
</body>
<script type="text/javascript">

	var CUST_LVL =[{ id: "0", text: '普通' },{ id: "2", text: '金桂'},{ id: "3", text: '富嘉'},{ id: "5", text: '鼎福'},{ id: "9", text: '富嘉（钻石）'}];

    nui.parse();
	var grid = nui.get("datagrid1");
	var form = new nui.Form("#form1");
   
    //grid.load();
	function search(){
     	var form = new nui.Form("#form1");
    	form.validate();
        if (form.isValid() == false) return; 
        var data = form.getData(true,true);     
        grid.load(data);
    }
    
    function lelDetail(e){
		for (var i = 0, l = CUST_LVL.length; i < l; i++) {
            var g = CUST_LVL[i];
            if (g.id == e.value) return g.text;
        }
		return "";
	}
	
	function clean(){
	   var form = new nui.Form("#form1");
	   form.clear();
	}
	
	//机构树点击事件
	function OrgonButtonEdit(e) {	
        var btnEdit = this;
        nui.open({
            url:"<%=request.getContextPath() %>/achieve/report/org_tree.jsp",
        showMaxButton: false,
        title: "选择树",
        width: 350,
        height: 350,
        ondestroy: function (action) {                    
            if (action == "ok") {
                var iframe = this.getIFrameEl();
                var data = iframe.contentWindow.GetData();
                data = nui.clone(data);
                if (data) {
                    btnEdit.setValue(data.CODE);
                    btnEdit.setText(data.TEXT);
                    var form = new nui.Form("#form1");
       				form.validate();
                    
                }
            }
        }
    	});            
	}
	
	//导出Excel
	function excel(){		      
		var form=new nui.Form("form1");
		form.validate();
	    if (form.isValid() == false) return;
		var data=form.getData(true, true);
		var fileName="AUM资产细项";
		var queryPath="com.gotop.xmzg.achieve.report.query_aum_detail";
		var columns=grid.getBottomColumns();
		columns=columns.clone();
		for(var i=0;i<columns.length;i++){
			var column=columns[i];
			if(!column.field){
				columns.removeAt(i);
			}else{
				var c={header:column.header,field:column.field };
				columns[i]=c;
			}
		}
		columns=nui.encode(columns);
		data=nui.encode(data);
	    var url="<%=request.getContextPath()%>/util/exportExcel/exportExcel.jsp?fileName="+fileName+"&columns="+columns+"&queryPath="+queryPath+"&data="+data;
		window.location.replace(encodeURI(url));
		 
		//设置遮罩层(点导出时显示遮罩层，导出成功后自动隐藏遮罩层)
	    setMask();
	} 
		//初始化加载
	$(function(){
		setOrgInfo();
		 search();
	});
	
	function setOrgInfo(){
		$.ajax({
				url: "com.gotop.xmzg.achieve.report.getUserInfo.biz.ext",
	            type: 'POST',
	            
	            async:false,
	            cache: false,
	            contentType:'text/json',
	            success: function (text){
	           	 	var returnJson = nui.decode(text);
	       	 		var ORGCODE = returnJson.userInfo.ORGCODE;
	       	 		var ORGNAME = returnJson.userInfo.ORGNAME;
	       	 		nui.get("queryData.ORGCODE").setValue(ORGCODE);
	       	 		nui.get("queryData.ORGCODE").setText(ORGNAME);
				}
			});
	}
	
    </script>
</html>
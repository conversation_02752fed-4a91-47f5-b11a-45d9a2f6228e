<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%@page import="com.eos.data.datacontext.UserObject" %>
<%@include file="/coframe/dict/common.jsp"%>
<html>
<!-- 
  - Author(s): lzd
  - Date: 2018-06-04 09:36:10
  - Description:
-->
<head>
<!-- stream插件 -->
<link href="../../css/stream-v1.css" rel="stylesheet" type="text/css">
</head>
<%
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");
%>
<body>
	<div id="interBusiForm" style="padding-top:5px;">
		<input class="nui-hidden" name="applyData.INFORMATION_ID"/>
		<table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
	      	<tr>
				<th class="nui-form-label"><label for="type$text">档案种类：</label></th>
					<td colspan="4" >  
		        		<input class="nui-combobox" name="applyData.FILES_TYPE" id="files_type" data="filesType" style="width:150px;"
       					onvaluechanged="onFilesTypeChanged" valueField="id" textField="text" emptyText="请选择"  nullItemText="请选择" showNullItem="true"/> 
		        	</td>
		        <th class="nui-form-label"><label for="type$text">附件上传：</label></th>
		      	<td colspan="4">
		      		<!-- <div id="i_select_files" >
						</div> -->
					<input type="button" class="btn btn-default" id="i_select_files"  value="添加文件"/>
	        	</td>
	      	</tr>
	      	<tr id="fieldset1" style="display: none">	        
		        <td style="text-align:left;" colspan="26">
			    	<fieldset style="border: solid 1px #aaa; position: relative;">
						<legend style="text-align:left;">
							<label style="text-align:left;"></label>
						</legend>
						<table style="width:99%;height:100%;table-layout:fixed;" class="nui-form-table">
							<tr>
					      		<th class="nui-form-label"><label for="type$text">原档案名称：</label></th>
					      		<td colspan="4">
					      			<input name="inputData1.FILES_NAME" id="FILES_NAME" vtype="maxLength:50;" readOnly="true" class="nui-textbox" style="width:150px;"/>
					      		</td>
				  				<th class="nui-form-label"><label for="type$text">经办机构：</label></th>
				  				<td colspan="4">
				  					<input id="DEAL_ORG1" name = "inputData1.DEAL_ORG"  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;"/>
					      		</td>
					      		<th class="nui-form-label"><label for="type$text">管户信贷员：</label></th>
				  				<td colspan="4">
				  					<input id="EMPNAME1" name = "inputData1.EMPNAME"  class="nui-buttonedit"  allowInput="false" onbuttonclick="selectEmp1" style="width:150px;"/>
					      		</td>
					      		<th class="nui-form-label"><label for="type$text">客户名称：</label></th>
					      		<td colspan="4">
					      			<input name="inputData1.CUSTOMER_NAME" id="CUSTOMER_NAME1" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
					      		</td>
					      		
					      	</tr>
					      	<tr>
					      		<th class="nui-form-label"><label for="type$text">客户号码：</label></th>
					      		<td colspan="4">
					      			<input name="inputData1.CUSTOMER_NO" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
					      		</td>
					      		<th class="nui-form-label"><label for="type$text">存放地址：</label></th>
								<td colspan="4">
									<input id="STORAGE_ADDRESS" class="nui-dictcombobox" name="inputData1.STORAGE_ADDRESS"  emptyText="请选择"
				  					valueField="dictID" textField="dictName" dictTypeId="FILES_STORAGE_ADDRESS" showNullItem="true" nullItemText="请选择" style="width:150px;"/>
				  				</td>
					      		<th class="nui-form-label"><label for="type$text">货架号/箱号：</label></th>
					      		<td colspan="4">
					      			<input name="inputData1.STORAGE_LOCATION" id="STORAGE_LOCATION" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
					      		</td>
					      		<th class="nui-form-label"><label for="type$text">档案盒号：</label></th>
					      		<td colspan="4">
					      			<input name="inputData1.BOX_NUM" id="BOX_NUM" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
					      		</td>
					      	</tr>
					      	<tr>
					      		<th class="nui-form-label"><label for="type$text">贷后检查时间：</label></th>
					      		<td colspan="4">  
							 		<input name="inputData1.CHECK_TIME" id="CHECK_TIME1" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:150px;"/>
					        	</td>
					      		<th class="nui-form-label"><label for="type$text">贷后检查类型：</label></th>
								<td colspan="4">
									<input id="check_type" class="nui-dictcombobox" name="inputData1.CHECK_TYPE"  emptyText="请选择"
				  					valueField="dictID" textField="dictName" dictTypeId="CHECK_TYPE" showNullItem="true" nullItemText="请选择" style="width:150px;"/>
				  				</td>
						</table>
					</fieldset>
				</td>
			</tr>
	      	
	      	<tr id="fieldset2" style="display: none">	        
		        <td style="text-align:left;" colspan="26">
			    	<fieldset style="border: solid 1px #aaa; position: relative;">
						<legend style="text-align:left;">
							<label style="text-align:left;"></label>
						</legend>
						<table style="width:99%;height:100%;table-layout:fixed;" class="nui-form-table">
							<tr>
					        	<tr>
					      		<th class="nui-form-label"><label for="type$text">原档案名称：</label></th>
					      		<td colspan="4">
					      			<input name="inputData2.FILES_NAME" id="FILES_NAME" vtype="maxLength:50;"  readOnly="true" class="nui-textbox" style="width:150px;"/>
					      		</td>
				  				<th class="nui-form-label"><label for="type$text">移交机构：</label></th>
				  				<td colspan="4">
				  					<input id="DEAL_ORG2" name = "inputData2.DEAL_ORG"  class="nui-buttonedit"  allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;"/>
					      		</td>
					      		<th class="nui-form-label"><label for="type$text">移交人：</label></th>
				  				<td colspan="4">
				  					<input id="EMPNAME2" name = "inputData2.EMPNAME"  class="nui-buttonedit"  allowInput="false" onbuttonclick="selectEmp2" style="width:150px;"/>
					      		</td>
					      		<th class="nui-form-label"><label for="type$text">客户名称：</label></th>
					      		<td colspan="4">
					      			<input name="inputData2.CUSTOMER_NAME" id="CUSTOMER_NAME2" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
					      		</td>					      		
					      	</tr>
					      	<tr>
					      		<th class="nui-form-label"><label for="type$text">客户号码：</label></th>
					      		<td colspan="4">
					      			<input name="inputData2.CUSTOMER_NO" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
					      		</td>
					      		<th class="nui-form-label"><label for="type$text">存放地址：</label></th>
								<td colspan="4">
									<input id="STORAGE_ADDRESS" class="nui-dictcombobox" name="inputData2.STORAGE_ADDRESS"  emptyText="请选择"
				  					valueField="dictID" textField="dictName" dictTypeId="FILES_STORAGE_ADDRESS" showNullItem="true" nullItemText="请选择" style="width:150px;"/>
				  				</td>
					      		<th class="nui-form-label"><label for="type$text">货架号/箱号：</label></th>
					      		<td colspan="4">
					      			<input name="inputData2.STORAGE_LOCATION" id="STORAGE_LOCATION" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
					      		</td>
					      		<th class="nui-form-label"><label for="type$text">档案盒号：</label></th>
					      		<td colspan="4">
					      			<input name="inputData2.BOX_NUM" id="BOX_NUM" vtype="maxLength:50;" class="nui-textbox" style="width:150px;"/>
					      		</td>
					      	</tr>
					      	<tr>
					      		<th class="nui-form-label"><label for="type$text">查询时间：</label></th>
					      		<td colspan="4">  
							 		<input name="inputData2.CHECK_TIME" id="CHECK_TIME2" class="nui-datepicker" format="yyyyMMdd" allowInput="false" style="width:150px;"/>
					        	</td>
					      	</tr>
						</table>
					</fieldset>
				</td>
			</tr>
			<tr>
	      		<th class="nui-form-label"><label for="inputData.type$text">上传进度：</label></th>
	      		<td colspan="15">
					<!-- 回显进度 -->
					<div id="i_stream_files_queue" ></div>
	      		</td>
	      	</tr>
			
	    <div class="nui-toolbar" style="padding:0px;"   borderStyle="border:0;">
			<table width="100%">
		    	<tr>
		        	<td style="text-align:center;">
		          		<a class="nui-button" iconCls="icon-save" id="saveButtorn" onclick="saveData">提交</a>
		          		<span style="display:inline-block;width:25px;"></span>
		          		<a class="nui-button" iconCls="icon-cancel" onclick="CloseWindow('cancel')">取消</a>
		        	</td>
		      	</tr>
		   	</table>
		</div>
	</div>
	
<script type="text/javascript" src="../../js/stream-v1.js"></script> 
<script type="text/javascript">
	var filesType = [{ id: '10', text: '零售授信贷后档案' },{ id: '11', text: '零售支用贷后档案' },{ id: '12', text: '法人授信贷后档案' },
	{ id: '13', text: '法人支用贷后档案' },{ id: '14', text: '票据承兑贷后档案'},{ id: '15', text: '信审档案'}];
	var isLoan  = [{ id: "是", text: '是' },{ id: "否", text: '否' }];
	
    nui.parse();
    var interBusiForm = new nui.Form("interBusiForm");
    var fatherData = '';
    $('#saveButtorn').css('display','none');
    
    var files=new Array();
	//stream 插件配置
	var config = {
		browseFileId : "i_select_files", /** 选择文件的ID, 默认: i_select_files */
		browseFileBtn : "<div>请选择文件</div>", /** 显示选择文件的样式, 默认: `<div>请选择文件</div>` */
		dragAndDropArea: "i_select_files", /** 拖拽上传区域，Id（字符类型"i_select_files"）或者DOM对象, 默认: `i_select_files` */
		dragAndDropTips: "<span>把文件(文件夹)拖拽到这里</span>", /** 拖拽提示, 默认: `<span>把文件(文件夹)拖拽到这里</span>` */
		filesQueueId : "i_stream_files_queue", /** 文件上传容器的ID, 默认: i_stream_files_queue */
		filesQueueHeight : 200, /** 文件上传容器的高度（px）, 默认: 450 */
		messagerId : "i_stream_message_container", /** 消息显示容器的ID, 默认: i_stream_message_container */
		multipleFiles: true, /** 多个文件一起上传, 默认: false */
		onRepeatedFile: function(f) {
			alert("文件："+f.name +" 大小："+f.size + " 已存在于上传队列中。");
			return false;	
		},
//		autoUploading: false, /** 选择文件后是否自动上传, 默认: true */
//		autoRemoveCompleted : true, /** 是否自动删除容器中已上传完毕的文件, 默认: false */
//		maxSize: 104857600//, /** 单个文件的最大大小，默认:2G */
//		retryCount : 5, /** HTML5上传失败的重试次数 */
//		postVarsPerFile : { /** 上传文件时传入的参数，默认: {} */
//			param1: "val1",
//			param2: "val2"
//		},
		swfURL : "/swf/FlashUploader.swf" ,/** SWF文件的位置 */
		tokenURL : "<%=request.getContextPath()%>/tk", /** 根据文件名、大小等信息获取Token的URI（用于生成断点续传、跨域的令牌） */
		frmUploadURL : "<%=request.getContextPath()%>/fd", /** Flash上传的URI */
		uploadURL : "<%=request.getContextPath()%>/upload" ,/** HTML5上传的URI */
		filesQueueHeight :100,
//		simLimit: 200, /** 单次最大上传文件个数, */
//		extFilters: [".txt", ".rpm", ".rmvb", ".gz", ".rar", ".zip", ".avi", ".mkv", ".mp3"], /** 允许的文件扩展名, 默认: [] */
//		onSelect: function(list) {alert('onSelect')}, /** 选择文件后的响应事件 */
//		onMaxSizeExceed: function(size, limited, name) {alert('onMaxSizeExceed')}, /** 文件大小超出的响应事件 */
//		onFileCountExceed: function(selected, limit) {alert('onFileCountExceed')}, /** 文件数量超出的响应事件 */
//		onExtNameMismatch: function(name, filters) {alert('onExtNameMismatch')}, /** 文件的扩展名不匹配的响应事件 */
//		onCancel : function(file) {alert('Canceled:  ' + file.name)}, /** 取消上传文件的响应事件 */
		onComplete: function(file) {
			//alert(file.name);
			files.push(file);
			console.log(files);
		
		} /** 单个文件上传完毕的响应事件 */
//		onQueueComplete: function() {alert('onQueueComplete')} /** 所以文件上传完毕的响应事件 */
//		onUploadError: function(status, msg) {alert('onUploadError')} /** 文件上传出错的响应事件 */
//		onDestroy: function() {alert('onDestroy')} /** 文件上传出错的响应事件 */
	};
	//启动stream
	var _t = new Stream(config);
    
    function notRequired(){
    	nui.get("CUSTOMER_NAME1").setRequired(false);
		nui.get("CHECK_TIME1").setRequired(false);
    	nui.get("CUSTOMER_NAME2").setRequired(false);
		nui.get("CHECK_TIME2").setRequired(false);
    }
    
    
    //根据档案种类将隐藏区域显示
    function onFilesTypeChanged(){
    	$('#fieldset1').css('display','none');
    	$('#fieldset2').css('display','none');
    	$('#saveButtorn').css('display','');
    	var fileType = nui.get("files_type").getValue();
		if(fileType == '15'){
			$('#fieldset2').css('display','');
			notRequired();
			nui.get("CUSTOMER_NAME2").setRequired(true);
			nui.get("CHECK_TIME2").setRequired(true);
		}else if(fileType == '10' || fileType == '11' || fileType == '12' || fileType == '13' || fileType == '14'){
			$('#fieldset1').css('display','');
			notRequired();
			nui.get("CUSTOMER_NAME1").setRequired(true);
			nui.get("CHECK_TIME1").setRequired(true);
		}else{
			$('#saveButtorn').css('display','none');
		}
    }
    
    //关闭添加窗口
 	function CloseWindow(action){
 		if((action == 'cancel'||action == 'close') && files.length != 0){
 			var data = {};
 			data.files = files;
 			var json = nui.encode(data);
        $.ajax({
                url:"com.gotop.xmzg.files.fileList.delTempFiles.biz.ext",
                type:'POST',
                data:json,
                cache:false,
                async:false,
                contentType:'text/json',
                success:function(text){
                   var returnJson = nui.decode(text);
                   if(returnJson.exception == null && returnJson.flag == 1){
                        files.splice(0, files.length);//文件上传成功清空文件对象数组避免重复提交
                     }else{
                         alert("临时文件删除失败！");
                        }
                   }
             });
 		}
  		if(window.CloseOwnerWindow) 
    		return window.CloseOwnerWindow(action);
  		else
    		return window.close();
    }
    
    //保存数据
    function saveData(){
    	interBusiForm.validate();            
        if (interBusiForm.isValid() == false) return;
        var data = interBusiForm.getData(true,true);
        debugger;
        var files_type = nui.get("files_type").getValue();
       	data.inputData = fatherData;
        if(files_type == '15'){
        	var temp1 = nui.get("CUSTOMER_NAME2").getValue()
			+'-'+nui.get("files_type").getText()+'-'
			+nui.get("CHECK_TIME2").getFormValue();
			data.inputData2.FILES_TYPE = data.applyData.FILES_TYPE;
        	data.inputData2.FILES_NAME = temp1;
        }else{
        	var temp2 = nui.get("CUSTOMER_NAME1").getValue()
        	+'-'+nui.get("files_type").getText()+'-'
			+nui.get("CHECK_TIME1").getFormValue();
        	data.inputData1.FILES_TYPE = data.applyData.FILES_TYPE;
        	data.inputData1.FILES_NAME = temp2;
        }
        var data1={data:data,filesType:files_type,files:files};
        var json = nui.encode(data1);
        debugger;
        var URL="com.gotop.xmzg.files.fileLedger.submitApply.biz.ext";
        $.ajax({
        	url: URL,
            type: 'POST',
            data: json,
            cache: false,
            contentType:'text/json',
            success: function (text){
                var returnJson = nui.decode(text);
				if(returnJson.exception == null && returnJson.flag == "1"){
					nui.alert("提交成功", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							CloseWindow("saveSuccess");
						}
					});
				}else if(returnJson.exception == null && returnJson.flag == "exist"){
					nui.alert("档案名称已存在！");
				}else{
					nui.alert("提交失败", "系统提示", function(action){
						if(action == "ok" || action == "close"){
							//CloseWindow("saveFailed");
						}
					});
				}
		    }
  		});   
    }
	
	//机构树回显
     function OrgonButtonEdit(e){
            var btnEdit = this;
            nui.open({
                url:"<%=request.getContextPath() %>/files/archiveList/org_tree.jsp",
                showMaxButton: false,
                title: "选择树",
                width: 350,
                height: 350,
                ondestroy: function (action) {                    
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = nui.clone(data);
                        if (data) {
                            btnEdit.setValue(data.ID);
                            btnEdit.setText(data.TEXT);
                            //将值机构id变成机构code
                            var data={orgId:data.ID};
					        var json = nui.encode(data);
					        var URL="com.gotop.xmzg.files.fileLedger.getOrgcode.biz.ext";
					        $.ajax({
					        	url: URL,
					            type: 'POST',
					            data: json,
					            async:false,
					            cache: false,
					            contentType:'text/json',
					            success: function (text){
					                var returnJson = nui.decode(text);
					                var result = returnJson.resultList;
					                btnEdit.setValue(result[0].ORGCODE);
							    }
					  		}); 
                        }
                    }
                }
            });            
        } 
        
        //人员树回显
        function selectEmp1(){
    		var emp = nui.get("EMPNAME1");
            nui.open({
                url:"<%=request.getContextPath()%>/files/archiveList/empTree.jsp?type=emp",
                title: "选择人员",
                width: 600,
                height: 400,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = emp.getValue();
                	//frame.contentWindow.setData(data);
                	frame.contentWindow.setTreeCheck(data.value);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);  //必须
                        if (data) {
                            emp.setValue(data.nodeId);
                            emp.setText(data.nodeName);
                            //将值人员id转换成人员code
                            var data={empId:data.nodeId};
					        var json = nui.encode(data);
					        var URL="com.gotop.xmzg.files.fileLedger.getEmpcode.biz.ext";
					        $.ajax({
					        	url: URL,
					            type: 'POST',
					            data: json,
					            async:false,
					            cache: false,
					            contentType:'text/json',
					            success: function (text){
					                var returnJson = nui.decode(text);
					                var result = returnJson.resultList;
					                emp.setValue(result[0].EMPCODE);
							    }
					  		});
                        }
                    }

                }
            });
    	}
    	
    	
    	//人员树回显
        function selectEmp2(){
    		var emp = nui.get("EMPNAME2");
            nui.open({
                url:"<%=request.getContextPath()%>/files/archiveList/empTree.jsp?type=emp",
                title: "选择人员",
                width: 600,
                height: 400,
                onload:function(){
                	var frame = this.getIFrameEl();
                	var data = {};
                	data.value = emp.getValue();
                	//frame.contentWindow.setData(data);
                	frame.contentWindow.setTreeCheck(data.value);
                },
                ondestroy: function (action) {
                    //if (action == "close") return false;
                    if (action == "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.getData();
                        data = nui.clone(data);    //必须
                        if (data) {
                            emp.setValue(data.nodeId);
                            emp.setText(data.nodeName);
                            //将值人员id转换成人员code
                            var data={empId:data.nodeId};
					        var json = nui.encode(data);
					        var URL="com.gotop.xmzg.files.fileLedger.getEmpcode.biz.ext";
					        $.ajax({
					        	url: URL,
					            type: 'POST',
					            data: json,
					            async:false,
					            cache: false,
					            contentType:'text/json',
					            success: function (text){
					                var returnJson = nui.decode(text);
					                var result = returnJson.resultList;
					                emp.setValue(result[0].EMPCODE);
							    }
					  		}); 
                        }
                    }

                }
            });
    	}  
    	
    	
    	
    	//与父页面的方法2对应：页面间传输json数据
    function setFormData(data){
        //跨页面传递的数据对象，克隆后才可以安全使用
    	var infos = nui.clone(data);
        //如果是点击编辑类型页面
        if (infos.pageType == "apply") {
	      	//保存父页面数据
        	fatherData = infos.record;
         	nui.get("DEAL_ORG1").setText(fatherData.DEAL_NAME);
         	nui.get("EMPNAME1").setText(fatherData.EMP_NAME);
         	nui.get("DEAL_ORG2").setText(fatherData.DEAL_NAME);
         	nui.get("EMPNAME2").setText(fatherData.EMP_NAME);
         	var form = new nui.Form("#interBusiForm");//将普通form转为nui的form
	    	var temp = {inputData1:fatherData,inputData2:fatherData};
	        form.setData(temp);
        }
    }
</script>
</body>
</html>
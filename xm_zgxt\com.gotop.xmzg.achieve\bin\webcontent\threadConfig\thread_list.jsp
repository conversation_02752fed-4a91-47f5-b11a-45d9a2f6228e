<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-05-29 16:10:11
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>线程配置</title>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <table class="table" style="width:100%;">
			<tr>	
				<th  class="tit">存储过程名称：</th>
				<td >
					<input id="PROC_ID" name = "queryData.PROC_ID" class="nui-textbox" style="width:100%;" />  
				</td>
				<th  class="tit">存储过程中文名称：</th>
				<td >
					<input id="PROC_NAME" name = "queryData.PROC_NAME" class="nui-textbox" style="width:100%;" />  
				</td>
				
				
			</tr>
			<tr>	
				<th  class="tit">前置存储过程名称：</th>
				<td >
					<input id="PROC_PRE_ID" name = "queryData.PROC_PRE_ID" class="nui-textbox" style="width:100%;" />  
				</td>
				<th  class="tit">前置存储过程中文名称：</th>
				<td >
					<input id="PROC_PRE_NAME" name = "queryData.PROC_PRE_NAME" class="nui-textbox" style="width:100%;" />  
				</td>
				
				<th>
				  <a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;
				  <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
				</th>
			</tr>
	    </table>
	  </div>
    </div>
  </div>
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
		     <a class="nui-button" iconCls="icon-add" onclick="add">增加</a>
		     <a id="update" class="nui-button" iconCls="icon-edit" onclick="update">修改</a>
		     <a class="nui-button" iconCls="icon-remove" onclick="remove">删除</a>
           </td>
        </tr>
      </table>
    </div>
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;"
	  url="com.gotop.xmzg.achieve.thread.thread_list.biz.ext" multiSelect="true"
	  sizeList=[5,10,20,50,100]  pageSize="10">
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      <div field="PROC_ID" headerAlign="center" align="center">存储过程名称</div>
	      <div field="PROC_NAME" headerAlign="center" align="center">存储过程中文名称</div>
	      <div field="PROC_DAYS_START" headerAlign="center" align="center" >开始时间</div>
	      <div field="PROC_DAYS_END" headerAlign="center" align="center" >结束时间</div>
	      <div field="PROC_PRE_ID" headerAlign="center" align="center">前置存储过程名称</div>
	      <div field="PROC_PRE_NAME" headerAlign="center" align="center" >前置存储过程中文名称</div>
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">  
    nui.parse();
 	
    var grid = nui.get("datagrid1");
    var form = new nui.Form("#form1");
    search();
    
    function search(){
       form.validate();
       if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
     
     function add(){
      nui.open({
          url:"<%=request.getContextPath() %>/achieve/threadConfig/thread_form.jsp",
          title:'新增',
          width:500,
          height:300,
          onload:function(){
          	var iframe = this.getIFrameEl();
	             var data = {pageType:"add"};
                  iframe.contentWindow.setData(data);
          },
          ondestroy:function(action){
             if(action=="saveSuccess"){
	            grid.reload();
	         }
          }
       });
    }
    
      function update(){
      var rows = grid.getSelecteds();
		if(rows.length > 1 || rows.length == 0){
			nui.alert("请选中一条记录");
		}else{
	       nui.open({
	          url:"<%=request.getContextPath() %>/achieve/threadConfig/thread_form.jsp",
	          title:'编辑',
	          width:500,
	          height:300,
	          onload:function(){
	             var iframe = this.getIFrameEl();
	             var data = {pageType:"edit",record:{map:rows[0]}};
                  iframe.contentWindow.setData(data);
	          },
	          ondestroy:function(action){
	             if(action=="saveSuccess"){
	                grid.reload();
	             }
	          }
	       });
       }
    }
    
    
 function remove(){
      var rows = grid.getSelecteds();
     if(rows.length > 0){
         nui.confirm("确定删除选中记录？","系统提示",
           function(action){
             if(action=="ok"){
	           var json = nui.encode({maps:rows});
	           var a= nui.loading("正在执行中,请稍等...","提示");
		        $.ajax({
		          url:"com.gotop.xmzg.achieve.thread.thread_delete.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	nui.hideMessageBox(a);
		            var result = text.result;
					if(result !=null && result.code==1){
						nui.alert(result.msg, "系统提示", function(action){
							grid.reload();
						});
					}else{
						nui.alert(result.msg, "系统提示");
						grid.unmask();
					}
		          }
		        });
		     }
		   });
      }else{
        nui.alert("请选中一条记录！");
      }
    }
    
    
  function CloseWindow(action){
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
  
   	//重置
    function clean(){
	   form.reset();
    }
 
  </script>
</body>
</html>
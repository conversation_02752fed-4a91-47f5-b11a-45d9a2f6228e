<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-05-29 16:10:11
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>积分预算配置</title>
</head>
<body style="width:100%;overflow:hidden;">
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <table class="table" style="width:100%;">
			<tr>
				<th  class="tit">考核方案：</th>
				<td >
					<input id="TA_ID" name="queryData.TA_ID" class="nui-combobox" 
        			url="com.gotop.xmzg.achieve.report.loadTACombox.biz.ext"
        			textField="TEXT" valueField="ID"  dataField="list" style="width:200px;" 
        			emptyText="全部" showNullItem="true" nullItemText="全部"/>
				</td>
					
				<th  class="tit">年份：</th>
				<td >
					<input class="nui-spinner" id="TIB_YEAR" name="queryData.TIB_YEAR" minValue="2020" maxValue="2100" allowNull="true" style="width:200px;" 
					format="#0000" />
				</td>
				<th>
				  <a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;
				  <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
				</th>
			</tr>
	    </table>
	  </div>
    </div>
  </div>
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
		     <a class="nui-button" iconCls="icon-edit" onclick="imp">导入</a>
           </td>
        </tr>
      </table>
    </div>
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;"
	  url="com.gotop.xmzg.achieve.config.tib_list.biz.ext" multiSelect="true"
	  sizeList=[5,10,20,50,100]  pageSize="10">
	    <div property="columns" >
	      <div type="checkcolumn"></div> 
	      <div field="TIB_YEAR" headerAlign="center" align="center">年份</div>
	      <div field="TA_NAME" headerAlign="center" align="center">考核方案</div>
	      <div field="TIP_CODE" headerAlign="center" align="center">业务条线code</div>
	      <div field="TIP_NAME" headerAlign="center" align="center">业务条线名称</div>
	      <div field="TIB_INTEGRAL" headerAlign="center" align="center" >积分预算</div>
	      <div field="EMPNAME" headerAlign="center" align="center" >操作人</div>
	      <div field="CREATE_TIME" headerAlign="center" align="center">创建日期</div>
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">  
    nui.parse();
 	nui.get("TIB_YEAR").setValue(null);
    var grid = nui.get("datagrid1");
    var form = new nui.Form("#form1");
    search();
    
    function search(){
       form.validate();
       console.log(form.isValid() )
       if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
     
    function imp(){
		nui.open({
	        url:"<%=request.getContextPath()%>/achieve/config/tib_imp.jsp",
	        title: "导入积分预算数据",
	        width: 550,
	        height: 280,
	        ondestroy: function (action) {
	    		grid.reload();
	        }
	    });	
	}
  function CloseWindow(action){
      if(window.CloseOwnerWindow) 
        return window.CloseOwnerWindow(action);
      else
        return window.close();
    }
  
   	//重置
    function clean(){
	   form.reset();
    }
    
  </script>
</body>
</html>
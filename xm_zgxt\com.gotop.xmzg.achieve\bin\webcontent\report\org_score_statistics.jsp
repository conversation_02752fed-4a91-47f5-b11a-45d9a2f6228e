<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): 51075
  - Date: 2022-12-22 11:28:19
  - Description:
-->
<head>
<style>
#table1 .tit{
	height: 10px;
    line-height: 10px;
    	text-align: right;
}
#table1 td{
	height: 10px;
    line-height: 10px;
}
#table1 .roleLabel{
	text-align: right;
}
#table1 .roleText{
	padding-left: 5px;
}
</style>
<%@include file="/coframe/tools/skins/common.jsp" %>
<%@ page import="com.eos.data.datacontext.UserObject" %>
<title>人员业绩统计</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
  <script type="text/javascript" src="/default/common/nui/nui.js"></script>
	<script type="text/javascript" src="/default/common/nui/locale/zh_CN.js"></script>
	<link id="css_skin" rel="stylesheet" type="text/css" href="/default/coframe/tools/skins/skin1/css/style.css" />
    
</head>
<% 
	UserObject userObject = (UserObject)request.getSession().getAttribute("userObject");

 %>
<body>
<div class="search-condition">
		<div class="list">
			<div id="form1">
			<table class="table" style="width:100%;">							
					<tr>	
					<td class="tit" STYLE="width:100px;">开户日期:</td>
						<td>
							<input id="queryData.OPEN_DT" class="nui-datepicker" name="queryData.OPEN_DT"  style="width:150px;" allowInput="false" required="true" />	
						</td>

                    <td class="tit" STYLE="width:100px;">客户名称:</td>
						<td>
					<input id="queryData.CUST_NAME" name = "queryData.CUST_NAME"  class="nui-textbox"  style="width:150px;" />
                        </td>
                        
                     <td class="tit" STYLE="width:100px;">养老金账号:</td>
						<td>
					<input id="queryData.MEDIUM_NO" name = "queryData.MEDIUM_NO"  class="nui-textbox"  style="width:150px;" />
                        </td>   
                        	  <th rowspan="3"><a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp; 
				            <input class="nui-button" text="重置" iconCls="icon-reset" onclick="clean"/>
				           <!--  <br><br><a class="nui-button"  iconCls="icon-edit" onclick="exportData">人员业绩统计</a> -->
				       </th>			      
					</tr>
					<tr>	
                          <td class="tit" STYLE="width:100px;">开户机构:</td>
					<td>
							<input id="queryData.OPEN_ORG" name = "queryData.OPEN_ORG"  class="nui-buttonedit" allowInput="false" onbuttonclick="OrgonButtonEdit" style="width:150px;" required="true"/>
						</td>
                          <td class="tit" STYLE="width:100px;">营销人员编号:</td>
						<td>
					<input id="queryData.YX_EMPCODE" name = "queryData.YX_EMPCODE"  class="nui-textbox"  style="width:150px;" />
                        </td>
                                 <td class="tit" STYLE="width:100px;">营销人员名称:</td>
						<td>
					<input id="queryData.YX_EMPNAME" name = "queryData.YX_EMPNAME"  class="nui-textbox"  style="width:150px;" />
                        </td>
                        	</tr>
					<tr>
					 <td class="tit" STYLE="width:100px;">开户手机号:</td>
						<td>
					<input id="queryData.OPEN_PHONE" name = "queryData.OPEN_PHONE"  class="nui-textbox"  style="width:150px;" />
                        </td>
                        
                        
                        </tr>
                        				
				</table>
			</div>
		</div>
	</div>
	
	
<div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.achieve.report.org_score_statistics.biz.ext" 

	  sizeList=[5,10,20,50,100] multiSelect="false" pageSize="10">
	  
	    <div property="columns" >
	           
	            <div field="OPEN_ORG" >开户机构</div>
			    <div field="">当日开户数</div>
			    <div field="">累计开户数</div>	
			    <div field="">员工数</div>
			    <div field="">累计人均开户数</div>
			    <div field="">任务数</div>
			    <div field="">完成率</div>
			    <div field="">完成率排名</div>
			    <div field="">账户资产余额</div>   
			    <div field="">户均资产余额</div>
			    <div field="">缴存户数</div>
			    <div field="">缴存占比</div>
					
	    </div>
	 </div>
  </div>
</body>
<script type="text/javascript">

    nui.parse();
	var grid = nui.get("datagrid1");
    grid.load();
	function search(){
		
		/* loadGrids(); */
     var form = new nui.Form("#form1");
      form.validate();
        if (form.isValid() == false) return; 
        var data = form.getData(true,true);   
        
        grid.load(data);
    }
	
		function clean(){
	   var form = new nui.Form("#form1");
	   form.clear();
	}
	
	function OrgonButtonEdit(e) {	
        var btnEdit = this;
        nui.open({
            url:"<%=request.getContextPath() %>/achieve/report/org_tree.jsp",
        showMaxButton: false,
        title: "选择树",
        width: 350,
        height: 350,
        ondestroy: function (action) {                    
            if (action == "ok") {
                var iframe = this.getIFrameEl();
                var data = iframe.contentWindow.GetData();
                data = nui.clone(data);
                if (data) {
                    btnEdit.setValue(data.CODE);
                    btnEdit.setText(data.TEXT);
                    var form = new nui.Form("#form1");
       				form.validate();
                    
                }
            }
        }
    	});            
	}
		
	
	
	
    </script>
</html>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%
	String contextPath=request.getContextPath();
%>

<script type="text/javascript" src="<%=contextPath%>/common/nui/nui.js"></script>
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <title></title>
 
  	<style type="text/css">
    html,body
    {
        padding:0;
        margin:0;
        border:0;     
        width:100%;
        height:100%;
        overflow:hidden;   
    }
    </style>
</head>
<body>
    <div class="nui-toolbar" style="text-align:center;line-height:30px;" 
        borderStyle="border-left:0;border-top:0;border-right:0;">
          <label >名称：</label>
          <input id="key" class="nui-textbox" style="width:150px;" onenter="onKeyEnter"/>
          <a class="nui-button" style="width:60px;" onclick="search()">查询</a>
    </div>
    <div class="nui-fit">
        
        <div id="grid1" class="nui-datagrid" style="width:100%;height:90%;" 
            textField="TEXT" idField="ID" allowResize="true" dataField="treeNodes"
            borderStyle="border-left:0;border-right:0;"
            multiSelect="true" 
        >
        
         <div property="columns">
                <div type="checkcolumn" ></div>
                <div field="TEXT" width="120" headerAlign="center" allowSort="true" align="center">业务角色</div>    
         </div>
    
    </div>                
    <div class="nui-toolbar" style="text-align:center;padding-top:8px;padding-bottom:8px;" borderStyle="border:0;">
        <a class="nui-button" style="width:60px;" onclick="onOk()">确定</a>
        <span style="display:inline-block;width:25px;"></span>
        <a class="nui-button" style="width:60px;" onclick="chooseAll()">全选</a>
        <span style="display:inline-block;width:25px;"></span>
        <a class="nui-button" style="width:60px;" onclick="onCancel()">取消</a>
    </div>
    </div>
  <script type="text/javascript">
    nui.parse();

    var grid = nui.get("grid1");

    //动态设置URL
    grid.setUrl("org.gocom.components.coframe.userInfoManage.userInfoManage.get_Role.biz.ext");
    
    grid.load();

    var firstLoad = true;
    var initIds;                   //存放初始数据id，这个作为选中数据。

    function selectRowsByIds(ids) {
        if (ids) {
            var rows = [];
            for (var i = 0, l = ids.length; i < l; i++) {
                var o = grid.getRow(ids[i]);
                if (o) rows.push(o);
            }
            grid.selects(rows);
        }
    }

    function SetData(ids) {

        if (typeof ids == "string") {
            ids = ids.split(",");     //"1,2" => [1, 2]
        }
        initIds = ids;
    }

    function GetSelecteds() {
        var rows = grid.getSelecteds();
        return rows;
    }
    function GetData() {    
        var rows = grid.getSelecteds();
        var ids = [], texts = [];
        for (var i = 0, l = rows.length; i < l; i++) {
            var row = rows[i];
            ids.push(row.ID);
            texts.push(row.TEXT);
        }
        var data = {};
        data.id = ids.join(",");
        data.text = texts.join(",");
        return data;
    }

    function search() {
        var key = nui.get("key").getValue();
        grid.load({ key: key });
    }
    function onKeyEnter(e) {
        search();
    }
    //////////////////////////////////
    function CloseWindow(action) {
        if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
        else window.close();
    }

    function onOk() {
        CloseWindow("ok");
    }
    function onCancel() {
        CloseWindow("cancel");
    }
    
    /* 
 	*  实现全选复选框
 	*/
	function chooseAll(){
		grid.selectAll();
	}
    
</script>
</body>
</html>

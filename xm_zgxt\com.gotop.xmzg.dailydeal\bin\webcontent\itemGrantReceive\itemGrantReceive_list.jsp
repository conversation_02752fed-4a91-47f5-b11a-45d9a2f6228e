<%@page pageEncoding="UTF-8"%>
<%-- <%@ include file="/common/common.jsp"%>
<%@ include file="/common/skins/skin0/component-debug.jsp"%> --%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): administrator
  - Date: 2018-05-23 12:01:11
  - Description:
-->
<head>
<%@include file="/coframe/tools/skins/common.jsp" %>
<title>单册发放-收到</title>
</head>
<body style="width:100%;overflow:hidden;">
 
 <div class="search-condition">
   <div class="list">
	  <div id="form1">
	    <table class="table" style="width:100%;">
	      <tr >
		        <th class="tit">请领日期：</th>	
				<td> 
					<input id="queryData.startDate" class="nui-datepicker" name="queryData.apply_time1"  style="width:120px;" allowInput="false"/>
	                 ~
	                <input id="queryData.endDate" class="nui-datepicker" name="queryData.apply_time2"  style="width:120px;" allowInput="false" onvalidation="comparedate"/>
				</td>
				<th class="tit">单册代码：</th>
				<td>
					<input id="item_no" name="queryData.item_no" class="nui-textbox" style="width:150px;" vtype="maxLength:250"/>
				</td>
			</tr>
			<tr>	
				<th class="tit">单册名称：</th>
				<td>
					<input id="item_name" name="queryData.item_name" class="nui-textbox" style="width:150px;" vtype="maxLength:10"/>
				</td>
				<th class="tit">状态：</th>
				<td>
				    <!--<input class="nui-combobox" name="queryData.APPROVAL_STATUS" id="res" data="Opinions" style="width:200px;" value="4"
		                   emptyText="请选择"  nullItemText="请选择" showNullItem="true"/>-->
		            <input class="nui-combobox"emptyText="全部"id="res" data="Opinions" name="queryData.APPROVAL_STATUS" showNullItem="true" nullItemText="全部" 
	          	      style="width:150px;" value="4"/>		   					
				</td>
				
		     <td>
	            <a class="nui-button"  iconCls="icon-search" onclick="search">查询</a>&nbsp;&nbsp;&nbsp;
	            <a class="nui-button" iconCls="icon-reset"  onclick="clean"/>重置</a>
	         </td>
	        </tr>
	    </table>
	  </div>
    </div>
  </div>
  
  
    <div class="nui-toolbar" style="border-bottom:0;padding:0px;">
      <table style="width:100%;">
        <tr>
           <td style="width:100%;">
		      <a id="update" class="nui-button" iconCls="icon-edit" onclick="update">发放收到</a> 
           </td>
        </tr>
      </table>
    </div>
 
  
  <div class="nui-fit">
	 <div id="datagrid1" dataField="resultList" class="nui-datagrid" style="width:100%;height:100%;" idField="id"
	  url="com.gotop.xmzg.dailydeal.itemGrantReceive.query_itemGrantReceive.biz.ext"
	  sizeList=[5,10,20,50,100] multiSelect="true" pageSize="10">
	    <div property="columns" >
	      <div type="checkcolumn"></div>
	      <div field="APPLY_TIME" headerAlign="center" align="center">请领日期</div>
	      <div field="APPROVAL_STATUSNAME" headerAlign="center" align="center">状态</div>
	      <!--<div field="ORGCODE" headerAlign="center" align="center" width=130>请领机构号</div>
	      <div field="APPLYORGNAME" headerAlign="center" align="center" width=150>请领机构名称</div>-->
	      <div field="ITEM_NO" headerAlign="center" align="center">单册代码</div>
	      <div field="NO" headerAlign="center" align="center">编号</div>
	      <div field="ITEM_NAME" headerAlign="center" align="center" width=250>单册名称</div>
	      <div field="UNIT" headerAlign="center" align="center" width=130>单位</div>
	      <div field="PRE_TAX_PRICE" headerAlign="center" align="center" width=130>税前单价</div>
	      <div field="TAX_PRICE" headerAlign="center" align="center">含税单价</div> 
	      <div field="APPLY_NUM" headerAlign="center" align="center" >请领数量</div> 
	      <div field="REAL_NUM" headerAlign="center" align="center">实发数量</div>
	      <div field="APPLYEMPNAME" headerAlign="center" align="center">请领人</div>
		  <div field="APPLYSUBMITNAME" headerAlign="center" align="center">请领授权人</div>
		  <div field="ITEM_ADDRESS" headerAlign="center" align="center" width=130>单册出处</div>
		  <!--<div field="GRANTEMPNAME" headerAlign="center" align="center">发放授权人</div>-->
	    </div>
	 </div>
  </div>
  
  <script type="text/javascript">
    nui.parse();
    var Opinions = [{ id: "4", text: '已下发' }, { id: "5", text: '下发已收到'}];
    nui.get("res").load(Opinions);
    
    var grid = nui.get("datagrid1");
    var form = new nui.Form("#form1");
    var data = form.getData(true,true);
    grid.load(data);
    
    
    function search(){

       var form = new nui.Form("#form1");
            form.validate();
       if (form.isValid() == false) return;
       var data = form.getData(true,true);
       grid.load(data);
    }
    
    function update(){
      var rows = grid.getSelecteds();
      if(rows.length > 0){
      for(var i=0;i<rows.length;i++)
      {
      		if(rows[i].APPROVAL_STATUS=='5')
      		{
      			nui.alert("该请领记录已做了收到，不能再操作！");
      			return false;
      		}
      }
     /*
      for(var i=0;i<rows.length;i++)
      {
      		if(rows[i].APPROVAL_STATUS!='4')
      		{
      			nui.alert("该请领记录还没下发，不可做下发已收到操作！");
      			return false;
      		}
      }
     */
         nui.confirm("确定收到选中记录？","系统提示",
           function(action){
             if(action=="ok"){
	            var json = nui.encode({maps:rows});
	           var a= nui.loading("正在执行中,请稍等...","提示");
		        $.ajax({
		          url:"com.gotop.xmzg.dailydeal.itemGrantReceive.update_itemGrantReceive.biz.ext",
		          type:'POST',
		          data:json,
		          cache: false,
		          contentType:'text/json',
		          success:function(text){
		          	nui.hideMessageBox(a);
		            var returnJson = nui.decode(text);
					if(returnJson.exception == null && returnJson.iRtn==1){
						nui.alert("收到成功", "系统提示", function(action){
							grid.reload();
						});
					}else{
						nui.alert("收到失败", "系统提示");
						grid.unmask();
					}
					
		          }
		        });
		     }
		   });
      }else{
        nui.alert("请选中一条记录！");
      }
    }
    
 
    
    
    //重置
    function clean(){
	   //不能用form.reset(),否则会把隐藏域信息清空
       nui.get("queryData.startDate").setValue("");
       nui.get("queryData.endDate").setValue("");
       nui.get("item_no").setValue("");
       nui.get("item_name").setValue("");
       nui.get("res").setValue("");
     }
   
       //时间判断开始时间不能大于结束时间
      function comparedate(e){
      var startDate = nui.get("queryData.startDate").getFormValue();
      var endDate = nui.get("queryData.endDate").getFormValue();
      if(startDate!="")
	    startDate=startDate.substring(0,4) + startDate.substring(5,7) + startDate.substring(8,10);
	  if(endDate!=""){
		endDate=endDate.substring(0,4) + endDate.substring(5,7) + endDate.substring(8,10);
        if(e.isValid){
          if(startDate>endDate){
            e.errorText="结束日期必须大于或等于开始日期";
            e.isValid=false;
          }else
          {
          e.errorText="";
          e.isValid=true;
          }
        }
      }
    } 
     
  </script>
</body>
</html>
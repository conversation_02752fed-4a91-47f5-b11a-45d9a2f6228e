<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%
	String contextPath=request.getContextPath();
%>

<script type="text/javascript" src="<%=contextPath%>/common/nui/nui.js"></script>

<html xmlns="http://www.w3.org/1999/xhtml">
<%@page import="com.eos.system.utility.StringUtil"%>
<head>
    <title></title>
   
    <link rel="stylesheet" type="text/css" href="<%=contextPath%>/coframe/tools/icons/icon.css"/>
    
    <style type="text/css">
    html,body
    {
        padding:0;
        margin:0;
        border:0;     
        width:100%;
        height:100%;
        overflow:hidden;   
    }
    </style>
</head>
<body>
    
     <div class="nui-toolbar" style="text-align:center;line-height:30px;" 
        borderStyle="border-left:0;border-top:0;border-right:0;">
          <label >名称：</label>
          <input id="key" class="nui-textbox" style="width:150px;" onenter="onKeyEnter"/>
          <a class="nui-button" style="width:60px;" onclick="search()">查询</a>
    </div>
    
            <div class="nui-fit">
       <ul id="tree" class="nui-tree" style="width:100%;height:100%;" showTreeIcon="true" 
       		textField="text" idField="id" parentField="pid"  dataField="dataList" resultAsTree="false"  
            expandOnLoad="true" onnodedblclick="onNodeDblClick" expandOnDblClick="false"
            onbeforeload="onBeforeTreeLoad"
            url="com.gotop.xmzg.achieve.report.selectEmpOrgZtree.biz.ext" 
            >
        </ul> 

</div>

              
    <div class="nui-toolbar" style="text-align:center;padding-top:8px;padding-bottom:8px;" 
        borderStyle="border-left:0;border-bottom:0;border-right:0;">
        <a class="nui-button" style="width:60px;" onclick="onOk()">确定</a>
        <span style="display:inline-block;width:25px;"></span>
        <a class="nui-button" style="width:60px;" onclick="onCancel()">取消</a>
    </div> 

</body>
</html>
<script type="text/javascript">
    nui.parse();

    var tree = nui.get("tree");
    
   tree.expandLevel(0); //展开第一级  就是展示厦门分行及下的
    
     function search() {
    
        var key = nui.get("key").getValue();
        if(key == ""){
            tree.clearFilter();
        }else{
            key = key.toLowerCase();
            tree.filter(function (node) {
                var text = node.text ? node.text.toLowerCase() : "";
                if (text.indexOf(key) != -1) {
                    return true;
                }
            });
        }
    }
    
    function beforenodeselect(e) {

    //组织机构不能选 
    if (e.node.nodeType=="OrgOrganization") {
        e.cancel = true;
    }
}

//回显
    //function GetData() {
    //    var node = tree.getSelectedNode();
    //    return node;
   // }
    
   function getData() {
		return tree.getSelectedNode();        
    }
    
    function setTreeCheck(idStr) {
        var tree = nui.get("tree");
    	tree.setValue(idStr);
    } 
	
	//组织机构树的加载事件
 	    function onBeforeTreeLoad(e) {
 	    
		e.params.nodeId = e.node.realId; 
    } 
    
	function GetData() {
        var node = tree.getSelectedNode();
        return node;
    }
   
    function onNodeDblClick(e) {
        onOk();
    }

    function onKeyEnter(e) {
        search();
    }
  
    function CloseWindow(action) {
        if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
        else window.close();
    }
    function onOk() {
         var node = tree.getSelectedNode();
         if(node.nodeType=="OrgEmployee"){
         	CloseWindow("ok");
         }else{
         	nui.alert("请选择人员");
         	return;
         }
        
    }
    function onCancel() {
        CloseWindow("cancel");
    }

    
</script>
<%@page import="com.alibaba.fastjson.JSONObject"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<%@include file="/coframe/dict/common.jsp"%>
</head>

<body>
  <form id="form1" action="com.gotop.xmzg.achieve.FtpImp.flow" method="post" enctype="multipart/form-data" style="padding-top:5px;">
    <table style="width:100%;height:100%;table-layout:fixed;" class="nui-form-table">
      
      
      <tr>
        <th class="nui-form-label"><label for="map.type$text">业务类型：</label></th>
        <td colspan="3" >    
      		<input name="paramMap/TJF_BUSI_TYPE" required="true" class="nui-dictcombobox" valueField="dictID" textField="dictName"  dictTypeId="JF_FTP_BUSI"  style="width:100%;"/>
        </td> 
      </tr>
      
      <tr>
        <th class="nui-form-label"><label for="map.type$text">开始时间：</label></th>
        <td colspan="3" >    
      		<input id="TJF_START" name="paramMap/TJF_START" required="true" class="nui-datepicker" allowInput="false" format="yyyyMMdd"  style="width:100%;"/>
        </td> 
      </tr>
      <tr>
        <th class="nui-form-label"><label for="map.type$text">选择文件：</label></th>
        <td colspan="3" >    
      		<input id="file" type="file" name="importFile" size="60" style="width:100%;"/>
        </td> 
      </tr>
    </table>     
    <div class="nui-toolbar" style="padding:0px;" borderStyle="border:0;">
	    <table width="100%">
	      <tr>
	        <td style="text-align:center;">
	          <a class="nui-button" iconCls="icon-save" onclick="downloadTemplate">下载模板</a>&nbsp;&nbsp;
	          <a class="nui-button" iconCls="icon-save" onclick="onOk">导入</a>&nbsp;&nbsp;
	         
	          <a class="nui-button" iconCls="icon-cancel" onclick="onCancel">取消</a>
	        </td>
	      </tr>
	    </table>
	 </div>
  </form>

  <script type="text/javascript">
    nui.parse();
    var form = new nui.Form("#form1");
	<%
    		JSONObject result = (JSONObject)request.getAttribute("result");
 	%>
	var result = <%=result %>;
	if(result != null && result != ""){
		console.log(result);
		nui.alert(result.msg);
		
	}
    	
    var objs = [];
    function setFormData(data){
    }
       
     
    function onOk(){
    	form.validate();
      	if(form.isValid()==false) return;
      
		var fileName = $("#file").val();
		if(fileName == null || fileName == "") {
			nui.alert("请选择导入的文件！");
			return;
		}
		var sufFileName = fileName.substring(fileName.lastIndexOf("."));
		if(sufFileName != ".xls" && sufFileName != ".xlsx"){
			nui.alert("请选择.xls或.xlsx文件");
			return;
		}
    	nui.loading("正在操作中,请稍等...","提示");
    	$("#form1").submit();	
    }
    
    function onCancel(){
        CloseWindow("close");
    }
    
    function CloseWindow(action){
     	var flag = form.isChanged();
		if(window.CloseOwnerWindow) 
		    return window.CloseOwnerWindow(action);
		else
		    return window.close();
    }
    
    function downloadTemplate(){
		var file_name = "ftp导入模板.xlsx";
		var url="<%=request.getContextPath() %>/achieve/templateDownload.jsp?filename="+file_name+"&filename2="+file_name+"&filePath=/achieve/config";
        window.location.replace(encodeURI(url));
	}
  </script>
</body>
</html>
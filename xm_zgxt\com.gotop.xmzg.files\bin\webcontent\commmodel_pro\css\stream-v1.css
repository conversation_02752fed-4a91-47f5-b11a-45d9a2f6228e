@charset "utf-8";
body {
	color: #000;
	line-height: 166.6%;
	float: left;
	font-family: verdana;
	font-size: 12px;
}

ul,ol {
	list-style: none;
}

ul,ol,li,img {
	border: 0 none;
	margin: 0;
	padding: 0;
}

.stream-browse-files {
	overflow: hidden;
	position: relative;
}

.stream-browse-drag-files-area {
	border: 2px dashed #555;
	padding: 10px 0;
	border-radius: 7px;
	text-align: center;
	margin: 10px 0;
	cursor: pointer;
}

.stream-disable-browser {
	color: #909090;
}

.stream-files-scroll {
	height: 450px;
	overflow: auto;
}

.stream-cell-file {
	cursor: default;
	position: relative;
	zoom: 1;
	padding: 10px 20px 10px 35px;
	border-bottom-width: 1px;
	border-bottom-style: dotted;
	border-color: #ccc;
}

.stream-cell-file .stream-cell-infos:before,.stream-cell-file .stream-cell-infos:after {
	clear: both;
	content: ".";
	font-size: 0;
	display: block;
	height: 0;
	overflow: hidden;
	visibility: hidden;
}

.stream-cell-file .stream-file-name {
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
}

.stream-cell-file .stream-process {
	zoom: 1;
	overflow: hidden;
}

.stream-cell-file .stream-cancel {
	margin-right: 5px;
	float: right;
}

a {
	color: #158144;
}

.stream-cell-file .stream-process-bar {
	width: 300px;
}

.stream-cell-file .stream-process-bar,.stream-cell-file .stream-percent {
	float: left;
	margin-right: 10px;
}

.stream-cell-file .stream-process-bar {
	margin-top: 5px;
	width: 430px;
}

.stream-process-bar {
	position: relative;
	border: 1px solid #ccc;
	background: #fff;
	width: 55px;
	height: 10px;
	overflow: hidden;
}

.stream-process-bar span {
	position: absolute;
	left: 0;
	top: 0;
	height: 8px;
	border: 1px solid #fff;
	font-size: 0;
	background-position: 0 -149px;
	background-image: url(./img/bgx.png);
	background-repeat: repeat-x;
	background-color: #A5DD3D;
}

.stream-process-bar,.stream-process-bar span {
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-khtml-box-sizing: border-box;
	box-sizing: border-box;
}

.stream-cell-file .stream-cell-infos {
	zoom: 1;
	color: #7D7D7D;
}

.stream-cell-file .stream-cell-infos .stream-cell-info {
	width: 170px;
}

.stream-cell-file .stream-cell-infos span {
	float: left;
	margin-right: 8px;
}

.stream-total-tips .stream-process-bar {
	width: 200px;
	margin-top: -1px;
}

.stream-process-bar, .stream-uploading-ico {
	-moz-box-align: center;
	display: inline-block;
	vertical-align: middle;
	zoom: 1;
}

.stream-total-tips {
	border: 1px solid #ccc;
	border-width: 1px 0 0;
	padding: 3px 5px 3px 25px;
	position: relative;
	zoom: 1;
	background-color: #FFFFE1;
	color: #565656;
}

.stream-main-upload-box {
	width: 610px;
	background-color: #FFFFFF;
	border-style: solid;
	border-width: 1px;
	border-color: #50A05B;
	clear: both;
	overflow: hidden;
}

.stream-uploading-ico {
	left: 11px;
	position: absolute;
	top: 11px;
	background: url("./img/upload.gif") no-repeat scroll 0 1px transparent !important;
	height: 18px;
	width: 18px;
}

.stream-disabled {
	cursor: not-allowed;
	pointer-events: none;
	opacity: .65;
	filter: alpha(opacity=65);
	-webkit-box-shadow: none;
	box-shadow: none;
	color: #909090;
}
